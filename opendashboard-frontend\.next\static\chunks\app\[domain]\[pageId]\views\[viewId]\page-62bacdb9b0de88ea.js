!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="eac4768d-a359-4984-a8d6-60fdacb5eded",e._sentryDebugIdIdentifier="sentry-dbid-eac4768d-a359-4984-a8d6-60fdacb5eded")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3414,8107],{85532:function(e,t,r){Promise.resolve().then(r.bind(r,35787))},53731:function(e,t){"use strict";var r=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;t.G=function(e){if(!e||e.length>254||!r.test(e))return!1;var t=e.split("@");return!(t[0].length>64||t[1].split(".").some(function(e){return e.length>63}))}},30166:function(e,t,r){"use strict";r.d(t,{default:function(){return l.a}});var n=r(55775),l=r.n(n)},99376:function(e,t,r){"use strict";var n=r(35475);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},55775:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(47043);r(57437),r(2265);let l=n._(r(15602));function o(e,t){var r;let n={loading:e=>{let{error:t,isLoading:r,pastDelay:n}=e;return null}};"function"==typeof e&&(n.loader=e);let o={...n,...t};return(0,l.default)({...o,modules:null==(r=o.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81523:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return l}});let n=r(18993);function l(e){let{reason:t,children:r}=e;if("undefined"==typeof window)throw new n.BailoutToCSRError(t);return r}},15602:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let n=r(57437),l=r(2265),o=r(81523),i=r(70049);function a(e){return{default:e&&"default"in e?e.default:e}}let s={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},d=function(e){let t={...s,...e},r=(0,l.lazy)(()=>t.loader().then(a)),d=t.loading;function u(e){let a=d?(0,n.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,s=t.ssr?(0,n.jsxs)(n.Fragment,{children:["undefined"==typeof window?(0,n.jsx)(i.PreloadCss,{moduleIds:t.modules}):null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(l.Suspense,{fallback:a,children:s})}return u.displayName="LoadableComponent",u}},70049:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return o}});let n=r(57437),l=r(20544);function o(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let r=(0,l.getExpectedRequestStore)("next/dynamic css"),o=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files.filter(e=>e.endsWith(".css"));o.push(...t)}}return 0===o.length?null:(0,n.jsx)(n.Fragment,{children:o.map(e=>(0,n.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},23500:function(e,t){"use strict";var r,n,l;t.Bq=t.Ly=t.bW=void 0,(r=t.bW||(t.bW={})).Table="table",r.Board="board",r.Form="form",r.Document="document",r.Dashboard="dashboard",r.SummaryTable="summary-table",r.ListView="list-view",r.Calendar="calendar",(n=t.Ly||(t.Ly={})).Left="left",n.Right="right",(l=t.Bq||(t.Bq={})).Infobox="infobox",l.LineChart="lineChart",l.BarChart="barChart",l.PieChart="pieChart",l.FunnelChart="funnelChart",l.Embed="embed",l.Image="image",l.Text="text"},35787:function(e,t,r){"use strict";r.r(t);var n=r(57437),l=r(99376),o=r(54921);r(2265);var i=r(29119),a=r(1009);let s=e=>{let{id:t,children:r}=e,{viewsMap:l}=(0,i.qt)(),o=l[t].definition.databaseId;return(0,n.jsx)(n.Fragment,{children:void 0===o?(0,n.jsx)(n.Fragment,{children:r}):(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(a.r,{id:o,refreshOnInitialRender:!0,children:(0,n.jsx)(n.Fragment,{children:r})})})})};t.default=()=>{let e=(0,l.useParams)().viewId;return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(s,{id:e,children:(0,n.jsx)(o.o,{id:e})})})}},1009:function(e,t,r){"use strict";r.d(t,{n:function(){return g},r:function(){return y}});var n=r(57437),l=r(2265),o=r(61192),i=r(29119),a=r(14805),s=r(42212),d=r(84440),u=r(99376),c=r(95473),f=r(59315),h=r(45419),p=r(12381),b=r(45402),m=r(51810),v=r(14803),w=r(52292);let g=e=>{let{databasePageStore:t,databaseStore:r}=(0,s.cF)(),l=t[e.id],o=r[e.id],i=l&&l.accessLevel;return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(y,{id:e.id,refreshOnInitialRender:!0,children:(0,n.jsx)(n.Fragment,{children:i&&l&&o&&(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(x,{database:o,databasePage:l,accessLevel:i,children:e.children})})})})})},x=e=>{let{database:t,databasePage:r}=e,[s,u]=(0,l.useState)(!1),c=0===t.database.definition.columnIds.length;return(0,n.jsxs)(n.Fragment,{children:[s&&(0,n.jsx)(h.j,{database:t.database,close:()=>u(!1)}),c?(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(d.PageLoader,{size:"full",error:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"my-4",children:(0,n.jsx)(b.Z,{width:48,height:48,className:"inline"})}),(0,n.jsx)("span",{className:"text-sm font-medium",children:"Database is empty"}),(0,n.jsxs)("div",{className:"my-4 flex justify-center items-center gap-2",children:[(0,n.jsx)(m.yF,{databaseId:t.database.id,trigger:(0,n.jsx)(p.z,{variant:"ghost",className:"text-xs h-8 font-semibold",children:"Add column"})}),(0,n.jsx)(p.z,{className:"text-xs px-4 h-8 text-center items-center font-semibold",onClick:()=>u(!0),children:"Import from file"})]})]})})}):(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(o.x,{database:t.database,children:(0,n.jsx)(i.Ti,{permissiblePage:r,id:r.page.id,children:(0,n.jsx)(a.G,{children:e.children})})})})]})},y=e=>{let{url:t,databasePageStore:r,databaseStore:o}=(0,s.cF)(),{refreshDatabase:i}=(0,c.Bf)(),a=!1,h=(0,v.cL)(),p=(0,w.nM)();try{a=(0,f.Kn)().isConnected}catch(e){(h||p)&&(a=!0)}let b=(0,u.useRouter)(),m=r[e.id],g=o[e.id];m&&m.accessLevel;let x=g&&m?"":"Entity not found",[y,j]=(0,l.useState)(!!e.refreshOnInitialRender||!g);return(0,l.useEffect)(()=>{a&&y&&i(e.id).finally(()=>{j(!1)})},[y,i,e.id,a]),(0,n.jsxs)(n.Fragment,{children:[(y||x)&&(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(d.PageLoader,{error:x,size:"full",cta:x?{label:"Go Home",onClick:()=>b.replace(t())}:null})}),!y&&m&&g&&(0,n.jsx)(n.Fragment,{children:e.children})]})}},54921:function(e,t,r){"use strict";r.d(t,{o:function(){return m}});var n=r(57437),l=r(23500),o=r(2265),i=r(30166),a=r(29119);let s=(0,i.default)(()=>Promise.all([r.e(1092),r.e(6018),r.e(8025),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(6462),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(7674),r.e(1506),r.e(7561),r.e(663),r.e(3879),r.e(4376),r.e(4239),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3139),r.e(7515),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(8267),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(6240)]).then(r.bind(r,26240)).then(e=>e.DashboardView),{loadableGenerated:{webpack:()=>[26240]},ssr:!1}),d=(0,i.default)(()=>Promise.all([r.e(8025),r.e(6018),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818)]).then(r.bind(r,97017)).then(e=>e.TableView),{loadableGenerated:{webpack:()=>[97017]},ssr:!1}),u=(0,i.default)(()=>Promise.all([r.e(6018),r.e(7360),r.e(696),r.e(8025),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(6640),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(6326),r.e(4805),r.e(8849),r.e(8603)]).then(r.bind(r,89034)).then(e=>e.BoardView),{loadableGenerated:{webpack:()=>[89034]},ssr:!1}),c=(0,i.default)(()=>Promise.all([r.e(6018),r.e(7360),r.e(696),r.e(8025),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(631)]).then(r.bind(r,60631)).then(e=>e.FormView),{loadableGenerated:{webpack:()=>[60631]},ssr:!1}),f=(0,i.default)(()=>Promise.all([r.e(6018),r.e(2191),r.e(7190),r.e(696),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(8107),r.e(5737),r.e(794),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(7918)]).then(r.bind(r,87918)).then(e=>e.DocumentView),{loadableGenerated:{webpack:()=>[87918]},ssr:!1}),h=(0,i.default)(()=>Promise.all([r.e(8025),r.e(6018),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(9625)]).then(r.bind(r,79625)).then(e=>e.SummaryTableView),{loadableGenerated:{webpack:()=>[79625]},ssr:!1}),p=(0,i.default)(()=>Promise.all([r.e(6018),r.e(8025),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(826),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(8792)]).then(r.bind(r,68792)).then(e=>e.ListView),{loadableGenerated:{webpack:()=>[68792]},ssr:!1}),b=(0,i.default)(()=>Promise.all([r.e(6018),r.e(8025),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(6640),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(6326),r.e(4805),r.e(8849),r.e(2949)]).then(r.bind(r,39910)).then(e=>e.CalendarView),{loadableGenerated:{webpack:()=>[39910]},ssr:!1}),m=e=>{let{viewsMap:t}=(0,a.qt)(),r=e.id,i=e.view||t[r],m=i.type;return(0,o.useEffect)(()=>{document.title=(null==i?void 0:i.name)||"Untitled"},[]),(0,n.jsx)(n.Fragment,{children:m===l.bW.Table?(0,n.jsx)(d,{view:i,definition:i.definition}):m===l.bW.Board?(0,n.jsx)(u,{view:i,definition:i.definition}):m===l.bW.Form?(0,n.jsx)(c,{view:i,definition:i.definition}):m===l.bW.Document?(0,n.jsx)(f,{view:i,definition:i.definition}):m===l.bW.SummaryTable?(0,n.jsx)(h,{view:i,definition:i.definition}):m===l.bW.Dashboard?(0,n.jsx)(s,{view:i,definition:i.definition}):m===l.bW.ListView?(0,n.jsx)(p,{view:i,definition:i.definition}):m===l.bW.Calendar?(0,n.jsx)(b,{view:i,definition:i.definition}):(0,n.jsx)(n.Fragment,{})})}},29119:function(e,t,r){"use strict";r.d(t,{Ti:function(){return o},ol:function(){return a},qt:function(){return s}});var n=r(57437),l=r(2265);let o=e=>{let{page:t,views:r,accessLevel:l,permissions:o}=e.permissiblePage,a={};for(let e of r)a[e.id]=e;return(0,n.jsx)(i.Provider,{value:{page:t,views:r,accessLevel:l,permissions:o,viewsMap:a},children:e.children})},i=(0,l.createContext)(void 0),a=()=>(0,l.useContext)(i)||null,s=()=>{let e=(0,l.useContext)(i);if(!e)throw Error("usePage must be used within a PageProvider");return e}},40178:function(e,t,r){"use strict";var n=r(2265);let l=n.forwardRef(function(e,t){let{title:r,titleId:l,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},o),r?n.createElement("title",{id:l},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"}))});t.Z=l},62484:function(e,t,r){"use strict";function n(e,[t,r]){return Math.min(r,Math.max(t,e))}r.d(t,{u:function(){return n}})},43643:function(e,t,r){"use strict";r.d(t,{Ns:function(){return q},fC:function(){return Y},gb:function(){return S},l_:function(){return Z},q4:function(){return z}});var n=r(2265),l=r(66840),o=r(71599),i=r(73966),a=r(98575),s=r(26606),d=r(29114),u=r(61188),c=r(62484),f=r(6741),h=r(57437),p="ScrollArea",[b,m]=(0,i.b)(p),[v,w]=b(p),g=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:o="hover",dir:i,scrollHideDelay:s=600,...u}=e,[c,f]=n.useState(null),[p,b]=n.useState(null),[m,w]=n.useState(null),[g,x]=n.useState(null),[y,j]=n.useState(null),[S,P]=n.useState(0),[C,E]=n.useState(0),[R,T]=n.useState(!1),[L,_]=n.useState(!1),W=(0,a.e)(t,e=>f(e)),D=(0,d.gm)(i);return(0,h.jsx)(v,{scope:r,type:o,dir:D,scrollHideDelay:s,scrollArea:c,viewport:p,onViewportChange:b,content:m,onContentChange:w,scrollbarX:g,onScrollbarXChange:x,scrollbarXEnabled:R,onScrollbarXEnabledChange:T,scrollbarY:y,onScrollbarYChange:j,scrollbarYEnabled:L,onScrollbarYEnabledChange:_,onCornerWidthChange:P,onCornerHeightChange:E,children:(0,h.jsx)(l.WV.div,{dir:D,...u,ref:W,style:{position:"relative","--radix-scroll-area-corner-width":S+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});g.displayName=p;var x="ScrollAreaViewport",y=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:o,nonce:i,...s}=e,d=w(x,r),u=n.useRef(null),c=(0,a.e)(t,u,d.onViewportChange);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,h.jsx)(l.WV.div,{"data-radix-scroll-area-viewport":"",...s,ref:c,style:{overflowX:d.scrollbarXEnabled?"scroll":"hidden",overflowY:d.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,h.jsx)("div",{ref:d.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});y.displayName=x;var j="ScrollAreaScrollbar",S=n.forwardRef((e,t)=>{let{forceMount:r,...l}=e,o=w(j,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=o,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===o.type?(0,h.jsx)(P,{...l,ref:t,forceMount:r}):"scroll"===o.type?(0,h.jsx)(C,{...l,ref:t,forceMount:r}):"auto"===o.type?(0,h.jsx)(E,{...l,ref:t,forceMount:r}):"always"===o.type?(0,h.jsx)(R,{...l,ref:t}):null});S.displayName=j;var P=n.forwardRef((e,t)=>{let{forceMount:r,...l}=e,i=w(j,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,h.jsx)(o.z,{present:r||a,children:(0,h.jsx)(E,{"data-state":a?"visible":"hidden",...l,ref:t})})}),C=n.forwardRef((e,t)=>{var r,l;let{forceMount:i,...a}=e,s=w(j,e.__scopeScrollArea),d="horizontal"===e.orientation,u=U(()=>p("SCROLL_END"),100),[c,p]=(r="hidden",l={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let r=l[e][t];return null!=r?r:e},r));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>p("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,s.scrollHideDelay,p]),n.useEffect(()=>{let e=s.viewport,t=d?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(p("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,d,p,u]),(0,h.jsx)(o.z,{present:i||"hidden"!==c,children:(0,h.jsx)(R,{"data-state":"hidden"===c?"hidden":"visible",...a,ref:t,onPointerEnter:(0,f.M)(e.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:(0,f.M)(e.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),E=n.forwardRef((e,t)=>{let r=w(j,e.__scopeScrollArea),{forceMount:l,...i}=e,[a,s]=n.useState(!1),d="horizontal"===e.orientation,u=U(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(d?e:t)}},10);return X(r.viewport,u),X(r.content,u),(0,h.jsx)(o.z,{present:l||a,children:(0,h.jsx)(R,{"data-state":a?"visible":"hidden",...i,ref:t})})}),R=n.forwardRef((e,t)=>{let{orientation:r="vertical",...l}=e,o=w(j,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,d]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=M(s.viewport,s.content),c={...l,sizes:s,onSizesChange:d,hasThumb:!!(u>0&&u<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function f(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",l=V(r),o=t||l/2,i=r.scrollbar.paddingStart+o,a=r.scrollbar.size-r.scrollbar.paddingEnd-(l-o),s=r.content-r.viewport;return H([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,h.jsx)(T,{...c,ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){let e=B(o.viewport.scrollLeft,s,o.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollLeft=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollLeft=f(e,o.dir))}}):"vertical"===r?(0,h.jsx)(L,{...c,ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){let e=B(o.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollTop=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollTop=f(e))}}):null}),T=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:l,...o}=e,i=w(j,e.__scopeScrollArea),[s,d]=n.useState(),u=n.useRef(null),c=(0,a.e)(t,u,i.onScrollbarXChange);return n.useEffect(()=>{u.current&&d(getComputedStyle(u.current))},[u]),(0,h.jsx)(D,{"data-orientation":"horizontal",...o,ref:c,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":V(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{u.current&&i.viewport&&s&&l({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:k(s.paddingLeft),paddingEnd:k(s.paddingRight)}})}})}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:l,...o}=e,i=w(j,e.__scopeScrollArea),[s,d]=n.useState(),u=n.useRef(null),c=(0,a.e)(t,u,i.onScrollbarYChange);return n.useEffect(()=>{u.current&&d(getComputedStyle(u.current))},[u]),(0,h.jsx)(D,{"data-orientation":"vertical",...o,ref:c,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":V(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{u.current&&i.viewport&&s&&l({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:k(s.paddingTop),paddingEnd:k(s.paddingBottom)}})}})}),[_,W]=b(j),D=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:o,hasThumb:i,onThumbChange:d,onThumbPointerUp:u,onThumbPointerDown:c,onThumbPositionChange:p,onDragScroll:b,onWheelScroll:m,onResize:v,...g}=e,x=w(j,r),[y,S]=n.useState(null),P=(0,a.e)(t,e=>S(e)),C=n.useRef(null),E=n.useRef(""),R=x.viewport,T=o.content-o.viewport,L=(0,s.W)(m),W=(0,s.W)(p),D=U(v,10);function F(e){C.current&&b({x:e.clientX-C.current.left,y:e.clientY-C.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==y?void 0:y.contains(t))&&L(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[R,y,T,L]),n.useEffect(W,[o,W]),X(y,D),X(x.content,D),(0,h.jsx)(_,{scope:r,scrollbar:y,hasThumb:i,onThumbChange:(0,s.W)(d),onThumbPointerUp:(0,s.W)(u),onThumbPositionChange:W,onThumbPointerDown:(0,s.W)(c),children:(0,h.jsx)(l.WV.div,{...g,ref:P,style:{position:"absolute",...g.style},onPointerDown:(0,f.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),C.current=y.getBoundingClientRect(),E.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),F(e))}),onPointerMove:(0,f.M)(e.onPointerMove,F),onPointerUp:(0,f.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=E.current,x.viewport&&(x.viewport.style.scrollBehavior=""),C.current=null})})})}),F="ScrollAreaThumb",z=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,l=W(F,e.__scopeScrollArea);return(0,h.jsx)(o.z,{present:r||l.hasThumb,children:(0,h.jsx)(A,{ref:t,...n})})}),A=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:o,...i}=e,s=w(F,r),d=W(F,r),{onThumbPositionChange:u}=d,c=(0,a.e)(t,e=>d.onThumbChange(e)),p=n.useRef(void 0),b=U(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{if(b(),!p.current){let t=G(e,u);p.current=t,u()}};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,b,u]),(0,h.jsx)(l.WV.div,{"data-state":d.hasThumb?"visible":"hidden",...i,ref:c,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:(0,f.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;d.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.M)(e.onPointerUp,d.onThumbPointerUp)})});z.displayName=F;var I="ScrollAreaCorner",N=n.forwardRef((e,t)=>{let r=w(I,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,h.jsx)(O,{...e,ref:t}):null});N.displayName=I;var O=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...o}=e,i=w(I,r),[a,s]=n.useState(0),[d,u]=n.useState(0),c=!!(a&&d);return X(i.scrollbarX,()=>{var e;let t=(null===(e=i.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),u(t)}),X(i.scrollbarY,()=>{var e;let t=(null===(e=i.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),c?(0,h.jsx)(l.WV.div,{...o,ref:t,style:{width:a,height:d,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function k(e){return e?parseInt(e,10):0}function M(e,t){let r=e/t;return isNaN(r)?0:r}function V(e){let t=M(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function B(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=V(t),l=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,o=t.scrollbar.size-l,i=t.content-t.viewport,a=(0,c.u)(e,"ltr"===r?[0,i]:[-1*i,0]);return H([0,i],[0,o-n])(a)}function H(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var G=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return function l(){let o={left:e.scrollLeft,top:e.scrollTop},i=r.left!==o.left,a=r.top!==o.top;(i||a)&&t(),r=o,n=window.requestAnimationFrame(l)}(),()=>window.cancelAnimationFrame(n)};function U(e,t){let r=(0,s.W)(e),l=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),n.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(r,t)},[r,t])}function X(e,t){let r=(0,s.W)(t);(0,u.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var Y=g,Z=y,q=N}},function(e){e.O(0,[6018,8025,7360,696,7698,2191,7190,8310,8218,9442,9900,3572,7902,5501,1425,6137,7648,311,2534,4451,1107,85,3493,3139,7515,5737,794,9175,7353,7900,2211,2212,6208,3818,4805,991,2971,6577,1744],function(){return e(e.s=85532)}),_N_E=e.O()}]);