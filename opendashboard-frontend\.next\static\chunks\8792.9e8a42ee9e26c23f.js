!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="9b2c9e47-f87f-4708-827a-4ccea970cb65",e._sentryDebugIdIdentifier="sentry-dbid-9b2c9e47-f87f-4708-827a-4ccea970cb65")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8792],{68792:function(e,a,t){"use strict";t.r(a),t.d(a,{ListView:function(){return I}});var s=t(57437),l=t(2265),r=t(42212),d=t(68738),i=t(95473),c=t(18626),n=t(82375),o=t(47899),b=t(56227),u=t(54207),f=t(59122),x=t(65994),h=t(32659),m=t(24754),p=t(29119),v=t(16033),D=t(41709),y=t(25881),k=t(35579),w=t(14803),N=t(52292),j=t(63127),g=t(22581),C=t(99376),T=t(32469);t(47069);let F=e=>{let{field:a,row:t,databaseId:l}=e,r={column:{key:a.id,__meta__:{databaseId:l,column:a,triggerEdit:!1,headerLocked:!0,contentLocked:!0},idx:0,name:a.title,frozen:!1,resizable:!1,sortable:!1,width:150,minWidth:50,maxWidth:void 0,cellClass:void 0,headerCellClass:void 0,editable:!1},row:t,rowIdx:0,tabIndex:-1,onRowChange:()=>{},isCellSelected:!1,selectCell:()=>{},isRowSelected:!1},i=n.tV;switch(a.type){case d.DatabaseFieldDataType.AI:i=x.c;break;case d.DatabaseFieldDataType.UUID:i=n.AO;break;case d.DatabaseFieldDataType.Number:case d.DatabaseFieldDataType.Text:case d.DatabaseFieldDataType.Derived:i=n.tV;break;case d.DatabaseFieldDataType.Linked:i=m.N4;break;case d.DatabaseFieldDataType.Summarize:i=v.oZ;break;case d.DatabaseFieldDataType.Select:i=h.V5;break;case d.DatabaseFieldDataType.Checkbox:i=o.I;break;case d.DatabaseFieldDataType.Date:case d.DatabaseFieldDataType.CreatedAt:case d.DatabaseFieldDataType.UpdatedAt:i=b.JK;break;case d.DatabaseFieldDataType.Person:case d.DatabaseFieldDataType.CreatedBy:case d.DatabaseFieldDataType.UpdatedBy:i=u.ZC;break;case d.DatabaseFieldDataType.Files:i=f.ih;break;case d.DatabaseFieldDataType.ScannableCode:i=y.qT;break;case d.DatabaseFieldDataType.ButtonGroup:i=D.cf;break;default:i=n.tV}return(0,s.jsx)(i,{...r})},I=e=>{var a,t;let{databaseStore:n,databaseErrorStore:o,members:b,workspace:u,url:f}=(0,r.cF)(),{definition:x}=e,{cache:h,setPeekRecordId:m}=(0,i.Bf)(),{filter:v,sorts:D,search:y}=(0,i.Jy)(),{selectedIds:I,setSelectedIds:L}=(0,i.eX)(),{accessLevel:A}=(0,p.qt)(),_=(0,w.cL)(),E=(0,k.pB)();(0,C.useRouter)();let R=(0,C.usePathname)(),{openRecord:S}=(0,T.x)(),z=(0,l.useRef)(null),V=(0,l.useRef)(null);x.filter=x.filter||{conditions:[],match:d.Match.All},x.sorts=x.sorts||[];let B=n[x.databaseId],M=!!_;x.lockContent;let U=(0,N.nM)();U||_||M||x.lockContent,U||_||M||x.lockContent,(0,l.useEffect)(()=>{let e=z.current,a=V.current;if(!e||!a)return;let t=()=>{a.scrollLeft=e.scrollLeft},s=()=>{e.scrollLeft=a.scrollLeft};return e.addEventListener("scroll",t),a.addEventListener("scroll",s),()=>{e.removeEventListener("scroll",t),a.removeEventListener("scroll",s)}},[]);let G=(()=>{if(!B)return[];let e=[];D.length>0?e.push(...D):x.sorts.length>0&&e.push(...x.sorts),0===e.length&&e.push({columnId:d.MagicColumn.CreatedAt,order:d.Sort.Asc});let a=h.getCache("newlyCreatedRecords"),t=a&&Array.isArray(a)?a:[],{rows:s}=(0,c.filterAndSortRecords)(B,b,n,x.filter,v,e,u.workspaceMember.userId,"",null==E?void 0:E.recordInfo.record.id,t);return s})(),P=(0,c.searchFilteredRecords)(y,G),W=(()=>{let e=[];if(!B)return e;let a=B.database.definition;if(!a)return e;let{columnsOrder:t,columnPropsMap:s}=x;for(let e of(t=Array.isArray(t)?t:[],s=s||{},a.columnIds))t.includes(e)||t.push(e),s[e]||(s[e]={});for(let l of t){let t=a.columnsMap[l];t&&(s[l].isHidden||e.push(t))}return e})();if(!B)return(0,s.jsx)("div",{className:"h-64 flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Loading..."})})});let q=(0,j.$P)(B.database),J=null==E?void 0:null===(t=E.recordInfo)||void 0===t?void 0:null===(a=t.record)||void 0===a?void 0:a.id;R.includes("/records/")&&R.endsWith("/records/".concat(J));let Z=(e,a)=>{x.lockContent||S(e,a)};return(0,s.jsx)("div",{className:"w-full h-full overflow-hidden listView",children:(0,s.jsxs)("div",{className:"overflow-hidden size-full flex flex-col",children:[!M&&x.lockContent&&(0,s.jsx)("div",{className:"p-2 border-b bg-yellow-50 text-xs text-center border-neutral-300 font-medium",children:"Content is locked, record navigation is disabled"}),(0,s.jsxs)("div",{className:"flex-1 overflow-hidden scroll-wrapper",children:[(0,s.jsx)("div",{className:"content-container",children:(0,s.jsx)("div",{ref:z,className:"content-horizontal-scroll",children:(0,s.jsxs)("div",{className:"scroll-container",children:[(0,s.jsxs)("div",{className:"border-b rowGrid border-neutral-200 header-row",children:[(0,s.jsx)("div",{className:"text-xs text-black font-bold bg-white check !w-1"}),(0,s.jsx)("div",{className:"text-xs text-black font-bold bg-white fluid",children:"Title"}),W.map(e=>(0,s.jsx)("div",{className:"text-xs text-black font-bold bg-white",children:e.title},e.id))]}),0===P.length?null:P.map(e=>{let a=(0,j.T5)(e.record,q.titleColId,q.defaultTitle,q.isContacts,B.database,b);return(0,s.jsxs)("div",{className:"rowGrid border-b ".concat(x.lockContent?"cursor-default":"hover:bg-neutral-100 cursor-pointer"),onClick:a=>{let t=a.target,s=t.closest('.r-button-group, .r-scannable-code, .r-files, button, [role="button"]');console.log("Row click:",{target:t.tagName,isInteractiveField:s,className:t.className}),s||Z(e.record.id,e.record.databaseId)},children:[(0,s.jsx)("div",{className:"text-xs check !w-1"}),(0,s.jsxs)("div",{className:"text-xs flex flex-col fluid",children:[(0,s.jsx)("div",{className:"title-text text-xs font-semibold",children:a||"Untitled"}),(0,s.jsxs)("div",{className:"flex gap-2 text-xs text-muted-foreground pt-1",children:[(0,s.jsx)("span",{className:"truncate",children:B.database.name}),(0,s.jsx)("span",{className:"flex-shrink-0",children:"•"}),(0,s.jsx)("span",{className:"truncate",children:(0,g.S)(new Date(e.updatedAt))})]})]}),W.map(a=>(0,s.jsx)("div",{className:"text-xs truncate",children:(0,s.jsx)(F,{field:a,row:e,databaseId:x.databaseId,isPublishedView:M,lockContent:x.lockContent||!1})},a.id))]},e.id)})]})})}),(0,s.jsx)("div",{ref:V,className:"horizontal-scroll-container",children:(0,s.jsx)("div",{className:"horizontal-scroll-content",children:(0,s.jsxs)("div",{className:"rowGrid",style:{visibility:"hidden",height:"1px"},children:[(0,s.jsx)("div",{className:"check !w-1"}),(0,s.jsx)("div",{className:"fluid"}),W.map(e=>(0,s.jsx)("div",{},e.id))]})})})]})]})})}},47069:function(){}}]);