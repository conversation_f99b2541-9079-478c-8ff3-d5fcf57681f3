!function(){try{var t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="4b741604-365b-4b90-9bf9-1a9af641ba7a",t._sentryDebugIdIdentifier="sentry-dbid-4b741604-365b-4b90-9bf9-1a9af641ba7a")}catch(t){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2210],{61984:function(t,e,n){n.d(e,{Z:function(){return i}});let r={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function i(t={}){let e,n,o,u;let c=null,a=0,l=!1,s=!1,f=!1,d=!1;function p(){if(!o){if(y()){f=!0;return}l||n.emit("autoplay:play"),function(){let{ownerWindow:t}=n.internalEngine();t.clearTimeout(a),a=t.setTimeout(w,u[n.selectedScrollSnap()]),c=new Date().getTime(),n.emit("autoplay:timerset")}(),l=!0}}function m(){o||(l&&n.emit("autoplay:stop"),function(){let{ownerWindow:t}=n.internalEngine();t.clearTimeout(a),a=0,c=null,n.emit("autoplay:timerstopped")}(),l=!1)}function g(){if(y())return f=l,m();f&&p()}function y(){let{ownerDocument:t}=n.internalEngine();return"hidden"===t.visibilityState}function h(){s||m()}function b(){s||p()}function v(){s=!0,m()}function x(){s=!1,p()}function w(){let{index:t}=n.internalEngine(),r=t.clone().add(1).get(),i=n.scrollSnapList().length-1,o=e.stopOnLastSnap&&r===i;if(n.canScrollNext()?n.scrollNext(d):n.scrollTo(0,d),n.emit("autoplay:select"),o)return m();p()}return{name:"autoplay",options:t,init:function(c,a){n=c;let{mergeOptions:l,optionsAtMedia:s}=a,f=l(r,i.globalOptions);if(e=s(l(f,t)),n.scrollSnapList().length<=1)return;d=e.jump,o=!1,u=function(t,e){let n=t.scrollSnapList();return"number"==typeof e?n.map(()=>e):e(n,t)}(n,e.delay);let{eventStore:y,ownerDocument:w}=n.internalEngine(),S=!!n.internalEngine().options.watchDrag,O=function(t,e){let n=t.rootNode();return e&&e(n)||n}(n,e.rootNode);y.add(w,"visibilitychange",g),S&&n.on("pointerDown",h),S&&!e.stopOnInteraction&&n.on("pointerUp",b),e.stopOnMouseEnter&&y.add(O,"mouseenter",v),e.stopOnMouseEnter&&!e.stopOnInteraction&&y.add(O,"mouseleave",x),e.stopOnFocusIn&&n.on("slideFocusStart",m),e.stopOnFocusIn&&!e.stopOnInteraction&&y.add(n.containerNode(),"focusout",p),e.playOnInit&&p()},destroy:function(){n.off("pointerDown",h).off("pointerUp",b).off("slideFocusStart",m),m(),o=!0,l=!1},play:function(t){void 0!==t&&(d=t),p()},stop:function(){l&&m()},reset:function(){l&&p()},isPlaying:function(){return l},timeUntilNext:function(){return c?u[n.selectedScrollSnap()]-(new Date().getTime()-c):null}}}i.globalOptions=void 0},9467:function(t,e,n){n.d(e,{Z:function(){return I}});var r=n(2265);function i(t){return"[object Object]"===Object.prototype.toString.call(t)||Array.isArray(t)}function o(t,e){let n=Object.keys(t),r=Object.keys(e);return n.length===r.length&&JSON.stringify(Object.keys(t.breakpoints||{}))===JSON.stringify(Object.keys(e.breakpoints||{}))&&n.every(n=>{let r=t[n],u=e[n];return"function"==typeof r?`${r}`==`${u}`:i(r)&&i(u)?o(r,u):r===u})}function u(t){return t.concat().sort((t,e)=>t.name>e.name?1:-1).map(t=>t.options)}function c(t){return"number"==typeof t}function a(t){return"string"==typeof t}function l(t){return"boolean"==typeof t}function s(t){return"[object Object]"===Object.prototype.toString.call(t)}function f(t){return Math.abs(t)}function d(t){return Math.sign(t)}function p(t){return h(t).map(Number)}function m(t){return t[g(t)]}function g(t){return Math.max(0,t.length-1)}function y(t,e=0){return Array.from(Array(t),(t,n)=>e+n)}function h(t){return Object.keys(t)}function b(t,e){return void 0!==e.MouseEvent&&t instanceof e.MouseEvent}function v(){let t=[],e={add:function(n,r,i,o={passive:!0}){let u;return"addEventListener"in n?(n.addEventListener(r,i,o),u=()=>n.removeEventListener(r,i,o)):(n.addListener(i),u=()=>n.removeListener(i)),t.push(u),e},clear:function(){t=t.filter(t=>t())}};return e}function x(t=0,e=0){let n=f(t-e);function r(n){return n<t||n>e}return{length:n,max:e,min:t,constrain:function(n){return r(n)?n<t?t:e:n},reachedAny:r,reachedMax:function(t){return t>e},reachedMin:function(e){return e<t},removeOffset:function(t){return n?t-n*Math.ceil((t-e)/n):t}}}function w(t){let e=t;function n(t){return c(t)?t:t.get()}return{get:function(){return e},set:function(t){e=n(t)},add:function(t){e+=n(t)},subtract:function(t){e-=n(t)}}}function S(t,e){let n="x"===t.scroll?function(t){return`translate3d(${t}px,0px,0px)`}:function(t){return`translate3d(0px,${t}px,0px)`},r=e.style,i=null,o=!1;return{clear:function(){o||(r.transform="",e.getAttribute("style")||e.removeAttribute("style"))},to:function(e){if(o)return;let u=Math.round(100*t.direction(e))/100;u!==i&&(r.transform=n(u),i=u)},toggleActive:function(t){o=!t}}}let O={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function E(t,e,n){let r,i,o,u,I;let D=t.ownerDocument,k=D.defaultView,L=function(t){function e(t,e){return function t(e,n){return[e,n].reduce((e,n)=>(h(n).forEach(r=>{let i=e[r],o=n[r],u=s(i)&&s(o);e[r]=u?t(i,o):o}),e),{})}(t,e||{})}return{mergeOptions:e,optionsAtMedia:function(n){let r=n.breakpoints||{},i=h(r).filter(e=>t.matchMedia(e).matches).map(t=>r[t]).reduce((t,n)=>e(t,n),{});return e(n,i)},optionsMediaQueries:function(e){return e.map(t=>h(t.breakpoints||{})).reduce((t,e)=>t.concat(e),[]).map(t.matchMedia)}}}(k),F=(I=[],{init:function(t,e){return(I=e.filter(({options:t})=>!1!==L.optionsAtMedia(t).active)).forEach(e=>e.init(t,L)),e.reduce((t,e)=>Object.assign(t,{[e.name]:e}),{})},destroy:function(){I=I.filter(t=>t.destroy())}}),M=v(),N=function(){let t,e={},n={init:function(e){t=e},emit:function(r){return(e[r]||[]).forEach(e=>e(t,r)),n},off:function(t,r){return e[t]=(e[t]||[]).filter(t=>t!==r),n},on:function(t,r){return e[t]=(e[t]||[]).concat([r]),n},clear:function(){e={}}};return n}(),{mergeOptions:A,optionsAtMedia:T,optionsMediaQueries:j}=L,{on:P,off:H,emit:V}=N,z=!1,C=A(O,E.globalOptions),_=A(C),U=[];function B(e,n){!z&&(_=T(C=A(C,e)),U=n||U,function(){let{container:e,slides:n}=_;o=(a(e)?t.querySelector(e):e)||t.children[0];let r=a(n)?o.querySelectorAll(n):n;u=[].slice.call(r||o.children)}(),r=function e(n){let r=function(t,e,n,r,i,o,u){let s,O;let{align:E,axis:I,direction:D,startIndex:k,loop:L,duration:F,dragFree:M,dragThreshold:N,inViewThreshold:A,slidesToScroll:T,skipSnaps:j,containScroll:P,watchResize:H,watchSlides:V,watchDrag:z,watchFocus:C}=o,_={measure:function(t){let{offsetTop:e,offsetLeft:n,offsetWidth:r,offsetHeight:i}=t;return{top:e,right:n+r,bottom:e+i,left:n,width:r,height:i}}},U=_.measure(e),B=n.map(_.measure),R=function(t,e){let n="rtl"===e,r="y"===t,i=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(t){let{height:e,width:n}=t;return r?e:n},direction:function(t){return t*i}}}(I,D),$=R.measureSize(U),q={measure:function(t){return t/100*$}},J=function(t,e){let n={start:function(){return 0},center:function(t){return(e-t)/2},end:function(t){return e-t}};return{measure:function(r,i){return a(t)?n[t](r):t(e,r,i)}}}(E,$),X=!L&&!!P,{slideSizes:Z,slideSizesWithGaps:Q,startGap:Y,endGap:G}=function(t,e,n,r,i,o){let{measureSize:u,startEdge:c,endEdge:a}=t,l=n[0]&&i,s=function(){if(!l)return 0;let t=n[0];return f(e[c]-t[c])}(),d=l?parseFloat(o.getComputedStyle(m(r)).getPropertyValue(`margin-${a}`)):0,p=n.map(u),y=n.map((t,e,n)=>{let r=e===g(n);return e?r?p[e]+d:n[e+1][c]-t[c]:p[e]+s}).map(f);return{slideSizes:p,slideSizesWithGaps:y,startGap:s,endGap:d}}(R,U,B,n,L||!!P,i),K=function(t,e,n,r,i,o,u,a,l){let{startEdge:s,endEdge:d,direction:y}=t,h=c(n);return{groupSlides:function(t){return h?p(t).filter(t=>t%n==0).map(e=>t.slice(e,e+n)):t.length?p(t).reduce((n,c,l)=>{let p=m(n)||0,h=c===g(t),b=i[s]-o[p][s],v=i[s]-o[c][d],x=r||0!==p?0:y(u),w=f(v-(!r&&h?y(a):0)-(b+x));return l&&w>e+2&&n.push(c),h&&n.push(t.length),n},[]).map((e,n,r)=>{let i=Math.max(r[n-1]||0);return t.slice(i,e)}):[]}}}(R,$,T,L,U,B,Y,G,0),{snaps:W,snapsAligned:tt}=function(t,e,n,r,i){let{startEdge:o,endEdge:u}=t,{groupSlides:c}=i,a=c(r).map(t=>m(t)[u]-t[0][o]).map(f).map(e.measure),l=r.map(t=>n[o]-t[o]).map(t=>-f(t)),s=c(l).map(t=>t[0]).map((t,e)=>t+a[e]);return{snaps:l,snapsAligned:s}}(R,J,U,B,K),te=-m(W)+m(Q),{snapsContained:tn,scrollContainLimit:tr}=function(t,e,n,r,i){let o=x(-e+t,0),u=n.map((t,e)=>{let{min:r,max:i}=o,u=o.constrain(t),c=e===g(n);return e?c||1>=f(r-u)?r:1>=f(i-u)?i:u:i}).map(t=>parseFloat(t.toFixed(3))),c=function(){let t=u[0],e=m(u);return x(u.lastIndexOf(t),u.indexOf(e)+1)}();return{snapsContained:function(){if(e<=t+2)return[o.max];if("keepSnaps"===r)return u;let{min:n,max:i}=c;return u.slice(n,i)}(),scrollContainLimit:c}}($,te,tt,P,0),ti=X?tn:tt,{limit:to}=function(t,e,n){let r=e[0];return{limit:x(n?r-t:m(e),r)}}(te,ti,L),tu=function t(e,n,r){let{constrain:i}=x(0,e),o=e+1,u=c(n);function c(t){return r?f((o+t)%o):i(t)}function a(){return t(e,u,r)}let l={get:function(){return u},set:function(t){return u=c(t),l},add:function(t){return a().set(u+t)},clone:a};return l}(g(ti),k,L),tc=tu.clone(),ta=p(n),tl=({dragHandler:t,scrollBody:e,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(t.pointerDown()),e.seek()},ts=({scrollBody:t,translate:e,location:n,offsetLocation:r,previousLocation:i,scrollLooper:o,slideLooper:u,dragHandler:c,animation:a,eventHandler:l,scrollBounds:s,options:{loop:f}},d)=>{let p=t.settled(),m=!s.shouldConstrain(),g=f?p:p&&m,y=g&&!c.pointerDown();y&&a.stop();let h=n.get()*d+i.get()*(1-d);r.set(h),f&&(o.loop(t.direction()),u.loop()),e.to(r.get()),y&&l.emit("settle"),g||l.emit("scroll")},tf=function(t,e,n,r){let i=v(),o=1e3/60,u=null,c=0,a=0;function l(t){if(!a)return;u||(u=t,n(),n());let i=t-u;for(u=t,c+=i;c>=o;)n(),c-=o;r(c/o),a&&(a=e.requestAnimationFrame(l))}function s(){e.cancelAnimationFrame(a),u=null,c=0,a=0}return{init:function(){i.add(t,"visibilitychange",()=>{t.hidden&&(u=null,c=0)})},destroy:function(){s(),i.clear()},start:function(){a||(a=e.requestAnimationFrame(l))},stop:s,update:n,render:r}}(r,i,()=>tl(tI),t=>ts(tI,t)),td=ti[tu.get()],tp=w(td),tm=w(td),tg=w(td),ty=w(td),th=function(t,e,n,r,i,o){let u=0,c=0,a=i,l=.68,s=t.get(),p=0;function m(t){return a=t,y}function g(t){return l=t,y}let y={direction:function(){return c},duration:function(){return a},velocity:function(){return u},seek:function(){let e=r.get()-t.get(),i=0;return a?(n.set(t),u+=e/a,u*=l,s+=u,t.add(u),i=s-p):(u=0,n.set(r),t.set(r),i=e),c=d(i),p=s,y},settled:function(){return .001>f(r.get()-e.get())},useBaseFriction:function(){return g(.68)},useBaseDuration:function(){return m(i)},useFriction:g,useDuration:m};return y}(tp,tg,tm,ty,F,0),tb=function(t,e,n,r,i){let{reachedAny:o,removeOffset:u,constrain:c}=r;function a(t){return t.concat().sort((t,e)=>f(t)-f(e))[0]}function l(e,r){let i=[e,e+n,e-n];if(!t)return e;if(!r)return a(i);let o=i.filter(t=>d(t)===r);return o.length?a(o):m(i)-n}return{byDistance:function(n,r){let a=i.get()+n,{index:s,distance:d}=function(n){let r=t?u(n):c(n),{index:i}=e.map((t,e)=>({diff:l(t-r,0),index:e})).sort((t,e)=>f(t.diff)-f(e.diff))[0];return{index:i,distance:r}}(a),p=!t&&o(a);if(!r||p)return{index:s,distance:n};let m=n+l(e[s]-d,0);return{index:s,distance:m}},byIndex:function(t,n){let r=l(e[t]-i.get(),n);return{index:t,distance:r}},shortcut:l}}(L,ti,te,to,ty),tv=function(t,e,n,r,i,o,u){function c(i){let c=i.distance,a=i.index!==e.get();o.add(c),c&&(r.duration()?t.start():(t.update(),t.render(1),t.update())),a&&(n.set(e.get()),e.set(i.index),u.emit("select"))}return{distance:function(t,e){c(i.byDistance(t,e))},index:function(t,n){let r=e.clone().set(t);c(i.byIndex(r.get(),n))}}}(tf,tu,tc,th,tb,ty,u),tx=function(t){let{max:e,length:n}=t;return{get:function(t){return n?-((t-e)/n):0}}}(to),tw=v(),tS=function(t,e,n,r){let i;let o={},u=null,c=null,a=!1;return{init:function(){i=new IntersectionObserver(t=>{a||(t.forEach(t=>{o[e.indexOf(t.target)]=t}),u=null,c=null,n.emit("slidesInView"))},{root:t.parentElement,threshold:r}),e.forEach(t=>i.observe(t))},destroy:function(){i&&i.disconnect(),a=!0},get:function(t=!0){if(t&&u)return u;if(!t&&c)return c;let e=h(o).reduce((e,n)=>{let r=parseInt(n),{isIntersecting:i}=o[r];return(t&&i||!t&&!i)&&e.push(r),e},[]);return t&&(u=e),t||(c=e),e}}}(e,n,u,A),{slideRegistry:tO}=function(t,e,n,r,i,o){let{groupSlides:u}=i,{min:c,max:a}=r;return{slideRegistry:function(){let r=u(o);return 1===n.length?[o]:t&&"keepSnaps"!==e?r.slice(c,a).map((t,e,n)=>{let r=e===g(n);return e?r?y(g(o)-m(n)[0]+1,m(n)[0]):t:y(m(n[0])+1)}):r}()}}(X,P,ti,tr,K,ta),tE=function(t,e,n,r,i,o,u,a){let s={passive:!0,capture:!0},f=0;function d(t){"Tab"===t.code&&(f=new Date().getTime())}return{init:function(p){a&&(o.add(document,"keydown",d,!1),e.forEach((e,d)=>{o.add(e,"focus",e=>{(l(a)||a(p,e))&&function(e){if(new Date().getTime()-f>10)return;u.emit("slideFocusStart"),t.scrollLeft=0;let o=n.findIndex(t=>t.includes(e));c(o)&&(i.useDuration(0),r.index(o,0),u.emit("slideFocus"))}(d)},s)}))}}}(t,n,tO,tv,th,tw,u,C),tI={ownerDocument:r,ownerWindow:i,eventHandler:u,containerRect:U,slideRects:B,animation:tf,axis:R,dragHandler:function(t,e,n,r,i,o,u,c,a,s,p,m,g,y,h,w,S,O,E){let{cross:I,direction:D}=t,k=["INPUT","SELECT","TEXTAREA"],L={passive:!1},F=v(),M=v(),N=x(50,225).constrain(y.measure(20)),A={mouse:300,touch:400},T={mouse:500,touch:600},j=h?43:25,P=!1,H=0,V=0,z=!1,C=!1,_=!1,U=!1;function B(t){if(!b(t,r)&&t.touches.length>=2)return R(t);let e=o.readPoint(t),n=o.readPoint(t,I),u=f(e-H),a=f(n-V);if(!C&&!U&&(!t.cancelable||!(C=u>a)))return R(t);let l=o.pointerMove(t);u>w&&(_=!0),s.useFriction(.3).useDuration(.75),c.start(),i.add(D(l)),t.preventDefault()}function R(t){let e=p.byDistance(0,!1).index!==m.get(),n=o.pointerUp(t)*(h?T:A)[U?"mouse":"touch"],r=function(t,e){let n=m.add(-1*d(t)),r=p.byDistance(t,!h).distance;return h||f(t)<N?r:S&&e?.5*r:p.byIndex(n.get(),0).distance}(D(n),e),i=function(t,e){var n,r;if(0===t||0===e||f(t)<=f(e))return 0;let i=(n=f(t),r=f(e),f(n-r));return f(i/t)}(n,r);C=!1,z=!1,M.clear(),s.useDuration(j-10*i).useFriction(.68+i/50),a.distance(r,!h),U=!1,g.emit("pointerUp")}function $(t){_&&(t.stopPropagation(),t.preventDefault(),_=!1)}return{init:function(t){E&&F.add(e,"dragstart",t=>t.preventDefault(),L).add(e,"touchmove",()=>void 0,L).add(e,"touchend",()=>void 0).add(e,"touchstart",c).add(e,"mousedown",c).add(e,"touchcancel",R).add(e,"contextmenu",R).add(e,"click",$,!0);function c(c){(l(E)||E(t,c))&&function(t){let c=b(t,r);U=c,_=h&&c&&!t.buttons&&P,P=f(i.get()-u.get())>=2,c&&0!==t.button||function(t){let e=t.nodeName||"";return k.includes(e)}(t.target)||(z=!0,o.pointerDown(t),s.useFriction(0).useDuration(0),i.set(u),function(){let t=U?n:e;M.add(t,"touchmove",B,L).add(t,"touchend",R).add(t,"mousemove",B,L).add(t,"mouseup",R)}(),H=o.readPoint(t),V=o.readPoint(t,I),g.emit("pointerDown"))}(c)}},destroy:function(){F.clear(),M.clear()},pointerDown:function(){return z}}}(R,t,r,i,ty,function(t,e){let n,r;function i(t){return t.timeStamp}function o(n,r){let i=r||t.scroll,o=`client${"x"===i?"X":"Y"}`;return(b(n,e)?n:n.touches[0])[o]}return{pointerDown:function(t){return n=t,r=t,o(t)},pointerMove:function(t){let e=o(t)-o(r),u=i(t)-i(n)>170;return r=t,u&&(n=t),e},pointerUp:function(t){if(!n||!r)return 0;let e=o(r)-o(n),u=i(t)-i(n),c=i(t)-i(r)>170,a=e/u;return u&&!c&&f(a)>.1?a:0},readPoint:o}}(R,i),tp,tf,tv,th,tb,tu,u,q,M,N,j,0,z),eventStore:tw,percentOfView:q,index:tu,indexPrevious:tc,limit:to,location:tp,offsetLocation:tg,previousLocation:tm,options:o,resizeHandler:function(t,e,n,r,i,o,u){let c,a;let s=[t].concat(r),d=[],p=!1;function m(t){return i.measureSize(u.measure(t))}return{init:function(i){o&&(a=m(t),d=r.map(m),c=new ResizeObserver(n=>{(l(o)||o(i,n))&&function(n){for(let o of n){if(p)return;let n=o.target===t,u=r.indexOf(o.target),c=n?a:d[u];if(f(m(n?t:r[u])-c)>=.5){i.reInit(),e.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{s.forEach(t=>c.observe(t))}))},destroy:function(){p=!0,c&&c.disconnect()}}}(e,u,i,n,R,H,_),scrollBody:th,scrollBounds:function(t,e,n,r,i){let o=i.measure(10),u=i.measure(50),c=x(.1,.99),a=!1;function l(){return!!(!a&&t.reachedAny(n.get())&&t.reachedAny(e.get()))}return{shouldConstrain:l,constrain:function(i){if(!l())return;let a=t.reachedMin(e.get())?"min":"max",s=f(t[a]-e.get()),d=n.get()-e.get(),p=c.constrain(s/u);n.subtract(d*p),!i&&f(d)<o&&(n.set(t.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(t){a=!t}}}(to,tg,ty,th,q),scrollLooper:function(t,e,n,r){let{reachedMin:i,reachedMax:o}=x(e.min+.1,e.max+.1);return{loop:function(e){if(!(1===e?o(n.get()):-1===e&&i(n.get())))return;let u=-1*e*t;r.forEach(t=>t.add(u))}}}(te,to,tg,[tp,tg,tm,ty]),scrollProgress:tx,scrollSnapList:ti.map(tx.get),scrollSnaps:ti,scrollTarget:tb,scrollTo:tv,slideLooper:function(t,e,n,r,i,o,u,c,a){let l=p(i),s=m(d(p(i).reverse(),u[0]),n,!1).concat(m(d(l,e-u[0]-1),-n,!0));function f(t,e){return t.reduce((t,e)=>t-i[e],e)}function d(t,e){return t.reduce((t,n)=>f(t,e)>0?t.concat([n]):t,[])}function m(i,u,l){let s=o.map((t,n)=>({start:t-r[n]+.5+u,end:t+e-.5+u}));return i.map(e=>{let r=l?0:-n,i=l?n:0,o=s[e][l?"end":"start"];return{index:e,loopPoint:o,slideLocation:w(-1),translate:S(t,a[e]),target:()=>c.get()>o?r:i}})}return{canLoop:function(){return s.every(({index:t})=>.1>=f(l.filter(e=>e!==t),e))},clear:function(){s.forEach(t=>t.translate.clear())},loop:function(){s.forEach(t=>{let{target:e,translate:n,slideLocation:r}=t,i=e();i!==r.get()&&(n.to(i),r.set(i))})},loopPoints:s}}(R,$,te,Z,Q,W,ti,tg,n),slideFocus:tE,slidesHandler:(O=!1,{init:function(t){V&&(s=new MutationObserver(e=>{!O&&(l(V)||V(t,e))&&function(e){for(let n of e)if("childList"===n.type){t.reInit(),u.emit("slidesChanged");break}}(e)})).observe(e,{childList:!0})},destroy:function(){s&&s.disconnect(),O=!0}}),slidesInView:tS,slideIndexes:ta,slideRegistry:tO,slidesToScroll:K,target:ty,translate:S(R,e)};return tI}(t,o,u,D,k,n,N);return n.loop&&!r.slideLooper.canLoop()?e(Object.assign({},n,{loop:!1})):r}(_),j([C,...U.map(({options:t})=>t)]).forEach(t=>M.add(t,"change",R)),_.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(X),r.eventHandler.init(X),r.resizeHandler.init(X),r.slidesHandler.init(X),r.options.loop&&r.slideLooper.loop(),o.offsetParent&&u.length&&r.dragHandler.init(X),i=F.init(X,U)))}function R(t,e){let n=J();$(),B(A({startIndex:n},t),e),N.emit("reInit")}function $(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),F.destroy(),M.clear()}function q(t,e,n){_.active&&!z&&(r.scrollBody.useBaseFriction().useDuration(!0===e?0:_.duration),r.scrollTo.index(t,n||0))}function J(){return r.index.get()}let X={canScrollNext:function(){return r.index.add(1).get()!==J()},canScrollPrev:function(){return r.index.add(-1).get()!==J()},containerNode:function(){return o},internalEngine:function(){return r},destroy:function(){z||(z=!0,M.clear(),$(),N.emit("destroy"),N.clear())},off:H,on:P,emit:V,plugins:function(){return i},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:R,rootNode:function(){return t},scrollNext:function(t){q(r.index.add(1).get(),t,-1)},scrollPrev:function(t){q(r.index.add(-1).get(),t,1)},scrollProgress:function(){return r.scrollProgress.get(r.offsetLocation.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:q,selectedScrollSnap:J,slideNodes:function(){return u},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return B(e,n),setTimeout(()=>N.emit("init"),0),X}function I(t={},e=[]){let n=(0,r.useRef)(t),i=(0,r.useRef)(e),[c,a]=(0,r.useState)(),[l,s]=(0,r.useState)(),f=(0,r.useCallback)(()=>{c&&c.reInit(n.current,i.current)},[c]);return(0,r.useEffect)(()=>{o(n.current,t)||(n.current=t,f())},[t,f]),(0,r.useEffect)(()=>{!function(t,e){if(t.length!==e.length)return!1;let n=u(t),r=u(e);return n.every((t,e)=>o(t,r[e]))}(i.current,e)&&(i.current=e,f())},[e,f]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&l){E.globalOptions=I.globalOptions;let t=E(l,n.current,i.current);return a(t),()=>t.destroy()}a(void 0)},[l,a]),[s,c]}E.globalOptions=void 0,I.globalOptions=void 0}}]);