!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="cd759e9f-0f87-4788-9f28-c6b75c4a775e",e._sentryDebugIdIdentifier="sentry-dbid-cd759e9f-0f87-4788-9f28-c6b75c4a775e")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7236],{99376:function(e,t,r){var n=r(35475);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},49027:function(e,t,r){r.d(t,{Dx:function(){return en},VY:function(){return er},aV:function(){return et},dk:function(){return eo},fC:function(){return Q},h_:function(){return ee},x8:function(){return ea},xz:function(){return $}});var n=r(2265),o=r(6741),a=r(98575),i=r(73966),u=r(99255),l=r(80886),s=r(15278),c=r(99103),d=r(83832),f=r(71599),p=r(66840),v=r(86097),g=r(60703),b=r(5478),h=r(37053),m=r(57437),y="Dialog",[w,x]=(0,i.b)(y),[D,R]=w(y),j=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:s=!0}=e,c=n.useRef(null),d=n.useRef(null),[f,p]=(0,l.T)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:y});return(0,m.jsx)(D,{scope:t,triggerRef:c,contentRef:d,contentId:(0,u.M)(),titleId:(0,u.M)(),descriptionId:(0,u.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:s,children:r})};j.displayName=y;var C="DialogTrigger",I=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=R(C,r),u=(0,a.e)(t,i.triggerRef);return(0,m.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":U(i.open),...n,ref:u,onClick:(0,o.M)(e.onClick,i.onOpenToggle)})});I.displayName=C;var k="DialogPortal",[P,_]=w(k,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=R(k,t);return(0,m.jsx)(P,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,m.jsx)(f.z,{present:r||i.open,children:(0,m.jsx)(d.h,{asChild:!0,container:a,children:e})}))})};E.displayName=k;var N="DialogOverlay",M=n.forwardRef((e,t)=>{let r=_(N,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(N,e.__scopeDialog);return a.modal?(0,m.jsx)(f.z,{present:n||a.open,children:(0,m.jsx)(S,{...o,ref:t})}):null});M.displayName=N;var O=(0,h.Z8)("DialogOverlay.RemoveScroll"),S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(N,r);return(0,m.jsx)(g.Z,{as:O,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(p.WV.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),W="DialogContent",F=n.forwardRef((e,t)=>{let r=_(W,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(W,e.__scopeDialog);return(0,m.jsx)(f.z,{present:n||a.open,children:a.modal?(0,m.jsx)(A,{...o,ref:t}):(0,m.jsx)(V,{...o,ref:t})})});F.displayName=W;var A=n.forwardRef((e,t)=>{let r=R(W,e.__scopeDialog),i=n.useRef(null),u=(0,a.e)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,b.Ry)(e)},[]),(0,m.jsx)(z,{...e,ref:u,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),V=n.forwardRef((e,t)=>{let r=R(W,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,m.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let u=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),z=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:u,...l}=e,d=R(W,r),f=n.useRef(null),p=(0,a.e)(t,f);return(0,v.EW)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:u,children:(0,m.jsx)(s.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":U(d.open),...l,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(G,{titleId:d.titleId}),(0,m.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),T="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(T,r);return(0,m.jsx)(p.WV.h2,{id:o.titleId,...n,ref:t})});B.displayName=T;var H="DialogDescription",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(H,r);return(0,m.jsx)(p.WV.p,{id:o.descriptionId,...n,ref:t})});Z.displayName=H;var q="DialogClose",L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=R(q,r);return(0,m.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,()=>a.onOpenChange(!1))})});function U(e){return e?"open":"closed"}L.displayName=q;var X="DialogTitleWarning",[K,Y]=(0,i.k)(X,{contentName:W,titleName:T,docsSlug:"dialog"}),G=e=>{let{titleId:t}=e,r=Y(X),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:r}=e,o=Y("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},Q=j,$=I,ee=E,et=M,er=F,en=B,eo=Z,ea=L},6394:function(e,t,r){r.d(t,{f:function(){return u}});var n=r(2265),o=r(66840),a=r(57437),i=n.forwardRef((e,t)=>(0,a.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var u=i},50721:function(e,t,r){r.d(t,{bU:function(){return R},fC:function(){return D}});var n=r(2265),o=r(6741),a=r(98575),i=r(73966),u=r(80886),l=r(6718),s=r(90420),c=r(66840),d=r(57437),f="Switch",[p,v]=(0,i.b)(f),[g,b]=p(f),h=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:l,defaultChecked:s,required:p,disabled:v,value:b="on",onCheckedChange:h,form:m,...y}=e,[D,R]=n.useState(null),j=(0,a.e)(t,e=>R(e)),C=n.useRef(!1),I=!D||m||!!D.closest("form"),[k,P]=(0,u.T)({prop:l,defaultProp:null!=s&&s,onChange:h,caller:f});return(0,d.jsxs)(g,{scope:r,checked:k,disabled:v,children:[(0,d.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":k,"aria-required":p,"data-state":x(k),"data-disabled":v?"":void 0,disabled:v,value:b,...y,ref:j,onClick:(0,o.M)(e.onClick,e=>{P(e=>!e),I&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),I&&(0,d.jsx)(w,{control:D,bubbles:!C.current,name:i,value:b,checked:k,required:p,disabled:v,form:m,style:{transform:"translateX(-100%)"}})]})});h.displayName=f;var m="SwitchThumb",y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=b(m,r);return(0,d.jsx)(c.WV.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});y.displayName=m;var w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:i,bubbles:u=!0,...c}=e,f=n.useRef(null),p=(0,a.e)(f,t),v=(0,l.D)(i),g=(0,s.t)(o);return n.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==i&&t){let r=new Event("click",{bubbles:u});t.call(e,i),e.dispatchEvent(r)}},[v,i,u]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...c,tabIndex:-1,ref:p,style:{...c.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var D=h,R=y},6718:function(e,t,r){r.d(t,{D:function(){return o}});var n=r(2265);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},90420:function(e,t,r){r.d(t,{t:function(){return a}});var n=r(2265),o=r(61188);function a(e){let[t,r]=n.useState(void 0);return(0,o.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}}]);