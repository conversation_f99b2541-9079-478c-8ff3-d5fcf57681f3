{"version": 3, "file": "static/chunks/9625.b18bea36d6962734.js", "mappings": "wrBAgBA,SAASA,EAAeC,CAGvB,EAGG,MACI,GAAAC,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,kCACX,GAAAH,EAAAC,GAAA,EAACG,EAAAA,CACGC,KAAMN,EAAMM,IAAI,CAChBC,WAAYP,EAAMO,UAAU,CAC5BC,QAAS,GAAAP,EAAAQ,IAAA,EAACN,MAAAA,CAAIC,UAAU,iEACpB,GAAAH,EAAAC,GAAA,EAACQ,EAAAA,CAAcA,CAAAA,CAACN,UAAU,WAC1B,GAAAH,EAAAC,GAAA,EAACS,OAAAA,UAAK,qBAK1B,CAEA,SAASC,EAAaZ,CAA+B,EACjD,MACI,GAAAC,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,oCAA2B,QAIlD,CAEO,IAAMS,EAAoB,CAACN,EAAoBD,IAC3C,EACHQ,IAAK,aACLC,KAAM,aACNC,MAAO,IAIPC,iBAAAA,GACW,GAAAhB,EAAAC,GAAA,EAACH,EAAAA,CAAgB,GAAGC,CAAK,CAAEO,WAAYA,EAAYD,KAAMA,IAEpEY,WAAAA,GACW,GAAAjB,EAAAC,GAAA,EAACU,EAAAA,CAAc,GAAGZ,CAAK,EAEtC,GAUSK,EAAoB,IAC7B,GAAM,CAACc,MAAAA,CAAK,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IACV,CAACC,cAAAA,CAAa,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAClB,CAACC,qBAAAA,CAAoB,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACzB,CAACC,EAAMC,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAE3BpB,EAAaP,EAAMO,UAAU,CAE7BqB,EAAa5B,EAAMM,IAAI,CAACsB,UAAU,CACxCA,EAAWC,OAAO,CAAGD,EAAWC,OAAO,EAAI,EAAE,CAE7C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KACtB,GAAI,CAACxB,GACD,CAACc,CAAa,CAACd,EAAW,CADb,MAAO,EAAE,CAE1B,IAAMyB,EAAKX,CAAa,CAACd,EAAW,CAE9BsB,EAAqC,EAAE,CAE7C,IAAK,IAAMI,KAAOC,OAAOC,MAAM,CAACH,EAAGI,QAAQ,CAACR,UAAU,CAACS,UAAU,EAAG,CAChE,IAAMC,EAAkC,CACpCC,KAAMN,EAAKO,GAAIP,EAAIO,EAAE,CAAEC,MAAOR,EAAIQ,KAAK,CAAEC,MAAOT,EAAIO,EAAE,EAE1DX,EAAQc,IAAI,CAACL,EACjB,CACA,OAAOT,CACX,EAAG,CAACtB,EAAYc,EAAc,EAExBuB,EAAW,IACb,IAAMX,EAAkC,CACpCO,GAAIK,CAAAA,EAAAA,EAAAA,YAAAA,IACJC,SAAUR,EAAOE,EAAE,CACnBO,YAAaC,EAAAA,2BAA2BA,CAACC,YAAY,EAEnDpB,EAAU,IAAID,EAAWC,OAAO,CAAC,CACvCA,EAAQc,IAAI,CAACV,GAEbV,EAAqBvB,EAAMM,IAAI,CAACkC,EAAE,CAAExC,EAAMM,IAAI,CAAC4C,MAAM,CAAE,CAACrB,QAAAA,CAAO,GAA8BsB,IAAI,EACrG,EAGA,MAAQ,GAAAlD,EAAAC,GAAA,EAAAD,EAAAmD,QAAA,WACJ,GAAAnD,EAAAQ,IAAA,EAAC4C,EAAAA,EAAYA,CAAAA,CAAC5B,KAAMA,EAAM6B,aAAc5B,YACpC,GAAAzB,EAAAC,GAAA,EAACqD,EAAAA,EAAmBA,CAAAA,CAACC,QAAO,YAIvBxD,EAAMQ,OAAO,GAElB,GAAAP,EAAAC,GAAA,EAACuD,EAAAA,EAAmBA,CAAAA,CAACrD,UAAU,2EACVsD,MAAO1D,EAAM2D,aAAa,EAAI,eAC/C,GAAA1D,EAAAC,GAAA,EAAC0D,EAAAA,UAAUA,CAAAA,CAACxD,UAAU,gBAClB,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,eACV0B,EAAU+B,GAAG,CAAC,CAACC,EAAGC,KACf,IAAMC,EAAIF,EAAEvB,IAAI,CAChB,MAAO,GAAAtC,EAAAQ,IAAA,EAACwD,EAAAA,EAAgBA,CAAAA,CAACC,QAAS,IAAMtB,EAASoB,GACb5D,UAAU,2CAC1C,GAAAH,EAAAC,GAAA,EAACiE,EAAAA,CAAqBA,CAAAA,CAACC,KAAMJ,EAAEI,IAAI,CAAEhE,UAAU,WAC/C,GAAAH,EAAAQ,IAAA,EAACE,OAAAA,WAAK,IAAEqD,EAAEvB,KAAK,MAFWuB,EAAExB,EAAE,CAItC,aAOxB,kDClIA,SAAS6B,EAAiBrE,CAAqC,EAC3D,GAAM,CAACsE,EAAeC,EAAqB,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAE9C,MACI,GAAAvE,EAAAC,GAAA,EAAAD,EAAAmD,QAAA,WACI,GAAAnD,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,+EAOH,UAKxB,CAEA,SAASqE,EAAgBzE,CAA+B,EACpD,GAAM,CAACsE,EAAeC,EAAsB,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAC/C,MACI,GAAAvE,EAAAC,GAAA,EAAAD,EAAAmD,QAAA,WACI,GAAAnD,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,2EAOVJ,EAAM0E,MAAM,CAAG,KAIhC,CAEA,SAASC,EAAqB3E,CAAoC,EAC9D,GAAM,CAACsE,EAAeC,EAAqB,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAE9C,MACI,GAAAvE,EAAAC,GAAA,EAAAD,EAAAmD,QAAA,WACI,GAAAnD,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,yDACX,GAAAH,EAAAC,GAAA,EAAC0E,EAAAA,CAAQA,CAAAA,CACLC,QAASP,EACTQ,gBAAiB,IACbP,EAAqB,CAACH,KAAM,MAAOW,IAAK/E,EAAM+E,GAAG,CAAEF,QAAS,CAAC,CAACA,EAASG,aAAc,EAAK,EAC9F,OAKpB,CAEO,IAAMC,EAAmC,CAC5CnE,IAAKoE,EAAAA,CAAiBA,CACtBnE,KAAM,GACNC,MAAO,GAGPmE,UAAW,GACXC,SAAU,GACVC,OAAQ,GACRpE,iBAAAA,GACW,GAAAhB,EAAAC,GAAA,EAACmE,EAAAA,CAAkB,GAAGrE,CAAK,GAEtCkB,WAAAA,GACW,GAAAjB,EAAAC,GAAA,EAACuE,EAAAA,CAAiB,GAAGzE,CAAK,GAErCsF,gBAAAA,GACW,GAAArF,EAAAC,GAAA,EAACyE,EAAAA,CAAsB,GAAG3E,CAAK,EAE9C,0DC9BO,IAAMuF,EAAmB,IAC5B,GAAM,CAAClE,cAAAA,CAAa,CAAEmE,QAAAA,CAAO,CAAEC,UAAAA,CAAS,CAAC,CAAGnE,CAAAA,EAAAA,EAAAA,EAAAA,IACtC,CAACC,qBAAAA,CAAoB,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACzB,CAACkE,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,OAAAA,CAAM,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAE1B,CAACC,YAAAA,CAAW,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAMhBC,EAAmB,CAFVC,CAAAA,EAAAA,EAAAA,EAAAA,KAEqB,CAAC,CAACH,GAAe,CAACI,EAAAA,CAAWA,CAACC,IAAI,CAAED,EAAAA,CAAWA,CAACE,IAAI,CAAC,CAACC,QAAQ,CAACP,GAG7F,CAAClE,WAAAA,CAAU,CAAC,CAAG5B,EAEfoC,EAAWf,CAAa,CAACO,EAAWrB,UAAU,CAAC,CAC/C+F,EAAWN,GAAW,CAACpE,EAAW2E,WAAW,CAG/C3E,EAAWC,OAAO,CAAGD,EAAWC,OAAO,EAAI,EAAE,CAKjD,IAAM2E,EAAe,CAAChE,EAAYiE,KAC9B,IAAM5E,EAAU,IAAID,EAAWC,OAAO,CAAC,CACvC,IAAK,IAAIkC,EAAI,EAAGA,EAAIlC,EAAQ6E,MAAM,CAAE3C,IAEhC,GAAIzB,CADkB,CAACyB,EAAE,CACdvB,EAAE,GAAKA,EAAI,CAClBX,CAAO,CAACkC,EAAE,CAAG,CAAC,GAAGlC,CAAO,CAACkC,EAAE,CAAE,GAAG0C,CAAM,EACtC,KACJ,CAEJlF,EAAqBvB,EAAMM,IAAI,CAACkC,EAAE,CAAExC,EAAMM,IAAI,CAAC4C,MAAM,CAAE,CAACrB,QAAAA,CAAO,GAA8BsB,IAAI,EACrG,EAEMwD,EAAe,IACjB,IAAM9E,EAAUD,EAAWC,OAAO,CAAC8D,MAAM,CAAC3B,GAAKA,EAAExB,EAAE,GAAKA,GACxDjB,EAAqBvB,EAAMM,IAAI,CAACkC,EAAE,CAAExC,EAAMM,IAAI,CAAC4C,MAAM,CAAE,CAACrB,QAAAA,CAAO,GAA8BsB,IAAI,EACrG,EAwCMyD,EAAOC,CAtCM,KACf,IAAMhF,EAA8B,EAAE,CAItC,GAFAA,EAAQc,IAAI,CAACsC,GAET,CAAC7C,EAAU,OAAOP,EACtB,IAAMiF,EAAe1E,EAASA,QAAQ,CAACR,UAAU,CACjD,GAAI,CAACkF,EAAc,OAAOjF,EAE1B,IAAK,IAAMI,KAAOL,EAAWC,OAAO,CAAE,CAClC,GAAII,EAAI8E,QAAQ,CAAE,SAClB,IAAMC,EAAQF,EAAazE,UAAU,CAACJ,EAAIa,QAAQ,CAAC,CACnD,GAAI,CAACkE,EAAO,SACZ,IAAMC,EAA8B,CAChC1G,WAAYqB,EAAWrB,UAAU,CACjC+B,OAAQ0E,EACRE,cAAejF,CACnB,EACMK,EAAS,CACXxB,IAAKmB,EAAIO,EAAE,CACXzB,KAAMkB,EAAIQ,KAAK,EAAIuE,EAAMvE,KAAK,CAC9B0E,eAAgBC,EAAAA,EAAUA,CAC1BlG,WAAYN,EAAAA,EAAYA,CACxBK,iBAAkB,GAAgB,GAAAhB,EAAAC,GAAA,EAACH,EAAAA,EAAcA,CAAAA,CAAE,GAAGC,CAAK,CAAEsG,SAAUN,EAASQ,aAAcA,EAAcG,aAAcA,IAC1H3F,MAAO,IACPsF,SAAU,GACVW,SAAAA,CACJ,CACA3E,CAAAA,EAAOgE,QAAQ,CAAGhE,EAAOgE,QAAQ,EAAIA,EACrCzE,EAAQc,IAAI,CAACL,EACjB,CAKA,OAHIgE,GAAUzE,EAAQc,IAAI,CAAC9B,EAAkBuB,EAASA,QAAQ,CAACI,EAAE,CAAExC,EAAMM,IAAI,GAGtEuB,CACX,KAyEMwF,EAAOnF,OAAOC,MAAM,CAACmF,CApEF,KAQrB,GAAI,CAAClF,EAAU,MAAO,CAACmF,OAAQ,CAAC,EAAGC,YAAa,CAAC,CAAC,EAElD,IAAMC,EAA8B,EAAE,CAClC/B,EAAMgB,MAAM,CAAG,EACfe,EAAY9E,IAAI,IAAI+C,GACb9D,EAAW8D,KAAK,CAACgB,MAAM,CAAG,GACjCe,EAAY9E,IAAI,IAAIf,EAAW8D,KAAK,EAEb,IAAvB+B,EAAYf,MAAM,EAAQe,EAAY9E,IAAI,CAAC,CAACG,SAAU4E,EAAAA,WAAWA,CAACC,SAAS,CAAEC,MAAOC,EAAAA,IAAIA,CAACC,GAAG,GAEhG,GAAI,CAACT,KAAAA,CAAI,CAAC,CAAGU,CAAAA,EAAAA,EAAAA,oBAAAA,EACT3F,EACAoD,EACAnE,EACAO,EAAW+D,MAAM,CACjBA,EACA8B,EACAhC,EAAUuC,eAAe,CAACC,MAAM,EAEhCrC,GAAUA,EAAOsC,IAAI,IACrBb,CAAAA,EAAOA,EAAK1B,MAAM,CAACwC,GAAKA,EAAEC,eAAe,CAACC,UAAU,CAACC,WAAW,GAAGjC,QAAQ,CAACT,EAAOsC,IAAI,GAAGI,WAAW,MAEzG,IAAMd,EAEF,CAAC,EACCD,EAEF,CAAC,EAEDgB,EAAU3G,EAAW4G,UAAU,CAEnC,IAAK,IAAMzD,KAAOsC,EAAM,CACpB,IAAMoB,EAA4B,CAAC,EACnC,IAAK,IAAMjG,KAAM+F,EACbE,CAAW,CAACjG,EAAG,CAAGuC,EAAI2D,MAAM,CAACC,YAAY,CAACnG,EAAG,CAEjD,IAAMoG,EAAaC,KAAKC,SAAS,CAACL,GAC5BM,EAAOC,IAAAA,UAAiB,CAAC,QAAQvC,MAAM,CAACmC,GAAYK,MAAM,CAAC,OAEjE,GAAIzB,CAAW,CAACuB,EAAK,CAAE,CACnB,IAAMG,EAAgBC,KAAKC,GAAG,CAAC,IAAIC,KAAK7B,CAAW,CAACuB,EAAK,CAACO,SAAS,EAAEC,OAAO,GAAI,IAAIF,KAAKtE,EAAIuE,SAAS,EAAEC,OAAO,GAC/G/B,CAAAA,CAAW,CAACuB,EAAK,CAACO,SAAS,CAAG,IAAID,KAAKH,GAAeM,WAAW,GACjEhC,CAAW,CAACuB,EAAK,CAACU,SAAS,CAAC9G,IAAI,CAACoC,EAAIvC,EAAE,CAC3C,MACIgF,CAAW,CAACuB,EAAK,CAAG,CAChBU,UAAW,CAAC1E,EAAIvC,EAAE,CAAC,CACnB8G,UAAWvE,EAAIuE,SAAS,CACxB9G,GAAIuG,EACJxB,OAAQ,CAAC,CACb,CAEJC,CAAAA,CAAW,CAACuB,EAAK,CAACxB,MAAM,CAACxC,EAAIvC,EAAE,CAAC,CAAGuC,EACnCwC,CAAM,CAACxC,EAAIvC,EAAE,CAAC,CAAGuC,CACrB,CACA,MAAO,CACHyC,YAAAA,EAAaD,OAAAA,CACjB,CACJ,KAGmCC,WAAW,EAOxCkC,EAAS9C,EAAKF,MAAM,CAAG,GAAKE,eAAAA,CAAI,CAAC,EAAE,CAAC9F,GAAG,CAE7C,MAAO,GAAAb,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,6DACjB,EAqBK,GAAAH,EAAAC,GAAA,EAAAD,EAAAmD,QAAA,WACD,GAAAnD,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,yDACX,GAAAH,EAAAC,GAAA,EAACyJ,EAAAA,EAAQA,CAAAA,CAAC9H,QAAS+E,EACTS,KAAMA,EACNuC,UAAW,GACXC,gBAAiB,GACjBC,iBAAkB,GAClB1J,UAAU,0BACV2J,aArCvB,SAAsBhF,CAAQ,EAE1B,OAAOA,EAAIvC,EAAE,QAMF,GAAAvC,EAAAC,GAAA,EAAAD,EAAAmD,QAAA,WAEP,GAAAnD,EAAAC,GAAA,EAAC8J,EAAAA,UAAUA,CAAAA,CACPC,KAAK,OACLC,MAAO,GAAAjK,EAAAQ,IAAA,EAACN,MAAAA,CAAIC,UAAU,iEAClB,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,YACX,GAAAH,EAAAC,GAAA,EAACiK,EAAAA,GAAWA,CAAAA,CAAC/J,UAAU,aAE3B,GAAAH,EAAAC,GAAA,EAACS,OAAAA,CAAKP,UAAU,+BAAsB,gCAEtC,GAAAH,EAAAC,GAAA,EAACG,EAAiBA,CACdC,KAAMN,EAAMM,IAAI,CAChBC,WAAYqB,EAAWrB,UAAU,CACjCoD,cAAc,SACdnD,QAAS,GAAAP,EAAAQ,IAAA,EAAC2J,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUjK,UAAU,wEACzC,GAAAH,EAAAC,GAAA,EAACoK,EAAAA,GAAcA,CAAAA,CAAClK,UAAU,WAAU,0BAqBhE", "sources": ["webpack://_N_E/./src/components/workspace/main/views/summaryTable/renderers/addColumn.tsx", "webpack://_N_E/./src/components/workspace/main/views/summaryTable/renderers/rowIndex.tsx", "webpack://_N_E/./src/components/workspace/main/views/summaryTable/summaryTableView.tsx"], "sourcesContent": ["import {Column, RenderCellProps, RenderHeaderCellProps} from \"react-data-grid\";\r\nimport React, {ReactNode, useMemo, useState} from \"react\";\r\nimport {PlusCircleIcon} from \"@heroicons/react/24/outline\";\r\nimport {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from \"@/components/ui/dropdown-menu\";\r\nimport {DatabaseColumn, DatabaseFieldDataType, SelectColumn} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {DatabaseFieldTypeIcon} from \"@/components/workspace/main/database/databaseFieldTypeIcon\";\r\nimport {ScrollArea} from \"@/components/ui/scroll-area\";\r\nimport {useAlert} from \"@/providers/alert\";\r\nimport {generateUUID} from \"opendb-app-db-utils/lib/methods/string\";\r\nimport {useWorkspace} from \"@/providers/workspace\";\r\nimport {TagItem} from \"@/components/workspace/main/views/table/renderer/common/tag\";\r\nimport {useViews} from \"@/providers/views\";\r\nimport {View} from \"@/typings/page\";\r\nimport {SummaryColumnCustomization, SummaryViewDefinition, ViewDefinition} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {ShowValuesAggregateFunction} from \"opendb-app-db-utils/lib\";\r\n\r\nfunction HeaderRenderer(props: RenderHeaderCellProps<unknown> & {\r\n    databaseId: string,\r\n    view: View\r\n}) {\r\n    // const [isRowSelected, onRowSelectionChange] = useRowSelection();\r\n\r\n    return (\r\n        <div className=\"r-header h-full w-full\">\r\n            <AddColumnDropdown\r\n                view={props.view}\r\n                databaseId={props.databaseId}\r\n                trigger={<div className=\"h-full text-xs flex items-center font-semibold gap-1\">\r\n                    <PlusCircleIcon className=\"size-3\"/>\r\n                    <span>Add column</span>\r\n                </div>}/>\r\n\r\n        </div>\r\n    );\r\n}\r\n\r\nfunction CellRenderer(props: RenderCellProps<unknown>) {\r\n    return (\r\n        <div className=\"!bg-white h-full w-full'\">\r\n            &nbsp;\r\n        </div>\r\n    );\r\n}\r\n\r\nexport const getNewColumnProps = (databaseId: string, view: View): Column<any, any> => {\r\n    return {\r\n        key: \"add-column\",\r\n        name: 'Add column',\r\n        width: 115,\r\n        // minWidth: 35,\r\n        // maxWidth: 35,\r\n        // frozen: true,\r\n        renderHeaderCell(props) {\r\n            return <HeaderRenderer {...props} databaseId={databaseId} view={view}/>;\r\n        },\r\n        renderCell(props) {\r\n            return <CellRenderer {...props} />;\r\n        }\r\n    }\r\n}\r\n\r\nexport interface AddColumnDropdownProps {\r\n    trigger: ReactNode\r\n    databaseId: string\r\n    view: View\r\n    dropdownAlign?: \"start\" | \"center\" | \"end\"\r\n}\r\n\r\nexport const AddColumnDropdown = (props: AddColumnDropdownProps) => {\r\n    const {toast} = useAlert()\r\n    const {databaseStore} = useWorkspace()\r\n    const {updateViewDefinition} = useViews()\r\n    const [open, setOpen] = useState(false)\r\n\r\n    const databaseId = props.databaseId\r\n\r\n    const definition = props.view.definition as SummaryViewDefinition\r\n    definition.columns = definition.columns || []\r\n\r\n    const dbColumns = useMemo(() => {\r\n        if (!databaseId) return []\r\n        if (!databaseStore[databaseId]) return []\r\n        const db = databaseStore[databaseId]\r\n\r\n        const columns: TagItem<DatabaseColumn>[] = []\r\n\r\n        for (const col of Object.values(db.database.definition.columnsMap)) {\r\n            const column: TagItem<DatabaseColumn> = {\r\n                data: col, id: col.id, title: col.title, value: col.id\r\n            }\r\n            columns.push(column)\r\n        }\r\n        return columns\r\n    }, [databaseId, databaseStore])\r\n\r\n    const doCreate = (column: DatabaseColumn) => {\r\n        const col: SummaryColumnCustomization = {\r\n            id: generateUUID(),\r\n            columnId: column.id,\r\n            aggregateBy: ShowValuesAggregateFunction.ShowOriginal\r\n        }\r\n        const columns = [...definition.columns]\r\n        columns.push(col)\r\n\r\n        updateViewDefinition(props.view.id, props.view.pageId, {columns} as Partial<ViewDefinition>).then()\r\n    }\r\n\r\n\r\n    return (<>\r\n        <DropdownMenu open={open} onOpenChange={setOpen}>\r\n            <DropdownMenuTrigger asChild>\r\n                {/*<Button variant={\"ghost\"} className=\"rounded-full h-auto p-1\"><*/}\r\n                {/*    EllipsisHorizontalIcon className='size-4'/>*/}\r\n                {/*</Button>*/}\r\n                {props.trigger}\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent className=\"w-56 p-0 rounded-none text-neutral-800 font-semibold flex flex-col gap-1\"\r\n                                 align={props.dropdownAlign || 'end'}>\r\n                <ScrollArea className='h-80'>\r\n                    <div className=\"p-2\">\r\n                        {dbColumns.map((f, i) => {\r\n                            const c = f.data\r\n                            return <DropdownMenuItem onClick={() => doCreate(c)}\r\n                                                     key={c.id} className=\"text-xs rounded-none p-2 gap-2\">\r\n                                <DatabaseFieldTypeIcon type={c.type} className=\"size-3\"/>\r\n                                <span> {c.title}</span>\r\n                            </DropdownMenuItem>\r\n                        })}\r\n                    </div>\r\n                </ScrollArea>\r\n            </DropdownMenuContent>\r\n        </DropdownMenu>\r\n\r\n    </>)\r\n}\r\n", "import {Column, RenderCellProps, RenderGroupCellProps, RenderHeaderCellProps, SELECT_COLUMN_KEY, useRowSelection} from \"react-data-grid\";\r\nimport React from \"react\";\r\nimport {Checkbox} from \"@/components/ui/checkbox\";\r\n\r\nfunction RowIndexRenderer(props: RenderHeaderCellProps<unknown>) {\r\n    const [isRowSelected, onRowSelectionChange] = useRowSelection();\r\n\r\n    return (\r\n        <>\r\n            <div className=\"r-row-select text-xs w-full h-full flex items-center justify-center\">\r\n                {/*<Checkbox*/}\r\n                {/*    checked={isRowSelected}*/}\r\n                {/*    onCheckedChange={(checked) => {*/}\r\n                {/*        onRowSelectionChange({type: 'HEADER', checked: !!checked});*/}\r\n                {/*        // onRowSelectionChange({type: 'ROW', row: props.row, checked: !!checked, isShiftClick: false});*/}\r\n                {/*    }}*/}\r\n                {/*/>*/}\r\n                &nbsp;\r\n            </div>\r\n        </>\r\n    );\r\n}\r\n\r\nfunction SelectFormatter(props: RenderCellProps<unknown>) {\r\n    const [isRowSelected, onRowSelectionChange,] = useRowSelection();\r\n    return (\r\n        <>\r\n            <div className=\"r-row-select text-xs size-full flex items-center justify-center\">\r\n                {/*<Checkbox*/}\r\n                {/*    checked={isRowSelected}*/}\r\n                {/*    onCheckedChange={(checked) => {*/}\r\n                {/*        onRowSelectionChange({type: 'ROW', row: props.row, checked: !!checked, isShiftClick: false});*/}\r\n                {/*    }}*/}\r\n                {/*/>*/}\r\n                {props.rowIdx + 1}\r\n            </div>\r\n        </>\r\n    );\r\n}\r\n\r\nfunction SelectGroupFormatter(props: RenderGroupCellProps<unknown>) {\r\n    const [isRowSelected, onRowSelectionChange] = useRowSelection();\r\n\r\n    return (\r\n        <>\r\n            <div className=\"r-row-select text-xs h-full flex items-center\">\r\n                <Checkbox\r\n                    checked={isRowSelected}\r\n                    onCheckedChange={(checked) => {\r\n                        onRowSelectionChange({type: 'ROW', row: props.row, checked: !!checked, isShiftClick: false});\r\n                    }}\r\n                />\r\n            </div>\r\n        </>\r\n    );\r\n}\r\n\r\nexport const RowIndexColumn: Column<any, any> = {\r\n    key: SELECT_COLUMN_KEY,\r\n    name: '',\r\n    width: 25,\r\n    // minWidth: 35,\r\n    // maxWidth: 35,\r\n    resizable: false,\r\n    sortable: false,\r\n    frozen: true,\r\n    renderHeaderCell(props) {\r\n        return <RowIndexRenderer {...props} />;\r\n    },\r\n    renderCell(props) {\r\n        return <SelectFormatter {...props} />;\r\n    },\r\n    renderGroupCell(props) {\r\n        return <SelectGroupFormatter {...props} />;\r\n    }\r\n}\r\n", "\"use client\";\r\n\r\nimport {DataViewRow, filterAndSortRecords, ViewRenderProps} from \"@/components/workspace/main/views/table\";\r\nimport {SummaryColumnCustomization, SummaryViewDefinition, ViewDefinition} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport DataGrid, {Column} from \"react-data-grid\";\r\nimport React from \"react\";\r\n\r\nimport 'react-data-grid/lib/styles.css';\r\nimport \"../table/table.css\"\r\nimport {PageLoader} from \"@/components/custom-ui/loader\";\r\nimport {BalloonIcon, CirclePlusIcon} from \"@/components/icons/FontAwesomeRegular\";\r\nimport {Button} from \"@/components/ui/button\";\r\nimport {AddColumnDropdown, getNewColumnProps} from \"@/components/workspace/main/views/summaryTable/renderers/addColumn\";\r\nimport {useWorkspace} from \"@/providers/workspace\";\r\nimport {DatabaseColumn, DatabaseColumnDbValue, DbRecordSort, MagicColumn, RecordValues, Sort} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {CellEditor, CellRenderer} from \"@/components/workspace/main/views/summaryTable/renderers/cellRenderer\";\r\nimport {HeaderRenderer} from \"@/components/workspace/main/views/summaryTable/renderers/header\";\r\nimport {RowIndexColumn} from \"@/components/workspace/main/views/summaryTable/renderers/rowIndex\";\r\nimport {useViews, useViewFiltering} from \"@/providers/views\";\r\nimport crypto from \"crypto\";\r\nimport {AccessLevel} from \"@/typings/page\";\r\nimport {usePage} from \"@/providers/page\";\r\nimport {useMaybeShared} from \"@/providers/shared\";\r\n\r\nexport interface SummaryViewRenderProps extends ViewRenderProps {\r\n    definition: SummaryViewDefinition\r\n}\r\n\r\nexport interface SummaryColumnMeta {\r\n    databaseId: string,\r\n    column: DatabaseColumn\r\n    customization: SummaryColumnCustomization\r\n}\r\n\r\nexport interface SummaryViewRow {\r\n    id: string\r\n    aggregateValues?: DatabaseColumnDbValue | number | string | boolean | number[] | string[] | boolean[] | Date[]\r\n    recordIds: string[]\r\n    valuesText?: string\r\n    updatedAt: string\r\n    rowMap: {\r\n        [key: string]: DataViewRow\r\n    }\r\n}\r\n\r\nexport const SummaryTableView = (props: SummaryViewRenderProps) => {\r\n    const {databaseStore, members, workspace} = useWorkspace()\r\n    const {updateViewDefinition} = useViews()\r\n    const {sorts, filter, search} = useViewFiltering()\r\n\r\n    const {accessLevel} = usePage()\r\n\r\n\r\n    // const {selectedIds, setSelectedIds, sorts, filter, search} = useViews()\r\n    const shared = useMaybeShared()\r\n\r\n    const canEdit: boolean = !shared && !!accessLevel && [AccessLevel.Full, AccessLevel.Edit].includes(accessLevel)\r\n\r\n\r\n    const {definition} = props;\r\n\r\n    const database = databaseStore[definition.databaseId]\r\n    const editable = canEdit && !definition.lockContent\r\n\r\n    const fixDefinition = () => {\r\n        definition.columns = definition.columns || []\r\n    }\r\n\r\n    fixDefinition()\r\n\r\n    const updateColumn = (id: string, update: Partial<SummaryColumnCustomization>) => {\r\n        const columns = [...definition.columns]\r\n        for (let i = 0; i < columns.length; i++) {\r\n            const column = columns[i];\r\n            if (column.id === id) {\r\n                columns[i] = {...columns[i], ...update}\r\n                break\r\n            }\r\n        }\r\n        updateViewDefinition(props.view.id, props.view.pageId, {columns} as Partial<ViewDefinition>).then()\r\n    }\r\n\r\n    const deleteColumn = (id: string) => {\r\n        const columns = definition.columns.filter(c => c.id !== id)\r\n        updateViewDefinition(props.view.id, props.view.pageId, {columns} as Partial<ViewDefinition>).then()\r\n    }\r\n\r\n    const getColDefs = () => {\r\n        const columns: Column<any, any>[] = []\r\n\r\n        columns.push(RowIndexColumn)\r\n\r\n        if (!database) return columns\r\n        const dbDefinition = database.database.definition\r\n        if (!dbDefinition) return columns\r\n\r\n        for (const col of definition.columns) {\r\n            if (col.isHidden) continue\r\n            const dbCol = dbDefinition.columnsMap[col.columnId]\r\n            if (!dbCol) continue\r\n            const __meta__: SummaryColumnMeta = {\r\n                databaseId: definition.databaseId,\r\n                column: dbCol,\r\n                customization: col\r\n            }\r\n            const column = {\r\n                key: col.id,\r\n                name: col.title || dbCol.title,\r\n                renderEditCell: CellEditor,\r\n                renderCell: CellRenderer,\r\n                renderHeaderCell: (props: any) => <HeaderRenderer {...props} editable={canEdit} updateColumn={updateColumn} deleteColumn={deleteColumn}/>,\r\n                width: 200,\r\n                editable: true,\r\n                __meta__\r\n            }\r\n            column.editable = column.editable && editable\r\n            columns.push(column)\r\n        }\r\n\r\n        if (editable) columns.push(getNewColumnProps(database.database.id, props.view))\r\n\r\n\r\n        return columns\r\n    }\r\n\r\n    const cols = getColDefs()\r\n\r\n\r\n    const getProcessedRows = (): {\r\n        rowMap: {\r\n            [key: string]: DataViewRow\r\n        },\r\n        groupRowMap: {\r\n            [key: string]: SummaryViewRow\r\n        }\r\n    } => {\r\n        if (!database) return {rowMap: {}, groupRowMap: {}}\r\n\r\n        const sortOptions: DbRecordSort[] = []\r\n        if (sorts.length > 0) {\r\n            sortOptions.push(...sorts)\r\n        } else if (definition.sorts.length > 0) {\r\n            sortOptions.push(...definition.sorts)\r\n        }\r\n        if (sortOptions.length === 0) sortOptions.push({columnId: MagicColumn.CreatedAt, order: Sort.Asc})\r\n\r\n        let {rows} = filterAndSortRecords(\r\n            database,\r\n            members,\r\n            databaseStore,\r\n            definition.filter,\r\n            filter,\r\n            sortOptions,\r\n            workspace.workspaceMember.userId,\r\n        )\r\n        if (search && search.trim()) {\r\n            rows = rows.filter(r => r.processedRecord.valuesText.toLowerCase().includes(search.trim().toLowerCase()))\r\n        }\r\n        const groupRowMap: {\r\n            [key: string]: SummaryViewRow\r\n        } = {}\r\n        const rowMap: {\r\n            [key: string]: DataViewRow\r\n        } = {}\r\n\r\n        let groupBy = definition.groupByIds\r\n\r\n        for (const row of rows) {\r\n            const groupValues: RecordValues = {}\r\n            for (const id of groupBy) {\r\n                groupValues[id] = row.record.recordValues[id]\r\n            }\r\n            const valuesJSON = JSON.stringify(groupValues)\r\n            const hash = crypto.createHash('sha1').update(valuesJSON).digest('hex')\r\n\r\n            if (groupRowMap[hash]) {\r\n                const updatedAtTime = Math.max(new Date(groupRowMap[hash].updatedAt).getTime(), new Date(row.updatedAt).getTime())\r\n                groupRowMap[hash].updatedAt = new Date(updatedAtTime).toISOString()\r\n                groupRowMap[hash].recordIds.push(row.id)\r\n            } else {\r\n                groupRowMap[hash] = {\r\n                    recordIds: [row.id],\r\n                    updatedAt: row.updatedAt,\r\n                    id: hash,\r\n                    rowMap: {}\r\n                }\r\n            }\r\n            groupRowMap[hash].rowMap[row.id] = row\r\n            rowMap[row.id] = row\r\n        }\r\n        return {\r\n            groupRowMap, rowMap\r\n        }\r\n    }\r\n\r\n    const rowData = getProcessedRows()\r\n    const rows = Object.values(rowData.groupRowMap)\r\n\r\n    function rowKeyGetter(row: any) {\r\n        // function rowKeyGetter(row: Row) {\r\n        return row.id;\r\n    }\r\n\r\n    const hasCol = cols.length > 1 && cols[1].key !== 'add-column'\r\n\r\n    return <div className=\"w-full h-full overflow-hidden table-view bg-white\">\r\n        {!hasCol ? <>\r\n\r\n            <PageLoader\r\n                size='full'\r\n                error={<div className='my-2 flex flex-col gap-4 items-center justify-center'>\r\n                    <div className=''>\r\n                        <BalloonIcon className='size-8'/>\r\n                    </div>\r\n                    <span className='text-sm font-medium'>Add a column to get started</span>\r\n\r\n                    <AddColumnDropdown\r\n                        view={props.view}\r\n                        databaseId={definition.databaseId}\r\n                        dropdownAlign='center'\r\n                        trigger={<Button variant=\"outline\" className=\"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 font-semibold\">\r\n                            <CirclePlusIcon className=\"size-3\"/>\r\n                            Add Column\r\n                        </Button>}\r\n                    />\r\n                </div>}\r\n            />\r\n        </> : <>\r\n             <div className=\"size-full overflow-hidden table-view bg-white\">\r\n                 <DataGrid columns={cols}\r\n                           rows={rows}\r\n                           rowHeight={40}\r\n                           headerRowHeight={40}\r\n                           summaryRowHeight={40}\r\n                           className=\"w-full h-full rdg-light\"\r\n                           rowKeyGetter={rowKeyGetter}\r\n                 />\r\n\r\n             </div>\r\n\r\n         </>}\r\n    </div>\r\n}\r\n\r\n\r\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "jsx_runtime", "jsx", "div", "className", "AddColumnDropdown", "view", "databaseId", "trigger", "jsxs", "PlusCircleIcon", "span", "<PERSON><PERSON><PERSON><PERSON>", "getNewColumnProps", "key", "name", "width", "renderHeaderCell", "renderCell", "toast", "useAlert", "databaseStore", "useWorkspace", "updateViewDefinition", "useViews", "open", "<PERSON><PERSON><PERSON>", "useState", "definition", "columns", "dbColumns", "useMemo", "db", "col", "Object", "values", "database", "columnsMap", "column", "data", "id", "title", "value", "push", "doCreate", "generateUUID", "columnId", "aggregateBy", "ShowValuesAggregateFunction", "ShowOriginal", "pageId", "then", "Fragment", "DropdownMenu", "onOpenChange", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "DropdownMenuContent", "align", "dropdownAlign", "ScrollArea", "map", "f", "i", "c", "DropdownMenuItem", "onClick", "DatabaseFieldTypeIcon", "type", "RowIndexRender<PERSON>", "isRowSelected", "onRowSelectionChange", "useRowSelection", "SelectFormatter", "rowIdx", "SelectGroupFormatter", "Checkbox", "checked", "onCheckedChange", "row", "isShiftClick", "RowIndexColumn", "SELECT_COLUMN_KEY", "resizable", "sortable", "frozen", "renderGroupCell", "SummaryTableView", "members", "workspace", "sorts", "filter", "search", "useViewFiltering", "accessLevel", "usePage", "canEdit", "useMaybeShared", "AccessLevel", "Full", "Edit", "includes", "editable", "lock<PERSON><PERSON><PERSON>", "updateColumn", "update", "length", "deleteColumn", "cols", "getColDefs", "dbDefinition", "isHidden", "dbCol", "__meta__", "customization", "renderEditCell", "CellEditor", "rows", "rowData", "rowMap", "groupRowMap", "sortOptions", "MagicColumn", "CreatedAt", "order", "Sort", "Asc", "filterAndSortRecords", "workspaceMember", "userId", "trim", "r", "processedRecord", "valuesText", "toLowerCase", "groupBy", "groupByIds", "groupValues", "record", "recordValues", "valuesJSON", "JSON", "stringify", "hash", "crypto", "digest", "updatedAtTime", "Math", "max", "Date", "updatedAt", "getTime", "toISOString", "recordIds", "hasCol", "DataGrid", "rowHeight", "headerRowHeight", "summaryRowHeight", "rowKeyGetter", "<PERSON><PERSON><PERSON><PERSON>", "size", "error", "BalloonIcon", "<PERSON><PERSON>", "variant", "CirclePlusIcon"], "sourceRoot": ""}