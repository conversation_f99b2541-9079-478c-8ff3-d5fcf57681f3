!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="322809c4-3c58-4d10-89dd-bcea2023a439",e._sentryDebugIdIdentifier="sentry-dbid-322809c4-3c58-4d10-89dd-bcea2023a439")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8497,8107],{13223:function(e,t,r){Promise.resolve().then(r.bind(r,4953))},53731:function(e,t){"use strict";var r=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;t.G=function(e){if(!e||e.length>254||!r.test(e))return!1;var t=e.split("@");return!(t[0].length>64||t[1].split(".").some(function(e){return e.length>63}))}},30166:function(e,t,r){"use strict";r.d(t,{default:function(){return o.a}});var n=r(55775),o=r.n(n)},99376:function(e,t,r){"use strict";var n=r(35475);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},55775:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(47043);r(57437),r(2265);let o=n._(r(15602));function l(e,t){var r;let n={loading:e=>{let{error:t,isLoading:r,pastDelay:n}=e;return null}};"function"==typeof e&&(n.loader=e);let l={...n,...t};return(0,o.default)({...l,modules:null==(r=l.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81523:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return o}});let n=r(18993);function o(e){let{reason:t,children:r}=e;if("undefined"==typeof window)throw new n.BailoutToCSRError(t);return r}},15602:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let n=r(57437),o=r(2265),l=r(81523),i=r(70049);function a(e){return{default:e&&"default"in e?e.default:e}}let s={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},d=function(e){let t={...s,...e},r=(0,o.lazy)(()=>t.loader().then(a)),d=t.loading;function u(e){let a=d?(0,n.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,s=t.ssr?(0,n.jsxs)(n.Fragment,{children:["undefined"==typeof window?(0,n.jsx)(i.PreloadCss,{moduleIds:t.modules}):null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(o.Suspense,{fallback:a,children:s})}return u.displayName="LoadableComponent",u}},70049:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return l}});let n=r(57437),o=r(20544);function l(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let r=(0,o.getExpectedRequestStore)("next/dynamic css"),l=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files.filter(e=>e.endsWith(".css"));l.push(...t)}}return 0===l.length?null:(0,n.jsx)(n.Fragment,{children:l.map(e=>(0,n.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},23500:function(e,t){"use strict";var r,n,o;t.Bq=t.Ly=t.bW=void 0,(r=t.bW||(t.bW={})).Table="table",r.Board="board",r.Form="form",r.Document="document",r.Dashboard="dashboard",r.SummaryTable="summary-table",r.ListView="list-view",r.Calendar="calendar",(n=t.Ly||(t.Ly={})).Left="left",n.Right="right",(o=t.Bq||(t.Bq={})).Infobox="infobox",o.LineChart="lineChart",o.BarChart="barChart",o.PieChart="pieChart",o.FunnelChart="funnelChart",o.Embed="embed",o.Image="image",o.Text="text"},4953:function(e,t,r){"use strict";r.r(t);var n=r(57437),o=r(16720);r(2265);var l=r(99376);t.default=e=>{let{children:t}=e,r=(0,l.useParams)(),i=r.pageId,a=r.viewId;return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(o.JJ,{context:"page",parentId:i,viewId:a,children:t})})}},54921:function(e,t,r){"use strict";r.d(t,{o:function(){return b}});var n=r(57437),o=r(23500),l=r(2265),i=r(30166),a=r(29119);let s=(0,i.default)(()=>Promise.all([r.e(1092),r.e(6018),r.e(8025),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(6462),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(7674),r.e(1506),r.e(7561),r.e(663),r.e(3879),r.e(4376),r.e(4239),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3139),r.e(7515),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(8267),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(6240)]).then(r.bind(r,26240)).then(e=>e.DashboardView),{loadableGenerated:{webpack:()=>[26240]},ssr:!1}),d=(0,i.default)(()=>Promise.all([r.e(8025),r.e(6018),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818)]).then(r.bind(r,97017)).then(e=>e.TableView),{loadableGenerated:{webpack:()=>[97017]},ssr:!1}),u=(0,i.default)(()=>Promise.all([r.e(6018),r.e(7360),r.e(696),r.e(8025),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(6640),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(6326),r.e(4805),r.e(8849),r.e(8603)]).then(r.bind(r,89034)).then(e=>e.BoardView),{loadableGenerated:{webpack:()=>[89034]},ssr:!1}),c=(0,i.default)(()=>Promise.all([r.e(6018),r.e(7360),r.e(696),r.e(8025),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(631)]).then(r.bind(r,60631)).then(e=>e.FormView),{loadableGenerated:{webpack:()=>[60631]},ssr:!1}),f=(0,i.default)(()=>Promise.all([r.e(6018),r.e(2191),r.e(7190),r.e(696),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(8107),r.e(5737),r.e(794),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(7918)]).then(r.bind(r,87918)).then(e=>e.DocumentView),{loadableGenerated:{webpack:()=>[87918]},ssr:!1}),h=(0,i.default)(()=>Promise.all([r.e(8025),r.e(6018),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(9625)]).then(r.bind(r,79625)).then(e=>e.SummaryTableView),{loadableGenerated:{webpack:()=>[79625]},ssr:!1}),p=(0,i.default)(()=>Promise.all([r.e(6018),r.e(8025),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(826),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(8792)]).then(r.bind(r,68792)).then(e=>e.ListView),{loadableGenerated:{webpack:()=>[68792]},ssr:!1}),w=(0,i.default)(()=>Promise.all([r.e(6018),r.e(8025),r.e(7360),r.e(696),r.e(7698),r.e(2191),r.e(7190),r.e(8310),r.e(8218),r.e(9442),r.e(9900),r.e(3572),r.e(7902),r.e(5501),r.e(1425),r.e(6137),r.e(7648),r.e(311),r.e(2534),r.e(4451),r.e(1107),r.e(85),r.e(3493),r.e(3139),r.e(7515),r.e(8107),r.e(5737),r.e(794),r.e(9175),r.e(7353),r.e(6640),r.e(7900),r.e(2211),r.e(2212),r.e(6208),r.e(3818),r.e(6326),r.e(4805),r.e(8849),r.e(2949)]).then(r.bind(r,39910)).then(e=>e.CalendarView),{loadableGenerated:{webpack:()=>[39910]},ssr:!1}),b=e=>{let{viewsMap:t}=(0,a.qt)(),r=e.id,i=e.view||t[r],b=i.type;return(0,l.useEffect)(()=>{document.title=(null==i?void 0:i.name)||"Untitled"},[]),(0,n.jsx)(n.Fragment,{children:b===o.bW.Table?(0,n.jsx)(d,{view:i,definition:i.definition}):b===o.bW.Board?(0,n.jsx)(u,{view:i,definition:i.definition}):b===o.bW.Form?(0,n.jsx)(c,{view:i,definition:i.definition}):b===o.bW.Document?(0,n.jsx)(f,{view:i,definition:i.definition}):b===o.bW.SummaryTable?(0,n.jsx)(h,{view:i,definition:i.definition}):b===o.bW.Dashboard?(0,n.jsx)(s,{view:i,definition:i.definition}):b===o.bW.ListView?(0,n.jsx)(p,{view:i,definition:i.definition}):b===o.bW.Calendar?(0,n.jsx)(w,{view:i,definition:i.definition}):(0,n.jsx)(n.Fragment,{})})}},29119:function(e,t,r){"use strict";r.d(t,{Ti:function(){return l},ol:function(){return a},qt:function(){return s}});var n=r(57437),o=r(2265);let l=e=>{let{page:t,views:r,accessLevel:o,permissions:l}=e.permissiblePage,a={};for(let e of r)a[e.id]=e;return(0,n.jsx)(i.Provider,{value:{page:t,views:r,accessLevel:o,permissions:l,viewsMap:a},children:e.children})},i=(0,o.createContext)(void 0),a=()=>(0,o.useContext)(i)||null,s=()=>{let e=(0,o.useContext)(i);if(!e)throw Error("usePage must be used within a PageProvider");return e}},40178:function(e,t,r){"use strict";var n=r(2265);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...l}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},l),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"}))});t.Z=o},74610:function(e,t,r){"use strict";var n=r(2265);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...l}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},l),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"}))});t.Z=o},21726:function(e,t,r){"use strict";var n=r(2265);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...l}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},l),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=o},62484:function(e,t,r){"use strict";function n(e,[t,r]){return Math.min(r,Math.max(t,e))}r.d(t,{u:function(){return n}})},43643:function(e,t,r){"use strict";r.d(t,{Ns:function(){return q},fC:function(){return Y},gb:function(){return P},l_:function(){return G},q4:function(){return A}});var n=r(2265),o=r(66840),l=r(71599),i=r(73966),a=r(98575),s=r(26606),d=r(29114),u=r(61188),c=r(62484),f=r(6741),h=r(57437),p="ScrollArea",[w,b]=(0,i.b)(p),[v,m]=w(p),g=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:l="hover",dir:i,scrollHideDelay:s=600,...u}=e,[c,f]=n.useState(null),[p,w]=n.useState(null),[b,m]=n.useState(null),[g,x]=n.useState(null),[y,S]=n.useState(null),[P,C]=n.useState(0),[E,j]=n.useState(0),[R,T]=n.useState(!1),[_,L]=n.useState(!1),W=(0,a.e)(t,e=>f(e)),D=(0,d.gm)(i);return(0,h.jsx)(v,{scope:r,type:l,dir:D,scrollHideDelay:s,scrollArea:c,viewport:p,onViewportChange:w,content:b,onContentChange:m,scrollbarX:g,onScrollbarXChange:x,scrollbarXEnabled:R,onScrollbarXEnabledChange:T,scrollbarY:y,onScrollbarYChange:S,scrollbarYEnabled:_,onScrollbarYEnabledChange:L,onCornerWidthChange:C,onCornerHeightChange:j,children:(0,h.jsx)(o.WV.div,{dir:D,...u,ref:W,style:{position:"relative","--radix-scroll-area-corner-width":P+"px","--radix-scroll-area-corner-height":E+"px",...e.style}})})});g.displayName=p;var x="ScrollAreaViewport",y=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:l,nonce:i,...s}=e,d=m(x,r),u=n.useRef(null),c=(0,a.e)(t,u,d.onViewportChange);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,h.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...s,ref:c,style:{overflowX:d.scrollbarXEnabled?"scroll":"hidden",overflowY:d.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,h.jsx)("div",{ref:d.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});y.displayName=x;var S="ScrollAreaScrollbar",P=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=m(S,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,h.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,h.jsx)(E,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,h.jsx)(j,{...o,ref:t,forceMount:r}):"always"===l.type?(0,h.jsx)(R,{...o,ref:t}):null});P.displayName=S;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=m(S,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,h.jsx)(l.z,{present:r||a,children:(0,h.jsx)(j,{"data-state":a?"visible":"hidden",...o,ref:t})})}),E=n.forwardRef((e,t)=>{var r,o;let{forceMount:i,...a}=e,s=m(S,e.__scopeScrollArea),d="horizontal"===e.orientation,u=U(()=>p("SCROLL_END"),100),[c,p]=(r="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let r=o[e][t];return null!=r?r:e},r));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>p("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,s.scrollHideDelay,p]),n.useEffect(()=>{let e=s.viewport,t=d?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(p("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,d,p,u]),(0,h.jsx)(l.z,{present:i||"hidden"!==c,children:(0,h.jsx)(R,{"data-state":"hidden"===c?"hidden":"visible",...a,ref:t,onPointerEnter:(0,f.M)(e.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:(0,f.M)(e.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),j=n.forwardRef((e,t)=>{let r=m(S,e.__scopeScrollArea),{forceMount:o,...i}=e,[a,s]=n.useState(!1),d="horizontal"===e.orientation,u=U(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(d?e:t)}},10);return X(r.viewport,u),X(r.content,u),(0,h.jsx)(l.z,{present:o||a,children:(0,h.jsx)(R,{"data-state":a?"visible":"hidden",...i,ref:t})})}),R=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=m(S,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,d]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=V(s.viewport,s.content),c={...o,sizes:s,onSizesChange:d,hasThumb:!!(u>0&&u<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function f(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=B(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return F([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,h.jsx)(T,{...c,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Z(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=f(e,l.dir))}}):"vertical"===r?(0,h.jsx)(_,{...c,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Z(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=f(e))}}):null}),T=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=m(S,e.__scopeScrollArea),[s,d]=n.useState(),u=n.useRef(null),c=(0,a.e)(t,u,i.onScrollbarXChange);return n.useEffect(()=>{u.current&&d(getComputedStyle(u.current))},[u]),(0,h.jsx)(D,{"data-orientation":"horizontal",...l,ref:c,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":B(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{u.current&&i.viewport&&s&&o({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:N(s.paddingLeft),paddingEnd:N(s.paddingRight)}})}})}),_=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=m(S,e.__scopeScrollArea),[s,d]=n.useState(),u=n.useRef(null),c=(0,a.e)(t,u,i.onScrollbarYChange);return n.useEffect(()=>{u.current&&d(getComputedStyle(u.current))},[u]),(0,h.jsx)(D,{"data-orientation":"vertical",...l,ref:c,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":B(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{u.current&&i.viewport&&s&&o({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:N(s.paddingTop),paddingEnd:N(s.paddingBottom)}})}})}),[L,W]=w(S),D=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:l,hasThumb:i,onThumbChange:d,onThumbPointerUp:u,onThumbPointerDown:c,onThumbPositionChange:p,onDragScroll:w,onWheelScroll:b,onResize:v,...g}=e,x=m(S,r),[y,P]=n.useState(null),C=(0,a.e)(t,e=>P(e)),E=n.useRef(null),j=n.useRef(""),R=x.viewport,T=l.content-l.viewport,_=(0,s.W)(b),W=(0,s.W)(p),D=U(v,10);function k(e){E.current&&w({x:e.clientX-E.current.left,y:e.clientY-E.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==y?void 0:y.contains(t))&&_(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[R,y,T,_]),n.useEffect(W,[l,W]),X(y,D),X(x.content,D),(0,h.jsx)(L,{scope:r,scrollbar:y,hasThumb:i,onThumbChange:(0,s.W)(d),onThumbPointerUp:(0,s.W)(u),onThumbPositionChange:W,onThumbPointerDown:(0,s.W)(c),children:(0,h.jsx)(o.WV.div,{...g,ref:C,style:{position:"absolute",...g.style},onPointerDown:(0,f.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),E.current=y.getBoundingClientRect(),j.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),k(e))}),onPointerMove:(0,f.M)(e.onPointerMove,k),onPointerUp:(0,f.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=j.current,x.viewport&&(x.viewport.style.scrollBehavior=""),E.current=null})})})}),k="ScrollAreaThumb",A=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=W(k,e.__scopeScrollArea);return(0,h.jsx)(l.z,{present:r||o.hasThumb,children:(0,h.jsx)(M,{ref:t,...n})})}),M=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:l,...i}=e,s=m(k,r),d=W(k,r),{onThumbPositionChange:u}=d,c=(0,a.e)(t,e=>d.onThumbChange(e)),p=n.useRef(void 0),w=U(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{if(w(),!p.current){let t=H(e,u);p.current=t,u()}};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,w,u]),(0,h.jsx)(o.WV.div,{"data-state":d.hasThumb?"visible":"hidden",...i,ref:c,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,f.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;d.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.M)(e.onPointerUp,d.onThumbPointerUp)})});A.displayName=k;var O="ScrollAreaCorner",z=n.forwardRef((e,t)=>{let r=m(O,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,h.jsx)(I,{...e,ref:t}):null});z.displayName=O;var I=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=m(O,r),[a,s]=n.useState(0),[d,u]=n.useState(0),c=!!(a&&d);return X(i.scrollbarX,()=>{var e;let t=(null===(e=i.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),u(t)}),X(i.scrollbarY,()=>{var e;let t=(null===(e=i.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),c?(0,h.jsx)(o.WV.div,{...l,ref:t,style:{width:a,height:d,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function N(e){return e?parseInt(e,10):0}function V(e,t){let r=e/t;return isNaN(r)?0:r}function B(e){let t=V(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function Z(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=B(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=(0,c.u)(e,"ltr"===r?[0,i]:[-1*i,0]);return F([0,i],[0,l-n])(a)}function F(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var H=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function U(e,t){let r=(0,s.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function X(e,t){let r=(0,s.W)(t);(0,u.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var Y=g,G=y,q=z}},function(e){e.O(0,[6018,7360,696,8025,7698,2191,7190,8310,8218,9442,9900,3572,7902,5501,1425,6137,7648,311,2534,4451,1107,85,3493,3139,7515,5737,794,9175,7353,6640,7900,2211,2212,6208,3818,6326,4805,8849,991,2971,6577,1744],function(){return e(e.s=13223)}),_N_E=e.O()}]);