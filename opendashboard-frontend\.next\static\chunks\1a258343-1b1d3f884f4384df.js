!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="cc0170ae-ed2b-44df-96dd-595d4ddd4e3b",e._sentryDebugIdIdentifier="sentry-dbid-cc0170ae-ed2b-44df-96dd-595d4ddd4e3b")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[167],{52542:function(e,t,n){n.r(t),n.d(t,{Background:function(){return t3},BackgroundVariant:function(){return r},BaseEdge:function(){return eQ},BezierEdge:function(){return e9},ConnectionLineType:function(){return s.t8},ConnectionMode:function(){return s.jD},ControlButton:function(){return t9},Controls:function(){return nn},EdgeLabelRenderer:function(){return tR},EdgeText:function(){return eK},Handle:function(){return eN},MarkerType:function(){return s.QZ},MiniMap:function(){return nu},NodeResizeControl:function(){return nf},NodeResizer:function(){return ng},NodeToolbar:function(){return nw},PanOnScrollMode:function(){return s.IY},Panel:function(){return k},Position:function(){return s.Ly},ReactFlow:function(){return tj},ReactFlowProvider:function(){return tN},ResizeControlVariant:function(){return s.pB},SelectionMode:function(){return s.oW},SimpleBezierEdge:function(){return eG},SmoothStepEdge:function(){return e0},StepEdge:function(){return e3},StraightEdge:function(){return e6},ViewportPortal:function(){return tD},addEdge:function(){return s.Z_},applyEdgeChanges:function(){return Q},applyNodeChanges:function(){return K},getBezierEdgeCenter:function(){return s.lM},getBezierPath:function(){return s.OQ},getConnectedEdges:function(){return s.my},getEdgeCenter:function(){return s.Pp},getIncomers:function(){return s.vG},getNodesBounds:function(){return s.RX},getOutgoers:function(){return s.xA},getSimpleBezierPath:function(){return eU},getSmoothStepPath:function(){return s.OW},getStraightPath:function(){return s.Hm},getViewportForBounds:function(){return s.$i},isEdge:function(){return J},isNode:function(){return $},reconnectEdge:function(){return s.Kz},useConnection:function(){return tv},useEdges:function(){return tB},useEdgesState:function(){return tF},useHandleConnections:function(){return tQ},useInternalNode:function(){return tG},useKeyPress:function(){return H},useNodeConnections:function(){return tU},useNodeId:function(){return eC},useNodes:function(){return tz},useNodesData:function(){return tq},useNodesInitialized:function(){return tK},useNodesState:function(){return tH},useOnSelectionChange:function(){return tT},useOnViewportChange:function(){return tX},useReactFlow:function(){return ei},useStore:function(){return m},useStoreApi:function(){return v},useUpdateNodeInternals:function(){return tA},useViewport:function(){return tZ}});var o,r,l=n(57437),i=n(2265),a=n(83568),s=n(19794),d=n(69829),c=n(85854),u=n(54887);let f=(0,i.createContext)(null),g=f.Provider,p=s.Qj.error001();function m(e,t){let n=(0,i.useContext)(f);if(null===n)throw Error(p);return(0,d.s)(n,e,t)}function v(){let e=(0,i.useContext)(f);if(null===e)throw Error(p);return(0,i.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe}),[e])}let h={display:"none"},y={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},w="react-flow__node-desc",b="react-flow__edge-desc",S=e=>e.ariaLiveMessage;function x(e){let{rfId:t}=e,n=m(S);return(0,l.jsx)("div",{id:"".concat("react-flow__aria-live","-").concat(t),"aria-live":"assertive","aria-atomic":"true",style:y,children:n})}function C(e){let{rfId:t,disableKeyboardA11y:n}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{id:"".concat(w,"-").concat(t),style:h,children:["Press enter or space to select a node.",!n&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "]}),(0,l.jsx)("div",{id:"".concat(b,"-").concat(t),style:h,children:"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."}),!n&&(0,l.jsx)(x,{rfId:t})]})}let E=e=>e.userSelectionActive?"none":"all",k=(0,i.forwardRef)((e,t)=>{let{position:n="top-left",children:o,className:r,style:i,...s}=e,d=m(E),c="".concat(n).split("-");return(0,l.jsx)("div",{className:(0,a.Z)(["react-flow__panel",r,...c]),style:{...i,pointerEvents:d},ref:t,...s,children:o})});function N(e){let{proOptions:t,position:n="bottom-right"}=e;return(null==t?void 0:t.hideAttribution)?null:(0,l.jsx)(k,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev",children:(0,l.jsx)("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution",children:"React Flow"})})}k.displayName="Panel";let M=e=>{let t=[],n=[];for(let[,n]of e.nodeLookup)n.selected&&t.push(n.internals.userNode);for(let[,t]of e.edgeLookup)t.selected&&n.push(t);return{selectedNodes:t,selectedEdges:n}},P=e=>e.id;function j(e,t){return(0,c.X)(e.selectedNodes.map(P),t.selectedNodes.map(P))&&(0,c.X)(e.selectedEdges.map(P),t.selectedEdges.map(P))}function _(e){let{onSelectionChange:t}=e,n=v(),{selectedNodes:o,selectedEdges:r}=m(M,j);return(0,i.useEffect)(()=>{let e={nodes:o,edges:r};null==t||t(e),n.getState().onSelectionChangeHandlers.forEach(t=>t(e))},[o,r,t]),null}let R=e=>!!e.onSelectionChangeHandlers;function I(e){let{onSelectionChange:t}=e,n=m(R);return t||n?(0,l.jsx)(_,{onSelectionChange:t}):null}let D=[0,0],A={x:0,y:0,zoom:1},O=["nodes","edges","defaultNodes","defaultEdges","onConnect","onConnectStart","onConnectEnd","onClickConnectStart","onClickConnectEnd","nodesDraggable","nodesConnectable","nodesFocusable","edgesFocusable","edgesReconnectable","elevateNodesOnSelect","elevateEdgesOnSelect","minZoom","maxZoom","nodeExtent","onNodesChange","onEdgesChange","elementsSelectable","connectionMode","snapGrid","snapToGrid","translateExtent","connectOnClick","defaultEdgeOptions","fitView","fitViewOptions","onNodesDelete","onEdgesDelete","onDelete","onNodeDrag","onNodeDragStart","onNodeDragStop","onSelectionDrag","onSelectionDragStart","onSelectionDragStop","onMoveStart","onMove","onMoveEnd","noPanClassName","nodeOrigin","autoPanOnConnect","autoPanOnNodeDrag","onError","connectionRadius","isValidConnection","selectNodesOnDrag","nodeDragThreshold","onBeforeDelete","debug","autoPanSpeed","paneClickDistance","rfId"],z=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setPaneClickDistance:e.setPaneClickDistance}),L={translateExtent:s.k5,nodeOrigin:D,minZoom:.5,maxZoom:2,elementsSelectable:!0,noPanClassName:"nopan",rfId:"1",paneClickDistance:0};function B(e){let{setNodes:t,setEdges:n,setMinZoom:o,setMaxZoom:r,setTranslateExtent:l,setNodeExtent:a,reset:s,setDefaultNodesAndEdges:d,setPaneClickDistance:u}=m(z,c.X),f=v();(0,i.useEffect)(()=>(d(e.defaultNodes,e.defaultEdges),()=>{g.current=L,s()}),[]);let g=(0,i.useRef)(L);return(0,i.useEffect)(()=>{for(let i of O){let s=e[i];s!==g.current[i]&&void 0!==e[i]&&("nodes"===i?t(s):"edges"===i?n(s):"minZoom"===i?o(s):"maxZoom"===i?r(s):"translateExtent"===i?l(s):"nodeExtent"===i?a(s):"paneClickDistance"===i?u(s):"fitView"===i?f.setState({fitViewQueued:s}):"fitViewOptions"===i?f.setState({fitViewOptions:s}):f.setState({[i]:s}))}g.current=e},O.map(t=>e[t])),null}function V(){return"undefined"!=typeof window&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null}let Z="undefined"!=typeof document?document:null;function H(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{target:Z,actInsideInputWithModifier:!0},[n,o]=(0,i.useState)(!1),r=(0,i.useRef)(!1),l=(0,i.useRef)(new Set([])),[a,d]=(0,i.useMemo)(()=>{if(null!==e){let t=(Array.isArray(e)?e:[e]).filter(e=>"string"==typeof e).map(e=>e.replace("+","\n").replace("\n\n","\n+").split("\n")),n=t.reduce((e,t)=>e.concat(...t),[]);return[t,n]}return[[],[]]},[e]);return(0,i.useEffect)(()=>{var n,i;let c=null!==(n=null==t?void 0:t.target)&&void 0!==n?n:Z,u=null===(i=null==t?void 0:t.actInsideInputWithModifier)||void 0===i||i;if(null!==e){let e=e=>{if(r.current=e.ctrlKey||e.metaKey||e.shiftKey||e.altKey,(!r.current||r.current&&!u)&&(0,s.s$)(e))return!1;let n=X(e.code,d);if(l.current.add(e[n]),F(a,l.current,!1)){var i,c;let n=(null===(c=e.composedPath)||void 0===c?void 0:null===(i=c.call(e))||void 0===i?void 0:i[0])||e.target,l=(null==n?void 0:n.nodeName)==="BUTTON"||(null==n?void 0:n.nodeName)==="A";!1!==t.preventDefault&&(r.current||!l)&&e.preventDefault(),o(!0)}},n=e=>{let t=X(e.code,d);F(a,l.current,!0)?(o(!1),l.current.clear()):l.current.delete(e[t]),"Meta"===e.key&&l.current.clear(),r.current=!1},i=()=>{l.current.clear(),o(!1)};return null==c||c.addEventListener("keydown",e),null==c||c.addEventListener("keyup",n),window.addEventListener("blur",i),window.addEventListener("contextmenu",i),()=>{null==c||c.removeEventListener("keydown",e),null==c||c.removeEventListener("keyup",n),window.removeEventListener("blur",i),window.removeEventListener("contextmenu",i)}}},[e,o]),n}function F(e,t,n){return e.filter(e=>n||e.length===t.size).some(e=>e.every(e=>t.has(e)))}function X(e,t){return t.includes(e)?"code":"key"}let T=()=>{let e=v();return(0,i.useMemo)(()=>({zoomIn:t=>{let{panZoom:n}=e.getState();return n?n.scaleBy(1.2,{duration:null==t?void 0:t.duration}):Promise.resolve(!1)},zoomOut:t=>{let{panZoom:n}=e.getState();return n?n.scaleBy(1/1.2,{duration:null==t?void 0:t.duration}):Promise.resolve(!1)},zoomTo:(t,n)=>{let{panZoom:o}=e.getState();return o?o.scaleTo(t,{duration:null==n?void 0:n.duration}):Promise.resolve(!1)},getZoom:()=>e.getState().transform[2],setViewport:async(t,n)=>{var o,r,l;let{transform:[i,a,s],panZoom:d}=e.getState();return d?(await d.setViewport({x:null!==(o=t.x)&&void 0!==o?o:i,y:null!==(r=t.y)&&void 0!==r?r:a,zoom:null!==(l=t.zoom)&&void 0!==l?l:s},{duration:null==n?void 0:n.duration}),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>{let[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},setCenter:async(t,n,o)=>{let{width:r,height:l,maxZoom:i,panZoom:a}=e.getState(),s=void 0!==(null==o?void 0:o.zoom)?o.zoom:i;return a?(await a.setViewport({x:r/2-t*s,y:l/2-n*s,zoom:s},{duration:null==o?void 0:o.duration}),Promise.resolve(!0)):Promise.resolve(!1)},fitBounds:async(t,n)=>{var o;let{width:r,height:l,minZoom:i,maxZoom:a,panZoom:d}=e.getState(),c=(0,s.$i)(t,r,l,i,a,null!==(o=null==n?void 0:n.padding)&&void 0!==o?o:.1);return d?(await d.setViewport(c,{duration:null==n?void 0:n.duration}),Promise.resolve(!0)):Promise.resolve(!1)},screenToFlowPosition:function(t){var n,o;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{transform:l,snapGrid:i,snapToGrid:a,domNode:d}=e.getState();if(!d)return t;let{x:c,y:u}=d.getBoundingClientRect(),f={x:t.x-c,y:t.y-u},g=null!==(n=r.snapGrid)&&void 0!==n?n:i,p=null!==(o=r.snapToGrid)&&void 0!==o?o:a;return(0,s.m)(f,l,p,g)},flowToScreenPosition:t=>{let{transform:n,domNode:o}=e.getState();if(!o)return t;let{x:r,y:l}=o.getBoundingClientRect(),i=(0,s.oj)(t,n);return{x:i.x+r,y:i.y+l}}}),[])};function W(e,t){let n=[],o=new Map,r=[];for(let t of e){if("add"===t.type){r.push(t);continue}if("remove"===t.type||"replace"===t.type)o.set(t.id,[t]);else{let e=o.get(t.id);e?e.push(t):o.set(t.id,[t])}}for(let e of t){let t=o.get(e.id);if(!t){n.push(e);continue}if("remove"===t[0].type)continue;if("replace"===t[0].type){n.push({...t[0].item});continue}let r={...e};for(let e of t)!function(e,t){switch(e.type){case"select":t.selected=e.selected;break;case"position":void 0!==e.position&&(t.position=e.position),void 0!==e.dragging&&(t.dragging=e.dragging);break;case"dimensions":if(void 0!==e.dimensions){var n;null!==(n=t.measured)&&void 0!==n||(t.measured={}),t.measured.width=e.dimensions.width,t.measured.height=e.dimensions.height,e.setAttributes&&((!0===e.setAttributes||"width"===e.setAttributes)&&(t.width=e.dimensions.width),(!0===e.setAttributes||"height"===e.setAttributes)&&(t.height=e.dimensions.height))}"boolean"==typeof e.resizing&&(t.resizing=e.resizing)}}(e,r);n.push(r)}return r.length&&r.forEach(e=>{void 0!==e.index?n.splice(e.index,0,{...e.item}):n.push({...e.item})}),n}function K(e,t){return W(e,t)}function Q(e,t){return W(e,t)}function Y(e,t){return{id:e,type:"select",selected:t}}function U(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=[];for(let[r,l]of e){let e=t.has(r);(void 0!==l.selected||e)&&l.selected!==e&&(n&&(l.selected=e),o.push(Y(l.id,e)))}return o}function q(e){let{items:t=[],lookup:n}=e,o=[],r=new Map(t.map(e=>[e.id,e]));for(let[e,r]of t.entries()){var l,i;let t=n.get(r.id),a=null!==(i=null==t?void 0:null===(l=t.internals)||void 0===l?void 0:l.userNode)&&void 0!==i?i:t;void 0!==a&&a!==r&&o.push({id:r.id,item:r,type:"replace"}),void 0===a&&o.push({item:r,type:"add",index:e})}for(let[e]of n)void 0===r.get(e)&&o.push({id:e,type:"remove"});return o}function G(e){return{id:e.id,type:"remove"}}let $=e=>(0,s.Vt)(e),J=e=>(0,s.J3)(e);function ee(e){return(0,i.forwardRef)(e)}let et="undefined"!=typeof window?i.useLayoutEffect:i.useEffect;function en(e){let[t,n]=(0,i.useState)(BigInt(0)),[o]=(0,i.useState)(()=>{var e;let t;return e=()=>n(e=>e+BigInt(1)),t=[],{get:()=>t,reset:()=>{t=[]},push:n=>{t.push(n),e()}}});return et(()=>{let t=o.get();t.length&&(e(t),o.reset())},[t]),o}let eo=(0,i.createContext)(null);function er(e){let{children:t}=e,n=v(),o=en((0,i.useCallback)(e=>{let{nodes:t=[],setNodes:o,hasDefaultNodes:r,onNodesChange:l,nodeLookup:i,fitViewQueued:a}=n.getState(),s=t;for(let t of e)s="function"==typeof t?t(s):t;let d=q({items:s,lookup:i});r&&o(s),d.length>0?null==l||l(d):a&&window.requestAnimationFrame(()=>{let{fitViewQueued:e,nodes:t,setNodes:o}=n.getState();e&&o(t)})},[])),r=en((0,i.useCallback)(e=>{let{edges:t=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:l,edgeLookup:i}=n.getState(),a=t;for(let t of e)a="function"==typeof t?t(a):t;r?o(a):l&&l(q({items:a,lookup:i}))},[])),a=(0,i.useMemo)(()=>({nodeQueue:o,edgeQueue:r}),[]);return(0,l.jsx)(eo.Provider,{value:a,children:t})}let el=e=>!!e.panZoom;function ei(){let e=T(),t=v(),n=function(){let e=(0,i.useContext)(eo);if(!e)throw Error("useBatchContext must be used within a BatchProvider");return e}(),o=m(el),r=(0,i.useMemo)(()=>{let e=e=>t.getState().nodeLookup.get(e),o=e=>{n.nodeQueue.push(e)},r=e=>{n.edgeQueue.push(e)},l=e=>{var n,o,r,l;let{nodeLookup:i,nodeOrigin:a}=t.getState(),d=$(e)?e:i.get(e.id),c=d.parentId?(0,s.ZB)(d.position,d.measured,d.parentId,i,a):d.position,u={...d,position:c,width:null!==(r=null===(n=d.measured)||void 0===n?void 0:n.width)&&void 0!==r?r:d.width,height:null!==(l=null===(o=d.measured)||void 0===o?void 0:o.height)&&void 0!==l?l:d.height};return(0,s.PS)(u)},i=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{replace:!1};o(o=>o.map(o=>{if(o.id===e){let e="function"==typeof t?t(o):t;return n.replace&&$(e)?e:{...o,...e}}return o}))},a=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{replace:!1};r(o=>o.map(o=>{if(o.id===e){let e="function"==typeof t?t(o):t;return n.replace&&J(e)?e:{...o,...e}}return o}))};return{getNodes:()=>t.getState().nodes.map(e=>({...e})),getNode:t=>{var n;return null===(n=e(t))||void 0===n?void 0:n.internals.userNode},getInternalNode:e,getEdges:()=>{let{edges:e=[]}=t.getState();return e.map(e=>({...e}))},getEdge:e=>t.getState().edgeLookup.get(e),setNodes:o,setEdges:r,addNodes:e=>{let t=Array.isArray(e)?e:[e];n.nodeQueue.push(e=>[...e,...t])},addEdges:e=>{let t=Array.isArray(e)?e:[e];n.edgeQueue.push(e=>[...e,...t])},toObject:()=>{let{nodes:e=[],edges:n=[],transform:o}=t.getState(),[r,l,i]=o;return{nodes:e.map(e=>({...e})),edges:n.map(e=>({...e})),viewport:{x:r,y:l,zoom:i}}},deleteElements:async e=>{let{nodes:n=[],edges:o=[]}=e,{nodes:r,edges:l,onNodesDelete:i,onEdgesDelete:a,triggerNodeChanges:d,triggerEdgeChanges:c,onDelete:u,onBeforeDelete:f}=t.getState(),{nodes:g,edges:p}=await (0,s.WD)({nodesToRemove:n,edgesToRemove:o,nodes:r,edges:l,onBeforeDelete:f}),m=p.length>0,v=g.length>0;if(m){let e=p.map(G);null==a||a(p),c(e)}if(v){let e=g.map(G);null==i||i(g),d(e)}return(v||m)&&(null==u||u({nodes:g,edges:p})),{deletedNodes:g,deletedEdges:p}},getIntersectingNodes:function(e){let n=!(arguments.length>1)||void 0===arguments[1]||arguments[1],o=arguments.length>2?arguments[2]:void 0,r=(0,s.J$)(e),i=r?e:l(e),a=void 0!==o;return i?(o||t.getState().nodes).filter(o=>{let l=t.getState().nodeLookup.get(o.id);if(l&&!r&&(o.id===e.id||!l.internals.positionAbsolute))return!1;let d=(0,s.PS)(a?o:l),c=(0,s.lp)(d,i);return n&&c>0||c>=i.width*i.height}):[]},isNodeIntersecting:function(e,t){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],o=(0,s.J$)(e)?e:l(e);if(!o)return!1;let r=(0,s.lp)(o,t);return n&&r>0||r>=o.width*o.height},updateNode:i,updateNodeData:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{replace:!1};i(e,e=>{let o="function"==typeof t?t(e):t;return n.replace?{...e,data:o}:{...e,data:{...e.data,...o}}},n)},updateEdge:a,updateEdgeData:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{replace:!1};a(e,e=>{let o="function"==typeof t?t(e):t;return n.replace?{...e,data:o}:{...e,data:{...e.data,...o}}},n)},getNodesBounds:e=>{let{nodeLookup:n,nodeOrigin:o}=t.getState();return(0,s.RX)(e,{nodeLookup:n,nodeOrigin:o})},getHandleConnections:e=>{var n,o;let{type:r,id:l,nodeId:i}=e;return Array.from(null!==(o=null===(n=t.getState().connectionLookup.get("".concat(i,"-").concat(r).concat(l?"-".concat(l):"")))||void 0===n?void 0:n.values())&&void 0!==o?o:[])},getNodeConnections:e=>{var n,o;let{type:r,handleId:l,nodeId:i}=e;return Array.from(null!==(o=null===(n=t.getState().connectionLookup.get("".concat(i).concat(r?l?"-".concat(r,"-").concat(l):"-".concat(r):"")))||void 0===n?void 0:n.values())&&void 0!==o?o:[])},fitView:async e=>{var o;let r=null!==(o=t.getState().fitViewResolver)&&void 0!==o?o:(0,s.nu)();return t.setState({fitViewQueued:!0,fitViewOptions:e,fitViewResolver:r}),n.nodeQueue.push(e=>[...e]),r.promise}}},[]);return(0,i.useMemo)(()=>({...r,...e,viewportInitialized:o}),[o])}let ea=e=>e.selected,es="undefined"!=typeof window?window:void 0,ed={position:"absolute",width:"100%",height:"100%",top:0,left:0},ec=e=>({userSelectionActive:e.userSelectionActive,lib:e.lib});function eu(e){let{onPaneContextMenu:t,zoomOnScroll:n=!0,zoomOnPinch:o=!0,panOnScroll:r=!1,panOnScrollSpeed:a=.5,panOnScrollMode:d=s.IY.Free,zoomOnDoubleClick:u=!0,panOnDrag:f=!0,defaultViewport:g,translateExtent:p,minZoom:h,maxZoom:y,zoomActivationKeyCode:w,preventScrolling:b=!0,children:S,noWheelClassName:x,noPanClassName:C,onViewportChange:E,isControlledViewport:k,paneClickDistance:N}=e,M=v(),P=(0,i.useRef)(null),{userSelectionActive:j,lib:_}=m(ec,c.X),R=H(w),I=(0,i.useRef)();!function(e){let t=v();(0,i.useEffect)(()=>{let n=()=>{if(!e.current)return!1;let n=(0,s.t_)(e.current);if(0===n.height||0===n.width){var o,r;null===(o=(r=t.getState()).onError)||void 0===o||o.call(r,"004",s.Qj.error004())}t.setState({width:n.width||500,height:n.height||500})};if(e.current){n(),window.addEventListener("resize",n);let t=new ResizeObserver(()=>n());return t.observe(e.current),()=>{window.removeEventListener("resize",n),t&&e.current&&t.unobserve(e.current)}}},[])}(P);let D=(0,i.useCallback)(e=>{null==E||E({x:e[0],y:e[1],zoom:e[2]}),k||M.setState({transform:e})},[E,k]);return(0,i.useEffect)(()=>{if(P.current){I.current=(0,s.X6)({domNode:P.current,minZoom:h,maxZoom:y,translateExtent:p,viewport:g,paneClickDistance:N,onDraggingChange:e=>M.setState({paneDragging:e}),onPanZoomStart:(e,t)=>{let{onViewportChangeStart:n,onMoveStart:o}=M.getState();null==o||o(e,t),null==n||n(t)},onPanZoom:(e,t)=>{let{onViewportChange:n,onMove:o}=M.getState();null==o||o(e,t),null==n||n(t)},onPanZoomEnd:(e,t)=>{let{onViewportChangeEnd:n,onMoveEnd:o}=M.getState();null==o||o(e,t),null==n||n(t)}});let{x:e,y:t,zoom:n}=I.current.getViewport();return M.setState({panZoom:I.current,transform:[e,t,n],domNode:P.current.closest(".react-flow")}),()=>{var e;null===(e=I.current)||void 0===e||e.destroy()}}},[]),(0,i.useEffect)(()=>{var e;null===(e=I.current)||void 0===e||e.update({onPaneContextMenu:t,zoomOnScroll:n,zoomOnPinch:o,panOnScroll:r,panOnScrollSpeed:a,panOnScrollMode:d,zoomOnDoubleClick:u,panOnDrag:f,zoomActivationKeyPressed:R,preventScrolling:b,noPanClassName:C,userSelectionActive:j,noWheelClassName:x,lib:_,onTransformChange:D})},[t,n,o,r,a,d,u,f,R,b,C,j,x,_,D]),(0,l.jsx)("div",{className:"react-flow__renderer",ref:P,style:ed,children:S})}let ef=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function eg(){let{userSelectionActive:e,userSelectionRect:t}=m(ef,c.X);return e&&t?(0,l.jsx)("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:"translate(".concat(t.x,"px, ").concat(t.y,"px)")}}):null}let ep=(e,t)=>n=>{n.target===t.current&&(null==e||e(n))},em=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,connectionInProgress:e.connection.inProgress,dragging:e.paneDragging});function ev(e){let{isSelecting:t,selectionKeyPressed:n,selectionMode:o=s.oW.Full,panOnDrag:r,selectionOnDrag:d,onSelectionStart:u,onSelectionEnd:f,onPaneClick:g,onPaneContextMenu:p,onPaneScroll:h,onPaneMouseEnter:y,onPaneMouseMove:w,onPaneMouseLeave:b,children:S}=e,x=v(),{userSelectionActive:C,elementsSelectable:E,dragging:k,connectionInProgress:N}=m(em,c.X),M=E&&(t||C),P=(0,i.useRef)(null),j=(0,i.useRef)(),_=(0,i.useRef)(new Set),R=(0,i.useRef)(new Set),I=(0,i.useRef)(!1),D=(0,i.useRef)(!1),A=e=>{if(I.current||N){I.current=!1;return}null==g||g(e),x.getState().resetSelectedElements(),x.setState({nodesSelectionActive:!1})},O=!0===r||Array.isArray(r)&&r.includes(0);return(0,l.jsxs)("div",{className:(0,a.Z)(["react-flow__pane",{draggable:O,dragging:k,selection:t}]),onClick:M?void 0:ep(A,P),onContextMenu:ep(e=>{if(Array.isArray(r)&&(null==r?void 0:r.includes(2))){e.preventDefault();return}null==p||p(e)},P),onWheel:ep(h?e=>h(e):void 0,P),onPointerEnter:M?void 0:y,onPointerDown:M?e=>{var n,o;let{resetSelectedElements:r,domNode:l}=x.getState();if(j.current=null==l?void 0:l.getBoundingClientRect(),!E||!t||0!==e.button||e.target!==P.current||!j.current)return;null===(o=e.target)||void 0===o||null===(n=o.setPointerCapture)||void 0===n||n.call(o,e.pointerId),D.current=!0,I.current=!1;let{x:i,y:a}=(0,s.wv)(e.nativeEvent,j.current);r(),x.setState({userSelectionRect:{width:0,height:0,startX:i,startY:a,x:i,y:a}}),null==u||u(e)}:w,onPointerMove:M?e=>{var t,n;let{userSelectionRect:r,transform:l,nodeLookup:i,edgeLookup:a,connectionLookup:d,triggerNodeChanges:c,triggerEdgeChanges:u,defaultEdgeOptions:f}=x.getState();if(!j.current||!r)return;I.current=!0;let{x:g,y:p}=(0,s.wv)(e.nativeEvent,j.current),{startX:m,startY:v}=r,h={startX:m,startY:v,x:g<m?g:m,y:p<v?p:v,width:Math.abs(g-m),height:Math.abs(p-v)},y=_.current,w=R.current;_.current=new Set((0,s.f5)(i,h,l,o===s.oW.Partial,!0).map(e=>e.id)),R.current=new Set;let b=null===(t=null==f?void 0:f.selectable)||void 0===t||t;for(let e of _.current){let t=d.get(e);if(t)for(let{edgeId:e}of t.values()){let t=a.get(e);t&&(null!==(n=t.selectable)&&void 0!==n?n:b)&&R.current.add(e)}}(0,s.OL)(y,_.current)||c(U(i,_.current,!0)),(0,s.OL)(w,R.current)||u(U(a,R.current)),x.setState({userSelectionRect:h,userSelectionActive:!0,nodesSelectionActive:!1})}:w,onPointerUp:M?e=>{var t,o;if(0!==e.button||!D.current)return;null===(o=e.target)||void 0===o||null===(t=o.releasePointerCapture)||void 0===t||t.call(o,e.pointerId);let{userSelectionRect:r}=x.getState();!C&&r&&e.target===P.current&&(null==A||A(e)),x.setState({userSelectionActive:!1,userSelectionRect:null,nodesSelectionActive:_.current.size>0}),null==f||f(e),(n||d)&&(I.current=!1),D.current=!1}:void 0,onPointerLeave:b,ref:P,style:ed,children:[S,(0,l.jsx)(eg,{})]})}function eh(e){let{id:t,store:n,unselect:o=!1,nodeRef:r}=e,{addSelectedNodes:l,unselectNodesAndEdges:i,multiSelectionActive:a,nodeLookup:d,onError:c}=n.getState(),u=d.get(t);if(!u){null==c||c("012",s.Qj.error012(t));return}n.setState({nodesSelectionActive:!1}),u.selected?(o||u.selected&&a)&&(i({nodes:[u],edges:[]}),requestAnimationFrame(()=>{var e;return null==r?void 0:null===(e=r.current)||void 0===e?void 0:e.blur()})):l([t])}function ey(e){let{nodeRef:t,disabled:n=!1,noDragClassName:o,handleSelector:r,nodeId:l,isSelectable:a,nodeClickDistance:d}=e,c=v(),[u,f]=(0,i.useState)(!1),g=(0,i.useRef)();return(0,i.useEffect)(()=>{g.current=(0,s.oC)({getStoreItems:()=>c.getState(),onNodeMouseDown:e=>{eh({id:e,store:c,nodeRef:t})},onDragStart:()=>{f(!0)},onDragStop:()=>{f(!1)}})},[]),(0,i.useEffect)(()=>{var e,i;if(n)null===(e=g.current)||void 0===e||e.destroy();else if(t.current)return null===(i=g.current)||void 0===i||i.update({noDragClassName:o,handleSelector:r,domNode:t.current,isSelectable:a,nodeId:l,nodeClickDistance:d}),()=>{var e;null===(e=g.current)||void 0===e||e.destroy()}},[o,r,n,a,t,l]),u}let ew=e=>t=>t.selected&&(t.draggable||e&&void 0===t.draggable);function eb(){let e=v();return(0,i.useCallback)(t=>{let{nodeExtent:n,snapToGrid:o,snapGrid:r,nodesDraggable:l,onError:i,updateNodePositions:a,nodeLookup:d,nodeOrigin:c}=e.getState(),u=new Map,f=ew(l),g=o?r[0]:5,p=o?r[1]:5,m=t.direction.x*g*t.factor,v=t.direction.y*p*t.factor;for(let[,e]of d){if(!f(e))continue;let t={x:e.internals.positionAbsolute.x+m,y:e.internals.positionAbsolute.y+v};o&&(t=(0,s._2)(t,r));let{position:l,positionAbsolute:a}=(0,s.q7)({nodeId:e.id,nextPosition:t,nodeLookup:d,nodeExtent:n,nodeOrigin:c,onError:i});e.position=l,e.internals.positionAbsolute=a,u.set(e.id,e)}a(u)},[])}let eS=(0,i.createContext)(null),ex=eS.Provider;eS.Consumer;let eC=()=>(0,i.useContext)(eS),eE=e=>({connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName,rfId:e.rfId}),ek=(e,t,n)=>o=>{let{connectionClickStartHandle:r,connectionMode:l,connection:i}=o,{fromHandle:a,toHandle:d,isValid:c}=i,u=(null==d?void 0:d.nodeId)===e&&(null==d?void 0:d.id)===t&&(null==d?void 0:d.type)===n;return{connectingFrom:(null==a?void 0:a.nodeId)===e&&(null==a?void 0:a.id)===t&&(null==a?void 0:a.type)===n,connectingTo:u,clickConnecting:(null==r?void 0:r.nodeId)===e&&(null==r?void 0:r.id)===t&&(null==r?void 0:r.type)===n,isPossibleEndHandle:l===s.jD.Strict?(null==a?void 0:a.type)!==n:e!==(null==a?void 0:a.nodeId)||t!==(null==a?void 0:a.id),connectionInProcess:!!a,clickConnectionInProcess:!!r,valid:u&&c}},eN=(0,i.memo)(ee(function(e,t){let{type:n="source",position:o=s.Ly.Top,isValidConnection:r,isConnectable:i=!0,isConnectableStart:d=!0,isConnectableEnd:u=!0,id:f,onConnect:g,children:p,className:h,onMouseDown:y,onTouchStart:w,...b}=e,S=f||null,x="target"===n,C=v(),E=eC(),{connectOnClick:k,noPanClassName:N,rfId:M}=m(eE,c.X),{connectingFrom:P,connectingTo:j,clickConnecting:_,isPossibleEndHandle:R,connectionInProcess:I,clickConnectionInProcess:D,valid:A}=m(ek(E,S,n),c.X);if(!E){var O,z;null===(O=(z=C.getState()).onError)||void 0===O||O.call(z,"010",s.Qj.error010())}let L=e=>{let{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=C.getState(),r={...t,...e};if(o){let{edges:e,setEdges:t}=C.getState();t((0,s.Z_)(r,e))}null==n||n(r),null==g||g(r)},B=e=>{if(!E)return;let t=(0,s.N5)(e.nativeEvent);if(d&&(t&&0===e.button||!t)){let t=C.getState();s.Ql.onPointerDown(e.nativeEvent,{autoPanOnConnect:t.autoPanOnConnect,connectionMode:t.connectionMode,connectionRadius:t.connectionRadius,domNode:t.domNode,nodeLookup:t.nodeLookup,lib:t.lib,isTarget:x,handleId:S,nodeId:E,flowId:t.rfId,panBy:t.panBy,cancelConnection:t.cancelConnection,onConnectStart:t.onConnectStart,onConnectEnd:t.onConnectEnd,updateConnection:t.updateConnection,onConnect:L,isValidConnection:r||t.isValidConnection,getTransform:()=>C.getState().transform,getFromHandle:()=>C.getState().connection.fromHandle,autoPanSpeed:t.autoPanSpeed})}t?null==y||y(e):null==w||w(e)};return(0,l.jsx)("div",{"data-handleid":S,"data-nodeid":E,"data-handlepos":o,"data-id":"".concat(M,"-").concat(E,"-").concat(S,"-").concat(n),className:(0,a.Z)(["react-flow__handle","react-flow__handle-".concat(o),"nodrag",N,h,{source:!x,target:x,connectable:i,connectablestart:d,connectableend:u,clickconnecting:_,connectingfrom:P,connectingto:j,valid:A,connectionindicator:i&&(!I||R)&&(I||D?u:d)}]),onMouseDown:B,onTouchStart:B,onClick:k?e=>{let{onClickConnectStart:t,onClickConnectEnd:o,connectionClickStartHandle:l,connectionMode:i,isValidConnection:a,lib:c,rfId:u,nodeLookup:f,connection:g}=C.getState();if(!E||!l&&!d)return;if(!l){null==t||t(e.nativeEvent,{nodeId:E,handleId:S,handleType:n}),C.setState({connectionClickStartHandle:{nodeId:E,type:n,id:S}});return}let p=(0,s.S2)(e.target),m=r||a,{connection:v,isValid:h}=s.Ql.isValid(e.nativeEvent,{handle:{nodeId:E,id:S,type:n},connectionMode:i,fromNodeId:l.nodeId,fromHandleId:l.id||null,fromType:l.type,isValidConnection:m,flowId:u,doc:p,lib:c,nodeLookup:f});h&&v&&L(v);let y=structuredClone(g);delete y.inProgress,y.toPosition=y.toHandle?y.toHandle.position:null,null==o||o(e,y),C.setState({connectionClickStartHandle:null})}:void 0,ref:t,...b,children:p})})),eM={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}},eP={input:function(e){let{data:t,isConnectable:n,sourcePosition:o=s.Ly.Bottom}=e;return(0,l.jsxs)(l.Fragment,{children:[null==t?void 0:t.label,(0,l.jsx)(eN,{type:"source",position:o,isConnectable:n})]})},default:function(e){let{data:t,isConnectable:n,targetPosition:o=s.Ly.Top,sourcePosition:r=s.Ly.Bottom}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(eN,{type:"target",position:o,isConnectable:n}),null==t?void 0:t.label,(0,l.jsx)(eN,{type:"source",position:r,isConnectable:n})]})},output:function(e){let{data:t,isConnectable:n,targetPosition:o=s.Ly.Top}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(eN,{type:"target",position:o,isConnectable:n}),null==t?void 0:t.label]})},group:function(){return null}},ej=e=>{let{width:t,height:n,x:o,y:r}=(0,s.W0)(e.nodeLookup,{filter:e=>!!e.selected});return{width:(0,s.kE)(t)?t:null,height:(0,s.kE)(n)?n:null,userSelectionActive:e.userSelectionActive,transformString:"translate(".concat(e.transform[0],"px,").concat(e.transform[1],"px) scale(").concat(e.transform[2],") translate(").concat(o,"px,").concat(r,"px)")}};function e_(e){let{onSelectionContextMenu:t,noPanClassName:n,disableKeyboardA11y:o}=e,r=v(),{width:s,height:d,transformString:u,userSelectionActive:f}=m(ej,c.X),g=eb(),p=(0,i.useRef)(null);return((0,i.useEffect)(()=>{if(!o){var e;null===(e=p.current)||void 0===e||e.focus({preventScroll:!0})}},[o]),ey({nodeRef:p}),!f&&s&&d)?(0,l.jsx)("div",{className:(0,a.Z)(["react-flow__nodesselection","react-flow__container",n]),style:{transform:u},children:(0,l.jsx)("div",{ref:p,className:"react-flow__nodesselection-rect",onContextMenu:t?e=>{t(e,r.getState().nodes.filter(e=>e.selected))}:void 0,tabIndex:o?void 0:-1,onKeyDown:o?void 0:e=>{Object.prototype.hasOwnProperty.call(eM,e.key)&&(e.preventDefault(),g({direction:eM[e.key],factor:e.shiftKey?4:1}))},style:{width:s,height:d}})}):null}let eR="undefined"!=typeof window?window:void 0,eI=e=>({nodesSelectionActive:e.nodesSelectionActive,userSelectionActive:e.userSelectionActive});function eD(e){let{children:t,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:a,onPaneContextMenu:s,onPaneScroll:d,paneClickDistance:c,deleteKeyCode:u,selectionKeyCode:f,selectionOnDrag:g,selectionMode:p,onSelectionStart:h,onSelectionEnd:y,multiSelectionKeyCode:w,panActivationKeyCode:b,zoomActivationKeyCode:S,elementsSelectable:x,zoomOnScroll:C,zoomOnPinch:E,panOnScroll:k,panOnScrollSpeed:N,panOnScrollMode:M,zoomOnDoubleClick:P,panOnDrag:j,defaultViewport:_,translateExtent:R,minZoom:I,maxZoom:D,preventScrolling:A,onSelectionContextMenu:O,noWheelClassName:z,noPanClassName:L,disableKeyboardA11y:B,onViewportChange:V,isControlledViewport:Z}=e,{nodesSelectionActive:F,userSelectionActive:X}=m(eI),T=H(f,{target:eR}),W=H(b,{target:eR}),K=W||j,Q=W||k,Y=g&&!0!==K,U=T||X||Y;return!function(e){let{deleteKeyCode:t,multiSelectionKeyCode:n}=e,o=v(),{deleteElements:r}=ei(),l=H(t,{actInsideInputWithModifier:!1}),a=H(n,{target:es});(0,i.useEffect)(()=>{if(l){let{edges:e,nodes:t}=o.getState();r({nodes:t.filter(ea),edges:e.filter(ea)}),o.setState({nodesSelectionActive:!1})}},[l]),(0,i.useEffect)(()=>{o.setState({multiSelectionActive:a})},[a])}({deleteKeyCode:u,multiSelectionKeyCode:w}),(0,l.jsx)(eu,{onPaneContextMenu:s,elementsSelectable:x,zoomOnScroll:C,zoomOnPinch:E,panOnScroll:Q,panOnScrollSpeed:N,panOnScrollMode:M,zoomOnDoubleClick:P,panOnDrag:!T&&K,defaultViewport:_,translateExtent:R,minZoom:I,maxZoom:D,zoomActivationKeyCode:S,preventScrolling:A,noWheelClassName:z,noPanClassName:L,onViewportChange:V,isControlledViewport:Z,paneClickDistance:c,children:(0,l.jsxs)(ev,{onSelectionStart:h,onSelectionEnd:y,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:a,onPaneContextMenu:s,onPaneScroll:d,panOnDrag:K,isSelecting:!!U,selectionMode:p,selectionKeyPressed:T,selectionOnDrag:Y,children:[t,F&&(0,l.jsx)(e_,{onSelectionContextMenu:O,noPanClassName:L,disableKeyboardA11y:B})]})})}eD.displayName="FlowRenderer";let eA=(0,i.memo)(eD),eO=e=>t=>e?(0,s.f5)(t.nodeLookup,{x:0,y:0,width:t.width,height:t.height},t.transform,!0).map(e=>e.id):Array.from(t.nodeLookup.keys()),ez=e=>e.updateNodeInternals;function eL(e){var t,n,o,r,d,u,f,g,p,h,y,b;let{id:S,onClick:x,onMouseEnter:C,onMouseMove:E,onMouseLeave:k,onContextMenu:N,onDoubleClick:M,nodesDraggable:P,elementsSelectable:j,nodesConnectable:_,nodesFocusable:R,resizeObserver:I,noDragClassName:D,noPanClassName:A,disableKeyboardA11y:O,rfId:z,nodeTypes:L,nodeClickDistance:B,onError:V}=e,{node:Z,internals:H,isParent:F}=m(e=>{let t=e.nodeLookup.get(S),n=e.parentLookup.has(S);return{node:t,internals:t.internals,isParent:n}},c.X),X=Z.type||"default",T=(null==L?void 0:L[X])||eP[X];void 0===T&&(null==V||V("003",s.Qj.error003(X)),X="default",T=eP.default);let W=!!(Z.draggable||P&&void 0===Z.draggable),K=!!(Z.selectable||j&&void 0===Z.selectable),Q=!!(Z.connectable||_&&void 0===Z.connectable),Y=!!(Z.focusable||R&&void 0===Z.focusable),U=v(),q=(0,s.nb)(Z),G=function(e){let{node:t,nodeType:n,hasDimensions:o,resizeObserver:r}=e,l=v(),a=(0,i.useRef)(null),s=(0,i.useRef)(null),d=(0,i.useRef)(t.sourcePosition),c=(0,i.useRef)(t.targetPosition),u=(0,i.useRef)(n),f=o&&!!t.internals.handleBounds;return(0,i.useEffect)(()=>{!a.current||t.hidden||f&&s.current===a.current||(s.current&&(null==r||r.unobserve(s.current)),null==r||r.observe(a.current),s.current=a.current)},[f,t.hidden]),(0,i.useEffect)(()=>()=>{s.current&&(null==r||r.unobserve(s.current),s.current=null)},[]),(0,i.useEffect)(()=>{if(a.current){let e=u.current!==n,o=d.current!==t.sourcePosition,r=c.current!==t.targetPosition;(e||o||r)&&(u.current=n,d.current=t.sourcePosition,c.current=t.targetPosition,l.getState().updateNodeInternals(new Map([[t.id,{id:t.id,nodeElement:a.current,force:!0}]])))}},[t.id,n,t.sourcePosition,t.targetPosition]),a}({node:Z,nodeType:X,hasDimensions:q,resizeObserver:I}),$=ey({nodeRef:G,disabled:Z.hidden||!W,noDragClassName:D,handleSelector:Z.dragHandle,nodeId:S,isSelectable:K,nodeClickDistance:B}),J=eb();if(Z.hidden)return null;let ee=(0,s.Rf)(Z),et=void 0===Z.internals.handleBounds?{width:null!==(g=null!==(f=Z.width)&&void 0!==f?f:Z.initialWidth)&&void 0!==g?g:null===(d=Z.style)||void 0===d?void 0:d.width,height:null!==(h=null!==(p=Z.height)&&void 0!==p?p:Z.initialHeight)&&void 0!==h?h:null===(u=Z.style)||void 0===u?void 0:u.height}:{width:null!==(y=Z.width)&&void 0!==y?y:null===(o=Z.style)||void 0===o?void 0:o.width,height:null!==(b=Z.height)&&void 0!==b?b:null===(r=Z.style)||void 0===r?void 0:r.height},en=K||W||x||C||E||k;return(0,l.jsx)("div",{className:(0,a.Z)(["react-flow__node","react-flow__node-".concat(X),{[A]:W},Z.className,{selected:Z.selected,selectable:K,parent:F,draggable:W,dragging:$}]),ref:G,style:{zIndex:H.z,transform:"translate(".concat(H.positionAbsolute.x,"px,").concat(H.positionAbsolute.y,"px)"),pointerEvents:en?"all":"none",visibility:q?"visible":"hidden",...Z.style,...et},"data-id":S,"data-testid":"rf__node-".concat(S),onMouseEnter:C?e=>C(e,{...H.userNode}):void 0,onMouseMove:E?e=>E(e,{...H.userNode}):void 0,onMouseLeave:k?e=>k(e,{...H.userNode}):void 0,onContextMenu:N?e=>N(e,{...H.userNode}):void 0,onClick:e=>{let{selectNodesOnDrag:t,nodeDragThreshold:n}=U.getState();K&&(!t||!W||n>0)&&eh({id:S,store:U,nodeRef:G}),x&&x(e,{...H.userNode})},onDoubleClick:M?e=>M(e,{...H.userNode}):void 0,onKeyDown:Y?e=>{!(0,s.s$)(e.nativeEvent)&&!O&&(s.wQ.includes(e.key)&&K?eh({id:S,store:U,unselect:"Escape"===e.key,nodeRef:G}):W&&Z.selected&&Object.prototype.hasOwnProperty.call(eM,e.key)&&(e.preventDefault(),U.setState({ariaLiveMessage:"Moved selected node ".concat(e.key.replace("Arrow","").toLowerCase(),". New position, x: ").concat(~~H.positionAbsolute.x,", y: ").concat(~~H.positionAbsolute.y)}),J({direction:eM[e.key],factor:e.shiftKey?4:1})))}:void 0,tabIndex:Y?0:void 0,role:Y?"button":void 0,"aria-describedby":O?void 0:"".concat(w,"-").concat(z),"aria-label":Z.ariaLabel,children:(0,l.jsx)(ex,{value:S,children:(0,l.jsx)(T,{id:S,data:Z.data,type:X,positionAbsoluteX:H.positionAbsolute.x,positionAbsoluteY:H.positionAbsolute.y,selected:null!==(t=Z.selected)&&void 0!==t&&t,selectable:K,draggable:W,deletable:null===(n=Z.deletable)||void 0===n||n,isConnectable:Q,sourcePosition:Z.sourcePosition,targetPosition:Z.targetPosition,dragging:$,dragHandle:Z.dragHandle,zIndex:H.z,parentId:Z.parentId,...ee})})})}let eB=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,onError:e.onError});function eV(e){var t;let{nodesDraggable:n,nodesConnectable:o,nodesFocusable:r,elementsSelectable:a,onError:s}=m(eB,c.X),d=(t=e.onlyRenderVisibleElements,m((0,i.useCallback)(eO(t),[t]),c.X)),u=function(){let e=m(ez),[t]=(0,i.useState)(()=>"undefined"==typeof ResizeObserver?null:new ResizeObserver(t=>{let n=new Map;t.forEach(e=>{let t=e.target.getAttribute("data-id");n.set(t,{id:t,nodeElement:e.target,force:!0})}),e(n)}));return(0,i.useEffect)(()=>()=>{null==t||t.disconnect()},[t]),t}();return(0,l.jsx)("div",{className:"react-flow__nodes",style:ed,children:d.map(t=>(0,l.jsx)(eL,{id:t,nodeTypes:e.nodeTypes,nodeExtent:e.nodeExtent,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,resizeObserver:u,nodesDraggable:n,nodesConnectable:o,nodesFocusable:r,elementsSelectable:a,nodeClickDistance:e.nodeClickDistance,onError:s},t))})}eV.displayName="NodeRenderer";let eZ=(0,i.memo)(eV),eH={[s.QZ.Arrow]:e=>{let{color:t="none",strokeWidth:n=1}=e;return(0,l.jsx)("polyline",{style:{stroke:t,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"})},[s.QZ.ArrowClosed]:e=>{let{color:t="none",strokeWidth:n=1}=e;return(0,l.jsx)("polyline",{style:{stroke:t,fill:t,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})}},eF=e=>{let{id:t,type:n,color:o,width:r=12.5,height:a=12.5,markerUnits:d="strokeWidth",strokeWidth:c,orient:u="auto-start-reverse"}=e,f=function(e){let t=v();return(0,i.useMemo)(()=>{if(!Object.prototype.hasOwnProperty.call(eH,e)){var n,o;return null===(n=(o=t.getState()).onError)||void 0===n||n.call(o,"009",s.Qj.error009(e)),null}return eH[e]},[e])}(n);return f?(0,l.jsx)("marker",{className:"react-flow__arrowhead",id:t,markerWidth:"".concat(r),markerHeight:"".concat(a),viewBox:"-10 -10 20 20",markerUnits:d,orient:u,refX:"0",refY:"0",children:(0,l.jsx)(f,{color:o,strokeWidth:c})}):null},eX=e=>{let{defaultColor:t,rfId:n}=e,o=m(e=>e.edges),r=m(e=>e.defaultEdgeOptions),a=(0,i.useMemo)(()=>(0,s.n3)(o,{id:n,defaultColor:t,defaultMarkerStart:null==r?void 0:r.markerStart,defaultMarkerEnd:null==r?void 0:r.markerEnd}),[o,r,n,t]);return a.length?(0,l.jsx)("svg",{className:"react-flow__marker","aria-hidden":"true",children:(0,l.jsx)("defs",{children:a.map(e=>(0,l.jsx)(eF,{id:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient},e.id))})}):null};eX.displayName="MarkerDefinitions";var eT=(0,i.memo)(eX);function eW(e){let{x:t,y:n,label:o,labelStyle:r,labelShowBg:s=!0,labelBgStyle:d,labelBgPadding:c=[2,4],labelBgBorderRadius:u=2,children:f,className:g,...p}=e,[m,v]=(0,i.useState)({x:1,y:0,width:0,height:0}),h=(0,a.Z)(["react-flow__edge-textwrapper",g]),y=(0,i.useRef)(null);return((0,i.useEffect)(()=>{if(y.current){let e=y.current.getBBox();v({x:e.x,y:e.y,width:e.width,height:e.height})}},[o]),o)?(0,l.jsxs)("g",{transform:"translate(".concat(t-m.width/2," ").concat(n-m.height/2,")"),className:h,visibility:m.width?"visible":"hidden",...p,children:[s&&(0,l.jsx)("rect",{width:m.width+2*c[0],x:-c[0],y:-c[1],height:m.height+2*c[1],className:"react-flow__edge-textbg",style:d,rx:u,ry:u}),(0,l.jsx)("text",{className:"react-flow__edge-text",y:m.height/2,dy:"0.3em",ref:y,style:r,children:o}),f]}):null}eW.displayName="EdgeText";let eK=(0,i.memo)(eW);function eQ(e){let{path:t,labelX:n,labelY:o,label:r,labelStyle:i,labelShowBg:d,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:f,interactionWidth:g=20,...p}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("path",{...p,d:t,fill:"none",className:(0,a.Z)(["react-flow__edge-path",p.className])}),g&&(0,l.jsx)("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),r&&(0,s.kE)(n)&&(0,s.kE)(o)?(0,l.jsx)(eK,{x:n,y:o,label:r,labelStyle:i,labelShowBg:d,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:f}):null]})}function eY(e){let{pos:t,x1:n,y1:o,x2:r,y2:l}=e;return t===s.Ly.Left||t===s.Ly.Right?[.5*(n+r),o]:[n,.5*(o+l)]}function eU(e){let{sourceX:t,sourceY:n,sourcePosition:o=s.Ly.Bottom,targetX:r,targetY:l,targetPosition:i=s.Ly.Top}=e,[a,d]=eY({pos:o,x1:t,y1:n,x2:r,y2:l}),[c,u]=eY({pos:i,x1:r,y1:l,x2:t,y2:n}),[f,g,p,m]=(0,s.lM)({sourceX:t,sourceY:n,targetX:r,targetY:l,sourceControlX:a,sourceControlY:d,targetControlX:c,targetControlY:u});return["M".concat(t,",").concat(n," C").concat(a,",").concat(d," ").concat(c,",").concat(u," ").concat(r,",").concat(l),f,g,p,m]}function eq(e){return(0,i.memo)(t=>{let{id:n,sourceX:o,sourceY:r,targetX:i,targetY:a,sourcePosition:s,targetPosition:d,label:c,labelStyle:u,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,style:v,markerEnd:h,markerStart:y,interactionWidth:w}=t,[b,S,x]=eU({sourceX:o,sourceY:r,sourcePosition:s,targetX:i,targetY:a,targetPosition:d}),C=e.isInternal?void 0:n;return(0,l.jsx)(eQ,{id:C,path:b,labelX:S,labelY:x,label:c,labelStyle:u,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,style:v,markerEnd:h,markerStart:y,interactionWidth:w})})}let eG=eq({isInternal:!1}),e$=eq({isInternal:!0});function eJ(e){return(0,i.memo)(t=>{let{id:n,sourceX:o,sourceY:r,targetX:i,targetY:a,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:f,labelBgPadding:g,labelBgBorderRadius:p,style:m,sourcePosition:v=s.Ly.Bottom,targetPosition:h=s.Ly.Top,markerEnd:y,markerStart:w,pathOptions:b,interactionWidth:S}=t,[x,C,E]=(0,s.OW)({sourceX:o,sourceY:r,sourcePosition:v,targetX:i,targetY:a,targetPosition:h,borderRadius:null==b?void 0:b.borderRadius,offset:null==b?void 0:b.offset}),k=e.isInternal?void 0:n;return(0,l.jsx)(eQ,{id:k,path:x,labelX:C,labelY:E,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:f,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:y,markerStart:w,interactionWidth:S})})}eG.displayName="SimpleBezierEdge",e$.displayName="SimpleBezierEdgeInternal";let e0=eJ({isInternal:!1}),e1=eJ({isInternal:!0});function e2(e){return(0,i.memo)(t=>{var n;let{id:o,...r}=t,a=e.isInternal?void 0:o;return(0,l.jsx)(e0,{...r,id:a,pathOptions:(0,i.useMemo)(()=>{var e;return{borderRadius:0,offset:null===(e=r.pathOptions)||void 0===e?void 0:e.offset}},[null===(n=r.pathOptions)||void 0===n?void 0:n.offset])})})}e0.displayName="SmoothStepEdge",e1.displayName="SmoothStepEdgeInternal";let e3=e2({isInternal:!1}),e4=e2({isInternal:!0});function e5(e){return(0,i.memo)(t=>{let{id:n,sourceX:o,sourceY:r,targetX:i,targetY:a,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:f,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:v,markerStart:h,interactionWidth:y}=t,[w,b,S]=(0,s.Hm)({sourceX:o,sourceY:r,targetX:i,targetY:a}),x=e.isInternal?void 0:n;return(0,l.jsx)(eQ,{id:x,path:w,labelX:b,labelY:S,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:f,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:v,markerStart:h,interactionWidth:y})})}e3.displayName="StepEdge",e4.displayName="StepEdgeInternal";let e6=e5({isInternal:!1}),e8=e5({isInternal:!0});function e7(e){return(0,i.memo)(t=>{let{id:n,sourceX:o,sourceY:r,targetX:i,targetY:a,sourcePosition:d=s.Ly.Bottom,targetPosition:c=s.Ly.Top,label:u,labelStyle:f,labelShowBg:g,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:v,style:h,markerEnd:y,markerStart:w,pathOptions:b,interactionWidth:S}=t,[x,C,E]=(0,s.OQ)({sourceX:o,sourceY:r,sourcePosition:d,targetX:i,targetY:a,targetPosition:c,curvature:null==b?void 0:b.curvature}),k=e.isInternal?void 0:n;return(0,l.jsx)(eQ,{id:k,path:x,labelX:C,labelY:E,label:u,labelStyle:f,labelShowBg:g,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:v,style:h,markerEnd:y,markerStart:w,interactionWidth:S})})}e6.displayName="StraightEdge",e8.displayName="StraightEdgeInternal";let e9=e7({isInternal:!1}),te=e7({isInternal:!0});e9.displayName="BezierEdge",te.displayName="BezierEdgeInternal";let tt={default:te,straight:e8,step:e4,smoothstep:e1,simplebezier:e$},tn={sourceX:null,sourceY:null,targetX:null,targetY:null,sourcePosition:null,targetPosition:null},to=(e,t,n)=>n===s.Ly.Left?e-t:n===s.Ly.Right?e+t:e,tr=(e,t,n)=>n===s.Ly.Top?e-t:n===s.Ly.Bottom?e+t:e,tl="react-flow__edgeupdater";function ti(e){let{position:t,centerX:n,centerY:o,radius:r=10,onMouseDown:i,onMouseEnter:s,onMouseOut:d,type:c}=e;return(0,l.jsx)("circle",{onMouseDown:i,onMouseEnter:s,onMouseOut:d,className:(0,a.Z)([tl,"".concat(tl,"-").concat(c)]),cx:to(n,r,t),cy:tr(o,r,t),r:r,stroke:"transparent",fill:"transparent"})}function ta(e){let{isReconnectable:t,reconnectRadius:n,edge:o,sourceX:r,sourceY:i,targetX:a,targetY:d,sourcePosition:c,targetPosition:u,onReconnect:f,onReconnectStart:g,onReconnectEnd:p,setReconnecting:m,setUpdateHover:h}=e,y=v(),w=(e,t)=>{if(0!==e.button)return;let{autoPanOnConnect:n,domNode:r,isValidConnection:l,connectionMode:i,connectionRadius:a,lib:d,onConnectStart:c,onConnectEnd:u,cancelConnection:v,nodeLookup:h,rfId:w,panBy:b,updateConnection:S}=y.getState(),x="target"===t.type;m(!0),null==g||g(e,o,t.type),s.Ql.onPointerDown(e.nativeEvent,{autoPanOnConnect:n,connectionMode:i,connectionRadius:a,domNode:r,handleId:t.id,nodeId:t.nodeId,nodeLookup:h,isTarget:x,edgeUpdaterType:t.type,lib:d,flowId:w,cancelConnection:v,panBy:b,isValidConnection:l,onConnect:e=>null==f?void 0:f(o,e),onConnectStart:c,onConnectEnd:u,onReconnectEnd:(e,n)=>{m(!1),null==p||p(e,o,t.type,n)},updateConnection:S,getTransform:()=>y.getState().transform,getFromHandle:()=>y.getState().connection.fromHandle})},b=()=>h(!0),S=()=>h(!1);return(0,l.jsxs)(l.Fragment,{children:[(!0===t||"source"===t)&&(0,l.jsx)(ti,{position:c,centerX:r,centerY:i,radius:n,onMouseDown:e=>{var t;return w(e,{nodeId:o.target,id:null!==(t=o.targetHandle)&&void 0!==t?t:null,type:"target"})},onMouseEnter:b,onMouseOut:S,type:"source"}),(!0===t||"target"===t)&&(0,l.jsx)(ti,{position:u,centerX:a,centerY:d,radius:n,onMouseDown:e=>{var t;return w(e,{nodeId:o.source,id:null!==(t=o.sourceHandle)&&void 0!==t?t:null,type:"source"})},onMouseEnter:b,onMouseOut:S,type:"target"})]})}function ts(e){var t;let{id:n,edgesFocusable:o,edgesReconnectable:r,elementsSelectable:d,onClick:u,onDoubleClick:f,onContextMenu:g,onMouseEnter:p,onMouseMove:h,onMouseLeave:y,reconnectRadius:w,onReconnect:S,onReconnectStart:x,onReconnectEnd:C,rfId:E,edgeTypes:k,noPanClassName:N,onError:M,disableKeyboardA11y:P}=e,j=m(e=>e.edgeLookup.get(n)),_=m(e=>e.defaultEdgeOptions),R=(j=_?{..._,...j}:j).type||"default",I=(null==k?void 0:k[R])||tt[R];void 0===I&&(null==M||M("011",s.Qj.error011(R)),R="default",I=tt.default);let D=!!(j.focusable||o&&void 0===j.focusable),A=void 0!==S&&(j.reconnectable||r&&void 0===j.reconnectable),O=!!(j.selectable||d&&void 0===j.selectable),z=(0,i.useRef)(null),[L,B]=(0,i.useState)(!1),[V,Z]=(0,i.useState)(!1),H=v(),{zIndex:F,sourceX:X,sourceY:T,targetX:W,targetY:K,sourcePosition:Q,targetPosition:Y}=m((0,i.useCallback)(e=>{let t=e.nodeLookup.get(j.source),o=e.nodeLookup.get(j.target);if(!t||!o)return{zIndex:j.zIndex,...tn};let r=(0,s.JU)({id:n,sourceNode:t,targetNode:o,sourceHandle:j.sourceHandle||null,targetHandle:j.targetHandle||null,connectionMode:e.connectionMode,onError:M});return{zIndex:(0,s.xx)({selected:j.selected,zIndex:j.zIndex,sourceNode:t,targetNode:o,elevateOnSelect:e.elevateEdgesOnSelect}),...r||tn}},[j.source,j.target,j.sourceHandle,j.targetHandle,j.selected,j.zIndex]),c.X),U=(0,i.useMemo)(()=>j.markerStart?"url('#".concat((0,s.dW)(j.markerStart,E),"')"):void 0,[j.markerStart,E]),q=(0,i.useMemo)(()=>j.markerEnd?"url('#".concat((0,s.dW)(j.markerEnd,E),"')"):void 0,[j.markerEnd,E]);if(j.hidden||null===X||null===T||null===W||null===K)return null;let G=f?e=>{f(e,{...j})}:void 0,$=g?e=>{g(e,{...j})}:void 0,J=p?e=>{p(e,{...j})}:void 0,ee=h?e=>{h(e,{...j})}:void 0,et=y?e=>{y(e,{...j})}:void 0;return(0,l.jsx)("svg",{style:{zIndex:F},children:(0,l.jsxs)("g",{className:(0,a.Z)(["react-flow__edge","react-flow__edge-".concat(R),j.className,N,{selected:j.selected,animated:j.animated,inactive:!O&&!u,updating:L,selectable:O}]),onClick:e=>{let{addSelectedEdges:t,unselectNodesAndEdges:o,multiSelectionActive:r}=H.getState();if(O){if(H.setState({nodesSelectionActive:!1}),j.selected&&r){var l;o({nodes:[],edges:[j]}),null===(l=z.current)||void 0===l||l.blur()}else t([n])}u&&u(e,j)},onDoubleClick:G,onContextMenu:$,onMouseEnter:J,onMouseMove:ee,onMouseLeave:et,onKeyDown:D?e=>{if(!P&&s.wQ.includes(e.key)&&O){let{unselectNodesAndEdges:o,addSelectedEdges:r}=H.getState();if("Escape"===e.key){var t;null===(t=z.current)||void 0===t||t.blur(),o({edges:[j]})}else r([n])}}:void 0,tabIndex:D?0:void 0,role:D?"button":"img","data-id":n,"data-testid":"rf__edge-".concat(n),"aria-label":null===j.ariaLabel?void 0:j.ariaLabel||"Edge from ".concat(j.source," to ").concat(j.target),"aria-describedby":D?"".concat(b,"-").concat(E):void 0,ref:z,children:[!V&&(0,l.jsx)(I,{id:n,source:j.source,target:j.target,type:j.type,selected:j.selected,animated:j.animated,selectable:O,deletable:null===(t=j.deletable)||void 0===t||t,label:j.label,labelStyle:j.labelStyle,labelShowBg:j.labelShowBg,labelBgStyle:j.labelBgStyle,labelBgPadding:j.labelBgPadding,labelBgBorderRadius:j.labelBgBorderRadius,sourceX:X,sourceY:T,targetX:W,targetY:K,sourcePosition:Q,targetPosition:Y,data:j.data,style:j.style,sourceHandleId:j.sourceHandle,targetHandleId:j.targetHandle,markerStart:U,markerEnd:q,pathOptions:"pathOptions"in j?j.pathOptions:void 0,interactionWidth:j.interactionWidth}),A&&(0,l.jsx)(ta,{edge:j,isReconnectable:A,reconnectRadius:w,onReconnect:S,onReconnectStart:x,onReconnectEnd:C,sourceX:X,sourceY:T,targetX:W,targetY:K,sourcePosition:Q,targetPosition:Y,setUpdateHover:B,setReconnecting:Z})]})})}let td=e=>({edgesFocusable:e.edgesFocusable,edgesReconnectable:e.edgesReconnectable,elementsSelectable:e.elementsSelectable,connectionMode:e.connectionMode,onError:e.onError});function tc(e){let{defaultMarkerColor:t,onlyRenderVisibleElements:n,rfId:o,edgeTypes:r,noPanClassName:a,onReconnect:d,onEdgeContextMenu:u,onEdgeMouseEnter:f,onEdgeMouseMove:g,onEdgeMouseLeave:p,onEdgeClick:v,reconnectRadius:h,onEdgeDoubleClick:y,onReconnectStart:w,onReconnectEnd:b,disableKeyboardA11y:S}=e,{edgesFocusable:x,edgesReconnectable:C,elementsSelectable:E,onError:k}=m(td,c.X),N=m((0,i.useCallback)(e=>{if(!n)return e.edges.map(e=>e.id);let t=[];if(e.width&&e.height)for(let n of e.edges){let o=e.nodeLookup.get(n.source),r=e.nodeLookup.get(n.target);o&&r&&(0,s.RY)({sourceNode:o,targetNode:r,width:e.width,height:e.height,transform:e.transform})&&t.push(n.id)}return t},[n]),c.X);return(0,l.jsxs)("div",{className:"react-flow__edges",children:[(0,l.jsx)(eT,{defaultColor:t,rfId:o}),N.map(e=>(0,l.jsx)(ts,{id:e,edgesFocusable:x,edgesReconnectable:C,elementsSelectable:E,noPanClassName:a,onReconnect:d,onContextMenu:u,onMouseEnter:f,onMouseMove:g,onMouseLeave:p,onClick:v,reconnectRadius:h,onDoubleClick:y,onReconnectStart:w,onReconnectEnd:b,rfId:o,onError:k,edgeTypes:r,disableKeyboardA11y:S},e))]})}tc.displayName="EdgeRenderer";let tu=(0,i.memo)(tc),tf=e=>"translate(".concat(e.transform[0],"px,").concat(e.transform[1],"px) scale(").concat(e.transform[2],")");function tg(e){let{children:t}=e,n=m(tf);return(0,l.jsx)("div",{className:"react-flow__viewport xyflow__viewport react-flow__container",style:{transform:n},children:t})}let tp=e=>{var t;return null===(t=e.panZoom)||void 0===t?void 0:t.syncViewport};function tm(e){return e.connection.inProgress?{...e.connection,to:(0,s.m)(e.connection.to,e.transform)}:{...e.connection}}function tv(e){return m(e?t=>e(tm(t)):tm,c.X)}let th=e=>({nodesConnectable:e.nodesConnectable,isValid:e.connection.isValid,inProgress:e.connection.inProgress,width:e.width,height:e.height});function ty(e){let{containerStyle:t,style:n,type:o,component:r}=e,{nodesConnectable:i,width:d,height:u,isValid:f,inProgress:g}=m(th,c.X);return d&&i&&g?(0,l.jsx)("svg",{style:t,width:d,height:u,className:"react-flow__connectionline react-flow__container",children:(0,l.jsx)("g",{className:(0,a.Z)(["react-flow__connection",(0,s.Zp)(f)]),children:(0,l.jsx)(tw,{style:n,type:o,CustomComponent:r,isValid:f})})}):null}let tw=e=>{let{style:t,type:n=s.t8.Bezier,CustomComponent:o,isValid:r}=e,{inProgress:i,from:a,fromNode:d,fromHandle:c,fromPosition:u,to:f,toNode:g,toHandle:p,toPosition:m}=tv();if(!i)return;if(o)return(0,l.jsx)(o,{connectionLineType:n,connectionLineStyle:t,fromNode:d,fromHandle:c,fromX:a.x,fromY:a.y,toX:f.x,toY:f.y,fromPosition:u,toPosition:m,connectionStatus:(0,s.Zp)(r),toNode:g,toHandle:p});let v="",h={sourceX:a.x,sourceY:a.y,sourcePosition:u,targetX:f.x,targetY:f.y,targetPosition:m};switch(n){case s.t8.Bezier:[v]=(0,s.OQ)(h);break;case s.t8.SimpleBezier:[v]=eU(h);break;case s.t8.Step:[v]=(0,s.OW)({...h,borderRadius:0});break;case s.t8.SmoothStep:[v]=(0,s.OW)(h);break;default:[v]=(0,s.Hm)(h)}return(0,l.jsx)("path",{d:v,fill:"none",className:"react-flow__connection-path",style:t})};tw.displayName="ConnectionLine";let tb={};function tS(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tb;(0,i.useRef)(e),v(),(0,i.useEffect)(()=>{},[e])}function tx(e){let{nodeTypes:t,edgeTypes:n,onInit:o,onNodeClick:r,onEdgeClick:a,onNodeDoubleClick:s,onEdgeDoubleClick:d,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:f,onNodeContextMenu:g,onSelectionContextMenu:p,onSelectionStart:h,onSelectionEnd:y,connectionLineType:w,connectionLineStyle:b,connectionLineComponent:S,connectionLineContainerStyle:x,selectionKeyCode:C,selectionOnDrag:E,selectionMode:k,multiSelectionKeyCode:N,panActivationKeyCode:M,zoomActivationKeyCode:P,deleteKeyCode:j,onlyRenderVisibleElements:_,elementsSelectable:R,defaultViewport:I,translateExtent:D,minZoom:A,maxZoom:O,preventScrolling:z,defaultMarkerColor:L,zoomOnScroll:B,zoomOnPinch:V,panOnScroll:Z,panOnScrollSpeed:H,panOnScrollMode:F,zoomOnDoubleClick:X,panOnDrag:T,onPaneClick:W,onPaneMouseEnter:K,onPaneMouseMove:Q,onPaneMouseLeave:Y,onPaneScroll:U,onPaneContextMenu:q,paneClickDistance:G,nodeClickDistance:$,onEdgeContextMenu:J,onEdgeMouseEnter:ee,onEdgeMouseMove:et,onEdgeMouseLeave:en,reconnectRadius:eo,onReconnect:er,onReconnectStart:el,onReconnectEnd:ea,noDragClassName:es,noWheelClassName:ed,noPanClassName:ec,disableKeyboardA11y:eu,nodeExtent:ef,rfId:eg,viewport:ep,onViewportChange:em}=e;return tS(t),tS(n),v(),(0,i.useRef)(!1),(0,i.useEffect)(()=>{},[]),!function(e){let t=ei(),n=(0,i.useRef)(!1);(0,i.useEffect)(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}(o),!function(e){let t=m(tp),n=v();(0,i.useEffect)(()=>{e&&(null==t||t(e),n.setState({transform:[e.x,e.y,e.zoom]}))},[e,t])}(ep),(0,l.jsx)(eA,{onPaneClick:W,onPaneMouseEnter:K,onPaneMouseMove:Q,onPaneMouseLeave:Y,onPaneContextMenu:q,onPaneScroll:U,paneClickDistance:G,deleteKeyCode:j,selectionKeyCode:C,selectionOnDrag:E,selectionMode:k,onSelectionStart:h,onSelectionEnd:y,multiSelectionKeyCode:N,panActivationKeyCode:M,zoomActivationKeyCode:P,elementsSelectable:R,zoomOnScroll:B,zoomOnPinch:V,zoomOnDoubleClick:X,panOnScroll:Z,panOnScrollSpeed:H,panOnScrollMode:F,panOnDrag:T,defaultViewport:I,translateExtent:D,minZoom:A,maxZoom:O,onSelectionContextMenu:p,preventScrolling:z,noDragClassName:es,noWheelClassName:ed,noPanClassName:ec,disableKeyboardA11y:eu,onViewportChange:em,isControlledViewport:!!ep,children:(0,l.jsxs)(tg,{children:[(0,l.jsx)(tu,{edgeTypes:n,onEdgeClick:a,onEdgeDoubleClick:d,onReconnect:er,onReconnectStart:el,onReconnectEnd:ea,onlyRenderVisibleElements:_,onEdgeContextMenu:J,onEdgeMouseEnter:ee,onEdgeMouseMove:et,onEdgeMouseLeave:en,reconnectRadius:eo,defaultMarkerColor:L,noPanClassName:ec,disableKeyboardA11y:eu,rfId:eg}),(0,l.jsx)(ty,{style:b,type:w,component:S,containerStyle:x}),(0,l.jsx)("div",{className:"react-flow__edgelabel-renderer"}),(0,l.jsx)(eZ,{nodeTypes:t,onNodeClick:r,onNodeDoubleClick:s,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:f,onNodeContextMenu:g,nodeClickDistance:$,onlyRenderVisibleElements:_,noPanClassName:ec,noDragClassName:es,disableKeyboardA11y:eu,nodeExtent:ef,rfId:eg}),(0,l.jsx)("div",{className:"react-flow__viewport-portal"})]})})}tx.displayName="GraphView";let tC=(0,i.memo)(tx),tE=function(){var e,t,n;let{nodes:o,edges:r,defaultNodes:l,defaultEdges:i,width:a,height:d,fitView:c,fitViewOptions:u,minZoom:f=.5,maxZoom:g=2,nodeOrigin:p,nodeExtent:m}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},v=new Map,h=new Map,y=new Map,w=new Map,b=null!==(e=null!=i?i:r)&&void 0!==e?e:[],S=null!==(t=null!=l?l:o)&&void 0!==t?t:[],x=null!=p?p:[0,0],C=null!=m?m:s.k5;(0,s.be)(y,w,b);let E=(0,s.yF)(S,v,h,{nodeOrigin:x,nodeExtent:C,elevateNodesOnSelect:!1}),k=[0,0,1];if(c&&a&&d){let e=(0,s.W0)(v,{filter:e=>!!((e.width||e.initialWidth)&&(e.height||e.initialHeight))}),{x:t,y:o,zoom:r}=(0,s.$i)(e,a,d,f,g,null!==(n=null==u?void 0:u.padding)&&void 0!==n?n:.1);k=[t,o,r]}return{rfId:"1",width:0,height:0,transform:k,nodes:S,nodesInitialized:E,nodeLookup:v,parentLookup:h,edges:b,edgeLookup:w,connectionLookup:y,onNodesChange:null,onEdgesChange:null,hasDefaultNodes:void 0!==l,hasDefaultEdges:void 0!==i,panZoom:null,minZoom:f,maxZoom:g,translateExtent:s.k5,nodeExtent:C,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionMode:s.jD.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:x,nodeDragThreshold:1,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesReconnectable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,elevateEdgesOnSelect:!1,selectNodesOnDrag:!0,multiSelectionActive:!1,fitViewQueued:null!=c&&c,fitViewOptions:u,fitViewResolver:null,connection:{...s.Ky},connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,autoPanSpeed:15,connectionRadius:20,onError:s.Ki,isValidConnection:void 0,onSelectionChangeHandlers:[],lib:"react",debug:!1}},tk=e=>{let{nodes:t,edges:n,defaultNodes:o,defaultEdges:r,width:l,height:i,fitView:a,fitViewOptions:c,minZoom:u,maxZoom:f,nodeOrigin:g,nodeExtent:p}=e;return(0,d.F)((e,d)=>{async function m(){let{nodeLookup:t,panZoom:n,fitViewOptions:o,fitViewResolver:r,width:l,height:i,minZoom:a,maxZoom:c}=d();n&&(await (0,s.Eg)({nodes:t,width:l,height:i,panZoom:n,minZoom:a,maxZoom:c},o),null==r||r.resolve(!0),e({fitViewResolver:null}))}return{...tE({nodes:t,edges:n,width:l,height:i,fitView:a,fitViewOptions:c,minZoom:u,maxZoom:f,nodeOrigin:g,nodeExtent:p,defaultNodes:o,defaultEdges:r}),setNodes:t=>{let{nodeLookup:n,parentLookup:o,nodeOrigin:r,elevateNodesOnSelect:l,fitViewQueued:i}=d(),a=(0,s.yF)(t,n,o,{nodeOrigin:r,nodeExtent:p,elevateNodesOnSelect:l,checkEquality:!0});i&&a?(m(),e({nodes:t,nodesInitialized:a,fitViewQueued:!1,fitViewOptions:void 0})):e({nodes:t,nodesInitialized:a})},setEdges:t=>{let{connectionLookup:n,edgeLookup:o}=d();(0,s.be)(n,o,t),e({edges:t})},setDefaultNodesAndEdges:(t,n)=>{if(t){let{setNodes:n}=d();n(t),e({hasDefaultNodes:!0})}if(n){let{setEdges:t}=d();t(n),e({hasDefaultEdges:!0})}},updateNodeInternals:t=>{let{triggerNodeChanges:n,nodeLookup:o,parentLookup:r,domNode:l,nodeOrigin:i,nodeExtent:a,debug:c,fitViewQueued:u}=d(),{changes:f,updatedInternals:g}=(0,s.B1)(t,o,r,l,i,a);g&&((0,s.VV)(o,r,{nodeOrigin:i,nodeExtent:a}),u?(m(),e({fitViewQueued:!1,fitViewOptions:void 0})):e({}),(null==f?void 0:f.length)>0&&(c&&console.log("React Flow: trigger node changes",f),null==n||n(f)))},updateNodePositions:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=[],o=[],{nodeLookup:r,triggerNodeChanges:l}=d();for(let[l,s]of e){let e=r.get(l),d=!!((null==e?void 0:e.expandParent)&&(null==e?void 0:e.parentId)&&(null==s?void 0:s.position)),c={id:l,type:"position",position:d?{x:Math.max(0,s.position.x),y:Math.max(0,s.position.y)}:s.position,dragging:t};if(d&&e.parentId){var i,a;n.push({id:l,parentId:e.parentId,rect:{...s.internals.positionAbsolute,width:null!==(i=s.measured.width)&&void 0!==i?i:0,height:null!==(a=s.measured.height)&&void 0!==a?a:0}})}o.push(c)}if(n.length>0){let{parentLookup:e,nodeOrigin:t}=d(),l=(0,s.so)(n,r,e,t);o.push(...l)}l(o)},triggerNodeChanges:e=>{let{onNodesChange:t,setNodes:n,nodes:o,hasDefaultNodes:r,debug:l}=d();(null==e?void 0:e.length)&&(r&&n(W(e,o)),l&&console.log("React Flow: trigger node changes",e),null==t||t(e))},triggerEdgeChanges:e=>{let{onEdgesChange:t,setEdges:n,edges:o,hasDefaultEdges:r,debug:l}=d();(null==e?void 0:e.length)&&(r&&n(W(e,o)),l&&console.log("React Flow: trigger edge changes",e),null==t||t(e))},addSelectedNodes:e=>{let{multiSelectionActive:t,edgeLookup:n,nodeLookup:o,triggerNodeChanges:r,triggerEdgeChanges:l}=d();if(t){r(e.map(e=>Y(e,!0)));return}r(U(o,new Set([...e]),!0)),l(U(n))},addSelectedEdges:e=>{let{multiSelectionActive:t,edgeLookup:n,nodeLookup:o,triggerNodeChanges:r,triggerEdgeChanges:l}=d();if(t){l(e.map(e=>Y(e,!0)));return}l(U(n,new Set([...e]))),r(U(o,new Set,!0))},unselectNodesAndEdges:function(){let{nodes:e,edges:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{edges:n,nodes:o,nodeLookup:r,triggerNodeChanges:l,triggerEdgeChanges:i}=d(),a=(e||o).map(e=>{let t=r.get(e.id);return t&&(t.selected=!1),Y(e.id,!1)}),s=(t||n).map(e=>Y(e.id,!1));l(a),i(s)},setMinZoom:t=>{let{panZoom:n,maxZoom:o}=d();null==n||n.setScaleExtent([t,o]),e({minZoom:t})},setMaxZoom:t=>{let{panZoom:n,minZoom:o}=d();null==n||n.setScaleExtent([o,t]),e({maxZoom:t})},setTranslateExtent:t=>{var n;null===(n=d().panZoom)||void 0===n||n.setTranslateExtent(t),e({translateExtent:t})},setPaneClickDistance:e=>{var t;null===(t=d().panZoom)||void 0===t||t.setClickDistance(e)},resetSelectedElements:()=>{let{edges:e,nodes:t,triggerNodeChanges:n,triggerEdgeChanges:o,elementsSelectable:r}=d();if(!r)return;let l=t.reduce((e,t)=>t.selected?[...e,Y(t.id,!1)]:e,[]),i=e.reduce((e,t)=>t.selected?[...e,Y(t.id,!1)]:e,[]);n(l),o(i)},setNodeExtent:t=>{let{nodes:n,nodeLookup:o,parentLookup:r,nodeOrigin:l,elevateNodesOnSelect:i,nodeExtent:a}=d();(t[0][0]!==a[0][0]||t[0][1]!==a[0][1]||t[1][0]!==a[1][0]||t[1][1]!==a[1][1])&&((0,s.yF)(n,o,r,{nodeOrigin:l,nodeExtent:t,elevateNodesOnSelect:i,checkEquality:!1}),e({nodeExtent:t}))},panBy:e=>{let{transform:t,width:n,height:o,panZoom:r,translateExtent:l}=d();return(0,s.hO)({delta:e,panZoom:r,transform:t,translateExtent:l,width:n,height:o})},cancelConnection:()=>{e({connection:{...s.Ky}})},updateConnection:t=>{e({connection:t})},reset:()=>e({...tE()})}},Object.is)};function tN(e){let{initialNodes:t,initialEdges:n,defaultNodes:o,defaultEdges:r,initialWidth:a,initialHeight:s,initialMinZoom:d,initialMaxZoom:c,initialFitViewOptions:u,fitView:f,nodeOrigin:p,nodeExtent:m,children:v}=e,[h]=(0,i.useState)(()=>tk({nodes:t,edges:n,defaultNodes:o,defaultEdges:r,width:a,height:s,fitView:f,minZoom:d,maxZoom:c,fitViewOptions:u,nodeOrigin:p,nodeExtent:m}));return(0,l.jsx)(g,{value:h,children:(0,l.jsx)(er,{children:v})})}function tM(e){let{children:t,nodes:n,edges:o,defaultNodes:r,defaultEdges:a,width:s,height:d,fitView:c,fitViewOptions:u,minZoom:g,maxZoom:p,nodeOrigin:m,nodeExtent:v}=e;return(0,i.useContext)(f)?(0,l.jsx)(l.Fragment,{children:t}):(0,l.jsx)(tN,{initialNodes:n,initialEdges:o,defaultNodes:r,defaultEdges:a,initialWidth:s,initialHeight:d,fitView:c,initialFitViewOptions:u,initialMinZoom:g,initialMaxZoom:p,nodeOrigin:m,nodeExtent:v,children:t})}let tP={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0};var tj=ee(function(e,t){let{nodes:n,edges:o,defaultNodes:r,defaultEdges:d,className:c,nodeTypes:u,edgeTypes:f,onNodeClick:g,onEdgeClick:p,onInit:m,onMove:v,onMoveStart:h,onMoveEnd:y,onConnect:w,onConnectStart:b,onConnectEnd:S,onClickConnectStart:x,onClickConnectEnd:E,onNodeMouseEnter:k,onNodeMouseMove:M,onNodeMouseLeave:P,onNodeContextMenu:j,onNodeDoubleClick:_,onNodeDragStart:R,onNodeDrag:O,onNodeDragStop:z,onNodesDelete:L,onEdgesDelete:Z,onDelete:H,onSelectionChange:F,onSelectionDragStart:X,onSelectionDrag:T,onSelectionDragStop:W,onSelectionContextMenu:K,onSelectionStart:Q,onSelectionEnd:Y,onBeforeDelete:U,connectionMode:q,connectionLineType:G=s.t8.Bezier,connectionLineStyle:$,connectionLineComponent:J,connectionLineContainerStyle:ee,deleteKeyCode:et="Backspace",selectionKeyCode:en="Shift",selectionOnDrag:eo=!1,selectionMode:er=s.oW.Full,panActivationKeyCode:el="Space",multiSelectionKeyCode:ei=(0,s.Q5)()?"Meta":"Control",zoomActivationKeyCode:ea=(0,s.Q5)()?"Meta":"Control",snapToGrid:es,snapGrid:ed,onlyRenderVisibleElements:ec=!1,selectNodesOnDrag:eu,nodesDraggable:ef,nodesConnectable:eg,nodesFocusable:ep,nodeOrigin:em=D,edgesFocusable:ev,edgesReconnectable:eh,elementsSelectable:ey=!0,defaultViewport:ew=A,minZoom:eb=.5,maxZoom:eS=2,translateExtent:ex=s.k5,preventScrolling:eC=!0,nodeExtent:eE,defaultMarkerColor:ek="#b1b1b7",zoomOnScroll:eN=!0,zoomOnPinch:eM=!0,panOnScroll:eP=!1,panOnScrollSpeed:ej=.5,panOnScrollMode:e_=s.IY.Free,zoomOnDoubleClick:eR=!0,panOnDrag:eI=!0,onPaneClick:eD,onPaneMouseEnter:eA,onPaneMouseMove:eO,onPaneMouseLeave:ez,onPaneScroll:eL,onPaneContextMenu:eB,paneClickDistance:eV=0,nodeClickDistance:eZ=0,children:eH,onReconnect:eF,onReconnectStart:eX,onReconnectEnd:eT,onEdgeContextMenu:eW,onEdgeDoubleClick:eK,onEdgeMouseEnter:eQ,onEdgeMouseMove:eY,onEdgeMouseLeave:eU,reconnectRadius:eq=10,onNodesChange:eG,onEdgesChange:e$,noDragClassName:eJ="nodrag",noWheelClassName:e0="nowheel",noPanClassName:e1="nopan",fitView:e2,fitViewOptions:e3,connectOnClick:e4,attributionPosition:e5,proOptions:e6,defaultEdgeOptions:e8,elevateNodesOnSelect:e7,elevateEdgesOnSelect:e9,disableKeyboardA11y:te=!1,autoPanOnConnect:tt,autoPanOnNodeDrag:tn,autoPanSpeed:to,connectionRadius:tr,isValidConnection:tl,onError:ti,style:ta,id:ts,nodeDragThreshold:td,viewport:tc,onViewportChange:tu,width:tf,height:tg,colorMode:tp="light",debug:tm,onScroll:tv,...th}=e,ty=ts||"1",tw=function(e){var t;let[n,o]=(0,i.useState)("system"===e?null:e);return(0,i.useEffect)(()=>{if("system"!==e){o(e);return}let t=V(),n=()=>o((null==t?void 0:t.matches)?"dark":"light");return n(),null==t||t.addEventListener("change",n),()=>{null==t||t.removeEventListener("change",n)}},[e]),null!==n?n:(null===(t=V())||void 0===t?void 0:t.matches)?"dark":"light"}(tp),tb=(0,i.useCallback)(e=>{e.currentTarget.scrollTo({top:0,left:0,behavior:"instant"}),null==tv||tv(e)},[tv]);return(0,l.jsx)("div",{"data-testid":"rf__wrapper",...th,onScroll:tb,style:{...ta,...tP},ref:t,className:(0,a.Z)(["react-flow",c,tw]),id:ts,children:(0,l.jsxs)(tM,{nodes:n,edges:o,width:tf,height:tg,fitView:e2,fitViewOptions:e3,minZoom:eb,maxZoom:eS,nodeOrigin:em,nodeExtent:eE,children:[(0,l.jsx)(tC,{onInit:m,onNodeClick:g,onEdgeClick:p,onNodeMouseEnter:k,onNodeMouseMove:M,onNodeMouseLeave:P,onNodeContextMenu:j,onNodeDoubleClick:_,nodeTypes:u,edgeTypes:f,connectionLineType:G,connectionLineStyle:$,connectionLineComponent:J,connectionLineContainerStyle:ee,selectionKeyCode:en,selectionOnDrag:eo,selectionMode:er,deleteKeyCode:et,multiSelectionKeyCode:ei,panActivationKeyCode:el,zoomActivationKeyCode:ea,onlyRenderVisibleElements:ec,defaultViewport:ew,translateExtent:ex,minZoom:eb,maxZoom:eS,preventScrolling:eC,zoomOnScroll:eN,zoomOnPinch:eM,zoomOnDoubleClick:eR,panOnScroll:eP,panOnScrollSpeed:ej,panOnScrollMode:e_,panOnDrag:eI,onPaneClick:eD,onPaneMouseEnter:eA,onPaneMouseMove:eO,onPaneMouseLeave:ez,onPaneScroll:eL,onPaneContextMenu:eB,paneClickDistance:eV,nodeClickDistance:eZ,onSelectionContextMenu:K,onSelectionStart:Q,onSelectionEnd:Y,onReconnect:eF,onReconnectStart:eX,onReconnectEnd:eT,onEdgeContextMenu:eW,onEdgeDoubleClick:eK,onEdgeMouseEnter:eQ,onEdgeMouseMove:eY,onEdgeMouseLeave:eU,reconnectRadius:eq,defaultMarkerColor:ek,noDragClassName:eJ,noWheelClassName:e0,noPanClassName:e1,rfId:ty,disableKeyboardA11y:te,nodeExtent:eE,viewport:tc,onViewportChange:tu}),(0,l.jsx)(B,{nodes:n,edges:o,defaultNodes:r,defaultEdges:d,onConnect:w,onConnectStart:b,onConnectEnd:S,onClickConnectStart:x,onClickConnectEnd:E,nodesDraggable:ef,nodesConnectable:eg,nodesFocusable:ep,edgesFocusable:ev,edgesReconnectable:eh,elementsSelectable:ey,elevateNodesOnSelect:e7,elevateEdgesOnSelect:e9,minZoom:eb,maxZoom:eS,nodeExtent:eE,onNodesChange:eG,onEdgesChange:e$,snapToGrid:es,snapGrid:ed,connectionMode:q,translateExtent:ex,connectOnClick:e4,defaultEdgeOptions:e8,fitView:e2,fitViewOptions:e3,onNodesDelete:L,onEdgesDelete:Z,onDelete:H,onNodeDragStart:R,onNodeDrag:O,onNodeDragStop:z,onSelectionDrag:T,onSelectionDragStart:X,onSelectionDragStop:W,onMove:v,onMoveStart:h,onMoveEnd:y,noPanClassName:e1,nodeOrigin:em,rfId:ty,autoPanOnConnect:tt,autoPanOnNodeDrag:tn,autoPanSpeed:to,onError:ti,connectionRadius:tr,isValidConnection:tl,selectNodesOnDrag:eu,nodeDragThreshold:td,onBeforeDelete:U,paneClickDistance:eV,debug:tm}),(0,l.jsx)(I,{onSelectionChange:F}),eH,(0,l.jsx)(N,{proOptions:e6,position:e5}),(0,l.jsx)(C,{rfId:ty,disableKeyboardA11y:te})]})})});let t_=e=>{var t;return null===(t=e.domNode)||void 0===t?void 0:t.querySelector(".react-flow__edgelabel-renderer")};function tR(e){let{children:t}=e,n=m(t_);return n?(0,u.createPortal)(t,n):null}let tI=e=>{var t;return null===(t=e.domNode)||void 0===t?void 0:t.querySelector(".react-flow__viewport-portal")};function tD(e){let{children:t}=e,n=m(tI);return n?(0,u.createPortal)(t,n):null}function tA(){let e=v();return(0,i.useCallback)(t=>{let{domNode:n,updateNodeInternals:o}=e.getState(),r=Array.isArray(t)?t:[t],l=new Map;r.forEach(e=>{let t=null==n?void 0:n.querySelector('.react-flow__node[data-id="'.concat(e,'"]'));t&&l.set(e,{id:e,nodeElement:t,force:!0})}),requestAnimationFrame(()=>o(l,{triggerFitView:!1}))},[])}let tO=e=>e.nodes;function tz(){return m(tO,c.X)}let tL=e=>e.edges;function tB(){return m(tL,c.X)}let tV=e=>({x:e.transform[0],y:e.transform[1],zoom:e.transform[2]});function tZ(){return m(tV,c.X)}function tH(e){let[t,n]=(0,i.useState)(e),o=(0,i.useCallback)(e=>n(t=>W(e,t)),[]);return[t,n,o]}function tF(e){let[t,n]=(0,i.useState)(e),o=(0,i.useCallback)(e=>n(t=>W(e,t)),[]);return[t,n,o]}function tX(e){let{onStart:t,onChange:n,onEnd:o}=e,r=v();(0,i.useEffect)(()=>{r.setState({onViewportChangeStart:t})},[t]),(0,i.useEffect)(()=>{r.setState({onViewportChange:n})},[n]),(0,i.useEffect)(()=>{r.setState({onViewportChangeEnd:o})},[o])}function tT(e){let{onChange:t}=e,n=v();(0,i.useEffect)(()=>{let e=[...n.getState().onSelectionChangeHandlers,t];return n.setState({onSelectionChangeHandlers:e}),()=>{let e=n.getState().onSelectionChangeHandlers.filter(e=>e!==t);n.setState({onSelectionChangeHandlers:e})}},[t])}let tW=e=>t=>{if(!e.includeHiddenNodes)return t.nodesInitialized;if(0===t.nodeLookup.size)return!1;for(let[,{internals:e}]of t.nodeLookup)if(void 0===e.handleBounds||!(0,s.nb)(e.userNode))return!1;return!0};function tK(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{includeHiddenNodes:!1};return m(tW(e))}function tQ(e){let{type:t,id:n,nodeId:o,onConnect:r,onDisconnect:l}=e;console.warn("[DEPRECATED] `useHandleConnections` is deprecated. Instead use `useNodeConnections` https://reactflow.dev/api-reference/hooks/useNodeConnections");let a=eC(),d=null!=o?o:a,c=(0,i.useRef)(null),u=m(e=>e.connectionLookup.get("".concat(d,"-").concat(t).concat(n?"-".concat(n):"")),s.tj);return(0,i.useEffect)(()=>{if(c.current&&c.current!==u){let e=null!=u?u:new Map;(0,s.WI)(c.current,e,l),(0,s.WI)(e,c.current,r)}c.current=null!=u?u:new Map},[u,r,l]),(0,i.useMemo)(()=>{var e;return Array.from(null!==(e=null==u?void 0:u.values())&&void 0!==e?e:[])},[u])}let tY=s.Qj.error014();function tU(){let{id:e,handleType:t,handleId:n,onConnect:o,onDisconnect:r}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},l=eC(),a=null!=e?e:l;if(!a)throw Error(tY);let d=(0,i.useRef)(null),c=m(e=>e.connectionLookup.get("".concat(a).concat(t?n?"-".concat(t,"-").concat(n):"-".concat(t):"")),s.tj);return(0,i.useEffect)(()=>{if(d.current&&d.current!==c){let e=null!=c?c:new Map;(0,s.WI)(d.current,e,r),(0,s.WI)(e,d.current,o)}d.current=null!=c?c:new Map},[c,o,r]),(0,i.useMemo)(()=>{var e;return Array.from(null!==(e=null==c?void 0:c.values())&&void 0!==e?e:[])},[c])}function tq(e){return m((0,i.useCallback)(t=>{var n;let o=[],r=Array.isArray(e);for(let n of r?e:[e]){let e=t.nodeLookup.get(n);e&&o.push({id:e.id,type:e.type,data:e.data})}return r?o:null!==(n=o[0])&&void 0!==n?n:null},[e]),s.BT)}function tG(e){return m((0,i.useCallback)(t=>t.nodeLookup.get(e),[e]),c.X)}function t$(e){let{dimensions:t,lineWidth:n,variant:o,className:r}=e;return(0,l.jsx)("path",{strokeWidth:n,d:"M".concat(t[0]/2," 0 V").concat(t[1]," M0 ").concat(t[1]/2," H").concat(t[0]),className:(0,a.Z)(["react-flow__background-pattern",o,r])})}function tJ(e){let{radius:t,className:n}=e;return(0,l.jsx)("circle",{cx:t,cy:t,r:t,className:(0,a.Z)(["react-flow__background-pattern","dots",n])})}(o=r||(r={})).Lines="lines",o.Dots="dots",o.Cross="cross";let t0={[r.Dots]:1,[r.Lines]:1,[r.Cross]:6},t1=e=>({transform:e.transform,patternId:"pattern-".concat(e.rfId)});function t2(e){let{id:t,variant:n=r.Dots,gap:o=20,size:s,lineWidth:d=1,offset:u=0,color:f,bgColor:g,style:p,className:v,patternClassName:h}=e,y=(0,i.useRef)(null),{transform:w,patternId:b}=m(t1,c.X),S=s||t0[n],x=n===r.Dots,C=n===r.Cross,E=Array.isArray(o)?o:[o,o],k=[E[0]*w[2]||1,E[1]*w[2]||1],N=S*w[2],M=Array.isArray(u)?u:[u,u],P=C?[N,N]:k,j=[M[0]*w[2]||1+P[0]/2,M[1]*w[2]||1+P[1]/2],_="".concat(b).concat(t||"");return(0,l.jsxs)("svg",{className:(0,a.Z)(["react-flow__background",v]),style:{...p,...ed,"--xy-background-color-props":g,"--xy-background-pattern-color-props":f},ref:y,"data-testid":"rf__background",children:[(0,l.jsx)("pattern",{id:_,x:w[0]%k[0],y:w[1]%k[1],width:k[0],height:k[1],patternUnits:"userSpaceOnUse",patternTransform:"translate(-".concat(j[0],",-").concat(j[1],")"),children:x?(0,l.jsx)(tJ,{radius:N/2,className:h}):(0,l.jsx)(t$,{dimensions:P,lineWidth:d,variant:n,className:h})}),(0,l.jsx)("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:"url(#".concat(_,")")})]})}t2.displayName="Background";let t3=(0,i.memo)(t2);function t4(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",children:(0,l.jsx)("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"})})}function t5(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5",children:(0,l.jsx)("path",{d:"M0 0h32v4.2H0z"})})}function t6(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30",children:(0,l.jsx)("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"})})}function t8(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,l.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"})})}function t7(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,l.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"})})}function t9(e){let{children:t,className:n,...o}=e;return(0,l.jsx)("button",{type:"button",className:(0,a.Z)(["react-flow__controls-button",n]),...o,children:t})}let ne=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom});function nt(e){let{style:t,showZoom:n=!0,showFitView:o=!0,showInteractive:r=!0,fitViewOptions:i,onZoomIn:s,onZoomOut:d,onFitView:u,onInteractiveChange:f,className:g,children:p,position:h="bottom-left",orientation:y="vertical","aria-label":w="React Flow controls"}=e,b=v(),{isInteractive:S,minZoomReached:x,maxZoomReached:C}=m(ne,c.X),{zoomIn:E,zoomOut:N,fitView:M}=ei();return(0,l.jsxs)(k,{className:(0,a.Z)(["react-flow__controls","horizontal"===y?"horizontal":"vertical",g]),position:h,style:t,"data-testid":"rf__controls","aria-label":w,children:[n&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t9,{onClick:()=>{E(),null==s||s()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:C,children:(0,l.jsx)(t4,{})}),(0,l.jsx)(t9,{onClick:()=>{N(),null==d||d()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:x,children:(0,l.jsx)(t5,{})})]}),o&&(0,l.jsx)(t9,{className:"react-flow__controls-fitview",onClick:()=>{M(i),null==u||u()},title:"fit view","aria-label":"fit view",children:(0,l.jsx)(t6,{})}),r&&(0,l.jsx)(t9,{className:"react-flow__controls-interactive",onClick:()=>{b.setState({nodesDraggable:!S,nodesConnectable:!S,elementsSelectable:!S}),null==f||f(!S)},title:"toggle interactivity","aria-label":"toggle interactivity",children:S?(0,l.jsx)(t7,{}):(0,l.jsx)(t8,{})}),p]})}nt.displayName="Controls";let nn=(0,i.memo)(nt),no=(0,i.memo)(function(e){let{id:t,x:n,y:o,width:r,height:i,style:s,color:d,strokeColor:c,strokeWidth:u,className:f,borderRadius:g,shapeRendering:p,selected:m,onClick:v}=e,{background:h,backgroundColor:y}=s||{};return(0,l.jsx)("rect",{className:(0,a.Z)(["react-flow__minimap-node",{selected:m},f]),x:n,y:o,rx:g,ry:g,width:r,height:i,style:{fill:d||h||y,stroke:c,strokeWidth:u},shapeRendering:p,onClick:v?e=>v(e,t):void 0})}),nr=e=>e.nodes.map(e=>e.id),nl=e=>e instanceof Function?e:()=>e,ni=(0,i.memo)(function(e){let{id:t,nodeColorFunc:n,nodeStrokeColorFunc:o,nodeClassNameFunc:r,nodeBorderRadius:i,nodeStrokeWidth:a,shapeRendering:d,NodeComponent:u,onClick:f}=e,{node:g,x:p,y:v,width:h,height:y}=m(e=>{let{internals:n}=e.nodeLookup.get(t),o=n.userNode,{x:r,y:l}=n.positionAbsolute,{width:i,height:a}=(0,s.Rf)(o);return{node:o,x:r,y:l,width:i,height:a}},c.X);return g&&!g.hidden&&(0,s.nb)(g)?(0,l.jsx)(u,{x:p,y:v,width:h,height:y,style:g.style,selected:!!g.selected,className:r(g),color:n(g),borderRadius:i,strokeColor:o(g),strokeWidth:a,shapeRendering:d,onClick:f,id:g.id}):null});var na=(0,i.memo)(function(e){let{nodeStrokeColor:t,nodeColor:n,nodeClassName:o="",nodeBorderRadius:r=5,nodeStrokeWidth:i,nodeComponent:a=no,onClick:s}=e,d=m(nr,c.X),u=nl(n),f=nl(t),g=nl(o),p="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return(0,l.jsx)(l.Fragment,{children:d.map(e=>(0,l.jsx)(ni,{id:e,nodeColorFunc:u,nodeStrokeColorFunc:f,nodeClassNameFunc:g,nodeBorderRadius:r,nodeStrokeWidth:i,NodeComponent:a,onClick:s,shapeRendering:p},e))})});let ns=e=>!e.hidden,nd=e=>{let t={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:t,boundingRect:e.nodeLookup.size>0?(0,s.oI)((0,s.W0)(e.nodeLookup,{filter:ns}),t):t,rfId:e.rfId,panZoom:e.panZoom,translateExtent:e.translateExtent,flowWidth:e.width,flowHeight:e.height}};function nc(e){var t,n;let{style:o,className:r,nodeStrokeColor:d,nodeColor:u,nodeClassName:f="",nodeBorderRadius:g=5,nodeStrokeWidth:p,nodeComponent:h,bgColor:y,maskColor:w,maskStrokeColor:b,maskStrokeWidth:S,position:x="bottom-right",onClick:C,onNodeClick:E,pannable:N=!1,zoomable:M=!1,ariaLabel:P="React Flow mini map",inversePan:j,zoomStep:_=10,offsetScale:R=5}=e,I=v(),D=(0,i.useRef)(null),{boundingRect:A,viewBB:O,rfId:z,panZoom:L,translateExtent:B,flowWidth:V,flowHeight:Z}=m(nd,c.X),H=null!==(t=null==o?void 0:o.width)&&void 0!==t?t:200,F=null!==(n=null==o?void 0:o.height)&&void 0!==n?n:150,X=Math.max(A.width/H,A.height/F),T=X*H,W=X*F,K=R*X,Q=A.x-(T-A.width)/2-K,Y=A.y-(W-A.height)/2-K,U=T+2*K,q=W+2*K,G="".concat("react-flow__minimap-desc","-").concat(z),$=(0,i.useRef)(0),J=(0,i.useRef)();$.current=X,(0,i.useEffect)(()=>{if(D.current&&L)return J.current=(0,s.FD)({domNode:D.current,panZoom:L,getTransform:()=>I.getState().transform,getViewScale:()=>$.current}),()=>{var e;null===(e=J.current)||void 0===e||e.destroy()}},[L]),(0,i.useEffect)(()=>{var e;null===(e=J.current)||void 0===e||e.update({translateExtent:B,width:V,height:Z,inversePan:j,pannable:N,zoomStep:_,zoomable:M})},[N,M,j,_,B,V,Z]);let ee=C?e=>{var t;let[n,o]=(null===(t=J.current)||void 0===t?void 0:t.pointer(e))||[0,0];C(e,{x:n,y:o})}:void 0,et=E?(0,i.useCallback)((e,t)=>{E(e,I.getState().nodeLookup.get(t).internals.userNode)},[]):void 0;return(0,l.jsx)(k,{position:x,style:{...o,"--xy-minimap-background-color-props":"string"==typeof y?y:void 0,"--xy-minimap-mask-background-color-props":"string"==typeof w?w:void 0,"--xy-minimap-mask-stroke-color-props":"string"==typeof b?b:void 0,"--xy-minimap-mask-stroke-width-props":"number"==typeof S?S*X:void 0,"--xy-minimap-node-background-color-props":"string"==typeof u?u:void 0,"--xy-minimap-node-stroke-color-props":"string"==typeof d?d:void 0,"--xy-minimap-node-stroke-width-props":"number"==typeof p?p:void 0},className:(0,a.Z)(["react-flow__minimap",r]),"data-testid":"rf__minimap",children:(0,l.jsxs)("svg",{width:H,height:F,viewBox:"".concat(Q," ").concat(Y," ").concat(U," ").concat(q),className:"react-flow__minimap-svg",role:"img","aria-labelledby":G,ref:D,onClick:ee,children:[P&&(0,l.jsx)("title",{id:G,children:P}),(0,l.jsx)(na,{onClick:et,nodeColor:u,nodeStrokeColor:d,nodeBorderRadius:g,nodeClassName:f,nodeStrokeWidth:p,nodeComponent:h}),(0,l.jsx)("path",{className:"react-flow__minimap-mask",d:"M".concat(Q-K,",").concat(Y-K,"h").concat(U+2*K,"v").concat(q+2*K,"h").concat(-U-2*K,"z\n        M").concat(O.x,",").concat(O.y,"h").concat(O.width,"v").concat(O.height,"h").concat(-O.width,"z"),fillRule:"evenodd",pointerEvents:"none"})]})})}nc.displayName="MiniMap";let nu=(0,i.memo)(nc),nf=(0,i.memo)(function(e){let{nodeId:t,position:n,variant:o=s.pB.Handle,className:r,style:d={},children:c,color:u,minWidth:f=10,minHeight:g=10,maxWidth:p=Number.MAX_VALUE,maxHeight:m=Number.MAX_VALUE,keepAspectRatio:h=!1,resizeDirection:y,shouldResize:w,onResizeStart:b,onResize:S,onResizeEnd:x}=e,C=eC(),E="string"==typeof t?t:C,k=v(),N=(0,i.useRef)(null),M=o===s.pB.Line?"right":"bottom-right",P=null!=n?n:M,j=(0,i.useRef)(null);(0,i.useEffect)(()=>{if(N.current&&E)return j.current||(j.current=(0,s.Cz)({domNode:N.current,nodeId:E,getStoreItems:()=>{let{nodeLookup:e,transform:t,snapGrid:n,snapToGrid:o,nodeOrigin:r,domNode:l}=k.getState();return{nodeLookup:e,transform:t,snapGrid:n,snapToGrid:o,nodeOrigin:r,paneDomNode:l}},onChange:(e,t)=>{let{triggerNodeChanges:n,nodeLookup:o,parentLookup:r,nodeOrigin:l}=k.getState(),i=[],a={x:e.x,y:e.y},d=o.get(E);if(d&&d.expandParent&&d.parentId){var c,u,f,g,p,m,v;let t=null!==(c=d.origin)&&void 0!==c?c:l,n=null!==(f=null!==(u=e.width)&&void 0!==u?u:d.measured.width)&&void 0!==f?f:0,h=null!==(p=null!==(g=e.height)&&void 0!==g?g:d.measured.height)&&void 0!==p?p:0,y={id:d.id,parentId:d.parentId,rect:{width:n,height:h,...(0,s.ZB)({x:null!==(m=e.x)&&void 0!==m?m:d.position.x,y:null!==(v=e.y)&&void 0!==v?v:d.position.y},{width:n,height:h},d.parentId,o,t)}},w=(0,s.so)([y],o,r,l);i.push(...w),a.x=e.x?Math.max(t[0]*n,e.x):void 0,a.y=e.y?Math.max(t[1]*h,e.y):void 0}if(void 0!==a.x&&void 0!==a.y){let e={id:E,type:"position",position:{...a}};i.push(e)}if(void 0!==e.width&&void 0!==e.height){let t={id:E,type:"dimensions",resizing:!0,setAttributes:!y||("horizontal"===y?"width":"height"),dimensions:{width:e.width,height:e.height}};i.push(t)}for(let e of t){let t={...e,type:"position"};i.push(t)}n(i)},onEnd:e=>{let{width:t,height:n}=e;k.getState().triggerNodeChanges([{id:E,type:"dimensions",resizing:!1,dimensions:{width:t,height:n}}])}})),j.current.update({controlPosition:P,boundaries:{minWidth:f,minHeight:g,maxWidth:p,maxHeight:m},keepAspectRatio:h,resizeDirection:y,onResizeStart:b,onResize:S,onResizeEnd:x,shouldResize:w}),()=>{var e;null===(e=j.current)||void 0===e||e.destroy()}},[P,f,g,p,m,h,b,S,x,w]);let _=P.split("-"),R=o===s.pB.Line?"borderColor":"backgroundColor",I=u?{...d,[R]:u}:d;return(0,l.jsx)("div",{className:(0,a.Z)(["react-flow__resize-control","nodrag",..._,o,r]),ref:N,style:I,children:c})});function ng(e){let{nodeId:t,isVisible:n=!0,handleClassName:o,handleStyle:r,lineClassName:i,lineStyle:a,color:d,minWidth:c=10,minHeight:u=10,maxWidth:f=Number.MAX_VALUE,maxHeight:g=Number.MAX_VALUE,keepAspectRatio:p=!1,shouldResize:m,onResizeStart:v,onResize:h,onResizeEnd:y}=e;return n?(0,l.jsxs)(l.Fragment,{children:[s.Kl.map(e=>(0,l.jsx)(nf,{className:i,style:a,nodeId:t,position:e,variant:s.pB.Line,color:d,minWidth:c,minHeight:u,maxWidth:f,maxHeight:g,onResizeStart:v,keepAspectRatio:p,shouldResize:m,onResize:h,onResizeEnd:y},e)),s.dw.map(e=>(0,l.jsx)(nf,{className:o,style:r,nodeId:t,position:e,color:d,minWidth:c,minHeight:u,maxWidth:f,maxHeight:g,onResizeStart:v,keepAspectRatio:p,shouldResize:m,onResize:h,onResizeEnd:y},e))]}):null}let np=e=>{var t;return null===(t=e.domNode)||void 0===t?void 0:t.querySelector(".react-flow__renderer")};function nm(e){let{children:t}=e,n=m(np);return n?(0,u.createPortal)(t,n):null}let nv=(e,t)=>(null==e?void 0:e.internals.positionAbsolute.x)!==(null==t?void 0:t.internals.positionAbsolute.x)||(null==e?void 0:e.internals.positionAbsolute.y)!==(null==t?void 0:t.internals.positionAbsolute.y)||(null==e?void 0:e.measured.width)!==(null==t?void 0:t.measured.width)||(null==e?void 0:e.measured.height)!==(null==t?void 0:t.measured.height)||(null==e?void 0:e.selected)!==(null==t?void 0:t.selected)||(null==e?void 0:e.internals.z)!==(null==t?void 0:t.internals.z),nh=(e,t)=>{if(e.size!==t.size)return!1;for(let[n,o]of e)if(nv(o,t.get(n)))return!1;return!0},ny=e=>({x:e.transform[0],y:e.transform[1],zoom:e.transform[2],selectedNodesCount:e.nodes.filter(e=>e.selected).length});function nw(e){var t;let{nodeId:n,children:o,className:r,style:d,isVisible:u,position:f=s.Ly.Top,offset:g=10,align:p="center",...v}=e,h=eC(),y=m((0,i.useCallback)(e=>(Array.isArray(n)?n:[n||h||""]).reduce((t,n)=>{let o=e.nodeLookup.get(n);return o&&t.set(o.id,o),t},new Map),[n,h]),nh),{x:w,y:b,zoom:S,selectedNodesCount:x}=m(ny,c.X);if(!("boolean"==typeof u?u:1===y.size&&(null===(t=y.values().next().value)||void 0===t?void 0:t.selected)&&1===x)||!y.size)return null;let C=(0,s.W0)(y),E=Array.from(y.values()),k=Math.max(...E.map(e=>e.internals.z+1)),N={position:"absolute",transform:(0,s.ZJ)(C,{x:w,y:b,zoom:S},f,g,p),zIndex:k,...d};return(0,l.jsx)(nm,{children:(0,l.jsx)("div",{style:N,className:(0,a.Z)(["react-flow__node-toolbar",r]),...v,"data-id":E.reduce((e,t)=>"".concat(e).concat(t.id," "),"").trim(),children:o})})}}}]);