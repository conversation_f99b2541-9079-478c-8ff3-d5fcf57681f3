!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="ad71e346-a6c9-4eca-b9b7-cb0eeba61d8f",e._sentryDebugIdIdentifier="sentry-dbid-ad71e346-a6c9-4eca-b9b7-cb0eeba61d8f")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8849,8942],{71348:function(e,a,t){t.d(a,{Nt:function(){return m},O3:function(){return c},Sg:function(){return u},Z:function(){return o},_z:function(){return h},bO:function(){return x},hm:function(){return r},l9:function(){return i},mK:function(){return l},t$:function(){return d}});var s=t(3163),n=t(25144);let l=async(e,a,t)=>{let l="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns"),i=await (0,n.c)("post",l,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},t);return(0,s.fq)(i)},i=async(e,a,t)=>{let l="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns/").concat(t),i=await (0,n.c)("get",l,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)});return(0,s.fq)(i)},r=async(e,a,t,l)=>{let i="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns/").concat(t),r=await (0,n.c)("patch",i,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},l);return(0,s.fq)(r)},d=async(e,a,t)=>{let l="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns/").concat(t,"/review"),i=await (0,n.c)("post",l,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)});return(0,s.fq)(i)},o=async(e,a,t)=>{let l="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns/").concat(t,"/review"),i=await (0,n.c)("delete",l,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)});return(0,s.fq)(i)},c=async(e,a,t,l,i)=>{let r="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns/").concat(t,"/emails/").concat(l),d=await (0,n.c)("patch",r,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},i);return(0,s.fq)(d)},u=async(e,a,t,l)=>{let i="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns/").concat(t,"/publish"),r=await (0,n.c)("post",i,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},l);return(0,s.fq)(r)},x=async(e,a,t)=>{let l="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns/").concat(t),i=await (0,n.c)("delete",l,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)});return(0,s.fq)(i)},m=async(e,a,t)=>{let l="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns/").concat(t,"/sequences"),i=await (0,n.c)("post",l,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)});return(0,s.fq)(i)},h=async(e,a)=>{let t="".concat((0,s.JW)(),"/workspaces/").concat(a,"/campaigns"),l=await (0,n.c)("get",t,{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)});return(0,s.fq)(l)}},54886:function(e,a,t){t.d(a,{x:function(){return d},y:function(){return o}});var s=t(57437),n=t(2265),l=t(90641),i=t(12381),r=t(93448);let d=e=>{let{tabs:a,defaultTab:t,activeTab:i,onTabChange:d,tabTitleClassName:c,tabTitleExtra:u,className:x,tabSwitcherClassName:m,tabHeaderClassName:h,tabContentClassName:f}=e,[p,j]=(0,n.useState)(t||(a.length>0?a[0].id:"")),b=null!=i?i:p;return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:(0,r.cn)("flex-1 flex flex-col h-full overflow-hidden tabView",x||""),children:[(0,s.jsx)(o,{...e,onTabChange:e=>{d?d(e):j(e)},tab:b}),(0,s.jsx)("div",{className:(0,r.cn)("flex-1 !max-w-full !max-h-full overflow-hidden",f||""),children:a.filter(e=>e.id===b).map(e=>(0,s.jsx)("div",{className:"w-full h-full overflow-hidden",children:e.scroll?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(l.ScrollArea,{className:"w-full h-full scrollBlockChild",children:e.content})}):(0,s.jsx)(s.Fragment,{children:e.content})},e.id))})]})})},o=e=>{let{tabs:a,tabTitleClassName:t,tabTitleExtra:n,tabSwitcherClassName:d,tabHeaderClassName:o,tab:c}=e;return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:(0,r.cn)("h-12 w-full overflow-hidden flex items-center border-b border-neutral-300 gap-1",o||""),children:[(0,s.jsx)("div",{className:"flex-1 overflow-hidden min-w-0 h-full",children:(0,s.jsx)("div",{className:"w-full max-w-full h-full overflow-hidden",children:(0,s.jsxs)(l.ScrollArea,{className:"w-full max-w-full h-full",children:[(0,s.jsx)("div",{className:(0,r.cn)("flex gap-1 p-2 px-4 pr-10 h-full items-center",d||""),children:a.map(a=>(0,s.jsx)(i.z,{variant:"ghost",onClick:t=>e.onTabChange(a.id),className:(0,r.cn)("text-xs rounded-full p-1.5 !px-3 h-auto gap-1 ".concat(c===a.id&&"bg-neutral-200"),t||""),children:a.title},a.id))}),(0,s.jsx)(l.B,{orientation:"horizontal"})]})})}),n&&(0,s.jsx)(s.Fragment,{children:n})]})})}},91032:function(e,a,t){t.d(a,{Ei:function(){return v},FF:function(){return j},Tu:function(){return p},aM:function(){return c},bC:function(){return b},sw:function(){return u},ue:function(){return f},yo:function(){return o}});var s=t(57437),n=t(2265),l=t(49027),i=t(20653),r=t(90535),d=t(93448);let o=l.fC,c=l.xz,u=l.x8,x=l.h_,m=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,s.jsx)(l.aV,{className:(0,d.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...n,ref:a})});m.displayName=l.aV.displayName;let h=(0,r.j)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),f=n.forwardRef((e,a)=>{let{side:t="right",className:n,children:r,...o}=e;return(0,s.jsxs)(x,{children:[(0,s.jsx)(m,{}),(0,s.jsxs)(l.VY,{ref:a,className:(0,d.cn)(h({side:t}),n),...o,children:[(0,s.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,s.jsx)(i.Pxu,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]}),r]})]})});f.displayName=l.VY.displayName;let p=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...t})};p.displayName="SheetHeader";let j=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};j.displayName="SheetFooter";let b=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,s.jsx)(l.Dx,{ref:a,className:(0,d.cn)("text-lg font-semibold text-foreground",t),...n})});b.displayName=l.Dx.displayName;let v=n.forwardRef((e,a)=>{let{className:t,...n}=e;return(0,s.jsx)(l.dk,{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",t),...n})});v.displayName=l.dk.displayName},78585:function(e,a,t){t.d(a,{t:function(){return c},G:function(){return u}});var s=t(57437),n=t(2265),l=t(90175),i=t(93448),r=t(9987);let d=(0,t(90535).j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",{variants:{variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground"},size:{default:"h-9 px-3",sm:"h-8 px-2",lg:"h-10 px-3"}},defaultVariants:{variant:"default",size:"default"}});n.forwardRef((e,a)=>{let{className:t,variant:n,size:l,...o}=e;return(0,s.jsx)(r.f,{ref:a,className:(0,i.cn)(d({variant:n,size:l,className:t})),...o})}).displayName=r.f.displayName;let o=n.createContext({size:"default",variant:"default"}),c=n.forwardRef((e,a)=>{let{className:t,variant:n,size:r,children:d,...c}=e;return(0,s.jsx)(l.fC,{ref:a,className:(0,i.cn)("flex items-center justify-center gap-1",t),...c,children:(0,s.jsx)(o.Provider,{value:{variant:n,size:r},children:d})})});c.displayName=l.fC.displayName;let u=n.forwardRef((e,a)=>{let{className:t,children:r,variant:c,size:u,...x}=e,m=n.useContext(o);return(0,s.jsx)(l.ck,{ref:a,className:(0,i.cn)(d({variant:m.variant||c,size:m.size||u}),t),...x,children:r})});u.displayName=l.ck.displayName},1009:function(e,a,t){t.d(a,{n:function(){return v},r:function(){return N}});var s=t(57437),n=t(2265),l=t(61192),i=t(29119),r=t(14805),d=t(42212),o=t(84440),c=t(99376),u=t(95473),x=t(59315),m=t(45419),h=t(12381),f=t(45402),p=t(51810),j=t(14803),b=t(52292);let v=e=>{let{databasePageStore:a,databaseStore:t}=(0,d.cF)(),n=a[e.id],l=t[e.id],i=n&&n.accessLevel;return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(N,{id:e.id,refreshOnInitialRender:!0,children:(0,s.jsx)(s.Fragment,{children:i&&n&&l&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(g,{database:l,databasePage:n,accessLevel:i,children:e.children})})})})})},g=e=>{let{database:a,databasePage:t}=e,[d,c]=(0,n.useState)(!1),u=0===a.database.definition.columnIds.length;return(0,s.jsxs)(s.Fragment,{children:[d&&(0,s.jsx)(m.j,{database:a.database,close:()=>c(!1)}),u?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o.PageLoader,{size:"full",error:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"my-4",children:(0,s.jsx)(f.Z,{width:48,height:48,className:"inline"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Database is empty"}),(0,s.jsxs)("div",{className:"my-4 flex justify-center items-center gap-2",children:[(0,s.jsx)(p.yF,{databaseId:a.database.id,trigger:(0,s.jsx)(h.z,{variant:"ghost",className:"text-xs h-8 font-semibold",children:"Add column"})}),(0,s.jsx)(h.z,{className:"text-xs px-4 h-8 text-center items-center font-semibold",onClick:()=>c(!0),children:"Import from file"})]})]})})}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(l.x,{database:a.database,children:(0,s.jsx)(i.Ti,{permissiblePage:t,id:t.page.id,children:(0,s.jsx)(r.G,{children:e.children})})})})]})},N=e=>{let{url:a,databasePageStore:t,databaseStore:l}=(0,d.cF)(),{refreshDatabase:i}=(0,u.Bf)(),r=!1,m=(0,j.cL)(),h=(0,b.nM)();try{r=(0,x.Kn)().isConnected}catch(e){(m||h)&&(r=!0)}let f=(0,c.useRouter)(),p=t[e.id],v=l[e.id];p&&p.accessLevel;let g=v&&p?"":"Entity not found",[N,w]=(0,n.useState)(!!e.refreshOnInitialRender||!v);return(0,n.useEffect)(()=>{r&&N&&i(e.id).finally(()=>{w(!1)})},[N,i,e.id,r]),(0,s.jsxs)(s.Fragment,{children:[(N||g)&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o.PageLoader,{error:g,size:"full",cta:g?{label:"Go Home",onClick:()=>f.replace(a())}:null})}),!N&&p&&v&&(0,s.jsx)(s.Fragment,{children:e.children})]})}},26831:function(e,a,t){t.d(a,{W:function(){return k},o:function(){return C}});var s=t(57437),n=t(56415),l=t(68738),i=t(42212),r=t(12381),d=t(2265),o=t(95473),c=t(23500),u=t(18626),x=t(32060),m=t(20029),h=t(75060),f=t(26644),p=t(8351),j=t(36675),b=t(89399),v=t(39255),g=t(71348),N=t(84977),w=t(3163),y=t(84440),F=t(99376);let C=e=>{let[a,t]=(0,d.useState)(!1),{token:o}=(0,v.a)(),{databaseStore:c,workspace:u,url:C}=(0,i.cF)(),{toast:k}=(0,N.V)(),z=(0,F.useRouter)(),{database:I,recordIds:D,hasCustomSelection:S,isRecordPage:T}=e,W=(0,n.getCompanyDbDefinition)(),A=(0,n.getCustomerDbDefinition)(""),V=[(0,n.getDatabasePackageName)(W),(0,n.getDatabasePackageName)(A)],P=!1,E=!1,O=[],M="";(()=>{if(I.isMessagingEnabled||V.includes(I.srcPackageName)){P=!0;return}for(let e of Object.values(I.definition.columnsMap))if(e.type===l.DatabaseFieldDataType.Linked&&e.databaseId){let a=c[e.databaseId];if(!a)continue;(a.database.isMessagingEnabled||V.includes(a.database.srcPackageName))&&(P=!0,E=!0,O.push(e.id),M||(M=e.id))}})();let[_,R]=(0,d.useState)(M),[L,B]=(0,d.useState)(p.eZ.All),[q,U]=(0,d.useState)(!1),Z=async()=>{let e=u.workspace.id,a={databaseId:I.id,recordIds:D,targetColId:_,targetEntityScope:L};U(!0);let t=await (0,g.mK)((null==o?void 0:o.token)||"",e,a);if(U(!1),!t.isSuccess){k.error(t.error||(0,w.E)());return}let s=t.data.data.campaign;z.push(C("/emails/".concat(s.id)))};return P?(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(r.z,{variant:T?"outline":"ghost",onClick:()=>{if(!E){Z().then();return}t(!0)},className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[T?(0,s.jsx)(s.Fragment,{children:"Send email"}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.Z,{className:"size-4"}),"Email",S&&D&&D.length>0?"":" all"]}),q&&(0,s.jsx)(y.a,{className:"size-3"})]}),(0,s.jsxs)(x.h_,{open:a,onOpenChange:t,children:[(0,s.jsx)(x.$F,{asChild:!0,children:(0,s.jsx)("div",{className:"absolute w-0 h-0"})}),(0,s.jsx)(x.AW,{className:"min-w-80 p-2 rounded-none",align:"end",children:(0,s.jsxs)("div",{className:"flex flex-col gap-2 h-auto max-h-96",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,s.jsx)(h._,{className:"text-xs text-neutral-500",children:"Email Via"}),(0,s.jsx)(f.Y,{onChange:e=>{R(e[0])},selected:_?[_]:[],databaseId:I.id,filterFn:e=>O.includes(e.id)})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,s.jsx)(h._,{className:"text-xs text-neutral-500",children:"Target scope"}),(0,s.jsx)(j.A,{onChange:e=>{0!==e.length&&B(e[0])},selectedIds:[L],options:(()=>{let e="column",a="record",t="records";if(_){let s=I.definition.columnsMap[_];if(s){let l=c[s.databaseId];e=s.title,l&&(0,n.getDatabasePackageName)(W)===l.database.srcPackageName?(t="companies",a="company"):l&&(0,n.getDatabasePackageName)(A)===l.database.srcPackageName&&(t="contacts",a="contact")}}let s=[];return s.push({id:p.eZ.All,title:"All ".concat(t," in ").concat(e),value:p.eZ.All,data:p.eZ.All}),s.push({id:p.eZ.First,title:"First ".concat(a),value:p.eZ.First,data:p.eZ.First}),s})()})]}),(0,s.jsx)("div",{className:"",children:(0,s.jsxs)(r.z,{variant:"outline",className:"text-xs rounded-none p-1.5 h-auto gap-2 justify-start",disabled:!_||!L||q,onClick:Z,children:[(0,s.jsx)(m.oFk,{className:"size-3"}),"Create email",q&&(0,s.jsx)(y.a,{className:"size-3"})]})})]})})]})]})}):null},k=e=>{let{database:a,view:t}=e,{members:n,databaseStore:r,workspace:d}=(0,i.cF)(),{selectedIds:x,search:m,filter:h,sorts:f}=(0,o.Bf)();if(t.type!==c.bW.Table&&t.type!==c.bW.Board)return null;let p=t.definition;p.filter=p.filter||{conditions:[],match:l.Match.All},p.sorts=p.sorts||[];let j=[];return x.length>0?j.push(...x):j=(()=>{if(!a)return[];let e=[];f.length>0?e.push(...f):p.sorts.length>0&&e.push(...p.sorts),0===e.length&&e.push({columnId:l.MagicColumn.CreatedAt,order:l.Sort.Asc});let t=r[a.id],{rows:s}=(0,u.filterAndSortRecords)(t,n,r,p.filter,h,e,d.workspaceMember.userId);return m&&m.trim()?s.filter(e=>e.processedRecord.valuesText.toLowerCase().includes(m.trim().toLowerCase())):s})().map(e=>e.id),(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(C,{database:e.database,recordIds:j,hasCustomSelection:x.length>0})})}},83251:function(e,a,t){t.d(a,{j:function(){return eb}});var s,n,l=t(57437),i=t(12381),r=t(18055),d=t(2265),o=t(32060),c=t(9734),u=t(40178),x=t(55026),m=t(26652),h=t(3343),f=t(81801),p=t(96246),j=t(962);let b=j.fC,v=j.wy,g=j.Fw;var N=t(90641),w=t(35579),y=t(95473),F=t(77505),C=t(3163),k=t(84440),z=t(42212),I=t(22581),D=t(24681),S=t(68738),T=t(20029),W=t(36109),A=t(32659),V=t(24754),P=t(54207);(s=n||(n={})).Created="created",s.Updated="updated",s.Commented="commented",s.NoteAdded="note_added",s.Published="published",s.Unpublished="unpublished";let E=()=>{let{getActivities:e}=(0,y.Bf)(),{recordInfo:a}=(0,w.a)(),{id:t,databaseId:s}=a.record,[n,i]=(0,d.useState)(null),[r,o]=(0,d.useState)(""),c=(0,d.useRef)({}),u=e=>{let a={},t=e=>{let a=new Date(e),t=a.getFullYear(),s="0".concat(a.getMonth()+1).slice(-2);return"".concat(t,"-").concat(s)},s=e=>{let a=new Date(e),t=new Date,s=t.getFullYear(),n=t.getMonth(),l=a.toLocaleString("default",{month:"long"}),i=a.getFullYear(),r=a.getMonth();return i===s&&r===n-1?"Last month":i===s&&r===n?"This month":i<s?"".concat(l," ").concat(i):l};e.forEach(e=>{let n=t(e.createdAt);a[n]||(a[n]={label:s(e.createdAt),activities:[]}),a[n].activities.push(e)}),Object.keys(a).forEach(e=>{a[e].activities.sort((e,a)=>new Date(a.createdAt).getTime()-new Date(e.createdAt).getTime())});let n=Object.keys(a).sort((e,a)=>new Date(a).getTime()-new Date(e).getTime()),l={};n.forEach(e=>{l[e]=a[e]}),c.current=l},x=async()=>{o("");let a=await e(s,F.tK.Record,t);if(!a){o((0,C.E)());return}u(a),i(a),console.log({activities:a})};return(0,d.useEffect)(()=>{x().then()},[]),(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"h-full w-full flex flex-col",children:n?(0,l.jsx)(l.Fragment,{children:0===n.length?(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(k.PageLoader,{size:"full",error:"It's empty here, check back later"})}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h2",{className:"font-semibold p-4",children:"Activities"}),(0,l.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,l.jsx)(N.ScrollArea,{className:"w-full h-full",children:(0,l.jsx)("div",{className:"p-4 pt-0 pb-12",children:Object.values(c.current).map((e,a)=>(0,l.jsx)(O,{activities:e},a))})})})]})}):(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(k.PageLoader,{size:"full",error:r,cta:r?{label:"Reload",onClick:x}:null})})})})},O=e=>(0,l.jsx)("div",{className:"pb-2",children:(0,l.jsxs)(b,{defaultOpen:!0,children:[(0,l.jsx)(v,{children:(0,l.jsxs)(i.z,{variant:"ghost",className:"text-xs rounded-full p-1 px-3 -ml-3 h-auto gap-1 font-medium items-center",children:[(0,l.jsx)("span",{children:e.activities.label}),(0,l.jsx)(f.Z,{className:"size-3"})]})}),(0,l.jsx)(g,{children:(0,l.jsx)("div",{className:"flex flex-col gap-1.5 pt-1.5 pb-2",children:e.activities.activities.map(e=>(0,l.jsx)(M,{activity:e},e.id))})})]})}),M=e=>{var a;let{membersMap:t}=(0,z.cF)(),s=t[e.activity.createdById],n=s?"".concat(s.user.firstName," ").concat(s.user.lastName).trim():"Unknown member",i=e.activity.activityType;return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:"flex gap-4 pb-2",children:[(0,l.jsx)("div",{className:"p-1.5 bg-neutral-200 rounded-full size-6",children:(0,l.jsx)(T.jrq,{className:"size-full"})}),(0,l.jsxs)("div",{className:"flex-1 flex flex-col gap-1 text-sm",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsxs)(p.qE,{className:"mr-1 size-6 !rounded-full",children:[(0,l.jsx)(p.F$,{src:(null==s?void 0:null===(a=s.user)||void 0===a?void 0:a.profilePhoto)||"",alt:"Random",className:"!rounded"}),(0,l.jsx)(p.Q5,{className:"!rounded",children:n[0]})]}),(0,l.jsx)("div",{className:"flex-1 overflow-hidden truncate text-xs font-semibold",children:n}),(0,l.jsx)("div",{className:"text-muted-foreground text-[10px] pt-1",children:(0,I.S)(new Date(e.activity.createdAt))})]}),(0,l.jsx)("div",{className:"text-neutral-800  font-medium text-xs",children:i===F.T8.ItemCreated?(0,l.jsx)(L,{activity:e.activity}):i===F.T8.ValuesUpdated?(0,l.jsx)(_,{activity:e.activity}):(0,l.jsx)(l.Fragment,{})})]})]})})},_=e=>{let{database:a}=(0,w.a)(),t=e.activity.changeData,s=Object.keys(t.newValue);return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:"flex flex-col gap-2 py-1",children:[(0,l.jsxs)("div",{children:["Updated ",s.length," fields"]}),s.map(e=>(0,l.jsx)(R,{database:a,change:t,id:e},e))]})})},R=e=>{var a,t;let{database:s,change:n,id:i}=e,{members:r,databaseErrorStore:d,databaseStore:o}=(0,z.cF)(),c=s.definition.columnsMap[i],u=(null==c?void 0:c.type)||(null==n?void 0:null===(t=n.meta)||void 0===t?void 0:null===(a=t.fields)||void 0===a?void 0:a[i])||S.DatabaseFieldDataType.Text,x=n.newValue[i],m=e=>c&&c.type===S.DatabaseFieldDataType.Linked&&Array.isArray(x)&&0!==x.length&&"string"==typeof x[0]?(0,l.jsx)(V.HG,{refDatabaseId:e,values:n.newValue,column:c,databaseStore:o,databaseErrorStore:d}):null,h=()=>c&&c.type===S.DatabaseFieldDataType.Select&&Array.isArray(x)&&0!==x.length&&"string"==typeof x[0]?(console.log({value:x,type:u,change:n,column:c}),(0,l.jsx)(A.So,{values:n.newValue,column:c})):null,f=()=>c&&c.type===S.DatabaseFieldDataType.Person?Array.isArray(x)&&0!==x.length&&"string"==typeof x[0]?(0,l.jsx)(P.l0,{values:n.newValue,column:c,members:r,updatedById:"",createdById:""}):(0,l.jsx)(l.Fragment,{}):null,p=(e=>{if(void 0===x)return null;let a=null;switch(u){case S.DatabaseFieldDataType.UUID:case S.DatabaseFieldDataType.Text:case S.DatabaseFieldDataType.Number:case S.DatabaseFieldDataType.AI:case S.DatabaseFieldDataType.Date:case S.DatabaseFieldDataType.Files:a=(0,l.jsx)("div",{className:"truncate",children:(0,W.recordValueToText)(x)});break;case S.DatabaseFieldDataType.Checkbox:a=(0,l.jsx)("div",{className:"truncate",children:x?"Checked":"Unchecked"});break;case S.DatabaseFieldDataType.Linked:a=m(e);break;case S.DatabaseFieldDataType.Select:a=h();break;case S.DatabaseFieldDataType.Person:a=f();break;default:return null}return a||(0,l.jsx)(l.Fragment,{children:"Empty/Deleted value"})})(s.id);return(0,l.jsxs)("div",{className:"flex items-center gap-2 overflow-hidden",children:[(0,l.jsx)(D.e,{type:u}),(0,l.jsx)("strong",{className:"text-muted-foreground",children:(null==c?void 0:c.title)||"Deleted column"}),(0,l.jsx)(T.LZ3,{className:"size-3"}),(0,l.jsx)("div",{className:"flex-1 overflow-hidden font-medium",children:p})]},i)},L=e=>{let{database:a}=(0,w.a)(),t=e.activity.changeData,s=t&&t.newValue?Object.keys(t.newValue):[];return(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"flex flex-col gap-2 py-1",children:s.length>0?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{children:["Created the record with ",s.length," fields"]}),s.map(e=>(0,l.jsx)(R,{database:a,change:t,id:e},e))]}):(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{children:"Created the record"})})})})};var B=t(54886),q=t(66326),U=t(28942),Z=t(25853),J=t(29119),H=t(7601),$=t(8481),Q=t(88119),K=t(13523);let Y=()=>{let{accessLevel:e}=(0,J.qt)(),{recordInfo:a}=(0,w.a)(),{membersMap:t,workspace:s}=(0,z.cF)(),{id:n,databaseId:r,summaryJSON:o}=a.record,c="rs:".concat(r,"|").concat(n),[u,x]=(0,d.useState)(!1),m=Array.isArray(o)?o:[],h=!e||![H.u.Full,H.u.Edit].includes(e);return(0,l.jsxs)("div",{className:"size-full flex flex-col overflow-hidden relative",children:[!u&&(0,l.jsx)(Q.Y0,{message:Q.jB.ConnectionLost}),(0,l.jsx)(K.EL,{recordId:n,documentId:"",membersMap:t,workspace:s.workspace,children:(0,l.jsx)(i.z,{variant:"ghost",title:"Version History",className:"size-6 p-1.5 rounded-full mr-2 absolute top-4 right-4 z-50",children:(0,l.jsx)(T.TZY,{className:"size-full"})})}),(0,l.jsx)(N.ScrollArea,{className:"scrollBlockChild",children:(0,l.jsx)("div",{className:"pb-10",children:(0,l.jsx)($.o,{documentId:"record:"+a.record.id,roomName:c,initialContent:m,readonly:h,onConnectionStatusChanged:x,collaborationEnabled:!0})})})]})};var G=t(30166),X=t(74291),ee=t(39255),ea=t(75744),et=t(84977),es=t(63127),en=t(59315),el=t(99376);let ei=e=>{let{workspace:a,membersMap:t}=(0,z.cF)(),{token:s}=(0,ee.a)(),{toast:n}=(0,et.V)(),[r,o]=(0,d.useState)(1),[c,u]=(0,d.useState)({}),[x,m]=(0,d.useState)(!0),[h,f]=(0,d.useState)(!1),[p,j]=(0,d.useState)(),b=async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;if(!s)return;u({isLoading:!0,error:void 0});let l={perPage:24,page:t};e.recordId&&e.databaseId?(l.type="record",l.recordId=e.recordId,l.databaseId=e.databaseId):l.type="user";let i=await (0,ea.Kk)(s.token,a.workspace.id,l);if(i.error){u({isLoading:!1,error:i.error}),t>1&&n.error(i.error);return}let r=(null==c?void 0:c.data)||{notes:[]};1===t&&(r.notes=[]),u({isLoading:!1,data:{notes:[...r.notes,...i.data.data.notes]}}),o(t),(0===i.data.data.notes.length||1===t&&i.data.data.notes.length<24)&&m(!1)},v=async()=>{var t;if(h||!s)return;let{recordId:l,databaseId:i}=e,r=await (0,ea.kT)(s.token,a.workspace.id,{recordId:l,databaseId:i});if(f(!1),r.error){n.error(r.error);return}u({data:{notes:[r.data.data.note,...(null===(t=c.data)||void 0===t?void 0:t.notes)||[]]}}),j(r.data.data.note)},g=(0,el.useRouter)(),w=(0,el.usePathname)(),y=e=>{if(A(!0),c.data){if(u({data:{notes:[...c.data.notes].filter(a=>a.document.id!==e)}}),j(void 0),S&&S===e){let e=new URLSearchParams;D.forEach((a,t)=>{"noteId"!==t&&e.append(t,a)});let a=e.toString();g.replace(w+(a?"?".concat(a):""),{scroll:!1}),setTimeout(()=>{A(!1)},300)}else A(!1)}},{socket:F,isConnected:C}=(0,en.Kn)(),I=(0,d.useRef)(c);I.current=c,(0,d.useEffect)(()=>{if(F&&C)return F.on("note",a=>{var t;if(console.log("New note callback",a),!I.current.data||a.note.document.recordId!==e.recordId||a.note.document.databaseId!==e.databaseId)return;let s=[...(null===(t=I.current.data)||void 0===t?void 0:t.notes)||[]],n=s.findIndex(e=>e.document.id===a.note.document.id);-1===n?s.unshift(a.note):s[n]=a.note,u({...I.current,data:{notes:s}})}),F.on("note-deleted",a=>{var t;if(console.log("Deleted note callback",a),!I.current.data||a.note.document.recordId!==e.recordId||a.note.document.databaseId!==e.databaseId)return;let s=[...(null===(t=I.current.data)||void 0===t?void 0:t.notes)||[]].filter(e=>e.document.id!==a.note.document.id);u({...I.current,data:{notes:s}})}),console.log("Notes listener defined"),()=>{F&&(F.off("note"),F.off("note-deleted"),console.log("Notes listener cleared"))}},[C,F]);let D=(0,el.useSearchParams)(),S=D.get("noteId");(0,d.useEffect)(()=>{u({data:void 0}),b(1).then()},[]);let[W,A]=(0,d.useState)(!1);return(0,d.useEffect)(()=>{if(S&&c.data&&!W&&!p){let e=c.data.notes.find(e=>e.document.id===S);e&&j(e)}},[S,c.data,W,p]),(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:"h-full w-full flex flex-col",children:[(0,l.jsxs)("div",{className:"flex p-4 gap-2",children:[(0,l.jsx)("h2",{className:"font-semibold flex-1",children:"Notes"}),c.data&&(0,l.jsx)(i.z,{disabled:h,onClick:v,className:"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1",children:"New note"})]}),(0,l.jsxs)("div",{className:"flex-1 overflow-hidden",children:[1===r&&(c.isLoading||c.error||!c.data)&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(k.PageLoader,{error:c.error,size:"full",cta:c.error?{label:"Retry",onClick:b}:void 0})}),c.data&&0===c.data.notes.length&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(k.PageLoader,{error:"It's empty here",size:"full",icon:(0,l.jsx)(T.Zi2,{className:"size-12"}),cta:{label:"Create Note",onClick:v}})}),c.data&&c.data.notes.length>0&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(N.ScrollArea,{className:"w-full h-full",children:(0,l.jsxs)("div",{className:"p-4 pb-12 pt-0",children:[(0,l.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:c.data.notes.map((e,s)=>(0,l.jsx)(er,{workspaceId:a.workspace.id,onDelete:()=>y(e.document.id),n:e,onClick:()=>j(e),membersMap:t},e.document.id))}),x&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"flex justify-center my-8 pb-16",children:(0,l.jsx)(i.z,{variant:"link",disabled:c.isLoading,onClick:()=>{b(r+1)},className:"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1",children:c.isLoading?"Loading...":"Load More"})})})]})})})]}),p&&(0,l.jsx)(ed,{close:e=>{if(A(!0),!c.data)return;let a=[...c.data.notes];for(let t=0;t<a.length;t++)if(c.data.notes[t].document.id===e.document.id){a[t]=e;break}if(u({data:{notes:a}}),j(void 0),S){let e=new URLSearchParams;D.forEach((a,t)=>{"noteId"!==t&&e.append(t,a)});let a=e.toString();g.replace(w+(a?"?".concat(a):""),{scroll:!1}),setTimeout(()=>{A(!1)},300)}else A(!1)},membersMap:t,note:p})]})})},er=e=>{var a,t,s;let{n,onClick:r,membersMap:c,onDelete:u,workspaceId:x}=e,{confirm:m,toast:h}=(0,et.V)(),{token:f,user:j}=(0,ee.a)(),b="";if(n.database&&n.record){let{defaultTitle:e,titleColId:a,isContacts:t}=(0,es.$P)(n.database);b=(0,es.T5)(n.record,a,e,t,n.database)}let v=c[n.document.createdById],g="".concat((null==v?void 0:null===(a=v.user)||void 0===a?void 0:a.firstName)||""," ").concat((null==v?void 0:null===(t=v.user)||void 0===t?void 0:t.lastName)||"").trim()||"Unknown member",[N,w]=(0,d.useState)(!1),y=async()=>{if(N||!f)return;w(!0);let e=await (0,ea.f_)(f.token,x,{id:n.document.id});if(w(!1),e.error){h.error("Error deleting note:"+e.error);return}null==u||u()},F=()=>{m("Delete note?","This cannot be reversed",async()=>{y().then()})};return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{onClick:r,className:"overflow-hidden p-3 border border-neutral-200 flex flex-col gap-2 hover:border-black transition-all relative select-none",children:[(0,l.jsxs)("div",{className:"flex gap-2",children:[n.record&&(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)(i.z,{className:"!p-0.5 gap-2 text-xs text-[10px] !h-auto hover:bg-neutral-200 truncate",variant:"ghost",children:[(0,l.jsxs)(p.qE,{className:"size-4",children:[(0,l.jsx)(p.F$,{className:"size-full",src:""}),(0,l.jsx)(p.Q5,{className:"text-xs text-[10px]",children:(b||"Untitled")[0]})]}),(0,l.jsx)("span",{className:"text-xs text-[10px] font-semibold underline decoration-dashed underline-offset-4 decoration-neutral-300",children:b})]})}),(0,l.jsx)("div",{className:"flex-1"})]}),(null==j?void 0:j.id)===n.document.createdById&&(0,l.jsx)("div",{className:"absolute right-2 top-2",children:(0,l.jsxs)(o.h_,{children:[(0,l.jsx)(o.$F,{asChild:!0,children:(0,l.jsx)(i.z,{variant:"ghost",className:"rounded-full h-auto p-1.5 size-6 text-xs gap-2",children:(0,l.jsx)(T.zGg,{className:"size-full"})})}),(0,l.jsx)(o.AW,{className:"w-28 rounded-none text-neutral-800 font-semibold",align:"end",children:(0,l.jsx)(o.Xi,{disabled:N,onClick:e=>{e.preventDefault(),e.stopPropagation(),F()},className:"text-xs rounded-none p-2 flex gap-2",children:"Delete"})})]})}),(0,l.jsx)("div",{className:"text-xs font-semibold truncate",children:n.document.name||"Untitled"}),(0,l.jsx)("div",{className:"text-xs text-muted-foreground font-medium text-[10px] h-16 overflow-hidden",children:n.document.contentText}),(0,l.jsxs)("div",{className:"flex gap-2 text-xs text-muted-foreground font-medium text-[10px] border-t pt-2",children:[(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsxs)("div",{className:"!p-0 gap-2 text-xs text-[10px] flex !h-auto",children:[(0,l.jsxs)(p.qE,{className:"size-4",children:[(0,l.jsx)(p.F$,{className:"size-full",src:null==v?void 0:null===(s=v.user)||void 0===s?void 0:s.profilePhoto}),(0,l.jsx)(p.Q5,{className:"text-xs text-[10px]",children:g[0]})]}),(0,l.jsx)("span",{className:"text-xs text-[10px]",children:g})]})}),(0,l.jsxs)("div",{children:["Edited ",(0,I.S)(new Date(n.document.createdAt))]})]})]})})},ed=e=>{let{token:a}=(0,ee.a)(),{workspace:t}=(0,z.cF)(),{toast:s}=(0,et.V)(),[n,r]=(0,d.useState)(e.note),[o,c]=(0,d.useState)(e.note.document.name),[u,x]=(0,d.useState)(!1),[m,h]=(0,d.useState)(!1),[f,j]=(0,d.useState)(!1),b="n:".concat(e.note.document.id,"|").concat(t.workspace.id),v="";if(n.database&&n.record){let{defaultTitle:e,titleColId:a,isContacts:t}=(0,es.$P)(n.database);v=(0,es.T5)(n.record,a,e,t,n.database)}let g=async()=>{if(!a)return;x(!0);let e=await (0,ea.Qk)(a.token,t.workspace.id,{id:n.document.id,name:o});if(x(!1),e.error){s.error("Error saving title: "+e.error);return}r({...n,document:{...n.document,name:o,updatedAt:new Date().toISOString()}})},w=e.note.document.contentJSON,y=Array.isArray(w)?w:[];return(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(X.Vq,{open:!0,onOpenChange:()=>e.close(n),children:(0,l.jsx)(X.cZ,{className:"max-w-[850px] h-3/4 !rounded-none p-4",children:(0,l.jsxs)("div",{className:"size-full overflow-hidden flex flex-col gap-0.5",children:[(0,l.jsx)(X.fK,{className:"h-8",children:(0,l.jsxs)(X.$N,{className:"font-bold text-xs flex gap-1 pr-4",children:[(0,l.jsxs)("div",{className:"flex-1 overflow-hidden",children:[n.document.name||"Untitled Note",n.record&&(0,l.jsxs)(l.Fragment,{children:["in",(0,l.jsxs)(i.z,{className:"!p-0.5 gap-2 text-xs text-[10px] ml-2 !h-auto hover:bg-neutral-200 truncate",variant:"ghost",children:[(0,l.jsxs)(p.qE,{className:"size-4",children:[(0,l.jsx)(p.F$,{className:"size-full",src:""}),(0,l.jsx)(p.Q5,{className:"text-xs text-[10px]",children:(v||"Untitled")[0]})]}),(0,l.jsx)("span",{className:"text-xs text-[10px] font-semibold underline decoration-dashed underline-offset-4 decoration-neutral-300",children:v})]})]})]}),(0,l.jsx)(K.EL,{documentId:n.document.id,membersMap:e.membersMap,workspace:t.workspace,children:(0,l.jsx)(i.z,{variant:"ghost",title:"Version History",className:"size-6 p-1.5 rounded-full mr-2 relative -top-1",children:(0,l.jsx)(T.TZY,{className:"size-full"})})})]})}),m&&!f&&(0,l.jsx)(Q.Y0,{message:Q.jB.ConnectionLost}),(0,l.jsx)("div",{className:"flex-1 flex flex-col gap-2 py-2 overflow-hidden",children:(0,l.jsxs)(N.ScrollArea,{className:"size-full scrollBlockChild",children:[(0,l.jsx)("div",{className:"px-1 w-full flex gap-1 items-center",children:(0,l.jsx)("input",{onBlur:e=>{e.target.value.trim()!==n.document.name&&g()},onChange:e=>c(e.target.value),className:"text-2xl p-2 h-auto lg:p-8 lg:px-12 lg:pb-4 lg:h-18 font-black text-black border-none outline-none flex-1",placeholder:"Untitled",value:o})}),(0,l.jsx)($.o,{documentId:"WorkspaceNotes:".concat(e.note.document.id),roomName:b,className:"min-h-[calc(100%-100px)]",initialContent:y,onEditorReady:()=>h(!0),onConnectionStatusChanged:j,onChange:e=>{r({...n,document:{...n.document,contentText:e}})},collaborationEnabled:!0})]})})]})})})})};var eo=t(37080),ec=t(22026),eu=t(54921),ex=t(16720),em=t(1009),eh=t(98116),ef=t(95422);let ep=(0,G.default)(()=>t.e(6485).then(t.bind(t,66485)).then(e=>e.RecordOverview),{loadableGenerated:{webpack:()=>[66485]},ssr:!1}),ej=e=>{var a;let{view:t,databaseId:s}=e,n=null===(a=t.definition)||void 0===a?void 0:a.databaseId;return(0,l.jsx)(ef.ls,{children:(0,l.jsx)("div",{className:"h-full w-full overflow-hidden",children:n?(0,l.jsx)(em.r,{id:n,refreshOnInitialRender:!0,children:(0,l.jsx)(ex.JJ,{context:"record_tab",parentId:s,viewId:t.id,children:(0,l.jsx)(eu.o,{id:t.id,view:t})})}):(0,l.jsx)(ex.JJ,{context:"record_tab",parentId:s,viewId:t.id,children:(0,l.jsx)(eu.o,{id:t.id,view:t})})})})},eb=e=>{var a,t;let{showOverview:s,activeTab:n}=e,{recordInfo:i,database:o}=(0,w.a)(),{id:c,databaseId:u}=i.record,{isMobile:x}=(0,Z.e)(),{databasePageStore:m}=(0,z.cF)(),h=(0,el.useSearchParams)(),f=(0,el.useRouter)(),p=(0,el.usePathname)(),{isInPeekContext:j}=(0,eo.uu)(),[b,v]=(0,d.useState)(null),{defaultTitle:g,isContacts:N,titleColId:y}=(0,es.$P)(o),F=(0,es.T5)(i.record,y,g,N,o),C=(null===(a=o.meta)||void 0===a?void 0:a.recordViewsOrder)||[],k=Object.values((null===(t=o.meta)||void 0===t?void 0:t.recordViewsMap)||{}).map(e=>{var a;return null===(a=e.definition)||void 0===a||a.databaseId,{id:e.id,title:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(r.O,{type:e.type,className:"size-3"}),(0,l.jsx)("div",{className:"overflow-hidden truncate flex-1 font-semibold",children:e.name})]}),content:(0,l.jsx)(ej,{view:e,databaseId:u})}}),I=[{id:"summary",title:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(T.vJ3,{className:"size-3"}),(0,l.jsx)("div",{className:"overflow-hidden truncate flex-1 font-semibold",children:"Summary"})]}),content:(0,l.jsx)(Y,{})},{id:"activity",title:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(T.jrq,{className:"size-3"}),(0,l.jsx)("div",{className:"overflow-hidden truncate flex-1 font-semibold",children:"Activities"})]}),content:(0,l.jsx)(E,{})},{id:"notes",title:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(T.Zi2,{className:"size-3"}),(0,l.jsx)("div",{className:"overflow-hidden truncate flex-1 font-semibold",children:"Notes"})]}),content:(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(ei,{recordId:c,databaseId:u})})},{id:"reminders",title:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(T.ZME,{className:"size-3"}),(0,l.jsx)("div",{className:"overflow-hidden truncate flex-1 font-semibold",children:"Reminders"})]}),content:(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(q.qp,{newReminderTitle:"Remind me about ".concat(F),recordId:c,databaseId:u})})},...k];if((x||s)&&I.unshift({id:"overview",title:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(T.Npz,{className:"size-3"}),(0,l.jsx)("div",{className:"overflow-hidden truncate flex-1 font-semibold",children:"Overview"})]}),content:(0,l.jsx)(ep,{isTabbed:!0})}),(0,ec.D0)()&&I.push({id:"custom-1",title:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(T.bV0,{className:"size-3"}),(0,l.jsx)("div",{className:"overflow-hidden truncate flex-1 font-semibold",children:"Stories"})]}),content:(0,l.jsx)(l.Fragment,{children:"Stories"})}),C.length>0){let e=I.filter(e=>"overview"!==e.id),a=I.find(e=>"overview"===e.id),t={};e.forEach(e=>{t[e.id]=e});let s=[];a&&s.push(a),C.forEach(e=>{t[e]&&(s.push(t[e]),delete t[e])}),Object.values(t).forEach(e=>{s.push(e)}),s.length>0&&(I.length=0,I.push(...s))}(0,d.useEffect)(()=>{let e=h.get("tab"),a=n||(j?null:e);a&&I.some(e=>e.id===a)?v(a):!b&&I.length>0&&(j&&s&&I.some(e=>"overview"===e.id)?v("overview"):v(I[0].id))},[I,b,n,h,j,s]);let D=e=>{if(v(e),e){let a=new URLSearchParams(h.toString());a.set("tab",e),a.delete("noteId"),a.delete("reminderId"),a.delete("viewId"),a.delete("recordId");let t=a.toString();f.replace(p+(t?"?".concat(t):""),{scroll:!1})}else{let e=new URLSearchParams(h.toString());e.delete("tab"),e.delete("noteId"),e.delete("reminderId"),e.delete("viewId"),e.delete("recordId");let a=e.toString();f.replace(p+(a?"?".concat(a):""),{scroll:!1})}};return(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(B.x,{tabs:I,activeTab:b,onTabChange:D,tabSwitcherClassName:"px-3",className:"extra rEx",tabTitleExtra:(0,l.jsx)(ev,{tabs:I,onNewView:e=>{D(e.id)},setActiveTabId:D})},x?1:2)})},ev=e=>{let{tabs:a,onNewView:t,setActiveTabId:s}=e,[n,r]=(0,d.useState)(!1),[f,p]=(0,d.useState)(!1),{deleteView:j,updateView:b,updateRecordTabView:v,deleteRecordTabView:g,reorderRecordTabViews:N}=(0,y.Bf)(),{databasePageStore:F,databaseStore:C}=(0,z.cF)(),{recordInfo:k,database:I}=(0,w.a)(),{confirm:D}=(0,et.V)(),S=a.filter(e=>"overview"!==e.id),T=e=>{var a,t;return null===(t=I.meta)||void 0===t?void 0:null===(a=t.recordViewsMap)||void 0===a?void 0:a[e]},W=e=>{N(I.id,e)},A=e=>{let t=a.findIndex(a=>a.id===e.id);if(g(I.id,e.id,e.name),a.length>1){let e=t===a.length-1?t-1:t+1,n=a[e];if(n&&"overview"!==n.id)s(n.id);else if(a.length>1){let e=a.find(e=>"overview"!==e.id);e&&s(e.id)}}else s(null)},V=(e,a)=>{var t,s;r(!1),(null===(s=I.meta)||void 0===s?void 0:null===(t=s.recordViewsMap)||void 0===t?void 0:t[e.id])?v(I.id,e.id,a):b(e,{name:e.name,isPublished:e.isPublished,description:e.description,...a}).finally(()=>{})};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(U._,{context:"record_tab",setOpen:p,open:f,onViewCreated:e=>{let s=[...a.filter(e=>"overview"!==e.id).map(e=>e.id),e.id];N(I.id,s),t(e)}}),(0,l.jsx)("div",{className:"p-2",children:(0,l.jsxs)(o.h_,{open:n,onOpenChange:r,children:[(0,l.jsx)(o.$F,{asChild:!0,children:(0,l.jsx)(i.z,{variant:"ghost",className:"text-xs rounded-full p-1.5 h-auto gap-2 overflow-hidden",children:(0,l.jsx)(c.Z,{className:"size-4"})})}),(0,l.jsx)(o.AW,{className:"w-96 p-0 rounded-none",align:"end",children:(0,l.jsxs)("div",{className:"flex flex-col h-auto max-h-96",children:[(0,l.jsx)("div",{className:"p-2 border-b flex-1 overflow-y-auto max-h-64",children:(0,l.jsx)(h.W,{items:S.map(e=>({id:e.id,data:e})),itemRenderer:function(e,a){let t=a.data,s=T(t.id),n=!!s;return(0,l.jsxs)(i.z,{variant:"ghost",className:"text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden",children:[(0,l.jsx)("div",{className:"flex items-center flex-1 gap-2 text-left justify-start",children:t.title}),(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsxs)(o.h_,{children:[(0,l.jsx)(o.$F,{asChild:!0,children:(0,l.jsx)(i.z,{variant:"ghost",onClick:e=>{e.stopPropagation(),e.preventDefault()},className:"size-6 p-1 rounded-full items-center hover:bg-neutral-300",children:(0,l.jsx)(u.Z,{className:"h-3 w-3"})})}),(0,l.jsxs)(o.AW,{className:"w-56",align:"end",children:[n&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(o.Qk,{className:"p-1 pb-0",children:(0,l.jsx)(eh.h,{placeHolder:"Name",value:(null==s?void 0:s.name)||"",onChange:e=>{e&&s&&V(s,{name:e})},shortEnter:!0,disabled:!1,wrapperClassname:"h-8 p-1"})}),(0,l.jsx)(o.VD,{}),(0,l.jsx)(o.Qk,{className:"p-1",children:(0,l.jsx)(o.Xi,{className:"text-xs rounded-none p-1.5 h-7",onClick:()=>{s&&D("Are you sure you want to delete ".concat(s.name,"?"),"This action cannot be undone.",()=>A(s))},children:"Delete"})})]}),!n&&(0,l.jsx)(o.Xi,{disabled:!0,className:"text-xs text-gray-400",children:"No actions available"})]})]})})]})},onChange:function(e){W(e.map(e=>e.id))},useDragHandle:!0,handlePosition:"center",wrapperClassName:(e,a)=>"hover:bg-neutral-100 gap-0.5 pl-2"})}),(0,l.jsx)("div",{className:"p-2",children:(0,l.jsxs)(i.z,{variant:"ghost",className:"text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start",onClick:()=>{N(I.id,[])},children:[(0,l.jsx)(x.Z,{className:"size-4"}),"Reset to Default Order"]})}),(0,l.jsx)("div",{className:"p-2 border-t",children:(0,l.jsxs)(i.z,{variant:"ghost",className:"text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start",onClick:()=>{r(!1),p(!0)},children:[(0,l.jsx)(m.Z,{className:"size-4"}),"New View"]})})]})})]})})]})}},92326:function(e,a,t){t.d(a,{S:function(){return f}});var s=t(57437),n=t(2265),l=t(74291),i=t(12381),r=t(95473),d=t(42212),o=t(35579),c=t(68738),u=t(36109),x=t(57154),m=t(7601),h=t(29119);let f=e=>{let{open:a,onClose:t,databaseId:f,viewFilter:j,contextualFilter:b,onRecordCreated:v}=e,{createRecords:g}=(0,r.Bf)(),{databaseStore:N}=(0,d.cF)(),{accessLevel:w}=(0,h.qt)(),y=(0,o.pB)(),[F,C]=(0,n.useState)({}),[k,z]=(0,n.useState)(!1),[I,D]=(0,n.useState)(null),S=N[f],T=w&&[m.u.Full,m.u.Edit].includes(w);(0,n.useEffect)(()=>{if(!S||!a)return;let e=function(e,a,t,s){let n={},l=[],i=[];return Object.values(e.definition.columnsMap).forEach(e=>{e.type===c.DatabaseFieldDataType.Text&&l.push(e)}),(null==a?void 0:a.conditions)&&p(a.conditions,e,n,i,s),(null==t?void 0:t.conditions)&&p(t.conditions,e,n,i,s),{prePopulatedValues:n,requiredFields:l,conflictingFields:Array.from(new Set(i))}}(S.database,j,b,null==y?void 0:y.recordInfo.record.id);D(e),C(e.prePopulatedValues)},[S,j,b,y,a]);let W=async()=>{if(T&&S){z(!0);try{let e=await g(f,[F]);e&&e.records&&e.records.length>0&&(null==v||v(e.records[0].id),t())}catch(e){console.error("Failed to create record:",e)}finally{z(!1)}}};if(!S||!I)return null;let A=I.requiredFields.filter(e=>!I.prePopulatedValues.hasOwnProperty(e.id)),V=A.length>0,P=I.conflictingFields.length>0;return(0,s.jsx)(l.Vq,{open:a,onOpenChange:e=>!e&&t(),children:(0,s.jsxs)(l.cZ,{className:"max-w-2xl max-h-[90vh] !rounded-none p-0",hideCloseBtn:!0,children:[(0,s.jsxs)(l.fK,{className:"p-4 border-b flex flex-row items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)(l.$N,{className:"text-sm font-semibold",children:"Add New Record"})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(i.z,{variant:"outline",size:"sm",className:"text-xs rounded-full",onClick:t,disabled:k,children:"Cancel"}),(0,s.jsx)(i.z,{onClick:W,size:"sm",className:"text-xs rounded-full bg-black text-white hover:bg-gray-800",disabled:k||!T,children:k?"Creating...":"Create"})]})]}),(0,s.jsx)("div",{className:"max-h-[calc(90vh-120px)] overflow-auto mention-input-container",children:(0,s.jsxs)("div",{className:"p-4 space-y-6",children:[(Object.keys(I.prePopulatedValues).length>0||P)&&(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[Object.keys(I.prePopulatedValues).length>0&&(0,s.jsx)("div",{className:"text-green-600 mb-1",children:"Some fields have been pre-populated based on current filters."}),P&&(0,s.jsx)("div",{className:"text-orange-600",children:"Some filters couldn't be used for pre-population and will need manual input."})]}),Object.keys(I.prePopulatedValues).length>0&&(0,s.jsxs)("div",{className:"border border-green-200 rounded-lg",children:[(0,s.jsx)("div",{className:"px-3 py-2 border-b border-green-200 bg-green-50",children:(0,s.jsx)("h4",{className:"text-sm font-semibold text-green-700",children:"Pre-populated Fields"})}),(0,s.jsx)("div",{className:"p-3 space-y-3",children:Object.entries(I.prePopulatedValues).map(e=>{let[a,t]=e,n=S.database.definition.columnsMap[a];return n?(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:n.title}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:(0,u.recordValueToText)(t)})]},a):null})})]}),V&&(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg",children:[(0,s.jsx)("div",{className:"px-3 py-2 border-b border-gray-200 bg-gray-50",children:(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"Required Fields"})}),(0,s.jsx)("div",{className:"p-3 space-y-4",children:A.map(e=>(0,s.jsx)("div",{children:(0,s.jsx)(x.oo,{id:e.id,values:F,updateValues:C,columnsMap:S.database.definition.columnsMap,columnsPropMap:{},databaseId:f,disabled:!T,isEditing:!0,activeField:"",setActiveField:()=>{},updateFieldProps:()=>{}})},e.id))})]}),P&&(0,s.jsxs)("div",{className:"border border-orange-200 rounded-lg",children:[(0,s.jsx)("div",{className:"px-3 py-2 border-b border-orange-200 bg-orange-50",children:(0,s.jsx)("h4",{className:"text-sm font-semibold text-orange-700",children:"Fields Requiring Input"})}),(0,s.jsx)("div",{className:"p-3",children:(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["The following fields have filters that prevent pre-population: ",I.conflictingFields.join(", ")]})})]})]})})]})})};function p(e,a,t,s,n){e.forEach(e=>{let l=a.definition.columnsMap[e.columnId];if(l&&!t.hasOwnProperty(e.columnId)){if("equals"===e.op||"is"===e.op){let a=e.value;"current_record"===a&&n?a=n:Array.isArray(a)&&a.includes("current_record")&&n&&(a=a.map(e=>"current_record"===e?n:e)),a&&"current_record"!==a&&(t[e.columnId]=a)}else s.push(l.title)}})}},16720:function(e,a,t){t.d(a,{JJ:function(){return ek},sV:function(){return eC}});var s=t(57437),n=t(12381),l=t(2265),i=(t(12087),t(89399),t(21726)),r=t(40279),d=t(75060),o=t(20029),c=t(29119),u=t(7601),x=t(84440),m=t(42212),h=t(99376),f=t(23500),p=t(14327),j=t(84977),b=t(79676),v=t(95473),g=t(32060),N=t(3343),w=t(24681),y=t(51810);let F=e=>{var a;let[t,i]=(0,l.useState)(!1),{databaseStore:r}=(0,m.cF)(),d=e.view.definition,c=r[d.databaseId],{cache:u}=(0,v.Bf)(),{id:x,pageId:h,name:f,isPublished:p}=e.view,j=null==c?void 0:null===(a=c.database)||void 0===a?void 0:a.definition,{columnsOrder:b,columnPropsMap:F}=d;if(b=Array.isArray(b)?b:[],F=F||{},j)for(let e of j.columnIds)b.includes(e)||b.push(e),F[e]||(F[e]={});let C=(()=>{let e=[],a=0;if(!j)return{columns:e,hiddenCount:a};for(let e of j.columnIds)b.includes(e)||b.push(e),F[e]||(F[e]={});for(let n of b){var t,s;j.columnsMap[n]&&(e.push({id:n,isHidden:!!(null===(t=F[n])||void 0===t?void 0:t.isHidden)}),(null===(s=F[n])||void 0===s?void 0:s.isHidden)&&a++)}return{columns:e,hiddenCount:a}})(),k=(e,a)=>{F[e]&&(F[e]={...F[e],...a},I({columnPropsMap:F}).then())},z=e=>{I({columnsOrder:e}).then()},I=async a=>{e.onDefinitionUpdate(a)},D=u.getCache(y.ac.NewlyCreatedColumnKey);return((0,l.useEffect)(()=>{var e;D&&D.databaseId===(null==c?void 0:null===(e=c.database)||void 0===e?void 0:e.id)&&k(D.columnId,{isHidden:!1})},[D]),c&&j)?(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(g.h_,{open:t,onOpenChange:i,children:[(0,s.jsx)(g.$F,{asChild:!0,children:(0,s.jsxs)(n.z,{variant:"ghost",className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,s.jsx)(o.kZF,{className:"size-3"}),C.hiddenCount>0?(0,s.jsxs)(s.Fragment,{children:[C.hiddenCount," hidden fields"]}):(0,s.jsx)(s.Fragment,{children:"Hide Fields"})]})}),(0,s.jsx)(g.AW,{className:"w-64 p-0 rounded-none",align:"start",children:(0,s.jsx)("div",{className:"flex flex-col h-auto max-h-96 overflow-hidden",children:(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsxs)("div",{className:"p-2 border-b flex-1",children:[(0,s.jsxs)("div",{className:"mb-2 text-xs font-semibold text-muted-foreground",children:[C.hiddenCount," hidden fields"]}),(0,s.jsx)(N.W,{items:C.columns.map(e=>({id:e.id,data:e})),itemRenderer:function(e,a){let t=a.data,l=j.columnsMap[t.id];return(0,s.jsxs)("div",{role:"button",className:"flex select-none font-medium cursor-pointer text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden relative items-center hover:bg-transparent",children:[(0,s.jsx)(w.e,{type:l.type,className:"size-3"}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden truncate text-left",children:l.title}),(0,s.jsx)(n.z,{variant:"ghost",onClick:e=>{e.stopPropagation(),e.preventDefault(),k(t.id,{isHidden:!t.isHidden})},className:"size-5 p-1 rounded-full items-center hover:bg-neutral-300",children:t.isHidden?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o.kZF,{className:"size-3"})}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o.tEF,{className:"size-3"})})})]},t.id)},onChange:function(e){z(e.map(e=>e.id))},useDragHandle:!0,handlePosition:"center",wrapperClassName:(e,a)=>"hover:bg-neutral-100 gap-0.5 pl-2 ".concat(a.data.isHidden&&"opacity-50")})]})})})})]})}):null};var C=t(28942),k=t(59315),z=t(87957),I=t(32852),D=t(18055),S=t(81801),T=t(39826),W=t(52292);let A=e=>{let a;let{viewsMap:t,page:i}=(0,c.qt)(),{url:r}=(0,m.cF)(),d=(0,h.useRouter)(),u=(0,W.nM)(),x=!!u,f=!1;try{let e=(0,k.Kn)();a=e.socket,f=e.isConnected}catch(e){x||console.log(e)}let[p,j]=(0,l.useState)(!1),b=(0,l.useMemo)(()=>i.viewsOrder||[],[i]),v=t[e.viewId]||null,w=(0,l.useMemo)(()=>{let e=[];return b.forEach(a=>{t[a]&&e.push(t[a])}),Object.keys(t).forEach(a=>{b.includes(a)||e.push(t[a])}),e},[b,t]),y=e=>{if(!a||!f)return;let t=(0,z.arrayDeDuplicate)(e);a.emit(I.S.UpdatePageViewsOrder,{pageId:i.id,viewsOrder:t},e=>{console.log("UpdatePageViewsOrder with response",e)})},F=a=>{let t="database"===e.context?"/databases/".concat(i.databaseId,"/views/").concat(a):"/".concat(i.id,"/views/").concat(a);u?u.setNavPath(t):d.push(r(t)),j(!1)};return(0,s.jsx)(s.Fragment,{children:v&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(g.h_,{open:p,onOpenChange:j,children:[(0,s.jsx)(g.$F,{asChild:!0,children:(0,s.jsxs)(n.z,{variant:"ghost",className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 max-w-52 overflow-hidden",children:[(0,s.jsx)(D.O,{type:v.type,className:"size-4"}),(0,s.jsx)("div",{className:"overflow-hidden truncate flex-1 font-semibold",children:v.name}),(0,s.jsx)(S.Z,{className:"ml-auto size-4 shrink-0 opacity-50"})]})}),(0,s.jsx)(g.AW,{className:"w-96 p-0 rounded-none",align:"start",children:(0,s.jsxs)("div",{className:"flex flex-col h-auto max-h-96",children:[(0,s.jsx)("div",{className:"p-2 border-b flex-1",children:(0,s.jsx)(N.W,{items:w.map(e=>({id:e.id,data:e})),itemRenderer:function(a,t){let l=t.data;return(0,s.jsxs)(n.z,{variant:"ghost",className:"text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden relative",children:[(0,s.jsx)(D.O,{type:l.type,className:"size-4"}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden text-left",children:(0,s.jsx)("div",{onClick:()=>{F(l.id)},className:"block truncate",children:l.name})}),l.isPublished&&(0,s.jsx)("div",{className:"",children:(0,s.jsx)(n.z,{variant:"ghost",onClick:e=>{e.stopPropagation(),e.preventDefault()},className:"size-6 p-1 rounded-full items-center hover:bg-neutral-300",children:(0,s.jsx)(o.n9J,{className:"size-3 transition-transform text-blue-600"})})}),!x&&e.editable&&(0,s.jsx)(T._,{deletable:e.deletable,cloneable:e.cloneable,view:l})]},l.id)},onChange:function(e){y(e.map(e=>e.data.id))},useDragHandle:!0,disabled:!e.editable||x,handlePosition:"center",wrapperClassName:(e,a)=>"hover:bg-neutral-100 gap-0.5 pl-2 ".concat(""===a.id&&"bg-neutral-100")})}),e.creatable&&!x&&(0,s.jsx)("div",{className:"p-2",children:(0,s.jsxs)(n.z,{variant:"ghost",className:"text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start",onClick:()=>{j(!1),e.requestNewView()},children:[(0,s.jsx)(o.oFk,{className:"size-3"}),"New View"]})})]})})]})})})};var V=t(17939),P=t(99972),E=t(38218),O=t(73299),M=t(93448),_=t(41426),R=t(36675),L=t(26831),B=t(74291),q=t(68738),U=t(90641),Z=t(26644),J=t(39702),H=t(73617),$=t(55196),Q=t(38156),K=t(21381),Y=t(63127),G=t(61806),X=t(94871),ee=t(57154);let ea=e=>{let{updateRecordValues:a}=(0,v.Bf)(),{toast:t}=(0,j.V)(),[i,r]=(0,l.useState)([{columnId:""}]),[d,o]=(0,l.useState)(!1),[c,u]=(0,l.useState)(!1);if(0===e.ids.length)return null;let m={},h=[];for(let{columnId:e,value:a}of i)e&&a&&(m[e]=a),e&&h.push(e);let f=(e,a)=>{let t=[...i];t[e]=a,r(t)},p=async()=>{let s=Object.keys(m),n=!1;s.length>0&&(n=!!await a(e.database.id,e.ids,m)),n&&(u(!1),e.onUpdate(),t.success("".concat(e.ids.length," record(s) updated")))};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(B.Vq,{open:c,onOpenChange:u,children:[(0,s.jsx)(B.hg,{asChild:!0,children:e.trigger}),(0,s.jsxs)(B.cZ,{className:"max-w-[700px]  !rounded-none p-4",children:[(0,s.jsx)(B.fK,{children:(0,s.jsxs)(B.$N,{className:"font-bold",children:["Update ",e.ids.length," Records"]})}),(0,s.jsx)("div",{className:"flex flex-col gap-2 overflow-hidden",children:(0,s.jsx)(U.ScrollArea,{className:"scrollBlockChild",children:(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:[i.map((a,t)=>{let{columnId:n,value:l}=a,i=n?[n]:[],r=i.length>0?e.database.definition.columnsMap[n]:void 0,d={databaseId:e.database.id,columnProps:{},columnsMap:e.database.definition.columnsMap,id:n,isEditing:!1,updateFieldProps:(e,a)=>{},updateValues:e=>{f(t,{columnId:n,value:e[n]})},values:m,setActiveField:e=>{},activeField:""};return console.log({fieldProps:d,column:r}),(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,s.jsx)("div",{className:"flex-1 max-w-64 flex items-center",children:(0,s.jsx)(Z.Y,{databaseId:e.database.id,selected:i,filterFn:e=>e.id===(null==r?void 0:r.id)||!h.includes(e.id),onChange:e=>{f(t,{columnId:e[0],value:void 0})}})}),(0,s.jsx)("div",{className:"flex-1 flex items-center pt-1",children:r&&(0,s.jsxs)(s.Fragment,{children:[r.type===q.DatabaseFieldDataType.AI&&(0,s.jsx)(J.o,{...d}),ee.y.includes(r.type)&&(0,s.jsx)(H.o,{...d}),r.type===q.DatabaseFieldDataType.Checkbox&&(0,s.jsx)($.D,{...d}),r.type===q.DatabaseFieldDataType.Select&&(0,s.jsx)(Q.f,{...d}),ee.e_.includes(r.type)&&(0,s.jsx)(K.p,{...d}),r.type===q.DatabaseFieldDataType.Linked&&(0,s.jsx)(Y.aL,{...d}),ee.VI.includes(r.type)&&(0,s.jsx)(G.T,{...d}),r.type===q.DatabaseFieldDataType.Files&&(0,s.jsx)(X.h,{...d})]})})]},t)}),(0,s.jsx)("div",{className:"text-xs font-medium text-muted-foreground mt-3",children:"Note: Fields with empty/undefined values will be ignored"})]})})}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(n.z,{variant:"ghost",onClick:()=>{let e=[...i];e.push({columnId:""}),r(e)},className:"mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1",children:"Add field"}),(0,s.jsx)("div",{className:"flex-1"}),(0,s.jsxs)(n.z,{disabled:d,onClick:p,className:"mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1",children:[d&&(0,s.jsx)(x.a,{className:"size-3"}),"Save"]})]})]})]})})};var et=t(17807),es=t(39255),en=t(54886),el=t(27648),ei=t(23675),er=t(65337);let ed=e=>{let[a,t]=(0,l.useState)(!1),n={view:e.view,page:e.page,domain:e.domain,documentId:e.documentId},i=[{title:"Share Link",content:(0,s.jsx)(ec,{...n}),id:"share-link"},{title:"Publish",content:(0,s.jsx)(eo,{...n}),id:"publish"},{title:"Embed",content:(0,s.jsx)(eu,{...n}),id:"embed"}];return(0,s.jsxs)(g.h_,{open:a,onOpenChange:t,children:[(0,s.jsx)(g.$F,{asChild:!0,children:e.trigger}),(0,s.jsx)(g.AW,{className:"min-w-[450px] p-0 rounded-none",align:e.triggerAlign||"end",children:(0,s.jsx)("div",{className:"min-h-20",children:(0,s.jsx)(en.x,{tabs:i,tabSwitcherClassName:"px-2"})})})]})},eo=e=>{let{view:a,page:t,domain:i}=e,{toast:d}=(0,j.V)(),{updateView:c}=(0,v.Bf)(),[u,x]=(0,l.useState)(!1),{id:m,name:h,isPublished:f,pageId:g,description:N}=a,w=(0,p.Bw)(m,h),y=e=>{x(!0),c(a,{name:h,isPublished:f,description:N,...e}).then(()=>{d("View published")}).finally(()=>{x(!1)})};return(0,s.jsxs)("div",{className:"size-full p-4 pb-8 text-center flex flex-col gap-2.5",children:[(0,s.jsx)("h2",{className:"text-base font-bold",children:"Publish View"}),(0,s.jsx)("p",{className:"text-xs font-medium",children:"Make this view publicly available without a signing in."}),a.isPublished&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(r.I,{value:w,className:"text-xs rounded-none",readOnly:!0})}),(0,s.jsx)("div",{className:"w-auto flex justify-center gap-2",children:a.isPublished?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.z,{variant:"ghost",asChild:!0,className:"text-xs rounded-full p-2 px-3 h-auto gap-2 font-semibold -mx-1",children:(0,s.jsxs)(el.default,{href:w,target:"_blank",children:[(0,s.jsx)(o.IYJ,{className:"size-3"}),"Visit link"]})}),(0,s.jsxs)(n.z,{variant:"outline",className:"text-xs rounded-full p-2 px-3 h-auto gap-2 font-semibold",onClick:()=>{(0,b.vQ)(w),d.success("Link copied to clipboard")},children:[(0,s.jsx)(o.cKz,{className:"size-3"}),"Copy Link"]}),(0,s.jsxs)(n.z,{variant:"destructive",disabled:u,className:"text-xs rounded-full p-2 px-3 h-auto gap-2 font-semibold",onClick:()=>y({isPublished:!1}),children:[(0,s.jsx)(o.f1w,{className:"size-3"}),"Unpublish"]})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(n.z,{className:"text-xs rounded-full p-2 px-3 h-auto gap-2 font-semibold",disabled:u,onClick:()=>y({isPublished:!0}),children:[(0,s.jsx)(o.n9J,{className:"size-3"}),"Publish"]})})})]})},ec=e=>{let{view:a,page:t,domain:l,documentId:i}=e,{toast:r}=(0,j.V)();return(0,s.jsxs)("div",{className:"size-full p-4 pb-8 text-center flex flex-col gap-2.5",children:[(0,s.jsx)("h2",{className:"text-base font-bold",children:"Share Link"}),(0,s.jsx)("p",{className:"text-xs font-medium",children:"Share a direct link for your team to access this view easily."}),(0,s.jsx)("div",{className:"w-auto flex justify-center",children:(0,s.jsxs)(n.z,{className:"text-xs rounded-full p-2 px-3 h-auto gap-2 font-semibold",onClick:()=>{let e=(0,p.Uq)(l,t,a.id,i);(0,b.vQ)(e),r.success("Link copied to clipboard")},children:[(0,s.jsx)(o.cKz,{className:"size-3"}),"Copy Link"]})})]})},eu=e=>{let{view:a,page:t,domain:i}=e,{toast:r}=(0,j.V)(),{updateView:d}=(0,v.Bf)(),[c,u]=(0,l.useState)(!1),{id:x,name:m,isPublished:h,pageId:f,description:g}=a,N=(0,er.Z)(),w=(0,p.cI)(x,m),y=e=>{u(!0),d(a,{name:m,isPublished:h,description:g,...e}).then(()=>{r("View published")}).finally(()=>{u(!1)})};return(0,s.jsxs)("div",{className:"size-full p-4 pb-8 text-center flex flex-col gap-2.5",children:[(0,s.jsx)("h2",{className:"text-base font-bold",children:"Embed View"}),(0,s.jsx)("p",{className:"text-xs font-medium",children:"Embed this view to your website for a smoother, integrated experience."}),a.isPublished&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(ei.g,{value:w,ref:N,className:"text-xs rounded-none",readOnly:!0})}),(0,s.jsx)("div",{className:"w-auto flex justify-center gap-2",children:a.isPublished?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(n.z,{variant:"outline",className:"text-xs rounded-full p-2 px-3 h-auto gap-2 font-semibold",onClick:()=>{(0,b.vQ)(w),r.success("Embed code copied to clipboard")},children:[(0,s.jsx)(o.dNJ,{className:"size-3"}),"Copy Embed Code"]}),(0,s.jsxs)(n.z,{variant:"destructive",disabled:c,className:"text-xs rounded-full p-2 px-3 h-auto gap-2 font-semibold",onClick:()=>y({isPublished:!1}),children:[(0,s.jsx)(o.f1w,{className:"size-3"}),"Unpublish"]})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(n.z,{className:"text-xs rounded-full p-2 px-3 h-auto gap-2 font-semibold",disabled:c,onClick:()=>y({isPublished:!0}),children:[(0,s.jsx)(o.n9J,{className:"size-3"}),"Publish"]})})})]})};var ex=t(91032),em=t(35579),eh=t(54207),ef=t(36109),ep=t(14803),ej=t(83251),eb=t(24190),ev=t(12749),eg=t(18626),eN=t(95422),ew=t(92326),ey=t(32469);let eF=(0,l.createContext)({context:"page"}),eC=()=>(0,l.useContext)(eF),ek=e=>{var a,t,p,b,g;let{url:N}=(0,m.cF)(),{token:w}=(0,es.a)(),y=(0,h.useSearchParams)().get("documentId"),k=(0,h.useRouter)(),{viewsMap:z,accessLevel:I,page:D}=(0,c.qt)(),[S,T]=(0,l.useState)(!1),{createRecords:_,deleteRecords:R,smartUpdateViewDefinition:B,peekRecordId:U,setPeekRecordId:Z}=(0,v.Bf)(),{filter:J,sorts:H,search:$,setFilter:Q,setSorts:K,setSearch:Y}=(0,v.Jy)(),{selectedIds:G,setSelectedIds:X}=(0,v.eX)(),{databaseStore:ee,workspace:en}=(0,m.cF)(),{toast:el}=(0,j.V)(),ei=(0,W.nM)(),er=(0,em.pB)(),{openRecord:eo}=(0,ey.x)(),[ec,eu]=(0,l.useState)(!1),ex=null;if("record_tab"===e.context){let a=null===(g=ee[e.parentId])||void 0===g?void 0:g.database;a&&a.meta&&a.meta.recordViewsMap&&(ex=a.meta.recordViewsMap[e.viewId]||null)}ex||(ex=z[e.viewId]||null);let eh=null==ex?void 0:ex.type,ef=a=>{let t="record_tab"===e.context,s=t?e.parentId:void 0;return B((null==ex?void 0:ex.id)||"",(null==ex?void 0:ex.pageId)||"",a,{databaseId:s,isRecordTab:t})},ep=I&&[u.u.Full,u.u.Edit].includes(I),ej=I&&I===u.u.Full,eb={conditions:[],match:q.Match.All};(eh===f.bW.Board||eh===f.bW.Table)&&(eb=ex.definition.filter||eb);let ev=async()=>{var e,a;if(!ex)return;let t=ex.definition;if(!t.databaseId)return;let s=await _(t.databaseId,[{}]),n=$&&$.trim()||(null==J?void 0:null===(e=J.conditions)||void 0===e?void 0:e.length)>0||eb&&eb.conditions&&(null==eb?void 0:null===(a=eb.conditions)||void 0===a?void 0:a.length)>0;s&&s.records&&s.records.length>0&&n&&Z(s.records[0].id)},eg=async()=>{if(!ex||ex.type!==f.bW.Board&&ex.type!==f.bW.Table)return;let e=ex.definition;if(!e.databaseId)return;let{databaseId:a}=e;await R(a,G)},eN=null,eC="";ex&&[f.bW.Table,f.bW.Board,f.bW.SummaryTable,f.bW.Form,f.bW.Calendar,f.bW.ListView].includes(eh)&&(eN=ee[eC=ex.definition.databaseId]?ee[eC].database:null);let ek=(0,l.useRef)(e.viewId||""),eS=e.viewId,eT=(null==ex?void 0:ex.pageId)||"",eW=en.workspace.id,eA=!!ex;return(0,l.useEffect)(()=>{if(!eA)return;let e=setTimeout(async()=>{let e=eC||void 0;await (0,et.AB)((null==w?void 0:w.token)||"",{workspaceId:eW,pageId:eT,event:et.tw.View,databaseId:e,viewId:eS})},3e3);return()=>{e&&clearTimeout(e)}},[eC,null==w?void 0:w.token,eW,eT,eS,eA]),(0,l.useEffect)(()=>{e.viewId!==ek.current&&(ek.current=e.viewId,K([]),Q({conditions:[],match:q.Match.All}),Y(""),X([]),Z(""))},[e.viewId]),(0,s.jsxs)(eF.Provider,{value:{context:e.context},children:[!ex&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(x.PageLoader,{size:"full",error:"The requested content does not exists",cta:{label:"Go Home",onClick:()=>k.replace(N())}})}),ex&&(0,s.jsxs)("div",{className:"w-full h-full overflow-hidden flex flex-col",children:[eh!==f.bW.Calendar&&(0,s.jsxs)("div",{className:"p-2 h-12 flex items-center border-b border-neutral-300 gap-0.5",children:["record_tab"!==e.context&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(A,{context:e.context,viewId:e.viewId,creatable:ep,editable:ep,deletable:ej,cloneable:ej,requestNewView:()=>T(!0)}),ep&&(0,s.jsx)(C._,{context:e.context,open:S,setOpen:T}),!ei&&(0,s.jsx)(ed,{view:ex,page:D,documentId:y||"",domain:en.workspace.domain,triggerAlign:"start",trigger:(0,s.jsxs)(n.z,{variant:"ghost",className:(0,M.cn)("text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium",ex.isPublished&&"text-blue-600 font-semibold"),children:[(0,s.jsx)(o.$mL,{className:"size-3"})," Share View"]})}),"page"===e.context&&eN&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(ez,{database:eN})}),(0,s.jsx)("div",{className:"flex-1"})]}),"record_tab"===e.context&&(0,s.jsx)("div",{className:"flex-1"}),eN&&[f.bW.Table,f.bW.Board].includes(eh)&&(0,s.jsx)(s.Fragment,{children:!ex.definition.lockContent&&(0,s.jsxs)(s.Fragment,{children:[ep&&eh===f.bW.Table&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(n.z,{variant:"ghost",onClick:()=>{ex&&(ex.type===f.bW.Board||ex.type===f.bW.Table)&&("record_tab"===e.context?eu(!0):ev())},className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,s.jsx)(o.oFk,{className:"size-3"}),"Add"]})}),G.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("span",{className:"font-semibold text-xs text-blue-600 select-none",children:[G.length," \xa0selected"]}),(0,s.jsx)(ea,{trigger:(0,s.jsxs)(n.z,{variant:"ghost",className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,s.jsx)(o.VMh,{className:"size-3"}),"Update"]}),database:eN,ids:G,onUpdate:()=>X([])}),(0,s.jsxs)(n.z,{variant:"ghost",onClick:eg,className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,s.jsx)(o.N20,{className:"size-3"}),"Delete"]})]})]})}),eN&&[f.bW.Table,f.bW.Board,f.bW.SummaryTable,f.bW.ListView,f.bW.Calendar].includes(eh)?(0,s.jsxs)(s.Fragment,{children:[!1,ep&&"record_tab"!==e.context&&(0,s.jsx)(L.W,{database:eN,view:ex}),(0,s.jsx)(V.o,{database:eN,trigger:(0,s.jsxs)(n.z,{variant:"ghost",className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,s.jsx)(o.hQu,{className:"size-3"}),J.conditions.length>0?"".concat(J.conditions.length," filters"):"Filter"]}),filter:J,onChange:Q,currentRecordId:null==er?void 0:er.recordInfo.record.id,currentRecordDatabaseId:null==er?void 0:er.recordInfo.record.databaseId}),(eh===f.bW.Table||eh===f.bW.SummaryTable)&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(P.H,{database:eN,sorts:H,onChange:K,trigger:(0,s.jsxs)(n.z,{variant:"ghost",className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,s.jsx)(o.E92,{className:"size-3"}),H.length>0?"".concat(H.length," sorts"):"Sort"]})})}),(0,s.jsxs)("div",{className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-accent focus:bg-accent active:bg-accent items-center whitespace-nowrap font-medium",children:[(0,s.jsx)(d._,{form:"search-input",children:(0,s.jsx)(i.Z,{className:"size-4"})}),(0,s.jsx)(r.I,{placeholder:"Search",value:$,onChange:e=>Y(e.target.value),className:"text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none"})]}),(0,s.jsx)(eD,{onRecordScan:Z,database:eN,viewFilter:eb,filter:J}),ep&&eh!==f.bW.SummaryTable&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(F,{onDefinitionUpdate:e=>ef(e),view:ex})}),ep&&eh===f.bW.SummaryTable&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(O.Bo,{onDefinitionUpdate:e=>ef(e),view:ex}),(0,s.jsx)(O.Mj,{onDefinitionUpdate:e=>ef(e),view:ex})]}),ep&&(0,s.jsx)(E.c7,{disabled:!ep,view:ex,database:eN,selectedIds:G,filter:J,sorts:H,search:$,onDefinitionUpdate:e=>ef(e),currentRecordId:null==er?void 0:null===(t=er.recordInfo)||void 0===t?void 0:null===(a=t.record)||void 0===a?void 0:a.id,currentRecordDatabaseId:null==er?void 0:null===(b=er.recordInfo)||void 0===b?void 0:null===(p=b.record)||void 0===p?void 0:p.databaseId})]}):(0,s.jsx)(s.Fragment,{}),ep&&eN&&eh===f.bW.Form&&"record_tab"!==e.context&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(E.fu,{disabled:!ep,view:ex,database:eN,onDefinitionUpdate:e=>ef(e)})}),ep&&eh===f.bW.Dashboard&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(E.Qz,{disabled:!ep,view:ex,onDefinitionUpdate:e=>ef(e)})}),ep&&eh===f.bW.Document&&"record_tab"!==e.context&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(E.Yi,{disabled:!ep,view:ex,onDefinitionUpdate:e=>ef(e)})})]}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden pr=1",children:e.children})]}),eN&&U&&!er&&(0,s.jsx)(eI,{canEdit:ep,onClose:()=>Z(""),recordId:U,databaseId:eN.id}),eN&&ec&&(0,s.jsx)(ew.S,{open:ec,onClose:()=>eu(!1),databaseId:eN.id,viewFilter:eb||void 0,contextualFilter:J,onRecordCreated:e=>{if(!ex)return;let a=ex.definition;a.databaseId&&(eo(e,a.databaseId),eu(!1))}})]})},ez=e=>{let{database:a}=e,{databasePageStore:t,databasePagesId:i}=(0,m.cF)(),r=t[a.id],c=a.id,u=(0,l.useMemo)(()=>{let e=[],a=[...i];for(let s of(a.includes(c)||a.push(c),a)){let a=t[s];if(!a)continue;let{page:n}=a,l=n.icon&&n.icon.type===_.ObjectType.Emoji?n.icon.emoji:"\uD83D\uDCD5",i={color:void 0,data:void 0,id:s,title:"".concat(l," ").concat(a.page.name),value:s};e.push(i)}return e},[t,i,c]);if(!r)return null;let{page:x}=r,h=x.icon&&x.icon.type===_.ObjectType.Emoji?x.icon.emoji:"\uD83D\uDCD5",f=(0,s.jsxs)(s.Fragment,{children:[h," \xa0 ",r.page.name]});return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(g.h_,{children:[(0,s.jsx)(g.$F,{asChild:!0,children:(0,s.jsxs)(n.z,{variant:"outline",className:(0,M.cn)("text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start max-w-48 items-center"),children:[(0,s.jsx)(o.VHe,{className:"size-3"}),(0,s.jsx)("span",{className:"truncate text-black font-medium",children:f})]})}),(0,s.jsx)(g.AW,{className:"p-0 rounded-none min-w-80",align:"start",children:(0,s.jsxs)("div",{className:"p-2 pt-0",children:[(0,s.jsx)(d._,{htmlFor:"db-select",className:"text-xs text-neutral-500",children:"Source database"}),(0,s.jsx)(R.A,{onChange:e=>{},disabled:!0,selectedIds:[c],placeholder:"Choose a database",options:u})]})})]})})},eI=e=>{let{databaseStore:a,members:t,url:l}=(0,m.cF)(),i=(0,W.nM)(),r=(0,ep.cL)(),d=a[e.databaseId],c=a[e.databaseId].recordsIdMap[e.recordId];if(!d||!c)return null;let u=null,x=(0,eh.Gq)(t),h=Object.values(d.database.definition.columnsMap).filter(e=>e.type===q.DatabaseFieldDataType.Linked&&e.databaseId).map(e=>e.databaseId),f={};for(let e of h){let t=a[e];if(t)for(let a of(f[e]={id:e,definition:t.database.definition,recordsMap:{},srcPackageName:t.database.srcPackageName},Object.values(t.recordsIdMap)))f[e].recordsMap[a.record.id]=a.record}let p=[c.record];u=(0,ef.transformRawRecords)(d.database.definition,p,x,f)[0];let j=l("/databases/".concat(c.record.databaseId,"/records/").concat(c.record.id));return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(ex.yo,{defaultOpen:!0,onOpenChange:a=>{if(!a){var t;null===(t=e.onClose)||void 0===t||t.call(e)}},children:[(0,s.jsx)(ex.aM,{asChild:!0,children:e.trigger}),(0,s.jsxs)(ex.ue,{className:"!w-[50vw] !min-w-[400px] !max-w-full bg-white p-0 pt-8",children:[!r&&!i&&(0,s.jsx)(n.z,{variant:"ghost",asChild:!0,className:"absolute right-12 top-2.5 !size-6 !p-1.5 rounded-full",children:(0,s.jsx)(el.default,{href:j,children:(0,s.jsx)(o.dlb,{className:"size-full"})})}),(0,s.jsxs)("div",{className:"size-full flex flex-col overflow-hidden",children:[(0,s.jsxs)(ex.Tu,{className:"hidden",children:[(0,s.jsx)(ex.bC,{className:"font-bold text-base",children:"Peek Record"}),(0,s.jsx)(ex.Ei,{className:"hidden",children:"Make changes to your record here. Click save when you're done."})]}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,s.jsx)(eN.ls,{children:(0,s.jsx)(em.HV,{recordInfo:{...c,processedRecord:u},database:d.database,children:(0,s.jsx)(ej.j,{showOverview:!0})})})}),(0,s.jsx)(ex.FF,{className:"hidden",children:(0,s.jsx)(ex.sw,{asChild:!0,children:(0,s.jsx)(n.z,{type:"submit",className:"rounded-full",children:"Save"})})})]})]})]})})},eD=e=>{let{database:a,filter:t,viewFilter:l,onRecordScan:i}=e,{toast:r}=(0,j.V)(),{databaseStore:d,members:c,workspace:u}=(0,m.cF)(),x=d[a.id],h=!1;for(let e of Object.values(a.definition.columnsMap))if(e.type===q.DatabaseFieldDataType.ScannableCode){h=!0;break}return h?(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(ev.J2,{children:[(0,s.jsx)(ev.xo,{asChild:!0,children:(0,s.jsx)(n.z,{variant:"ghost",className:"text-xs rounded-full p-1 size-7 gap-2 justify-center items-center font-medium",children:(0,s.jsx)(o.nGV,{className:"size-3"})})}),(0,s.jsxs)(ev.yk,{className:"w-80 p-4",children:[(0,s.jsx)("div",{children:(0,s.jsx)("h4",{className:"mb-4 text-xs font-medium",children:"Find record by scanning barcode/QR code"})}),(0,s.jsx)("div",{children:(0,s.jsx)(eb.ZY,{allowMultiple:!0,onScan:e=>{let a=e[0];if(!a){r.error("Nothing to search");return}let s=a.rawValue;if(!s){r.error("Nothing to search");return}let{rows:n}=(0,eg.filterAndSortRecords)(x,c,d,l,t,[],u.workspaceMember.userId);console.log("Scanned value: ",{rawValue:s,rows:n});let o=(0,eg.searchFilteredRecords)(s,n);if(!o||0===o.length){r.error("No records found");return}i(o[0].record.id)}})})]})]})}):null}},28942:function(e,a,t){t.d(a,{_:function(){return z}});var s=t(57437),n=t(93427),l=t(23500),i=t(39255),r=t(29119),d=t(42212),o=t(2265),c=t(84977),u=t(99376),x=t(41426),m=t(68738),h=t(14327),f=t(3163),p=t(32060),j=t(12381),b=t(20029),v=t(75060),g=t(40279),N=t(78585),w=t(18055),y=t(36675),F=t(24681),C=t(93448),k=t(95473);let z=e=>{let{token:a}=(0,i.a)(),{page:t,views:z}=(0,r.qt)(),{workspace:I,databasePageStore:D,databaseStore:S,databasePagesId:T,updatePageViews:W,url:A}=(0,d.cF)(),{createRecordTabView:V}=(0,k.Bf)(),P=(0,n.qA)(l.bW,!0,[]);if("record_tab"===e.context){let e=[l.bW.Table,l.bW.Board,l.bW.Dashboard,l.bW.ListView,l.bW.Calendar];P=(0,n.qA)(l.bW,!0,[]).filter(a=>e.includes(a.value))}let[E,O]=(0,o.useState)(P[0].value),[M,_]=(0,o.useState)("Untitled"),[R,L]=(0,o.useState)(t.databaseId||""),[B,q]=(0,o.useState)([]),[U,Z]=(0,o.useState)(!1),{toast:J}=(0,c.V)(),H=(0,u.useRouter)(),[$,Q]=(0,o.useState)(""),[K,Y]=(0,o.useState)(""),[G,X]=(0,o.useState)(60),ee=(()=>{let e=[],a=[...T];for(let t of(a.includes(R)||a.push(R),a)){let a=D[t];if(!a)continue;let{page:s}=a,n=s.icon&&s.icon.type===x.ObjectType.Emoji?s.icon.emoji:"\uD83D\uDCD5",l={color:void 0,data:void 0,id:t,title:"".concat(n," ").concat(a.page.name),value:t};e.push(l)}return e})(),ea=(()=>{let e=[];if(!R||!S[R])return e;for(let a of Object.values(S[R].database.definition.columnsMap)){let t={data:a,id:a.id,title:a.title,value:a.id,titleNode:(0,s.jsxs)("div",{className:"flex gap-1.5 overflow-hidden items-center",children:[(0,s.jsx)(F.e,{type:a.type}),(0,s.jsx)("div",{className:"flex-1 truncate",children:a.title})]})};e.push(t)}return e})(),et=!(!M||![l.bW.Document,l.bW.Dashboard].includes(E)&&!R||[l.bW.Board,l.bW.SummaryTable].includes(E)&&0===B.length)&&(E!==l.bW.Calendar||!!$),es=async()=>{if(!et||!a)return;let s=null;switch(E){case l.bW.Table:s={type:l.bW.Table,databaseId:R,filter:{match:m.Match.All,conditions:[]},sorts:[],columnPropsMap:{},columnsOrder:[]};break;case l.bW.Board:s={type:l.bW.Board,databaseId:R,filter:{match:m.Match.All,conditions:[]},sorts:[],groupByIds:B,columnPropsMap:{},columnsOrder:[],groupOrder:[]};break;case l.bW.Form:s={type:l.bW.Form,databaseId:R,columnsOrder:[],columnPropsMap:{}};break;case l.bW.Document:s={type:l.bW.Document};break;case l.bW.Dashboard:s={type:l.bW.Dashboard,definition:{children:[],elementMap:{},rowsMap:{}}};break;case l.bW.SummaryTable:s={type:l.bW.SummaryTable,databaseId:R,columns:[],filter:{match:m.Match.All,conditions:[]},sorts:[],groupByIds:B};break;case l.bW.ListView:s={type:l.bW.ListView,databaseId:R,filter:{match:m.Match.All,conditions:[]},sorts:[],columnPropsMap:{},columnsOrder:[]};break;case l.bW.Calendar:s={type:l.bW.Calendar,databaseId:R,eventStartColumnId:$,eventEndColumnId:K||void 0,defaultDuration:G,filter:{match:m.Match.All,conditions:[]},sorts:[],columnPropsMap:{},columnsOrder:[]};break;default:J.error("Unknown view type");return}if(!s)return;if("record_tab"===e.context){Z(!0);let a=t.databaseId||R,n=await V(a,R,M,E,s);if(Z(!1),!n)return;e.setOpen(!1),e.onViewCreated&&e.onViewCreated(n);return}Z(!0);let n=await (0,h.Rl)(a.token,I.workspace.id,t.id,{name:M,type:E,definition:s});if(Z(!1),n.error){J.error(n.error||(0,f.E)());return}e.setOpen(!1);let{view:i}=n.data.data;if(e.onViewCreated){e.onViewCreated(i);return}let r=[...t.viewsOrder||[],i.id],d=[...z,i];"database"===e.context?W("database",t.databaseId,r,d):"page"===e.context&&W("page",t.id,r,d);let o="database"===e.context?A("/databases/".concat(t.databaseId,"/views/").concat(i.id)):A("/".concat(t.id,"/views/").concat(i.id));H.push(o)};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(p.h_,{open:e.open,onOpenChange:e.setOpen,children:[!e.hideTrigger&&(0,s.jsx)(p.$F,{asChild:!0,children:(0,s.jsxs)(j.z,{variant:"ghost",className:(0,C.cn)("text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start",e.triggerClassName),children:[(0,s.jsx)(b.oFk,{className:"size-3"}),"New View"]})}),(0,s.jsxs)(p.AW,{className:" p-0 rounded-none w-screen lg:w-full lg:min-w-[850px]",side:e.dropdownSide,align:e.dropdownAlign||"start",children:[(0,s.jsxs)("div",{className:"p-4 py-2 flex flex-col gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(v._,{htmlFor:"name",className:"text-xs text-neutral-500",children:"View name"}),(0,s.jsx)(g.I,{id:"name",placeholder:"Acme Inc.",className:"rounded-none mt-1 text-xs font-medium",autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",spellCheck:"false",value:M,onChange:e=>_(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(v._,{htmlFor:"viewType",className:"text-xs text-neutral-500",children:"View type"}),(0,s.jsx)("div",{className:"mt-1 lg:flex",children:(0,s.jsx)(N.t,{type:"single",className:"w-full flex-col gap-1 lg:flex-row lg:gap-0",value:E,id:"viewType",onValueChange:e=>O(e),children:P.map(e=>{let a=e.value,t="view-".concat(a);return(0,s.jsx)(N.G,{variant:"outline",value:a,id:t,className:"p-3 block w-full items-center rounded-none h-auto overflow-hidden text-neutral-500 first:border-l lg:border-l-0 lg:flex-1",children:(0,s.jsxs)("div",{className:"flex lg:flex-col items-center gap-2 overflow-hidden",children:[(0,s.jsx)(w.O,{type:a,className:"size-4"}),(0,s.jsx)(v._,{htmlFor:t,className:"text-xs font-semibold",children:e.label})]})},a)})})})]}),"database"!==e.context&&![l.bW.Dashboard,l.bW.Document].includes(E)&&(0,s.jsxs)("div",{children:[(0,s.jsx)(v._,{htmlFor:"db-select",className:"text-xs text-neutral-500",children:"Choose database"}),(0,s.jsx)(y.A,{onChange:e=>{L(e[0]),q([])},selectedIds:[R],placeholder:"Choose a database",options:ee})]}),[l.bW.SummaryTable,l.bW.Board].includes(E)&&ea&&R&&(0,s.jsxs)("div",{children:[(0,s.jsx)(v._,{htmlFor:"group-by",className:"text-xs text-neutral-500",children:E===l.bW.SummaryTable?"Summarize by":"Group by"}),(0,s.jsx)(y.A,{onChange:e=>q(e),selectedIds:B,placeholder:E===l.bW.SummaryTable?"Choose columns":"Choose column",isMulti:E===l.bW.SummaryTable,options:E===l.bW.Board?ea.filter(e=>e.data.type===m.DatabaseFieldDataType.Select):ea})]}),E===l.bW.Calendar&&ea&&R&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(v._,{htmlFor:"event-start",className:"text-xs text-neutral-500",children:"Event Start Date/Time Column"}),(0,s.jsx)(y.A,{onChange:e=>Q(e[0]),selectedIds:[$],placeholder:"Choose date column",options:ea.filter(e=>e.data.type===m.DatabaseFieldDataType.Date)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(v._,{htmlFor:"event-end",className:"text-xs text-neutral-500",children:"Event End Date/Time Column (Optional)"}),(0,s.jsx)(y.A,{onChange:e=>Y(e[0]),selectedIds:[K],placeholder:"Choose date column",options:ea.filter(e=>e.data.type===m.DatabaseFieldDataType.Date)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(v._,{htmlFor:"default-duration",className:"text-xs text-neutral-500",children:"Default Duration (minutes)"}),(0,s.jsx)(g.I,{id:"default-duration",type:"number",min:"1",className:"rounded-none mt-1 text-xs font-medium",value:G,onChange:e=>X(parseInt(e.target.value)||60)})]})]})]}),(0,s.jsx)(p.VD,{}),(0,s.jsxs)("div",{className:"flex gap-2 p-4 pt-2 justify-end",children:[(0,s.jsx)(j.z,{variant:"ghost",onClick:()=>e.setOpen(!1),className:"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold",children:"Cancel"}),(0,s.jsx)(j.z,{disabled:!et||U,onClick:es,className:"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold",children:"Create"})]})]})]})})}},39826:function(e,a,t){t.d(a,{_:function(){return v}});var s=t(57437),n=t(84977),l=t(2265),i=t(14327),r=t(79676),d=t(32060),o=t(12381),c=t(93448),u=t(40178),x=t(98116),m=t(95473),h=t(3163),f=t(32852),p=t(59315),j=t(99376),b=t(42212);let v=e=>{let{toast:a,confirm:t}=(0,n.V)(),[v,g]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),{updateView:y,deleteView:F}=(0,m.Bf)(),{url:C}=(0,b.cF)(),{socket:k,isConnected:z}=(0,p.Kn)(),I=(0,j.useRouter)(),{id:D,name:S,isPublished:T,pageId:W,description:A}=e.view,V=a=>{w(!1),g(!0),y(e.view,{name:S,isPublished:T,description:A,...a}).finally(()=>{g(!1)})},P=e=>{a.error(e)},E=async()=>{if(!z||!k){a.error((0,h.E)());return}let t=await k.timeout(5e3).emitWithAck(f.S.DuplicatePageView,{id:D,pageId:e.view.pageId});if("ok"!==t.status){P(t.message||(0,h.E)());return}t.data.page.databaseId?I.push(C("/databases/".concat(t.data.page.databaseId,"/views/").concat(t.data.view.id))):I.push(C("/".concat(t.data.page.id,"/views/").concat(t.data.view.id)))};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(d.h_,{open:N,onOpenChange:w,children:[(0,s.jsx)(d.$F,{asChild:!0,children:(0,s.jsx)(o.z,{variant:"ghost",className:(0,c.cn)("rounded-full h-auto p-1",e.triggerClassName||"",N&&"block visible"),children:(0,s.jsx)(u.Z,{className:"size-3"})})}),(0,s.jsxs)(d.AW,{className:"w-56  rounded-none text-neutral-800 font-semibold",align:"start",side:"right",children:[(0,s.jsxs)(d.Qk,{className:"p-1 pb-0",children:[(0,s.jsx)(x.h,{placeHolder:"Name",value:e.view.name,onChange:e=>{e&&V({name:e})},shortEnter:!0,disabled:v,wrapperClassname:"h-8 p-1"}),(0,s.jsx)(d.Xi,{className:"text-xs rounded-none p-1.5 h-7 my-2",onClick:()=>V({isPublished:!T}),children:T?"Unpublish":"Publish"}),T&&(0,s.jsx)(d.Xi,{asChild:!0,className:"text-xs rounded-none p-1.5 h-7 my-2 w-full",children:(0,s.jsx)("button",{onClick:()=>{let e=(0,i.Bw)(D,S);(0,r.vQ)(e),a.success("Link copied to clipboard")},children:"Copy sharable link"})})]}),e.cloneable&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.VD,{className:""}),(0,s.jsx)(d.Qk,{className:"p-1",children:(0,s.jsx)(d.Xi,{className:"text-xs rounded-none p-1.5 h-7 ",onClick:E,children:"Duplicate"})})]}),e.deletable&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.VD,{className:""}),(0,s.jsx)(d.Qk,{className:"p-1",children:(0,s.jsx)(d.Xi,{className:"text-xs rounded-none p-1.5 h-7 ",onClick:()=>{w(!1),F(e.view).finally()},children:"Delete"})})]})]})]})})}},18055:function(e,a,t){t.d(a,{O:function(){return r}});var s=t(57437),n=t(23500);t(2265);var l=t(93448),i=t(20029);let r=e=>{let a=e.type,t=e.className||"";return(0,s.jsx)(s.Fragment,{children:a===n.bW.Table?(0,s.jsx)(i.kRt,{className:(0,l.cn)("size-4",t)}):a===n.bW.Form?(0,s.jsx)(i.IRc,{className:(0,l.cn)("size-4",t)}):a===n.bW.Board?(0,s.jsx)(i.No2,{className:(0,l.cn)("size-4",t)}):a===n.bW.SummaryTable?(0,s.jsx)(i.ri,{className:(0,l.cn)("size-4",t)}):a===n.bW.Document?(0,s.jsx)(i.vJ3,{className:(0,l.cn)("size-4",t)}):a===n.bW.Dashboard?(0,s.jsx)(i.Npz,{className:(0,l.cn)("size-4",t)}):a===n.bW.ListView?(0,s.jsx)(i.XyQ,{className:(0,l.cn)("size-4",t)}):a===n.bW.Calendar?(0,s.jsx)(i.kFL,{className:(0,l.cn)("size-4",t)}):(0,s.jsx)(i.kRt,{className:(0,l.cn)("size-4",t)})})}},25853:function(e,a,t){t.d(a,{ScreenSizeProvider:function(){return r},e:function(){return i}});var s=t(57437),n=t(2265);let l=(0,n.createContext)({isMobile:!1,isCollapsed:!1,setCollapsed:e=>{}}),i=()=>(0,n.useContext)(l),r=e=>{let{children:a}=e,[t,i]=(0,n.useState)(window.innerWidth<1024),[r,d]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let e=()=>{i(window.innerWidth<1024)};return window.addEventListener("resize",e),e(),()=>{window.removeEventListener("resize",e)}},[]),(0,s.jsx)(l.Provider,{value:{isMobile:t,isCollapsed:r,setCollapsed:d},children:a})}},8351:function(e,a,t){var s,n,l,i,r,d,o,c;t.d(a,{bt:function(){return i},c4:function(){return l},eZ:function(){return s},mL:function(){return n}}),(r=s||(s={})).All="all",r.First="first",(d=n||(n={})).Campaign="campaign",d.Sequence="sequence",(o=l||(l={})).Draft="draft",o.InReview="in_review",o.Published="published",o.Scheduled="scheduled",o.Queued="queued",o.Sending="sending",o.Failed="failed",o.Sent="sent",(c=i||(i={})).All="all",c.Opened="opened",c.Clicked="clicked",c.DidNotOpen="did_not_open",c.DidNotClick="did_not_click"}}]);