!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="66f9a025-e1a4-477e-a1b2-b4e11cff12da",e._sentryDebugIdIdentifier="sentry-dbid-66f9a025-e1a4-477e-a1b2-b4e11cff12da")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2949],{53731:function(e,t){var a=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;t.G=function(e){if(!e||e.length>254||!a.test(e))return!1;var t=e.split("@");return!(t[0].length>64||t[1].split(".").some(function(e){return e.length>63}))}},79820:function(e,t,a){a.d(t,{Zb:function(){return r},aY:function(){return i}});var l=a(57437),n=a(2265),s=a(93448);let r=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,l.jsx)("div",{ref:t,className:(0,s.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...n})});r.displayName="Card",n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,l.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",a),...n})}).displayName="CardHeader",n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,l.jsx)("h3",{ref:t,className:(0,s.cn)("font-semibold leading-none tracking-tight",a),...n})}).displayName="CardTitle",n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,l.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",a),...n})}).displayName="CardDescription";let i=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,l.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",a),...n})});i.displayName="CardContent",n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,l.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",a),...n})}).displayName="CardFooter"},39910:function(e,t,a){a.r(t),a.d(t,{CalendarView:function(){return e$}});var l=a(57437),n=a(2265),s=a(42212),r=a(68738),i=a(84440),d=a(29119),o=a(14803),c=a(52292),u=a(95473),m=a(18626),h=a(85994),x=a.n(h);a(21005);var f=a(93448);function g(e){let{className:t,mode:a="single",selected:n,onSelect:s,...r}=e;return(0,l.jsx)("div",{className:(0,f.cn)("r-date-edit-pop p-0 border-b border-neutral-200",t),children:(0,l.jsx)(x(),{selected:n,onChange:(e,t)=>{s&&e&&s(e)},inline:!0,showMonthDropdown:!0,showYearDropdown:!0,dropdownMode:"select",calendarClassName:"shadow-none border-0",...r})})}var v=a(12381),y=a(73903),p=a(6639),w=a(25721),b=a(13422),j=a(14613),N=a(10082),k=a(55463),D=a(67603),M=a(40279),C=a(14438),S=a(25853),E=a(35579),I=a(32469),T=a(88119),z=a(72526),A=a(25952),L=a(5867),O=a(68845),R=a(71205),Z=a(7656),F=a(50683);let B=e=>0>=function(e,t,a){(0,Z.Z)(2,arguments);var l=(0,R.Z)(e,t)/O.yJ;return(0,F.u)(null==a?void 0:a.roundingMethod)(l)}(new Date(e.end),new Date(e.start)),Y=(e,t,a)=>{let{isMobile:l=window.innerWidth<1024,shortFormat:n=!1}=a||{};return n||"month"===t&&l?(0,D.default)(e,"h:mma"):(0,D.default)(e,"h:mm a")},H=e=>null===e?"medium":e<40?"small":e<80?"medium":"large";var V=a(99735),_=a(29635);function W(e,t){var a=e.getFullYear()-t.getFullYear()||e.getMonth()-t.getMonth()||e.getDate()-t.getDate()||e.getHours()-t.getHours()||e.getMinutes()-t.getMinutes()||e.getSeconds()-t.getSeconds()||e.getMilliseconds()-t.getMilliseconds();return a<0?-1:a>0?1:a}function X(e,t){(0,Z.Z)(2,arguments);var a=(0,V.default)(e),l=(0,V.default)(t),n=W(a,l),s=Math.abs((0,_.default)(a,l));a.setDate(a.getDate()-n*s);var r=Number(W(a,l)===-n),i=n*(s-r);return 0===i?0:i}var P=a(30092),q=a(30702);let $=e=>{let t=new Date(e.start),a=new Date(e.end);return!(0,y.default)(t,a)},J=e=>{let t=new Date(e.start),a=new Date(e.end);if(!$(e))return[{id:"".concat(e.id,"-").concat((0,D.default)(t,"yyyy-MM-dd")),originalEventId:e.id,date:(0,p.default)(t),startTime:t,endTime:a,isFirstSegment:!0,isLastSegment:!0,isMiddleSegment:!1,segmentIndex:0,totalSegments:1,originalEvent:e,isAllDay:!1,isMultiDay:!1}];let l=[],n=X((0,P.default)(a),(0,p.default)(t))+1,s=(0,p.default)(t),r=(0,p.default)(a),i=0;for(;s<=r;){let d=0===i,o=(0,y.default)(s,r),c=!d&&!o,u=d?t:(0,p.default)(s),m=o?a:(0,P.default)(s),h=(m.getTime()-u.getTime())/36e5,x=h>=23||c&&h>=22;l.push({id:"".concat(e.id,"-").concat((0,D.default)(s,"yyyy-MM-dd")),originalEventId:e.id,date:s,startTime:u,endTime:m,isFirstSegment:d,isLastSegment:o,isMiddleSegment:c,segmentIndex:i,totalSegments:n,originalEvent:e,isAllDay:x,isMultiDay:!0}),s=(0,w.default)(s,1),i++}return l},G=(e,t)=>e.filter(e=>(0,y.default)(e.date,t)),Q=(e,t,a)=>e.filter(e=>(0,q.default)(e.date,{start:t,end:a})),K=e=>e.map(e=>er(e)).flatMap(e=>J(e)),U=e=>e.filter(e=>e.isAllDay||e.isMultiDay),ee=e=>e.filter(e=>!e.isAllDay),et=e=>{let t=e.startTime.getHours(),a=e.startTime.getMinutes();return Math.max(30,Math.min(60*e.endTime.getHours()+e.endTime.getMinutes(),1439)-(60*t+a))},ea=e=>(e.startTime.getHours(),e.startTime.getMinutes()),el=e=>{let t="rounded-md";if(!e.isMultiDay)return{roundedCorners:t,continuationIndicator:"",opacity:"opacity-100"};let a="",l="";return e.isFirstSegment&&e.isLastSegment?a=t:e.isFirstSegment?(a="rounded-l-md rounded-r-sm",l="pr-4"):e.isLastSegment?(a="rounded-r-md rounded-l-sm",l="pl-4"):(a="rounded-sm",l="pl-4 pr-4"),{roundedCorners:a,continuationIndicator:l,opacity:e.isMiddleSegment?"opacity-90":"opacity-100"}},en=(e,t)=>"month"===t?!e.isAllDay:!e.isAllDay&&(e.isFirstSegment||e.isLastSegment||!e.isMultiDay),es=e=>e.isMultiDay?e.isFirstSegment?"Continues for ".concat(e.totalSegments-1," more day").concat(e.totalSegments>2?"s":""):e.isLastSegment?"Continued from ".concat(e.segmentIndex," day").concat(e.segmentIndex>1?"s":""," ago"):"Day ".concat(e.segmentIndex+1," of ").concat(e.totalSegments):"",er=e=>{let t=new Date(e.start);if(new Date(e.end)<=t){let a=new Date(t.getTime()+18e5);return{...e,end:a}}return e};var ei=a(20029);let ed=e=>{let{segment:t,view:a,size:n="medium",className:s="",isEndOfEvent:r}=e;if(!t.isMultiDay)return null;let i=(0,f.cn)("inline-flex items-center justify-center font-medium text-xs","transition-colors duration-200",s),d="small"===n||"month"===a?(()=>{if("week"===a){let e=t.isFirstSegment;return e&&!r?(0,l.jsx)(ei.jAM,{className:"h-2 w-2"}):!e&&r?(0,l.jsx)(ei.YwX,{className:"h-2 w-2"}):e||r?null:(0,l.jsx)(ei.jVd,{className:"h-2 w-2"})}return t.isFirstSegment&&!t.isLastSegment?(0,l.jsx)(ei.jAM,{className:"h-2 w-2"}):!t.isFirstSegment&&t.isLastSegment?(0,l.jsx)(ei.YwX,{className:"h-2 w-2"}):t.isFirstSegment||t.isLastSegment?null:(0,l.jsx)(ei.jVd,{className:"h-2 w-2"})})():t.isFirstSegment?"1/".concat(t.totalSegments):t.isLastSegment?"".concat(t.totalSegments,"/").concat(t.totalSegments):"".concat(t.segmentIndex+1,"/").concat(t.totalSegments);return d?(0,l.jsx)("span",{className:(0,f.cn)(i,(()=>{switch(n){case"small":return"text-[0.6rem] px-1 py-0.5 min-w-4 h-4";case"large":return"text-xs px-2 py-1 min-w-6 h-6";default:return"text-[0.65rem] px-1.5 py-0.5 min-w-5 h-5"}})(),"bg-gray-100 text-gray-600 border border-gray-200","rounded-full shadow-sm"),title:(()=>{let e=t.originalEvent.title,l=Math.ceil(t.totalSegments/7);if("week"===a){let a=new Date(t.originalEvent.start),n=Math.floor(Math.abs(t.date.getTime()-a.getTime())/6048e5)+1;if(l>1)return"".concat(e," (Week ").concat(n," of ").concat(l,")")}return"Day ".concat(t.segmentIndex+1," of ").concat(t.totalSegments," - ").concat(e)})(),children:d}):null},eo=e=>{let{direction:t,className:a=""}=e;return(0,l.jsx)("span",{className:(0,f.cn)("inline-flex items-center justify-center","text-xs opacity-70 font-medium","transition-opacity duration-200",a),children:(()=>{switch(t){case"left":return(0,l.jsx)(ei.YwX,{className:"h-2 w-2"});case"right":return(0,l.jsx)(ei.jAM,{className:"h-2 w-2"});case"both":return(0,l.jsx)(ei.jVd,{className:"h-2 w-2"});default:return""}})()})};var ec=a(10795);let eu=e=>{let{segment:t,style:a,onClick:s,onContextMenu:r,view:i="month",isEndOfEvent:d,isDragging:o}=e,c=(0,n.useRef)(null),{attributes:u,listeners:m,setNodeRef:h,isDragging:x}=(0,ec.O1)({id:"segment-".concat(t.id),data:{type:"segment",payload:t},disabled:t.isMultiDay||t.isAllDay}),g=o||x,v=(0,n.useMemo)(()=>{let e=(null==a?void 0:a.height)?parseInt(a.height.toString().replace("px","")):null,l=en(t,i),n=es(t);return{eventSize:H(e),showTime:l,continuationText:n,formattedTime:l?Y(t.startTime,i,{shortFormat:!0}):null}},[t,a,i]),y=(0,n.useMemo)(()=>{let e=(0,L.S8)("Denim"),l=el(t),n=e.bg.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/),s=n?"rgb(".concat(n[1],", ").concat(n[2],", ").concat(n[3],")"):e.bg;return{style:{...a,backgroundColor:s,minHeight:"24px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)",opacity:g?.5:1},classes:l}},[a,t,g]),p=(0,n.useMemo)(()=>{let e=(0,f.cn)("select-none text-black text-xs overflow-hidden relative",!g&&"cursor-pointer",y.classes.roundedCorners,y.classes.continuationIndicator,y.classes.opacity,"p-1");return"month"===i||"small"===v.eventSize?{baseClasses:e,containerClasses:(0,f.cn)("flex items-center space-x-1 flex-nowrap"),titleClasses:(0,f.cn)("font-medium truncate leading-tight text-xs overflow-hidden",t.isMultiDay?"max-w-[60%]":"max-w-[70%]"),timeClasses:(0,f.cn)("opacity-75 text-xs flex-shrink-0 text-[0.65rem]"),continuationClasses:(0,f.cn)("text-xs opacity-60 flex-shrink-0 text-[0.6rem]")}:{baseClasses:(0,f.cn)(e,"p-2"),containerClasses:(0,f.cn)("flex flex-col","space-y-0.5"),titleClasses:(0,f.cn)("font-medium truncate leading-tight text-xs overflow-hidden"),timeClasses:(0,f.cn)("opacity-75 text-xs"),continuationClasses:(0,f.cn)("text-xs opacity-60")}},[v,i,t.isMultiDay,y.classes,g]);return(0,l.jsx)("div",{id:"event-".concat(t.originalEvent.id),ref:e=>{h(e),c.current=e},style:y.style,className:p.baseClasses,onClick:s,onContextMenu:r,...t.isMultiDay||t.isAllDay?{}:{...m,...u},children:(()=>{let e=t.originalEvent;return"month"===i||"small"===v.eventSize?(0,l.jsxs)("div",{className:p.containerClasses,children:[(0,l.jsx)("span",{className:p.titleClasses,children:e.title}),v.showTime&&v.formattedTime&&(0,l.jsx)("span",{className:p.timeClasses,children:v.formattedTime}),t.isMultiDay&&(0,l.jsx)(ed,{segment:t,view:i,size:"small",className:p.continuationClasses,isEndOfEvent:d})]}):(0,l.jsxs)("div",{className:p.containerClasses,children:[(0,l.jsxs)("div",{className:p.titleClasses,children:[e.title,t.isMultiDay&&!v.showTime&&(0,l.jsx)(eo,{direction:t.isFirstSegment?"right":t.isLastSegment?"left":"both",className:"ml-1"})]}),(v.showTime||t.isAllDay)&&v.formattedTime&&(0,l.jsxs)("div",{className:p.timeClasses,children:[v.formattedTime,t.isMultiDay&&(0,l.jsx)(eo,{direction:t.isFirstSegment?"right":t.isLastSegment?"left":"both",className:"ml-1"})]}),t.isMultiDay&&(0,l.jsx)("div",{className:p.continuationClasses,children:(0,l.jsx)(ed,{segment:t,view:i,size:"large"===v.eventSize?"medium":"small",isEndOfEvent:d})})]})})()})};var em=a(43836);let eh=e=>{let{title:t,message:a,showCreateButton:n,onCreate:s}=e;return(0,l.jsx)("div",{className:"flex-1 flex items-center justify-center bg-neutral-50",children:(0,l.jsxs)("div",{className:"text-center max-w-md mx-auto px-6",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center border border-neutral-300",children:(0,l.jsx)("svg",{className:"w-8 h-8 text-black",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z"})})}),(0,l.jsx)("h3",{className:"text-xs font-semibold text-black mb-2",children:t}),(0,l.jsx)("p",{className:"text-xs text-black mb-6",children:a}),n&&(0,l.jsxs)(v.z,{onClick:s,className:"rounded-full h-8 px-3 text-xs border bg-white hover:bg-neutral-100 text-black gap-1",children:[(0,l.jsx)(em.Z,{className:"w-3 h-3"}),"Create Event"]})]})})},ex=(e,t)=>!!(0,y.default)(e.date,t.date)&&e.startTime<t.endTime&&t.startTime<e.endTime,ef=e=>{let t={segmentLayouts:[]};if(!e.length)return t;let a=[...e].sort((e,t)=>{let a=e.startTime.getTime()-t.startTime.getTime();return 0!==a?a:t.endTime.getTime()-t.startTime.getTime()-(e.endTime.getTime()-e.startTime.getTime())}),l=new Set;for(let e of a){if(l.has(e.id))continue;let n=a.filter(t=>ex(e,t)),s=[];n.forEach(e=>{let t=!1;for(let a of s)if(a.every(t=>!ex(e,t))){a.push(e),t=!0;break}t||s.push([e])});let r=s.length,i=90-(r-1)*15,d=15;i<50&&r>1&&(d=(90-(i=50))/(r-1)),s.forEach((e,a)=>{e.forEach(e=>{if(l.has(e.id))return;let n=a*d;t.segmentLayouts.push({segment:e,left:n,width:i,zIndex:10+a,hasOverlap:r>1}),l.add(e.id)})}),n.forEach(e=>l.add(e.id))}return t},eg=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3;if(e.length<=t)return{visibleSegments:e,moreCount:0};let a=[...e].sort((e,t)=>{let a=e.startTime.getTime()-t.startTime.getTime();return 0!==a?a:t.endTime.getTime()-t.startTime.getTime()-(e.endTime.getTime()-e.startTime.getTime())}).slice(0,t-1),l=e.length-a.length;return{visibleSegments:a,moreCount:l}};var ev=a(71344);let ey=e=>{let{selectedDate:t,segments:a,selectedEvent:n,setSelectedEvent:s,handleEventClick:r,canEditData:i,openAddEventForm:d,view:o,activeDragData:c}=e,u=(0,ec.Zj)({id:"allday-".concat((0,D.default)(t,"yyyy-MM-dd")),data:{date:t,type:"allday-day"}}),m=(0,ec.Zj)({id:"allday-".concat((0,D.default)((0,w.default)((0,ev.default)(t,{weekStartsOn:0}),0),"yyyy-MM-dd")),data:{date:(0,w.default)((0,ev.default)(t,{weekStartsOn:0}),0),type:"allday-week"}}),h=(0,ec.Zj)({id:"allday-".concat((0,D.default)((0,w.default)((0,ev.default)(t,{weekStartsOn:0}),1),"yyyy-MM-dd")),data:{date:(0,w.default)((0,ev.default)(t,{weekStartsOn:0}),1),type:"allday-week"}}),x=(0,ec.Zj)({id:"allday-".concat((0,D.default)((0,w.default)((0,ev.default)(t,{weekStartsOn:0}),2),"yyyy-MM-dd")),data:{date:(0,w.default)((0,ev.default)(t,{weekStartsOn:0}),2),type:"allday-week"}}),g=(0,ec.Zj)({id:"allday-".concat((0,D.default)((0,w.default)((0,ev.default)(t,{weekStartsOn:0}),3),"yyyy-MM-dd")),data:{date:(0,w.default)((0,ev.default)(t,{weekStartsOn:0}),3),type:"allday-week"}}),v=[m,h,x,g,(0,ec.Zj)({id:"allday-".concat((0,D.default)((0,w.default)((0,ev.default)(t,{weekStartsOn:0}),4),"yyyy-MM-dd")),data:{date:(0,w.default)((0,ev.default)(t,{weekStartsOn:0}),4),type:"allday-week"}}),(0,ec.Zj)({id:"allday-".concat((0,D.default)((0,w.default)((0,ev.default)(t,{weekStartsOn:0}),5),"yyyy-MM-dd")),data:{date:(0,w.default)((0,ev.default)(t,{weekStartsOn:0}),5),type:"allday-week"}}),(0,ec.Zj)({id:"allday-".concat((0,D.default)((0,w.default)((0,ev.default)(t,{weekStartsOn:0}),6),"yyyy-MM-dd")),data:{date:(0,w.default)((0,ev.default)(t,{weekStartsOn:0}),6),type:"allday-week"}})];return 0!==a.length||c?"day"===o?(0,l.jsx)("div",{className:"border-b border-neutral-300 bg-white",children:(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsx)("div",{className:"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black",children:(0,l.jsx)("div",{className:"text-right",children:(0,l.jsx)("div",{className:"text-xs font-semibold",children:"All-day"})})}),(0,l.jsxs)("div",{ref:u.setNodeRef,className:(0,f.cn)("flex-1 relative p-2 space-y-1",u.isOver&&"bg-blue-50"),children:[a.slice(0,3).map(e=>{var t;return(0,l.jsx)(eu,{segment:e,style:{height:"24px",width:"100%"},onClick:t=>{t.stopPropagation(),s(e.originalEventId),r(e.originalEvent)},view:"day",isDragging:(null==c?void 0:null===(t=c.payload)||void 0===t?void 0:t.id)===e.id},e.id)}),a.length>3&&(0,l.jsxs)("div",{className:"text-xs text-neutral-600 font-medium cursor-pointer hover:text-neutral-800",children:["+ ",a.length-3," more"]})]})]})}):(()=>{let e=Array.from({length:7},(e,a)=>(0,w.default)((0,ev.default)(t,{weekStartsOn:0}),a)),n=(()=>{let t=new Map;a.forEach(e=>{if(e.isMultiDay||e.isAllDay){let a=e.originalEventId;t.has(a)||t.set(a,[]),t.get(a).push(e)}});let l=[];return t.forEach(t=>{t.sort((e,t)=>e.date.getTime()-t.date.getTime());let a=t[0],n=t[t.length-1],s=e.findIndex(e=>(0,y.default)(e,a.date)),r=e.findIndex(e=>(0,y.default)(e,n.date));s>=0&&r>=0&&l.push({segment:a,startDayIndex:s,endDayIndex:r,colSpan:r-s+1,isEndOfEvent:n.isLastSegment})}),l})(),u=(()=>{let e=[],t=[];return[...n].sort((e,t)=>e.startDayIndex-t.startDayIndex||t.colSpan-e.colSpan).forEach(a=>{let l=!1;for(let n=0;n<t.length;n++){let s=t[n];if(s.every(e=>a.startDayIndex>e.endDayIndex||a.endDayIndex<e.startDayIndex)){s.push(a),e.push({...a,row:n}),l=!0;break}}l||(t.push([a]),e.push({...a,row:t.length-1}))}),e})(),{visibleSegments:m,moreCount:h}=eg(u.map(e=>e.segment),3),x=u.filter(e=>m.some(t=>t.id===e.segment.id)),g=h>0,p=u.length>0?Math.min(...u.map(e=>e.startDayIndex)):0;if(0===u.length)return null;let b=u.length>0?Math.max(...u.map(e=>e.row))+1:0,j=g?3.5:Math.max(1,b);return(0,l.jsx)("div",{"data-all-day-row":"true",className:"border-b border-neutral-300 bg-white",children:(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsx)("div",{className:"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black",children:(0,l.jsx)("div",{className:"text-right",children:(0,l.jsx)("div",{className:"text-xs font-semibold",children:"All-day"})})}),(0,l.jsx)("div",{className:"flex-1 relative p-2",style:{height:"".concat(28*j+16,"px")},children:(0,l.jsx)("div",{className:"grid grid-cols-7 gap-1 h-full",children:e.map((e,t)=>{let a=v[t];return(0,l.jsxs)("div",{ref:a.setNodeRef,className:(0,f.cn)("relative cursor-pointer hover:bg-neutral-50 rounded-sm transition-colors",a.isOver&&"bg-blue-50"),onDoubleClick:()=>{if(i){let t=new Date(e);t.setHours(9,0,0,0),d(t)}},children:[x.filter(e=>e.startDayIndex===t).map(e=>{var t;return(0,l.jsx)("div",{className:"absolute z-10",style:{top:"".concat(28*e.row+2,"px"),left:"0px",width:"calc(".concat(100*e.colSpan,"% + ").concat((e.colSpan-1)*4,"px)"),height:"24px"},children:(0,l.jsx)(eu,{segment:e.segment,isEndOfEvent:e.isEndOfEvent,style:{height:"24px",width:"100%"},onClick:t=>{t.stopPropagation(),s(e.segment.originalEventId),r(e.segment.originalEvent)},view:o,isDragging:(null==c?void 0:null===(t=c.payload)||void 0===t?void 0:t.id)===e.segment.id})},e.segment.id)}),g&&t===p&&(0,l.jsxs)("div",{className:"absolute z-10 cursor-pointer text-xs text-gray-600 hover:underline",style:{top:"".concat(86,"px"),left:"4px",right:"4px"},onClick:()=>console.log("More clicked"),children:["+",h," more"]})]},t)})})})]})})})():null},ep=e=>{let{hour:t,date:a,onDoubleClick:s,children:r,isDragging:i}=e,[d,o]=(0,n.useState)(!1),[c,u]=(0,n.useState)(null),[m,h]=(0,n.useState)(0),x=n.useRef(null),f=d||i,g=(0,n.useCallback)(e=>{if(!x.current)return 0;let t=x.current.getBoundingClientRect();return Math.max(0,Math.min(59,Math.floor((e-t.top)/t.height*60)))},[]),v=(0,n.useCallback)(()=>{o(!0)},[]),y=(0,n.useCallback)(()=>{i||(o(!1),u(null),h(0))},[i]),p=(0,n.useCallback)(e=>{let t=g(e.clientY);u({x:e.clientX,y:e.clientY}),h(t)},[g]);n.useEffect(()=>{if(!i)return;let e=e=>{if(x.current){let t=x.current.getBoundingClientRect();if(e.clientX>=t.left&&e.clientX<=t.right&&e.clientY>=t.top&&e.clientY<=t.bottom){let t=g(e.clientY);u({x:e.clientX,y:e.clientY}),h(t),o(!0)}}};return document.addEventListener("mousemove",e),()=>document.removeEventListener("mousemove",e)},[i,g]);let w=(0,n.useCallback)(e=>{2===e.detail&&s(m)},[m,s]);return(0,l.jsxs)("div",{ref:x,className:"flex-1 relative min-h-[60px] cursor-pointer",style:{height:"60px"},onMouseEnter:v,onMouseLeave:y,onMouseMove:p,onClick:w,children:[(0,l.jsx)(ew,{date:a,hour:t,currentMinute:m,isActive:f}),r]})},ew=e=>{let{date:t,hour:a,currentMinute:n,isActive:s}=e,{setNodeRef:r,isOver:i}=(0,ec.Zj)({id:"hour-".concat((0,D.default)(t,"yyyy-MM-dd"),"-").concat(a),data:{date:t,hour:a,minute:n,type:"timeslot-minute"}});return(0,l.jsx)("div",{ref:r,className:"absolute inset-0 w-full h-full"})},eb=e=>{let{selectedDate:t,events:a,selectedEvent:s,setSelectedEvent:r,openAddEventForm:i,canEditData:d,savedScrollTop:o,handleEventClick:c,activeDragData:u}=e,m=Array.from({length:24},(e,t)=>t),h=(0,n.useMemo)(()=>G(K(a),t),[a,t]),x=(0,n.useMemo)(()=>U(h),[h]),g=(0,n.useMemo)(()=>ee(h),[h]),{segmentLayouts:v}=(0,n.useMemo)(()=>ef(g),[g]),y=(0,n.useMemo)(()=>(0,z.Z)(t)?{hour:new Date().getHours(),minutes:new Date().getMinutes()}:null,[t]);return(0,l.jsxs)("div",{className:"flex flex-col h-full bg-white",children:[(0,l.jsxs)("div",{className:"text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0",children:[(0,l.jsx)("div",{className:"font-semibold text-black mb-1 text-xs",children:(0,D.default)(t,"EEEE")}),(0,l.jsx)("div",{className:(0,f.cn)("inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full",(0,z.Z)(t)?"bg-black text-white":"text-black hover:bg-neutral-100"),children:(0,D.default)(t,"d")})]}),h.length>0&&(0,l.jsx)(ey,{selectedDate:t,segments:x,selectedEvent:s,setSelectedEvent:r,handleEventClick:c,canEditData:d,openAddEventForm:i,view:"day",activeDragData:u}),0===h.length?(0,l.jsx)(eh,{title:"No events scheduled",message:(0,z.Z)(t)?"You have a free day ahead! Add an event to get started.":"".concat((0,D.default)(t,"EEEE, MMMM d")," is completely free."),showCreateButton:d,onCreate:()=>{let e=new Date(t);e.setHours(9,0,0,0),i(e)}}):(0,l.jsxs)("div",{className:"flex-1 overflow-auto relative bg-white",id:"day-view-container",children:[m.map((e,a)=>(0,l.jsxs)("div",{className:(0,f.cn)("flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors",a===m.length-1&&"border-b-neutral-300"),style:{height:"60px"},children:[(0,l.jsx)("div",{"data-time-labels":"true",className:"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20",children:(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("div",{className:"text-xs font-semibold",children:(0,D.default)((0,A.default)(t,e),"h")}),(0,l.jsx)("div",{className:"text-xs text-black opacity-60",children:(0,D.default)((0,A.default)(t,e),"a")})]})}),(0,l.jsx)(ep,{hour:e,date:t,isDragging:!!u,onDoubleClick:a=>{if(d){let l=new Date(t);l.setHours(e,a,0,0),i(l)}},children:v.map(t=>{var a,n;if(t.segment.startTime.getHours()!==e)return null;let s=et(t.segment),i=ea(t.segment);return(0,l.jsx)(eu,{segment:t.segment,style:{height:"".concat(s,"px"),position:"absolute",top:"".concat(i,"px"),left:"".concat(t.left,"%"),width:"".concat(t.width,"%"),zIndex:(null==u?void 0:null===(a=u.payload)||void 0===a?void 0:a.id)===t.segment.id?50:t.zIndex,paddingRight:"2px",border:t.hasOverlap?"1px solid white":"none"},onClick:e=>{e.stopPropagation();let a=document.getElementById("day-view-container");a&&(o.current=a.scrollTop),r(t.segment.originalEventId),c(t.segment.originalEvent)},view:"day",isDragging:(null==u?void 0:null===(n=u.payload)||void 0===n?void 0:n.id)===t.segment.id},t.segment.id)})})]},a)),y&&(0,l.jsxs)("div",{className:"absolute flex items-center z-30 pointer-events-none left-14 lg:left-20",style:{top:"".concat((y.hour+y.minutes/60)*60,"px"),right:"4px"},children:[(0,l.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5"}),(0,l.jsx)("div",{className:"flex-1 border-t-2 border-red-500 shadow-sm"})]})]})]})};var ej=a(15);let eN=e=>{let{day:t,hour:a,children:s,onDoubleClick:r,isDragging:i}=e,[d,o]=(0,n.useState)(!1),[c,u]=(0,n.useState)(null),[m,h]=(0,n.useState)(0),x=n.useRef(null),f=d||i,g=(0,n.useCallback)(e=>{if(!x.current)return 0;let t=x.current.getBoundingClientRect();return Math.max(0,Math.min(59,Math.floor((e-t.top)/t.height*60)))},[]),v=(0,n.useCallback)(()=>{o(!0)},[]),y=(0,n.useCallback)(()=>{i||(o(!1),u(null),h(0))},[i]),p=(0,n.useCallback)(e=>{let t=g(e.clientY);u({x:e.clientX,y:e.clientY}),h(t)},[g]);n.useEffect(()=>{if(!i)return;let e=e=>{if(x.current){let t=x.current.getBoundingClientRect();if(e.clientX>=t.left&&e.clientX<=t.right&&e.clientY>=t.top&&e.clientY<=t.bottom){let t=g(e.clientY);u({x:e.clientX,y:e.clientY}),h(t),o(!0)}}};return document.addEventListener("mousemove",e),()=>document.removeEventListener("mousemove",e)},[i,g]);let w=(0,n.useCallback)(e=>{2===e.detail&&r(m)},[m,r]);return(0,l.jsxs)("div",{ref:x,className:"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer",onMouseEnter:v,onMouseLeave:y,onMouseMove:p,onClick:w,children:[(0,l.jsx)(ek,{day:t,hour:a,currentMinute:m,isActive:f}),s]})},ek=e=>{let{day:t,hour:a,currentMinute:n,isActive:s}=e,{setNodeRef:r,isOver:i}=(0,ec.Zj)({id:"hour-".concat((0,D.default)(t,"yyyy-MM-dd"),"-").concat(a),data:{date:t,hour:a,minute:n,type:"timeslot-minute"}});return(0,l.jsx)("div",{ref:r,className:"absolute inset-0 w-full h-full"})},eD=e=>{let{selectedDate:t,events:a,selectedEvent:s,setSelectedEvent:r,setSelectedDate:i,openAddEventForm:d,canEditData:o,savedScrollTop:c,handleEventClick:u,activeDragData:m}=e,h=(0,n.useMemo)(()=>{let e=(0,ev.default)(t,{weekStartsOn:0}),a=(0,ej.default)(t,{weekStartsOn:0}),l=Array.from({length:7},(t,a)=>(0,w.default)(e,a)),n=l.findIndex(e=>(0,z.Z)(e));return{weekStart:e,weekEnd:a,days:l,todayIndex:n}},[t]),{days:x,todayIndex:g}=h,v=Array.from({length:24},(e,t)=>t),y=(0,n.useMemo)(()=>Q(K(a),h.weekStart,h.weekEnd),[a,h.weekStart,h.weekEnd]),p=(0,n.useMemo)(()=>U(y),[y]),b=(0,n.useMemo)(()=>ee(y),[y]),j=(0,n.useMemo)(()=>-1!==g?{dayIndex:g,hour:new Date().getHours(),minutes:new Date().getMinutes()}:null,[g]);return(0,l.jsxs)("div",{className:"h-full flex flex-col bg-white",children:[(0,l.jsx)("div",{"data-day-headers":"true",className:"border-b border-neutral-300 bg-white sticky top-0 z-20",children:(0,l.jsxs)("div",{className:"flex overflow-x-hidden",children:[(0,l.jsx)("div",{className:"sticky left-0 bg-white z-10 w-14 lg:w-20"}),(0,l.jsx)("div",{className:"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0",children:x.map((e,t)=>(0,l.jsxs)("div",{className:"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4",onClick:()=>i(e),children:[(0,l.jsx)("div",{className:(0,f.cn)("font-semibold text-black mb-1","text-xs"),children:(0,D.default)(e,"EEE")}),(0,l.jsx)("div",{className:(0,f.cn)("inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full",(0,z.Z)(e)?"bg-black text-white":"text-black hover:bg-neutral-100"),children:(0,D.default)(e,"d")})]},t))})]})}),(0,l.jsx)(ey,{selectedDate:t,segments:p,selectedEvent:s,setSelectedEvent:r,handleEventClick:u,canEditData:o,openAddEventForm:d,view:"week",activeDragData:m}),0===y.length?(0,l.jsx)(eh,{title:"No events this week",message:"Your week is completely free. Add some events to get organized!",showCreateButton:o,onCreate:()=>d(t)}):(0,l.jsx)("div",{className:"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto",id:"week-view-container",children:(0,l.jsx)("div",{className:"relative overflow-x-auto lg:overflow-x-visible",children:(0,l.jsxs)("div",{className:"flex flex-col min-w-[700px] lg:min-w-0",children:[(0,l.jsx)("div",{className:"relative",children:v.map((e,t)=>(0,l.jsxs)("div",{className:(0,f.cn)("flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors",t===v.length-1&&"border-b-neutral-300"),style:{height:"60px"},children:[(0,l.jsx)("div",{"data-time-labels":"true",className:"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20",children:(0,l.jsx)("div",{className:"text-right",children:(0,l.jsx)("div",{className:"text-xs font-semibold",children:(0,D.default)((0,A.default)(new Date,e),"h a")})})}),x.map(t=>{let{segmentLayouts:a}=ef(G(b,t));return(0,l.jsx)(eN,{day:t,hour:e,isDragging:!!m,onDoubleClick:a=>{if(o){let l=new Date(t);l.setHours(e,a,0,0),d(l)}},children:a.map(t=>{var a,n;if(t.segment.startTime.getHours()!==e)return null;let s=et(t.segment),i=ea(t.segment);return(0,l.jsx)(eu,{segment:t.segment,style:{height:"".concat(s,"px"),position:"absolute",top:"".concat(i,"px"),left:"".concat(t.left,"%"),width:"".concat(t.width,"%"),zIndex:(null==m?void 0:null===(a=m.payload)||void 0===a?void 0:a.id)===t.segment.id?50:t.zIndex,paddingRight:"2px",border:t.hasOverlap?"1px solid white":"none"},onClick:e=>{e.stopPropagation();let a=document.getElementById("week-view-container");a&&(c.current=a.scrollTop),r(t.segment.originalEventId),u(t.segment.originalEvent)},view:"week",isDragging:(null==m?void 0:null===(n=m.payload)||void 0===n?void 0:n.id)===t.segment.id},t.segment.id)})},"".concat(t.toISOString(),"-").concat(e))})]},e))}),j&&(0,l.jsx)("div",{className:"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30",children:(0,l.jsx)("div",{className:"relative h-full w-full",children:(0,l.jsxs)("div",{className:"absolute flex items-center",style:{top:"".concat((j.hour+j.minutes/60)*60,"px"),left:"".concat(j.dayIndex/7*100,"%"),width:"".concat(1/7*100,"%")},children:[(0,l.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5"}),(0,l.jsx)("div",{className:"flex-1 border-t-2 border-red-500 shadow-sm"})]})})})]})})})]})};var eM=a(31559),eC=a(13657),eS=a(90641);let eE=e=>{let{event:t,style:a,onClick:s,onContextMenu:r,view:i="month",isDragging:d,showTitle:o=!0,isDraggable:c=!0}=e,u=(0,n.useRef)(null),{attributes:m,listeners:h,setNodeRef:x,isDragging:g}=(0,ec.O1)({id:"event-".concat(t.id),data:{type:"event",payload:t},disabled:!c||t.isMultiDay||t.isAllDay}),v=d||g,y=(0,n.useMemo)(()=>{let e=new Date(t.start);return{start:e,eventSize:H((null==a?void 0:a.height)?parseInt(a.height.toString().replace("px","")):null),isInstant:B(t),formattedTime:Y(e,i,{shortFormat:!0})}},[t,a,i]),p=(0,n.useMemo)(()=>{let e=(0,L.S8)("Denim"),t=e.bg.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/),l=t?"rgb(".concat(t[1],", ").concat(t[2],", ").concat(t[3],")"):e.bg;return{...a,backgroundColor:l,minHeight:"month"===i?"24px":"30px",marginBottom:"month"===i?"4px":"0px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)",opacity:v?.5:1}},[a,i,v]),w=(0,n.useMemo)(()=>{let e=c&&!t.isMultiDay&&!t.isAllDay;return"month"===i||"small"===y.eventSize?{baseClasses:(0,f.cn)("rounded-md select-none text-black text-xs overflow-hidden",!v&&e&&"cursor-pointer",!v&&!e&&"cursor-default","p-1"),containerClasses:(0,f.cn)("flex items-center space-x-1","flex-nowrap"),titleClasses:(0,f.cn)("font-medium truncate leading-tight text-xs overflow-hidden","max-w-[70%]"),timeClasses:(0,f.cn)("opacity-75 text-xs flex-shrink-0","text-[0.65rem]")}:{baseClasses:(0,f.cn)("rounded-md select-none text-black text-xs overflow-hidden",!v&&e&&"cursor-pointer",!v&&!e&&"cursor-default","p-2"),containerClasses:(0,f.cn)("flex flex-col","space-y-0.5"),titleClasses:(0,f.cn)("font-medium truncate leading-tight text-xs overflow-hidden"),timeClasses:(0,f.cn)("opacity-75 text-xs")}},[y,i,v,c,t.isMultiDay,t.isAllDay]);return(0,l.jsx)("div",{id:"event-".concat(t.id),ref:e=>{x(e),u.current=e},style:p,className:w.baseClasses,onClick:s,onContextMenu:r,...t.isMultiDay||t.isAllDay||!c?{}:{...h,...m},children:"month"===i||"small"===y.eventSize?(0,l.jsxs)("div",{className:w.containerClasses,children:[o&&(0,l.jsx)("span",{className:w.titleClasses,children:t.title}),o&&y.formattedTime&&(0,l.jsx)("span",{className:w.timeClasses,children:y.formattedTime})]}):(0,l.jsxs)("div",{className:w.containerClasses,children:[o&&(0,l.jsx)("div",{className:w.titleClasses,children:t.title}),o&&y.formattedTime&&(0,l.jsx)("div",{className:w.timeClasses,children:y.formattedTime})]})})};var eI=a(79820);let eT=e=>{let{selectedDate:t,events:a,selectedEvent:n,setSelectedEvent:s,handleEventClick:r}=e,{isMobile:i}=(0,S.e)(),d=a.filter(e=>(0,y.default)(new Date(e.start),t)).sort((e,t)=>new Date(e.start).getTime()-new Date(t.start).getTime());return(0,l.jsxs)("div",{"data-side-card":"true",className:(0,f.cn)("border-l border-neutral-300 flex flex-col bg-gradient-to-b from-neutral-50 to-neutral-100",i?"w-full border-t":"w-96"),children:[(0,l.jsxs)("div",{className:(0,f.cn)("border-b border-neutral-300 bg-gradient-to-r from-white to-neutral-50","p-4 relative overflow-hidden"),children:[(0,l.jsx)("div",{className:"absolute top-0 right-0 w-20 h-20 bg-neutral-100 rounded-full -translate-y-10 translate-x-10 opacity-30"}),(0,l.jsxs)("div",{className:"relative z-10",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-neutral-400 rounded-full"}),(0,l.jsx)("h2",{className:"font-semibold text-black text-xs",children:(0,D.default)(t,"MMM d, yyyy")})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)("svg",{className:"w-3 h-3 text-neutral-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,l.jsxs)("p",{className:"text-xs text-neutral-600",children:[d.length," ",1===d.length?"event":"events"," scheduled"]})]})]})]}),(0,l.jsx)(eS.ScrollArea,{className:"flex-1 p-4",children:0===d.length?(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center h-40 text-center relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-neutral-100 to-transparent rounded-lg opacity-50"}),(0,l.jsxs)("div",{className:"relative z-10",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-white to-neutral-100 rounded-2xl flex items-center justify-center border border-neutral-200 shadow-sm",children:(0,l.jsx)("svg",{className:"w-7 h-7 text-neutral-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z"})})}),(0,l.jsx)("p",{className:"text-xs text-neutral-500 font-medium mb-1",children:"No events scheduled"}),(0,l.jsx)("p",{className:"text-xs text-neutral-400",children:"Your day is completely free"})]})]}):(0,l.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,l.jsxs)(eI.Zb,{className:(0,f.cn)("cursor-pointer p-4 relative overflow-hidden",n===e.id?"bg-gradient-to-r from-neutral-200 to-neutral-300 text-black border-neutral-400 shadow-lg":"bg-gradient-to-r from-white to-neutral-50 hover:from-neutral-50 hover:to-neutral-100 border-neutral-200"),onClick:()=>{s(e.id),r(e)},children:[(0,l.jsx)("div",{className:"absolute top-0 right-0 w-8 h-8 bg-gradient-to-br from-transparent to-neutral-200 opacity-30 rounded-bl-full"}),(0,l.jsxs)("div",{className:"relative z-10",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,l.jsx)("div",{className:(0,f.cn)("font-semibold text-xs leading-relaxed",(e.id,"text-black")),children:e.title}),(0,l.jsx)("div",{className:(0,f.cn)("w-2 h-2 rounded-full flex-shrink-0 mt-1",n===e.id?"bg-neutral-600":"bg-neutral-400")})]}),(0,l.jsxs)("div",{className:(0,f.cn)("flex items-center space-x-2 text-xs",n===e.id?"text-neutral-700":"text-neutral-600"),children:[(0,l.jsx)("div",{className:(0,f.cn)("w-4 h-4 rounded-full flex items-center justify-center",n===e.id?"bg-neutral-400":"bg-neutral-200"),children:(0,l.jsx)("svg",{className:"w-2.5 h-2.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,l.jsxs)("span",{className:"font-medium",children:[(0,D.default)(new Date(e.start),"h:mm a")," - ",(0,D.default)(new Date(e.end),"h:mm a")]})]})]})]},e.id))})})]})};var ez=a(16720);let eA=e=>{let{date:t,children:a,onClick:n,isCurrentMonth:s}=e,{setNodeRef:r,isOver:i}=(0,ec.Zj)({id:"daycell-".concat((0,D.default)(t,"yyyy-MM-dd")),data:{date:t,type:"daycell"}});return(0,l.jsx)("div",{ref:r,onClick:n,className:(0,f.cn)("border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[170px] lg:p-3 lg:min-h-[190px]",s?"bg-white hover:bg-neutral-50":"bg-neutral-100 hover:bg-neutral-200",i&&"bg-blue-50 border-blue-200"),children:a})},eL=(e,t)=>{let a=new Date(e.start),l=new Date(e.end),n=new Date(t),s=new Date(t);return s.setHours(23,59,59,999),a<=s&&l>=n},eO=e=>{let t=new Date(e.start),a=new Date(e.end),l=new Date(t.getFullYear(),t.getMonth(),t.getDate()),n=new Date(a.getFullYear(),a.getMonth(),a.getDate());return l.getTime()!==n.getTime()},eR=(e,t)=>(0,n.useMemo)(()=>{let a=new Map,l=new Map;return e.forEach((e,n)=>{let s=e[0],r=new Date(e[6]);r.setHours(23,59,59,999);let i=t.filter(e=>{let t=new Date(e.start),a=new Date(e.end);return t<=r&&a>=s}),d=[];i.forEach(t=>{let a=new Date(t.start),l=new Date(t.end),n=e.findIndex(e=>(0,y.default)(e,a)),i=e.findIndex(e=>(0,y.default)(e,l)),o=-1!==n?n:0,c=-1!==i?i:6;(-1!==n||-1!==i||a<s&&l>r)&&d.push({event:t,startDayIndex:o,endDayIndex:c,colSpan:c-o+1,isMultiDay:eO(t)})});let o=d.sort((e,t)=>e.startDayIndex!==t.startDayIndex?e.startDayIndex-t.startDayIndex:t.colSpan-e.colSpan),c=[],u=[];o.forEach(t=>{let a=e.slice(t.startDayIndex,t.endDayIndex+1);if(!a.every(e=>{let t=(0,D.default)(e,"yyyy-MM-dd");return 4>(l.get(t)||0)}))return;let n=!1;for(let e=0;e<u.length;e++){let a=u[e];if(!a.some(e=>t.startDayIndex<=e.endDayIndex&&t.endDayIndex>=e.startDayIndex)){a.push(t),c.push({...t,row:e}),n=!0;break}}n||(u.push([t]),c.push({...t,row:u.length-1})),a.forEach(e=>{let t=(0,D.default)(e,"yyyy-MM-dd"),a=l.get(t)||0;l.set(t,a+1)})}),a.set(n,c)}),{positionedEventsByWeek:a,slotUsageByDay:l}},[e,t]),eZ=e=>{let{selectedDate:t,events:a,selectedEvent:s,setSelectedEvent:r,setSelectedDate:i,openAddEventForm:d,canEditData:o,handleEventClick:c,activeDragData:u}=e;(0,E.pB)();let m=(0,n.useMemo)(()=>{let e=(0,eM.default)(t),a=(0,eC.default)(t),l=(0,ev.default)(e,{weekStartsOn:0}),n=(0,ej.default)(a,{weekStartsOn:0}),s=[],r=l;for(;r<=n;)s.push(r),r=(0,w.default)(r,1);let i=[];for(let e=0;e<s.length;e+=7)i.push(s.slice(e,e+7));return{monthStart:e,monthEnd:a,startDay:l,endDay:n,days:s,weeks:i}},[t]),h=(0,n.useMemo)(()=>a.filter(e=>{let t=new Date(e.start),a=new Date(e.end);return t<=m.endDay&&a>=m.startDay}),[a,m.startDay,m.endDay]),{positionedEventsByWeek:x}=eR(m.weeks,h),{context:g}=(0,ez.sV)();if(0===h.length)return(0,l.jsxs)("div",{className:"h-full bg-background flex flex-col lg:flex-row",children:[(0,l.jsxs)("div",{className:"flex-1 flex flex-col min-h-0",children:[(0,l.jsx)("div",{className:"grid grid-cols-7 border-b border-neutral-300 bg-white",children:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"].map(e=>(0,l.jsx)("div",{className:"px-2 py-2 text-xs font-medium text-gray-500 text-center",children:e},e))}),(0,l.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,l.jsx)(eh,{title:"No events this month",message:"".concat((0,D.default)(t,"MMMM yyyy")," is completely free. Start planning your month!"),showCreateButton:o,onCreate:()=>d(t)})})]}),"record_tab"!==g&&(0,l.jsx)(eT,{selectedDate:t,events:a,selectedEvent:s,setSelectedEvent:r,handleEventClick:c})]});let y=(e,a)=>{let n=e.getMonth()===t.getMonth(),s=(0,z.Z)(e),m=h.filter(t=>eL(t,e)).length,x=a.sort((e,t)=>e.row-t.row).slice(0,4),g=m>4,y=x.reduce((e,t)=>Math.max(e,t.row),-1),p=g?132:(y+1)*28;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsx)("span",{className:(0,f.cn)("inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6",s?"bg-black text-white":n?"text-black hover:bg-neutral-100":"text-neutral-400"),children:(0,D.default)(e,"d")}),o&&n&&(0,l.jsx)(v.z,{variant:"ghost",className:"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex",onClick:t=>{t.stopPropagation(),setTimeout(()=>d(e),150)},children:(0,l.jsx)(em.Z,{className:"h-3 w-3 text-black"})})]}),(0,l.jsxs)("div",{className:"relative",style:{height:"".concat(p,"px")},children:[x.map(e=>{var t;return(0,l.jsx)("div",{className:"absolute",style:{top:"".concat(28*e.row,"px"),left:"2px",width:e.colSpan>1?"calc(".concat(100*e.colSpan,"% + ").concat((e.colSpan-1)*19.5,"px)"):"calc(100% - 4px)",zIndex:10+e.row},children:(0,l.jsx)(eE,{event:e.event,view:"month",onClick:t=>{t.stopPropagation(),r(e.event.id),c(e.event)},isDragging:(null==u?void 0:null===(t=u.payload)||void 0===t?void 0:t.id)===e.event.id,isDraggable:!e.isMultiDay})},e.event.id)}),g&&(0,l.jsxs)("div",{className:"text-black hover:text-black font-medium text-xs cursor-pointer",style:{position:"absolute",top:"".concat(112,"px"),left:"2px"},onClick:t=>{t.stopPropagation(),i(e)},children:["+ ",Math.max(0,m-4)," more"]})]})]})};return(0,l.jsxs)("div",{className:"h-full bg-background flex flex-col lg:flex-row",children:[(0,l.jsxs)("div",{className:"flex-1 flex flex-col min-h-0",children:[(0,l.jsx)("div",{"data-day-headers":"true",className:"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white",children:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"].map(e=>(0,l.jsx)("div",{className:(0,f.cn)("text-center font-semibold text-black","py-2 text-xs"),children:e.substring(0,3)},e))}),(0,l.jsx)(eS.ScrollArea,{className:"flex-1",children:(0,l.jsx)("div",{className:"grid grid-cols-7 border-neutral-300 border-b",children:m.weeks.map((e,a)=>e.map((e,n)=>{let s=(x.get(a)||[]).filter(e=>e.startDayIndex===n),r=e.getMonth()===t.getMonth();return(0,l.jsx)(eA,{date:e,isCurrentMonth:r,onClick:()=>i(e),children:y(e,s)},"".concat(a,"-").concat(n))}))})})]}),"record_tab"!==g&&(0,l.jsx)(eT,{selectedDate:t,events:a,selectedEvent:s,setSelectedEvent:r,handleEventClick:c})]})};var eF=a(63127),eB=a(36675);let eY=e=>{let{transform:t,draggingNodeRect:a,windowRect:l}=e;if(!a||!l)return t;let n=document.querySelector('[data-calendar-content="true"]');if(!n)return t;let s=n.getBoundingClientRect(),r=document.querySelector('[data-side-card="true"]'),i=s.right-a.width;r&&(i=Math.min(i,r.getBoundingClientRect().left-a.width));let d=document.querySelector('[data-time-labels="true"]'),o=document.querySelector('[data-day-headers="true"]'),c=document.querySelector('[data-all-day-row="true"]'),u=s.left,m=s.top,h=s.bottom-a.height;d&&(u=Math.max(u,d.getBoundingClientRect().right)),o&&(m=Math.max(m,o.getBoundingClientRect().bottom)),c&&(m=Math.max(m,c.getBoundingClientRect().bottom));let x=t.x+a.left,f=t.y+a.top,g=Math.min(Math.max(x,u),i),v=Math.min(Math.max(f,m),h);return{...t,x:g-a.left,y:v-a.top}};var eH=a(40178),eV=a(32060),e_=a(75060),eW=a(94589),eX=a(17939),eP=a(99972);let eq=e=>{let t=(0,n.useRef)();return(0,n.useEffect)(()=>{t.current=e}),t.current},e$=e=>{var t,a,h,x;let{databaseStore:z,members:A,workspace:L}=(0,s.cF)(),{definition:O}=e,{accessLevel:Z}=(0,d.qt)(),{isMobile:F}=(0,S.e)(),B=(0,E.pB)(),Y=(0,o.cL)(),H=(0,c.nM)(),[V,_]=(0,n.useState)(new Date),[W,P]=(0,n.useState)("week"),[q,$]=(0,n.useState)(null),[J,G]=(0,n.useState)(""),[Q,K]=(0,n.useState)(!F),[U,ee]=(0,n.useState)(null),et=(0,n.useRef)(0),ea=(0,n.useRef)({x:0,y:0}),el=!!B;O.filter=O.filter||{conditions:[],match:r.Match.All},O.sorts=O.sorts||[],O.databaseId;let en=z[O.databaseId],es=!!Y,er=!O.lockContent&&!es&&!!Z;H||Y||es||O.lockContent;let ed=!!(!H&&!Y&&!es&&!O.lockContent&&Z&&er),{createRecords:eo,updateRecordValues:em,setPeekRecordId:eh,peekRecordId:ex,refreshDatabase:ef,deleteRecords:eg,smartUpdateViewDefinition:ev}=(0,u.Bf)(),{sorts:ey,filter:ep,search:ew}=(0,u.Jy)(),ej=eq(ex),{openRecord:eN}=(0,I.x)(),ek=(0,ec.Dy)((0,ec.VT)(ec.we,{activationConstraint:{distance:8}}),(0,ec.VT)(ec.LO,{activationConstraint:{delay:150,tolerance:5}}));if((0,n.useEffect)(()=>{let e="day"===W?"day-view-container":"week-view-container",t=document.getElementById(e);t&&requestAnimationFrame(()=>{t.scrollTop=et.current})},[q,W]),(0,n.useEffect)(()=>{K(!F)},[F]),(0,n.useEffect)(()=>{ej&&!ex&&ef(O.databaseId)},[ex,ej,O.databaseId,ef]),!en)return(0,l.jsx)(i.PageLoader,{size:"full"});let eM=()=>{var e,t;if(!en)return[];let{rows:a}=(0,m.filterAndSortRecords)(en,A,z,O.filter||{match:r.Match.All,conditions:[]},ep,ey.length?ey:O.sorts||[],(null==L?void 0:null===(e=L.workspaceMember)||void 0===e?void 0:e.userId)||"",(null==en?void 0:null===(t=en.database)||void 0===t?void 0:t.id)||""),l=(0,m.searchFilteredRecords)(ew||"",a),n=(0,eF.$P)(en.database);return l.map(e=>{let t,a;let l=e.processedRecord.processedRecordValues[O.eventStartColumnId];if(t=l&&"string"==typeof l?new Date(l):new Date,O.eventEndColumnId){let l=e.processedRecord.processedRecordValues[O.eventEndColumnId];a=new Date(l&&"string"==typeof l?l:t.getTime()+6e4*(O.defaultDuration||30))}else a=new Date(t.getTime()+6e4*(O.defaultDuration||30));let s=(0,eF.T5)(e.record,n.titleColId,n.defaultTitle,n.isContacts),r=!(0,y.default)(t,a),i=(a.getTime()-t.getTime())/36e5;return{id:e.id,title:s,start:t,end:a,record:e.record,processedRecord:e.processedRecord,isMultiDay:r,isAllDay:i>=23||r&&i>=22}})},eC=async e=>{let t,{active:a,over:l}=e;if(document.removeEventListener("mousemove",eS),document.removeEventListener("touchmove",eI),ee(null),!l||!a||!ed||O.lockContent||a.id===l.id)return;let n=a.data.current,s=l.data.current;if(!n||!s)return;let{payload:r,type:i}=n,d="segment"===i?r.originalEvent:r,o=new Date(d.start),c=new Date(d.end),u=(0,R.Z)(c,o);if(s.type.startsWith("allday")){let e=X(s.date,(0,p.default)(o));(t=(0,w.default)(o,e)).setHours(0,0,0,0)}else if("timeslot-minute"===s.type)(t=new Date(s.date)).setHours(s.hour,s.minute,0,0);else{if("daycell"!==s.type)return;let e=X(s.date,(0,p.default)(o));t=(0,w.default)(o,e)}let m=new Date(t.getTime()+u),h=d.record.id,x={[O.eventStartColumnId]:t.toISOString(),...O.eventEndColumnId&&{[O.eventEndColumnId]:m.toISOString()}};try{await em(O.databaseId,[h],x),C.Am.success('Event "'.concat(d.title,'" updated.'))}catch(e){C.Am.error("Failed to update event.")}},eS=e=>{ea.current={x:e.clientX,y:e.clientY}},eI=e=>{let t=e.touches[0];ea.current={x:t.clientX,y:t.clientY}},eT=(()=>{let e=eM();return J.trim()?e.filter(e=>{let t=J.toLowerCase();return e.title.toLowerCase().includes(t)}):e})(),ez=()=>_(new Date),eA=()=>{switch(W){case"day":_(e=>(0,w.default)(e,-1));break;case"week":_(e=>(0,b.default)(e,1));break;case"month":_(e=>(0,j.default)(e,1))}},eL=()=>{switch(W){case"day":_(e=>(0,w.default)(e,1));break;case"week":_(e=>(0,N.default)(e,1));break;case"month":_(e=>(0,k.default)(e,1))}},eO=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t){let t=new Date,a=new Date(e);a.setHours(t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()),eR(a)}else eR(e)},eR=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;if(!ed||O.lockContent)return;let t=new Date(e),a=new Date(t.getTime()+6e4*(O.defaultDuration||30)),l=(0,eF.$P)(en.database);try{let e={[O.eventStartColumnId]:t.toISOString(),...O.eventEndColumnId&&{[O.eventEndColumnId]:a.toISOString()}};l.titleColId&&(e[l.titleColId]="New Event");let n=await eo(O.databaseId,[e]);if(n&&n.records&&n.records.length>0){let e=n.records[0].id;e?(await ef(O.databaseId),eh(e),C.Am.success("New event created")):C.Am.error("Error accessing the new event")}else C.Am.error("Failed to create event properly")}catch(e){C.Am.error("Failed to create event")}},e$=e=>{if(!O.lockContent&&e&&e.id){let t="day"===W?"day-view-container":"week-view-container",a=document.getElementById(t);a&&(et.current=a.scrollTop),eN(e.id,e.record.databaseId),$(e.id)}},eJ=()=>{switch(W){case"day":default:return(0,D.default)(V,"MMMM d, yyyy");case"week":return"".concat((0,D.default)((0,w.default)(V,-V.getDay()),"MMM d")," - ").concat((0,D.default)((0,w.default)(V,6-V.getDay()),"MMM d, yyyy"));case"month":return(0,D.default)(V,"MMMM yyyy")}},eG=[{id:"day",value:"day",title:"Day",data:"day"},{id:"week",value:"week",title:"Week",data:"week"},{id:"month",value:"month",title:"Month",data:"month"}],eQ="day"===W?["day"]:"week"===W?["week"]:["month"];return(0,l.jsxs)("div",{className:"h-full flex flex-col bg-white",children:[(0,l.jsxs)("div",{className:(0,f.cn)("border-b border-neutral-300 bg-white",el&&"py-1"),children:[F?(0,l.jsxs)("div",{className:(0,f.cn)("p-2",el&&"py-1"),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsx)("h1",{className:"text-xs font-semibold text-black truncate flex-1 mr-2",children:eJ()}),(0,l.jsx)(v.z,{variant:"ghost",onClick:()=>K(!Q),className:"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50",children:"Calendar"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(v.z,{variant:"ghost",onClick:ez,className:(0,f.cn)("rounded-full px-3 text-xs text-black hover:bg-gray-50",el?"h-6":"h-8"),children:"Today"}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(v.z,{variant:"ghost",onClick:eA,className:(0,f.cn)("rounded-full p-1 text-black hover:bg-gray-50",el?"h-6 w-6":"h-8 w-8"),children:(0,l.jsx)(ei.fPT,{className:"h-4 w-4"})}),(0,l.jsx)(v.z,{variant:"ghost",onClick:eL,className:(0,f.cn)("rounded-full p-1 text-black hover:bg-gray-50",el?"h-6 w-6":"h-8 w-8"),children:(0,l.jsx)(ei.oR,{className:"h-4 w-4"})})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(eB.A,{options:eG,selectedIds:eQ,onChange:e=>{e.length>0&&P(e[0])},className:(0,f.cn)("px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20",el?"h-6":"h-8"),placeholder:"View",hideSearch:!0}),ed&&!O.lockContent&&(0,l.jsx)(v.z,{onClick:()=>eO(V,!0),className:(0,f.cn)("rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black",el?"h-6":"h-8"),children:(0,l.jsx)(ei.pOD,{className:"h-3 w-3"})})]})]})]}):(0,l.jsxs)("div",{className:(0,f.cn)("flex items-center justify-between px-4",el?"py-1":"py-2"),children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(v.z,{variant:"ghost",onClick:ez,className:(0,f.cn)("rounded-full px-3 text-xs text-black hover:bg-gray-50",el?"h-6":"h-8"),children:"Today"}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(v.z,{variant:"ghost",onClick:eA,className:(0,f.cn)("rounded-full p-1 text-black hover:bg-gray-50",el?"h-6 w-6":"h-8 w-8"),children:(0,l.jsx)(ei.fPT,{className:"h-4 w-4"})}),(0,l.jsx)(v.z,{variant:"ghost",onClick:eL,className:(0,f.cn)("rounded-full p-1 text-black hover:bg-gray-50",el?"h-6 w-6":"h-8 w-8"),children:(0,l.jsx)(ei.oR,{className:"h-4 w-4"})})]}),(0,l.jsx)("h1",{className:"text-xs font-semibold text-black",children:eJ()})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[!el&&(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(M.I,{placeholder:"Search events...",value:J,onChange:e=>G(e.target.value),className:"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none"}),(0,l.jsx)(ei._Ve,{className:"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400"})]}),(0,l.jsx)(eB.A,{options:eG,selectedIds:eQ,onChange:e=>{e.length>0&&P(e[0])},className:(0,f.cn)("px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black",el?"h-6 w-20":"h-8 w-28"),placeholder:"View",hideSearch:!0}),ed&&!O.lockContent&&(0,l.jsxs)(v.z,{onClick:()=>eO(V,!0),className:(0,f.cn)("rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1",el?"h-6":"h-8"),children:[(0,l.jsx)(ei.pOD,{className:"h-3 w-3"}),!el&&(0,l.jsx)("span",{children:"Add Event"})]}),(0,l.jsxs)(eV.h_,{children:[(0,l.jsx)(eV.$F,{asChild:!0,children:(0,l.jsx)(v.z,{variant:"ghost",className:(0,f.cn)("rounded-full p-1 hover:bg-neutral-300 text-black",el?"h-6 w-6":"h-8 w-8"),children:(0,l.jsx)(eH.Z,{className:"h-4 w-4"})})}),(0,l.jsxs)(eV.AW,{className:"w-56 rounded-none py-2 flex flex-col gap-1",align:"end",children:[(0,l.jsxs)(e_._,{className:"text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer",children:[(0,l.jsx)(ei.mBM,{className:"size-3"}),(0,l.jsx)("span",{className:"flex-1 capitalize",children:"Lock content"}),(0,l.jsx)(eW.r,{className:"h-4 w-8",checked:!!O.lockContent,onCheckedChange:t=>{ev(e.view.id,e.view.pageId,{lockContent:t})},thumbClassName:"!size-3"})]}),(0,l.jsx)(eX.o,{database:en.database,trigger:(0,l.jsxs)(v.z,{variant:"ghost",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,l.jsx)(ei.hQu,{className:"size-3"}),"Default Filters",O.filter.conditions.length>0&&"(".concat(O.filter.conditions.length,")")]}),filter:O.filter,onChange:t=>ev(e.view.id,e.view.pageId,{filter:t}),currentRecordId:null==B?void 0:null===(a=B.recordInfo)||void 0===a?void 0:null===(t=a.record)||void 0===t?void 0:t.id,currentRecordDatabaseId:null==B?void 0:null===(x=B.recordInfo)||void 0===x?void 0:null===(h=x.record)||void 0===h?void 0:h.databaseId}),(0,l.jsx)(eP.H,{database:en.database,sorts:O.sorts,onChange:t=>ev(e.view.id,e.view.pageId,{sorts:t}),trigger:(0,l.jsxs)(v.z,{variant:"ghost",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,l.jsx)(ei.E92,{className:"size-3"}),"Default Sorts",O.sorts.length>0&&"(".concat(O.sorts.length,")")]})})]})]})]})]}),F&&!el&&(0,l.jsx)("div",{className:"px-2 pb-2",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(M.I,{placeholder:"Search events...",value:J,onChange:e=>G(e.target.value),className:"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none"}),(0,l.jsx)(ei._Ve,{className:"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400"})]})})]}),!es&&O.lockContent&&(0,l.jsx)(T.mb,{}),(0,l.jsxs)("div",{className:"flex-1 flex min-h-0",children:[Q&&(0,l.jsxs)("div",{className:(0,f.cn)("flex-none bg-white",F?"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg":"w-fit border-r border-neutral-300"),children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-2 border-b border-neutral-300",children:[(0,l.jsx)("h3",{className:"text-xs font-semibold text-black",children:"Calendar"}),F&&(0,l.jsxs)(v.z,{variant:"ghost",onClick:()=>K(!1),className:"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100",children:[(0,l.jsx)("span",{className:"sr-only",children:"Close"}),"\xd7"]})]}),(0,l.jsx)(g,{mode:"single",selected:V,onSelect:e=>{e&&(_(e),F&&K(!1))},className:"rounded-md border-0"})]}),(0,l.jsxs)(ec.LB,{sensors:ek,onDragStart:e=>{if(ed&&!O.lockContent&&e.active.data.current){var t,a,l,n,s;let r=ea.current.y,i=null!==(s=null===(a=e.active.rect.current)||void 0===a?void 0:null===(t=a.translated)||void 0===t?void 0:t.top)&&void 0!==s?s:0,{payload:d,type:o}=e.active.data.current,c="segment"===o?d.originalEvent.id:d.id,u=document.getElementById("event-".concat(c)),m=u?u.offsetWidth:null===(l=e.active.rect.current.translated)||void 0===l?void 0:l.width,h=u?u.offsetHeight:null===(n=e.active.rect.current.translated)||void 0===n?void 0:n.height;ee({...e.active.data.current,grabOffsetY:r-i,width:m,height:h}),document.addEventListener("mousemove",eS),document.addEventListener("touchmove",eI)}},onDragEnd:eC,modifiers:[eY],children:[(0,l.jsxs)("div",{className:"flex-1 min-w-0","data-calendar-content":"true",children:["day"===W&&(0,l.jsx)(eb,{selectedDate:V,events:eT,selectedEvent:q,setSelectedEvent:$,openAddEventForm:eO,canEditData:ed&&!O.lockContent,savedScrollTop:et,handleEventClick:e$,activeDragData:U}),"week"===W&&(0,l.jsx)(eD,{selectedDate:V,events:eT,selectedEvent:q,setSelectedEvent:$,setSelectedDate:_,openAddEventForm:eO,canEditData:ed&&!O.lockContent,savedScrollTop:et,handleEventClick:e$,activeDragData:U}),"month"===W&&(0,l.jsx)(eZ,{selectedDate:V,events:eT,selectedEvent:q,setSelectedEvent:$,setSelectedDate:_,openAddEventForm:e=>eO(e,!0),canEditData:ed&&!O.lockContent,handleEventClick:e$,activeDragData:U})]}),(0,l.jsx)(ec.y9,{dropAnimation:null,children:U&&"segment"===U.type?(0,l.jsx)(eu,{segment:U.payload,view:"day"===W?"day":"week",onClick:()=>{},style:{width:U.width,height:U.height}}):U&&"event"===U.type?(0,l.jsx)(eE,{event:U.payload,view:W,onClick:()=>{},style:{width:U.width,height:U.height}}):null})]})]})]})}},74610:function(e,t,a){var l=a(2265);let n=l.forwardRef(function(e,t){let{title:a,titleId:n,...s}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),a?l.createElement("title",{id:n},a):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"}))});t.Z=n},21726:function(e,t,a){var l=a(2265);let n=l.forwardRef(function(e,t){let{title:a,titleId:n,...s}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),a?l.createElement("title",{id:n},a):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=n}}]);