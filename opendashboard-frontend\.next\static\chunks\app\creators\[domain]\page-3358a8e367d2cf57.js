!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="608779d3-7c2b-4ec3-b7bd-bb3b76e60d42",e._sentryDebugIdIdentifier="sentry-dbid-608779d3-7c2b-4ec3-b7bd-bb3b76e60d42")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9483],{63260:function(e,n,t){Promise.resolve().then(t.bind(t,25137))},25137:function(e,n,t){"use strict";t.r(n);var d=t(57437),r=t(21966),b=t(9175);t(2265),n.default=()=>(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(b.<PERSON>ontentLayout,{title:"Welcome to Opendashboard Creator",children:(0,d.jsx)(r.E,{})})})}},function(e){e.O(0,[8310,6137,7648,311,2534,4451,1107,85,3493,7515,8107,4622,7900,2211,3561,7878,991,2971,6577,1744],function(){return e(e.s=63260)}),_N_E=e.O()}]);