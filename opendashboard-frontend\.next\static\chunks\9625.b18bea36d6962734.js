!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ed8f532b-88b5-4f84-99b8-be9daf8919c6",e._sentryDebugIdIdentifier="sentry-dbid-ed8f532b-88b5-4f84-99b8-be9daf8919c6")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9625],{79625:function(e,t,l){l.r(t),l.d(t,{SummaryTableView:function(){return R}});var s=l(57437),r=l(18626),d=l(95600),n=l(2265);l(69507),l(27659);var a=l(84440),i=l(20029),o=l(12381),c=l(26652),u=l(32060),f=l(24681),h=l(90641),m=l(84977),x=l(66312),g=l(42212),w=l(95473),p=l(87957);function b(e){return(0,s.jsx)("div",{className:"r-header h-full w-full",children:(0,s.jsx)(y,{view:e.view,databaseId:e.databaseId,trigger:(0,s.jsxs)("div",{className:"h-full text-xs flex items-center font-semibold gap-1",children:[(0,s.jsx)(c.Z,{className:"size-3"}),(0,s.jsx)("span",{children:"Add column"})]})})})}function j(e){return(0,s.jsx)("div",{className:"!bg-white h-full w-full'",children:"\xa0"})}let v=(e,t)=>({key:"add-column",name:"Add column",width:115,renderHeaderCell:l=>(0,s.jsx)(b,{...l,databaseId:e,view:t}),renderCell:e=>(0,s.jsx)(j,{...e})}),y=e=>{let{toast:t}=(0,m.V)(),{databaseStore:l}=(0,g.cF)(),{updateViewDefinition:r}=(0,w.Bf)(),[d,a]=(0,n.useState)(!1),i=e.databaseId,o=e.view.definition;o.columns=o.columns||[];let c=(0,n.useMemo)(()=>{if(!i||!l[i])return[];let e=l[i],t=[];for(let l of Object.values(e.database.definition.columnsMap)){let e={data:l,id:l.id,title:l.title,value:l.id};t.push(e)}return t},[i,l]),b=t=>{let l={id:(0,x.generateUUID)(),columnId:t.id,aggregateBy:p.ShowValuesAggregateFunction.ShowOriginal},s=[...o.columns];s.push(l),r(e.view.id,e.view.pageId,{columns:s}).then()};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(u.h_,{open:d,onOpenChange:a,children:[(0,s.jsx)(u.$F,{asChild:!0,children:e.trigger}),(0,s.jsx)(u.AW,{className:"w-56 p-0 rounded-none text-neutral-800 font-semibold flex flex-col gap-1",align:e.dropdownAlign||"end",children:(0,s.jsx)(h.ScrollArea,{className:"h-80",children:(0,s.jsx)("div",{className:"p-2",children:c.map((e,t)=>{let l=e.data;return(0,s.jsxs)(u.Xi,{onClick:()=>b(l),className:"text-xs rounded-none p-2 gap-2",children:[(0,s.jsx)(f.e,{type:l.type,className:"size-3"}),(0,s.jsxs)("span",{children:[" ",l.title]})]},l.id)})})})})]})})};var I=l(68738),C=l(13465),N=l(73299),k=l(68245);function A(e){let[t,l]=(0,d.Gt)();return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"r-row-select text-xs w-full h-full flex items-center justify-center",children:"\xa0"})})}function F(e){let[t,l]=(0,d.Gt)();return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"r-row-select text-xs size-full flex items-center justify-center",children:e.rowIdx+1})})}function z(e){let[t,l]=(0,d.Gt)();return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"r-row-select text-xs h-full flex items-center",children:(0,s.jsx)(k.X,{checked:t,onCheckedChange:t=>{l({type:"ROW",row:e.row,checked:!!t,isShiftClick:!1})}})})})}let M={key:d.W,name:"",width:25,resizable:!1,sortable:!1,frozen:!0,renderHeaderCell:e=>(0,s.jsx)(A,{...e}),renderCell:e=>(0,s.jsx)(F,{...e}),renderGroupCell:e=>(0,s.jsx)(z,{...e})};var S=l(68989),_=l.n(S),D=l(7601),H=l(29119),O=l(14803);let R=e=>{let{databaseStore:t,members:l,workspace:n}=(0,g.cF)(),{updateViewDefinition:c}=(0,w.Bf)(),{sorts:u,filter:f,search:h}=(0,w.Jy)(),{accessLevel:m}=(0,H.qt)(),x=!(0,O.cL)()&&!!m&&[D.u.Full,D.u.Edit].includes(m),{definition:p}=e,b=t[p.databaseId],j=x&&!p.lockContent;p.columns=p.columns||[];let k=(t,l)=>{let s=[...p.columns];for(let e=0;e<s.length;e++)if(s[e].id===t){s[e]={...s[e],...l};break}c(e.view.id,e.view.pageId,{columns:s}).then()},A=t=>{let l=p.columns.filter(e=>e.id!==t);c(e.view.id,e.view.pageId,{columns:l}).then()},F=(()=>{let t=[];if(t.push(M),!b)return t;let l=b.database.definition;if(!l)return t;for(let e of p.columns){if(e.isHidden)continue;let r=l.columnsMap[e.columnId];if(!r)continue;let d={databaseId:p.databaseId,column:r,customization:e},n={key:e.id,name:e.title||r.title,renderEditCell:C.YL,renderCell:C.rF,renderHeaderCell:e=>(0,s.jsx)(N._l,{...e,editable:x,updateColumn:k,deleteColumn:A}),width:200,editable:!0,__meta__:d};n.editable=n.editable&&j,t.push(n)}return j&&t.push(v(b.database.id,e.view)),t})(),z=Object.values((()=>{if(!b)return{rowMap:{},groupRowMap:{}};let e=[];u.length>0?e.push(...u):p.sorts.length>0&&e.push(...p.sorts),0===e.length&&e.push({columnId:I.MagicColumn.CreatedAt,order:I.Sort.Asc});let{rows:s}=(0,r.filterAndSortRecords)(b,l,t,p.filter,f,e,n.workspaceMember.userId);h&&h.trim()&&(s=s.filter(e=>e.processedRecord.valuesText.toLowerCase().includes(h.trim().toLowerCase())));let d={},a={},i=p.groupByIds;for(let e of s){let t={};for(let l of i)t[l]=e.record.recordValues[l];let l=JSON.stringify(t),s=_().createHash("sha1").update(l).digest("hex");if(d[s]){let t=Math.max(new Date(d[s].updatedAt).getTime(),new Date(e.updatedAt).getTime());d[s].updatedAt=new Date(t).toISOString(),d[s].recordIds.push(e.id)}else d[s]={recordIds:[e.id],updatedAt:e.updatedAt,id:s,rowMap:{}};d[s].rowMap[e.id]=e,a[e.id]=e}return{groupRowMap:d,rowMap:a}})().groupRowMap),S=F.length>1&&"add-column"!==F[1].key;return(0,s.jsx)("div",{className:"w-full h-full overflow-hidden table-view bg-white",children:S?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"size-full overflow-hidden table-view bg-white",children:(0,s.jsx)(d.ZP,{columns:F,rows:z,rowHeight:40,headerRowHeight:40,summaryRowHeight:40,className:"w-full h-full rdg-light",rowKeyGetter:function(e){return e.id}})})}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(a.PageLoader,{size:"full",error:(0,s.jsxs)("div",{className:"my-2 flex flex-col gap-4 items-center justify-center",children:[(0,s.jsx)("div",{className:"",children:(0,s.jsx)(i.aPz,{className:"size-8"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Add a column to get started"}),(0,s.jsx)(y,{view:e.view,databaseId:p.databaseId,dropdownAlign:"center",trigger:(0,s.jsxs)(o.z,{variant:"outline",className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 font-semibold",children:[(0,s.jsx)(i.oFk,{className:"size-3"}),"Add Column"]})})]})})})})}}}]);