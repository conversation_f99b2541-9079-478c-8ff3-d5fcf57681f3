{"/_not-found/page": "/_not-found", "/about/privacy/page": "/about/privacy", "/about/email-tos/page": "/about/email-tos", "/about/terms/page": "/about/terms", "/api/form-submit/route": "/api/form-submit", "/auth/error/page": "/auth/error", "/api/form-upload/route": "/api/form-upload", "/auth/magic/page": "/auth/magic", "/book-demo/page": "/book-demo", "/auth/mail/page": "/auth/mail", "/auth/waitlist/page": "/auth/waitlist", "/embed/[viewId]/[viewName]/page": "/embed/[viewId]/[viewName]", "/home/<USER>": "/home", "/favicon.ico/route": "/favicon.ico", "/free-tools/mention/page": "/free-tools/mention", "/invitation/page": "/invitation", "/shared/[viewId]/[viewName]/page": "/shared/[viewId]/[viewName]", "/free-tools/page": "/free-tools", "/free-tools/[toolId]/page": "/free-tools/[toolId]", "/page": "/", "/auth/sign-in/page": "/auth/sign-in", "/products/databases/page": "/products/databases", "/products/workflows/page": "/products/workflows", "/[domain]/search/page": "/[domain]/search", "/[domain]/emails/page": "/[domain]/emails", "/[domain]/page": "/[domain]", "/[domain]/reminders/page": "/[domain]/reminders", "/[domain]/workflows/page": "/[domain]/workflows", "/[domain]/welcome/page": "/[domain]/welcome", "/[domain]/workflows/[workflowId]/page": "/[domain]/workflows/[workflowId]", "/[domain]/setup/page": "/[domain]/setup", "/creators/new/page": "/creators/new", "/creators/page": "/creators", "/internal-admin/affiliates/approved/page": "/internal-admin/affiliates/approved", "/internal-admin/affiliates/page": "/internal-admin/affiliates", "/internal-admin/templates/approved/page": "/internal-admin/templates/approved", "/internal-admin/page": "/internal-admin", "/internal-admin/templates/awaiting-review/[id]/page": "/internal-admin/templates/awaiting-review/[id]", "/internal-admin/affiliates/waitlist/page": "/internal-admin/affiliates/waitlist", "/internal-admin/templates/awaiting-review/page": "/internal-admin/templates/awaiting-review", "/internal-admin/templates/reported/page": "/internal-admin/templates/reported", "/internal-admin/templates/most-installed/page": "/internal-admin/templates/most-installed", "/internal-admin/templates/page": "/internal-admin/templates", "/internal-admin/templates/most-purchased/page": "/internal-admin/templates/most-purchased", "/welcome/referrals/page": "/welcome/referrals", "/welcome/onboarding/page": "/welcome/onboarding", "/templates/[slug]/page": "/templates/[slug]", "/templates/purchases/page": "/templates/purchases", "/welcome/setup/workspace/page": "/welcome/setup/workspace", "/welcome/page": "/welcome", "/welcome/referral/page": "/welcome/referral", "/welcome/setup/page": "/welcome/setup", "/templates/categories/page": "/templates/categories", "/templates/[slug]/[id]/page": "/templates/[slug]/[id]", "/templates/page": "/templates", "/templates/tags/[slug]/page": "/templates/tags/[slug]", "/templates/creator/[username]/page": "/templates/creator/[username]", "/templates/categories/[slug]/page": "/templates/categories/[slug]", "/templates/search/page": "/templates/search", "/[domain]/[pageId]/page": "/[domain]/[pageId]", "/[domain]/databases/[databaseId]/page": "/[domain]/databases/[databaseId]", "/[domain]/databases/[databaseId]/records/[recordId]/page": "/[domain]/databases/[databaseId]/records/[recordId]", "/[domain]/emails/[emailId]/sequences/[sequenceId]/page": "/[domain]/emails/[emailId]/sequences/[sequenceId]", "/[domain]/settings/billing/page": "/[domain]/settings/billing", "/[domain]/emails/[emailId]/page": "/[domain]/emails/[emailId]", "/[domain]/settings/account/page": "/[domain]/settings/account", "/[domain]/settings/api-keys/page": "/[domain]/settings/api-keys", "/[domain]/settings/members/page": "/[domain]/settings/members", "/[domain]/settings/page": "/[domain]/settings", "/[domain]/settings/migration/page": "/[domain]/settings/migration", "/[domain]/settings/notifications/page": "/[domain]/settings/notifications", "/[domain]/settings/plans/page": "/[domain]/settings/plans", "/[domain]/settings/referrals/page": "/[domain]/settings/referrals", "/[domain]/settings/sessions/page": "/[domain]/settings/sessions", "/[domain]/settings/workspace/page": "/[domain]/settings/workspace", "/[domain]/settings/senders/page": "/[domain]/settings/senders", "/[domain]/templates/discover/page": "/[domain]/templates/discover", "/[domain]/templates/installed/page": "/[domain]/templates/installed", "/[domain]/templates/page": "/[domain]/templates", "/[domain]/settings/secrets/page": "/[domain]/settings/secrets", "/creators/[domain]/discounts/page": "/creators/[domain]/discounts", "/creators/[domain]/page": "/creators/[domain]", "/creators/[domain]/purchases/page": "/creators/[domain]/purchases", "/creators/[domain]/settings/page": "/creators/[domain]/settings", "/creators/[domain]/payouts/page": "/creators/[domain]/payouts", "/creators/[domain]/templates/page": "/creators/[domain]/templates", "/creators/[domain]/purchases/[purchaseId]/page": "/creators/[domain]/purchases/[purchaseId]", "/[domain]/[pageId]/views/[viewId]/page": "/[domain]/[pageId]/views/[viewId]", "/[domain]/databases/[databaseId]/views/[viewId]/page": "/[domain]/databases/[databaseId]/views/[viewId]", "/creators/[domain]/templates/[templateId]/page": "/creators/[domain]/templates/[templateId]", "/creators/[domain]/templates/[templateId]/purchases/page": "/creators/[domain]/templates/[templateId]/purchases", "/creators/[domain]/templates/[templateId]/presence/page": "/creators/[domain]/templates/[templateId]/presence", "/creators/[domain]/templates/[templateId]/discussions/page": "/creators/[domain]/templates/[templateId]/discussions", "/creators/[domain]/templates/[templateId]/releases/page": "/creators/[domain]/templates/[templateId]/releases"}