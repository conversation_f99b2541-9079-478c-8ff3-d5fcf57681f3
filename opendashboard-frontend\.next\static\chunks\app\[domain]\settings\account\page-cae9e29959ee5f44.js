!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="4c6f529e-530f-4f0b-a6af-c7454cb81c24",e._sentryDebugIdIdentifier="sentry-dbid-4c6f529e-530f-4f0b-a6af-c7454cb81c24")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3266,7170],{49047:function(e,t,r){Promise.resolve().then(r.bind(r,9175)),Promise.resolve().then(r.bind(r,49905))},96246:function(e,t,r){"use strict";r.d(t,{F$:function(){return o},Q5:function(){return d},qE:function(){return i}});var n=r(57437),a=r(2265),s=r(61146),l=r(93448);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.fC,{ref:t,className:(0,l.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",r),...a})});i.displayName=s.fC.displayName;let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.Ee,{ref:t,className:(0,l.cn)("aspect-square h-full w-full",r),...a})});o.displayName=s.Ee.displayName;let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.NY,{ref:t,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-none font-semibold bg-neutral-300 capitalize",r),...a})});d.displayName=s.NY.displayName},12381:function(e,t,r){"use strict";r.d(t,{d:function(){return o},z:function(){return d}});var n=r(57437),a=r(2265),s=r(37053),l=r(90535),i=r(93448);let o=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-neutral-300 border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-8 px-4 py-2",sm:"h-7 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-8 w-8"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:d=!1,...c}=e,u=d?s.Slot:"button";return(0,n.jsx)(u,{className:(0,i.cn)(o({variant:a,size:l,className:r})),ref:t,...c})});d.displayName="Button"},32060:function(e,t,r){"use strict";r.d(t,{$F:function(){return d},AW:function(){return p},Ju:function(){return g},KM:function(){return b},Ph:function(){return f},Qk:function(){return c},TG:function(){return x},VD:function(){return v},Xi:function(){return h},cq:function(){return u},h_:function(){return o},kt:function(){return m}});var n=r(57437),a=r(2265),s=r(70085),l=r(20653),i=r(93448);let o=s.fC,d=s.xz,c=s.ZA,u=s.Uv,f=s.Tr;s.Ee;let m=a.forwardRef((e,t)=>{let{className:r,inset:a,children:o,...d}=e;return(0,n.jsxs)(s.fF,{ref:t,className:(0,i.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",r),...d,children:[o,(0,n.jsx)(l.XCv,{className:"ml-auto h-4 w-4"})]})});m.displayName=s.fF.displayName;let x=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.tu,{ref:t,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...a})});x.displayName=s.tu.displayName;let p=a.forwardRef((e,t)=>{let{className:r,sideOffset:a=4,...l}=e;return(0,n.jsx)(s.Uv,{children:(0,n.jsx)(s.VY,{ref:t,sideOffset:a,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...l})})});p.displayName=s.VY.displayName;let h=a.forwardRef((e,t)=>{let{className:r,inset:a,...l}=e;return(0,n.jsx)(s.ck,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a&&"pl-8",r),...l})});h.displayName=s.ck.displayName,a.forwardRef((e,t)=>{let{className:r,children:a,checked:o,...d}=e;return(0,n.jsxs)(s.oC,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:o,...d,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(s.wU,{children:(0,n.jsx)(l.nQG,{className:"h-4 w-4"})})}),a]})}).displayName=s.oC.displayName,a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,n.jsxs)(s.Rk,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(s.wU,{children:(0,n.jsx)(l.jXb,{className:"h-4 w-4 fill-current"})})}),a]})}).displayName=s.Rk.displayName;let g=a.forwardRef((e,t)=>{let{className:r,inset:a,...l}=e;return(0,n.jsx)(s.__,{ref:t,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",r),...l})});g.displayName=s.__.displayName;let v=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.Z0,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",r),...a})});v.displayName=s.Z0.displayName;let b=e=>{let{className:t,...r}=e;return(0,n.jsx)("span",{className:(0,i.cn)("ml-auto text-xs tracking-widest opacity-60",t),...r})};b.displayName="DropdownMenuShortcut"},40279:function(e,t,r){"use strict";r.d(t,{I:function(){return l}});var n=r(57437),a=r(2265),s=r(93448);let l=a.forwardRef((e,t)=>{let{className:r,type:a,...l}=e;return(0,n.jsx)("input",{type:a,className:(0,s.cn)("flex h-8 w-full rounded-md border border-input border-neutral-300  bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...l})});l.displayName="Input"},75060:function(e,t,r){"use strict";r.d(t,{_:function(){return d}});var n=r(57437),a=r(2265),s=r(6394),l=r(90535),i=r(93448);let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.f,{ref:t,className:(0,i.cn)(o(),r),...a})});d.displayName=s.f.displayName},90641:function(e,t,r){"use strict";r.d(t,{B:function(){return o},ScrollArea:function(){return i}});var n=r(57437),a=r(2265),s=r(43643),l=r(93448);let i=a.forwardRef((e,t)=>{let{className:r,children:a,...i}=e;return(0,n.jsxs)(s.fC,{ref:t,className:(0,l.cn)("relative overflow-hidden",r),...i,children:[(0,n.jsx)(s.l_,{className:"h-full w-full rounded-[inherit]",children:a}),(0,n.jsx)(o,{}),(0,n.jsx)(s.Ns,{})]})});i.displayName=s.fC.displayName;let o=a.forwardRef((e,t)=>{let{className:r,orientation:a="vertical",...i}=e;return(0,n.jsx)(s.gb,{ref:t,orientation:a,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===a&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",r),...i,children:(0,n.jsx)(s.q4,{className:"relative flex-1 rounded-full bg-border"})})});o.displayName=s.gb.displayName},9175:function(e,t,r){"use strict";r.d(t,{J:function(){return a},MainContentLayout:function(){return u}});var n,a,s=r(57437);r(2265);var l=r(12381),i=r(40178),o=r(32060),d=r(40279),c=r(99376);(n=a||(a={})).Import="import",n.Export="export",n.ActivateMessaging="enableMessaging",n.ManageAccess="manageAccess",n.CopyLink="copyLink",n.ConfigureTitle="configureTitle";let u=e=>{let t=(0,c.useRouter)();return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"w-full h-full overflow-hidden flex flex-col",children:[(0,s.jsx)("div",{className:"title w-full border-b border-neutral-300 h-12 flex items-center",children:(0,s.jsxs)("div",{className:"overflow-hidden w-full flex items-center p-2 gap-2 justify-start",children:[e.onBack&&(0,s.jsx)(l.z,{className:"text-xs font-semibold h-auto p-1.5 items-center hover:bg-transparent",onClick:()=>{var r;"string"==typeof e.onBack?t.push(e.onBack):null===(r=e.onBack)||void 0===r||r.call(e)},variant:"ghost",children:"←"}),(e.icon||e.emoji)&&(0,s.jsx)(l.z,{variant:"ghost",className:"text-xl hover:bg-neutral-300 p-1 size-6 rounded-full items-center justify-center",children:(0,s.jsx)("span",{className:"relative",children:e.icon?e.icon:e.emoji})}),"string"==typeof e.title?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,s.jsx)(d.I,{className:"overflow-hidden truncate text-left font-semibold text-sm !border-0 pl-2 !shadow-none !outline-none !ring-0 text-black",readOnly:!e.editable,value:e.title||"Untitled"})})}):(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:e.title}),(0,s.jsx)("div",{children:e.titleRightContent&&(0,s.jsx)(s.Fragment,{children:e.titleRightContent})}),e.moreActions&&e.moreActions.onAction&&e.moreActions.actions.length>0&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(o.h_,{children:[(0,s.jsx)(o.$F,{asChild:!0,children:(0,s.jsx)(l.z,{variant:"ghost",className:"mr-2 hover:bg-neutral-300 p-1 size-6 rounded-full",children:(0,s.jsx)(i.Z,{className:"size-4"})})}),(0,s.jsx)(o.AW,{className:"w-56 rounded-none p-1.5",align:"end",children:(0,s.jsx)(o.Qk,{children:e.moreActions.actions.map((t,r)=>(0,s.jsxs)(o.Xi,{className:"rounded-none text-xs font-semibold cursor-pointer truncate",onClick:r=>{var n;return null===(n=e.moreActions)||void 0===n?void 0:n.onAction(t.key)},children:[t.label,t.shortcut&&(0,s.jsx)(o.KM,{children:t.shortcut})]},r))})})]})})]})}),(0,s.jsx)("div",{className:"body flex-1 overflow-hidden",children:(0,s.jsx)("div",{className:"w-full h-full",children:e.children})})]})})}},49905:function(e,t,r){"use strict";r.d(t,{ProfileSettings:function(){return h}});var n=r(57437),a=r(90641),s=r(96246),l=r(75060),i=r(40279),o=r(2265),d=r(39255),c=r(12381),u=r(3163),f=r(14438),m=r(17807),x=r(37810),p=r(12250);let h=()=>{let[e,t]=o.useState(!1),{user:r,token:h,updateUser:g}=(0,d.a)(),[v,b]=(0,o.useState)((null==r?void 0:r.firstName)||""),[N,y]=(0,o.useState)((null==r?void 0:r.lastName)||""),[j,w]=(0,o.useState)((null==r?void 0:r.email)||""),[k,C]=(0,o.useState)(!1),[z,R]=(0,o.useState)(!1),[F,_]=(0,o.useState)(!1),A=(0,o.useRef)(null),[S,E]=(0,o.useState)(!1),[I,B]=(0,o.useState)(0),P=async e=>{if(!r||!h)return;let{firstName:n,lastName:a,email:s}=r,l={firstName:n,lastName:a,email:s,...e},i=await (0,m.ck)(h.token,l);if(t(!1),i.error){f.Am.error(i.error);return}g(i.data.data.user),f.Am.success("Profile updated successfully")},U=async e=>{if(!h)return;let t=e.target.files;if(0===t.length)return;let r=t[0],n=x.g.Fit,a=await (0,x.t)(r,{width:500,height:500},n);(0,m.tU)(h.token,a,{onStart(){E(!0),B(0)},onComplete:e=>{if(E(!1),!e.isSuccess){let t=e.error||(0,u.E)();f.Am.error(t);return}let{profilePhoto:t}=e.data.data.user;g({profilePhoto:t})},onProgress:e=>{B(e)}})};v.trim()&&N.trim()&&(0,p.o)(j);let M="".concat(v[0]||"").concat(N[0]||"").trim()||"-";return(0,n.jsx)(a.ScrollArea,{className:"size-full",children:(0,n.jsx)("div",{className:"p-4 pb-20",children:(0,n.jsx)("div",{className:"max-w-[500px]",children:(0,n.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,n.jsx)("div",{className:"col-span-full",children:(0,n.jsxs)("div",{className:"flex items-center gap-x-3",children:[(0,n.jsx)("div",{children:(0,n.jsxs)(s.qE,{className:"w-14 h-14",children:[(0,n.jsx)(s.F$,{className:"size-full",src:(null==r?void 0:r.profilePhoto)||""}),(0,n.jsx)(s.Q5,{children:M})]})}),(0,n.jsxs)("div",{children:[(0,n.jsxs)(c.z,{variant:"outline",type:"button",onClick:()=>{A&&A.current&&A.current.click()},disabled:e||S,className:"rounded-none px-2.5 py-1.5 text-xs h-auto font-semibold",children:[!S&&(0,n.jsx)(n.Fragment,{children:"Upload picture"}),S&&(0,n.jsxs)(n.Fragment,{children:["Uploading (",I,"%)"]})]}),(0,n.jsx)("br",{}),(0,n.jsx)("div",{className:"hidden",children:(0,n.jsx)("input",{type:"file",ref:A,multiple:!0,accept:"image/*",onChange:U})}),(0,n.jsx)("text",{className:"text-xs text-muted-foreground",children:"JPG, GIF or PNG up to 5MB. Recommended size 400x400px"})]})]})}),(0,n.jsxs)("div",{className:"flex flex-col flex-1 gap-1",children:[(0,n.jsx)(l._,{className:"block text-sm font-medium leading-6 text-gray-900",htmlFor:"firstName",children:"Firstname"}),(0,n.jsx)(i.I,{id:"firstName",placeholder:"Firstname",type:"text",autoCapitalize:"none",autoCorrect:"off",value:v,className:"rounded-none ".concat(k&&"!ring-red-500"),onBlur:e=>{v.trim()&&v.trim()!==(null==r?void 0:r.firstName)&&P({firstName:v.trim()}).then()},onChange:e=>{b(e.target.value),C(!e.target.value.trim())}})]}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col gap-1",children:[(0,n.jsx)(l._,{className:"block font-medium leading-6 text-gray-900",htmlFor:"lastName",children:"Lastname"}),(0,n.jsx)(i.I,{id:"lastName",placeholder:"Lastname",type:"text",autoCapitalize:"none",autoCorrect:"off",value:N,className:"rounded-none ".concat(z&&"!ring-red-500"),onBlur:e=>{N.trim()&&N.trim()!==(null==r?void 0:r.lastName)&&P({lastName:N.trim()}).then()},onChange:e=>{y(e.target.value),R(!e.target.value.trim())}})]}),(0,n.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,n.jsx)(l._,{className:"block font-medium leading-6 text-gray-900",htmlFor:"email",children:"Email"}),(0,n.jsx)(i.I,{id:"email",placeholder:"Email",type:"email",autoCapitalize:"none",autoComplete:"email",autoCorrect:"off",value:j,className:"rounded-none ".concat(F&&"!ring-red-500"),onBlur:e=>{j.trim()&&j.trim()!==(null==r?void 0:r.email)&&P({email:j.trim()}).then()},onChange:e=>{w(e.target.value),_(!e.target.value.trim()||!(0,p.o)(e.target.value.trim()))}})]})]})})})})}},37810:function(e,t,r){"use strict";var n,a;function s(e,t,r,n){return new Promise((a,s)=>{let l=new Image;l.src=URL.createObjectURL(e),l.onload=()=>{let i,o,d,c;let u=document.createElement("canvas"),{width:f,height:m}=t;u.width=f,u.height=m;let x=u.getContext("2d"),p=l.width/l.height,h=f/m;p>h&&"fit"===r||p<h&&"fill"===r?(c=l.height,d=l.height*h,i=(l.width-d)/2,o=0):(d=l.width,c=l.width/h,i=0,o=(l.height-c)/2),x.drawImage(l,i,o,d,c,0,0,f,m);let g=(null==n?void 0:n.quality)?n.quality/100:void 0;u.toBlob(t=>{t?a(new File([t],e.name,{type:t.type,lastModified:new Date().getTime()})):s(Error("Canvas toBlob failed"))},e.type,g)},l.onerror=e=>{s(e)}})}r.d(t,{g:function(){return n},t:function(){return s}}),(a=n||(n={})).Fit="fit",a.Fill="fill"},12250:function(e,t,r){"use strict";r.d(t,{o:function(){return a}});var n=r(53731);let a=e=>n.G(e)}},function(e){e.O(0,[8310,6137,311,2534,1107,85,3493,8107,7297,7900,991,2971,6577,1744],function(){return e(e.s=49047)}),_N_E=e.O()}]);