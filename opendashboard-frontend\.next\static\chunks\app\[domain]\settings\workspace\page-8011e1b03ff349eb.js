!function(){try{var a="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e=(new a.<PERSON>rror).stack;e&&(a._sentryDebugIds=a._sentryDebugIds||{},a._sentryDebugIds[e]="9f5f3b9b-e90f-4947-ae80-1a96430255fb",a._sentryDebugIdIdentifier="sentry-dbid-9f5f3b9b-e90f-4947-ae80-1a96430255fb")}catch(a){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3639],{58368:function(a,e,i){Promise.resolve().then(i.bind(i,9175)),Promise.resolve().then(i.bind(i,19401))},99376:function(a,e,i){"use strict";var r=i(35475);i.o(r,"redirect")&&i.d(e,{redirect:function(){return r.redirect}}),i.o(r,"useParams")&&i.d(e,{useParams:function(){return r.useParams}}),i.o(r,"usePathname")&&i.d(e,{usePathname:function(){return r.usePathname}}),i.o(r,"useRouter")&&i.d(e,{useRouter:function(){return r.useRouter}}),i.o(r,"useSearchParams")&&i.d(e,{useSearchParams:function(){return r.useSearchParams}})},24369:function(a,e,i){"use strict";var r=i(2265),n="function"==typeof Object.is?Object.is:function(a,e){return a===e&&(0!==a||1/a==1/e)||a!=a&&e!=e},t=r.useState,c=r.useEffect,s=r.useLayoutEffect,o=r.useDebugValue;function l(a){var e=a.getSnapshot;a=a.value;try{var i=e();return!n(a,i)}catch(a){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,e){return e()}:function(a,e){var i=e(),r=t({inst:{value:i,getSnapshot:e}}),n=r[0].inst,u=r[1];return s(function(){n.value=i,n.getSnapshot=e,l(n)&&u({inst:n})},[a,i,e]),c(function(){return l(n)&&u({inst:n}),a(function(){l(n)&&u({inst:n})})},[a]),o(i),i};e.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},82558:function(a,e,i){"use strict";a.exports=i(24369)},49971:function(a,e,i){"use strict";i.d(e,{C:function(){return l}});var r=i(57437),n=i(67118);let t=["Africa/Abidjan","Africa/Accra","Africa/Addis_Ababa","Africa/Algiers","Africa/Asmara","Africa/Asmera","Africa/Bamako","Africa/Bangui","Africa/Banjul","Africa/Bissau","Africa/Blantyre","Africa/Brazzaville","Africa/Bujumbura","Africa/Cairo","Africa/Casablanca","Africa/Ceuta","Africa/Conakry","Africa/Dakar","Africa/Dar_es_Salaam","Africa/Djibouti","Africa/Douala","Africa/El_Aaiun","Africa/Freetown","Africa/Gaborone","Africa/Harare","Africa/Johannesburg","Africa/Juba","Africa/Kampala","Africa/Khartoum","Africa/Kigali","Africa/Kinshasa","Africa/Lagos","Africa/Libreville","Africa/Lome","Africa/Luanda","Africa/Lubumbashi","Africa/Lusaka","Africa/Malabo","Africa/Maputo","Africa/Maseru","Africa/Mbabane","Africa/Mogadishu","Africa/Monrovia","Africa/Nairobi","Africa/Ndjamena","Africa/Niamey","Africa/Nouakchott","Africa/Ouagadougou","Africa/Porto-Novo","Africa/Sao_Tome","Africa/Timbuktu","Africa/Tripoli","Africa/Tunis","Africa/Windhoek","America/Adak","America/Anchorage","America/Anguilla","America/Antigua","America/Araguaina","America/Argentina/Buenos_Aires","America/Argentina/Catamarca","America/Argentina/ComodRivadavia","America/Argentina/Cordoba","America/Argentina/Jujuy","America/Argentina/La_Rioja","America/Argentina/Mendoza","America/Argentina/Rio_Gallegos","America/Argentina/Salta","America/Argentina/San_Juan","America/Argentina/San_Luis","America/Argentina/Tucuman","America/Argentina/Ushuaia","America/Aruba","America/Asuncion","America/Atikokan","America/Atka","America/Bahia","America/Bahia_Banderas","America/Barbados","America/Belem","America/Belize","America/Blanc-Sablon","America/Boa_Vista","America/Bogota","America/Boise","America/Buenos_Aires","America/Cambridge_Bay","America/Campo_Grande","America/Cancun","America/Caracas","America/Catamarca","America/Cayenne","America/Cayman","America/Chicago","America/Chihuahua","America/Coral_Harbour","America/Cordoba","America/Costa_Rica","America/Creston","America/Cuiaba","America/Curacao","America/Danmarkshavn","America/Dawson","America/Dawson_Creek","America/Denver","America/Detroit","America/Dominica","America/Edmonton","America/Eirunepe","America/El_Salvador","America/Ensenada","America/Fort_Nelson","America/Fort_Wayne","America/Fortaleza","America/Glace_Bay","America/Godthab","America/Goose_Bay","America/Grand_Turk","America/Grenada","America/Guadeloupe","America/Guatemala","America/Guayaquil","America/Guyana","America/Halifax","America/Havana","America/Hermosillo","America/Indiana/Indianapolis","America/Indiana/Knox","America/Indiana/Marengo","America/Indiana/Petersburg","America/Indiana/Tell_City","America/Indiana/Vevay","America/Indiana/Vincennes","America/Indiana/Winamac","America/Indianapolis","America/Inuvik","America/Iqaluit","America/Jamaica","America/Jujuy","America/Juneau","America/Kentucky/Louisville","America/Kentucky/Monticello","America/Knox_IN","America/Kralendijk","America/La_Paz","America/Lima","America/Los_Angeles","America/Louisville","America/Lower_Princes","America/Maceio","America/Managua","America/Manaus","America/Marigot","America/Martinique","America/Matamoros","America/Mazatlan","America/Mendoza","America/Menominee","America/Merida","America/Metlakatla","America/Mexico_City","America/Miquelon","America/Moncton","America/Monterrey","America/Montevideo","America/Montreal","America/Montserrat","America/Nassau","America/New_York","America/Nipigon","America/Nome","America/Noronha","America/North_Dakota/Beulah","America/North_Dakota/Center","America/North_Dakota/New_Salem","America/Ojinaga","America/Panama","America/Pangnirtung","America/Paramaribo","America/Phoenix","America/Port-au-Prince","America/Port_of_Spain","America/Porto_Acre","America/Porto_Velho","America/Puerto_Rico","America/Punta_Arenas","America/Rainy_River","America/Rankin_Inlet","America/Recife","America/Regina","America/Resolute","America/Rio_Branco","America/Rosario","America/Santa_Isabel","America/Santarem","America/Santiago","America/Santo_Domingo","America/Sao_Paulo","America/Scoresbysund","America/Shiprock","America/Sitka","America/St_Barthelemy","America/St_Johns","America/St_Kitts","America/St_Lucia","America/St_Thomas","America/St_Vincent","America/Swift_Current","America/Tegucigalpa","America/Thule","America/Thunder_Bay","America/Tijuana","America/Toronto","America/Tortola","America/Vancouver","America/Virgin","America/Whitehorse","America/Winnipeg","America/Yakutat","America/Yellowknife","Antarctica/Casey","Antarctica/Davis","Antarctica/DumontDUrville","Antarctica/Macquarie","Antarctica/Mawson","Antarctica/McMurdo","Antarctica/Palmer","Antarctica/Rothera","Antarctica/South_Pole","Antarctica/Syowa","Antarctica/Troll","Antarctica/Vostok","Arctic/Longyearbyen","Asia/Aden","Asia/Almaty","Asia/Amman","Asia/Anadyr","Asia/Aqtau","Asia/Aqtobe","Asia/Ashgabat","Asia/Ashkhabad","Asia/Atyrau","Asia/Baghdad","Asia/Bahrain","Asia/Baku","Asia/Bangkok","Asia/Barnaul","Asia/Beirut","Asia/Bishkek","Asia/Brunei","Asia/Calcutta","Asia/Chita","Asia/Choibalsan","Asia/Chongqing","Asia/Chungking","Asia/Colombo","Asia/Dacca","Asia/Damascus","Asia/Dhaka","Asia/Dili","Asia/Dubai","Asia/Dushanbe","Asia/Famagusta","Asia/Gaza","Asia/Harbin","Asia/Hebron","Asia/Ho_Chi_Minh","Asia/Hong_Kong","Asia/Hovd","Asia/Irkutsk","Asia/Istanbul","Asia/Jakarta","Asia/Jayapura","Asia/Jerusalem","Asia/Kabul","Asia/Kamchatka","Asia/Karachi","Asia/Kashgar","Asia/Kathmandu","Asia/Katmandu","Asia/Khandyga","Asia/Kolkata","Asia/Krasnoyarsk","Asia/Kuala_Lumpur","Asia/Kuching","Asia/Kuwait","Asia/Macao","Asia/Macau","Asia/Magadan","Asia/Makassar","Asia/Manila","Asia/Muscat","Asia/Nicosia","Asia/Novokuznetsk","Asia/Novosibirsk","Asia/Omsk","Asia/Oral","Asia/Phnom_Penh","Asia/Pontianak","Asia/Pyongyang","Asia/Qatar","Asia/Qyzylorda","Asia/Rangoon","Asia/Riyadh","Asia/Saigon","Asia/Sakhalin","Asia/Samarkand","Asia/Seoul","Asia/Shanghai","Asia/Singapore","Asia/Srednekolymsk","Asia/Taipei","Asia/Tashkent","Asia/Tbilisi","Asia/Tehran","Asia/Tel_Aviv","Asia/Thimbu","Asia/Thimphu","Asia/Tokyo","Asia/Tomsk","Asia/Ujung_Pandang","Asia/Ulaanbaatar","Asia/Ulan_Bator","Asia/Urumqi","Asia/Ust-Nera","Asia/Vientiane","Asia/Vladivostok","Asia/Yakutsk","Asia/Yangon","Asia/Yekaterinburg","Asia/Yerevan","Atlantic/Azores","Atlantic/Bermuda","Atlantic/Canary","Atlantic/Cape_Verde","Atlantic/Faeroe","Atlantic/Faroe","Atlantic/Jan_Mayen","Atlantic/Madeira","Atlantic/Reykjavik","Atlantic/South_Georgia","Atlantic/St_Helena","Atlantic/Stanley","Australia/ACT","Australia/Adelaide","Australia/Brisbane","Australia/Broken_Hill","Australia/Canberra","Australia/Currie","Australia/Darwin","Australia/Eucla","Australia/Hobart","Australia/LHI","Australia/Lindeman","Australia/Lord_Howe","Australia/Melbourne","Australia/NSW","Australia/North","Australia/Perth","Australia/Queensland","Australia/South","Australia/Sydney","Australia/Tasmania","Australia/Victoria","Australia/West","Australia/Yancowinna","Brazil/Acre","Brazil/DeNoronha","Brazil/East","Brazil/West","CET","CST6CDT","Canada/Atlantic","Canada/Central","Canada/Eastern","Canada/Mountain","Canada/Newfoundland","Canada/Pacific","Canada/Saskatchewan","Canada/Yukon","Chile/Continental","Chile/EasterIsland","Cuba","EET","EST","EST5EDT","Egypt","Eire","Etc/GMT","Etc/GMT+0","Etc/GMT+1","Etc/GMT+10","Etc/GMT+11","Etc/GMT+12","Etc/GMT+2","Etc/GMT+3","Etc/GMT+4","Etc/GMT+5","Etc/GMT+6","Etc/GMT+7","Etc/GMT+8","Etc/GMT+9","Etc/GMT-0","Etc/GMT-1","Etc/GMT-10","Etc/GMT-11","Etc/GMT-12","Etc/GMT-13","Etc/GMT-14","Etc/GMT-2","Etc/GMT-3","Etc/GMT-4","Etc/GMT-5","Etc/GMT-6","Etc/GMT-7","Etc/GMT-8","Etc/GMT-9","Etc/GMT0","Etc/Greenwich","Etc/UCT","Etc/UTC","Etc/Universal","Etc/Zulu","Europe/Amsterdam","Europe/Andorra","Europe/Astrakhan","Europe/Athens","Europe/Belfast","Europe/Belgrade","Europe/Berlin","Europe/Bratislava","Europe/Brussels","Europe/Bucharest","Europe/Budapest","Europe/Busingen","Europe/Chisinau","Europe/Copenhagen","Europe/Dublin","Europe/Gibraltar","Europe/Guernsey","Europe/Helsinki","Europe/Isle_of_Man","Europe/Istanbul","Europe/Jersey","Europe/Kaliningrad","Europe/Kiev","Europe/Kirov","Europe/Lisbon","Europe/Ljubljana","Europe/London","Europe/Luxembourg","Europe/Madrid","Europe/Malta","Europe/Mariehamn","Europe/Minsk","Europe/Monaco","Europe/Moscow","Europe/Nicosia","Europe/Oslo","Europe/Paris","Europe/Podgorica","Europe/Prague","Europe/Riga","Europe/Rome","Europe/Samara","Europe/San_Marino","Europe/Sarajevo","Europe/Saratov","Europe/Simferopol","Europe/Skopje","Europe/Sofia","Europe/Stockholm","Europe/Tallinn","Europe/Tirane","Europe/Tiraspol","Europe/Ulyanovsk","Europe/Uzhgorod","Europe/Vaduz","Europe/Vatican","Europe/Vienna","Europe/Vilnius","Europe/Volgograd","Europe/Warsaw","Europe/Zagreb","Europe/Zaporozhye","Europe/Zurich","GB","GB-Eire","GMT","GMT+0","GMT-0","GMT0","Greenwich","HST","Hongkong","Iceland","Indian/Antananarivo","Indian/Chagos","Indian/Christmas","Indian/Cocos","Indian/Comoro","Indian/Kerguelen","Indian/Mahe","Indian/Maldives","Indian/Mauritius","Indian/Mayotte","Indian/Reunion","Iran","Israel","Jamaica","Japan","Kwajalein","Libya","MET","MST","MST7MDT","Mexico/BajaNorte","Mexico/BajaSur","Mexico/General","NZ","NZ-CHAT","Navajo","PRC","PST8PDT","Pacific/Apia","Pacific/Auckland","Pacific/Bougainville","Pacific/Chatham","Pacific/Chuuk","Pacific/Easter","Pacific/Efate","Pacific/Enderbury","Pacific/Fakaofo","Pacific/Fiji","Pacific/Funafuti","Pacific/Galapagos","Pacific/Gambier","Pacific/Guadalcanal","Pacific/Guam","Pacific/Honolulu","Pacific/Johnston","Pacific/Kiritimati","Pacific/Kosrae","Pacific/Kwajalein","Pacific/Majuro","Pacific/Marquesas","Pacific/Midway","Pacific/Nauru","Pacific/Niue","Pacific/Norfolk","Pacific/Noumea","Pacific/Pago_Pago","Pacific/Palau","Pacific/Pitcairn","Pacific/Pohnpei","Pacific/Ponape","Pacific/Port_Moresby","Pacific/Rarotonga","Pacific/Saipan","Pacific/Samoa","Pacific/Tahiti","Pacific/Tarawa","Pacific/Tongatapu","Pacific/Truk","Pacific/Wake","Pacific/Wallis","Pacific/Yap","Poland","Portugal","ROC","ROK","Singapore","Turkey","UCT","US/Alaska","US/Aleutian","US/Arizona","US/Central","US/East-Indiana","US/Eastern","US/Hawaii","US/Indiana-Starke","US/Michigan","US/Mountain","US/Pacific","US/Pacific-New","US/Samoa","UTC","Universal","W-SU","WET","Zulu"];var c=i(36675);i(2265);var s=i(12381),o=i(93448);let l=a=>{let e=u(t);return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(c.A,{options:e,onChange:e=>{var i;0!==e.length&&(null===(i=a.onChange)||void 0===i||i.call(a,e[0]))},selectedIds:a.timezone?[a.timezone]:[],itemSelectionRender:(a,e,i,n,t)=>(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(s.z,{variant:"ghost",onClick:a=>t(i.id,!n),className:(0,o.cn)("text-xs gap-2 rounded-none p-1.5 mb-1",n&&"bg-neutral-100"),children:[(0,r.jsx)("span",{className:"font-medium",children:i.title}),(0,r.jsx)("span",{className:"flex-1"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:i.data})]})}),itemRender:(a,e,i)=>(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"text-xs gap-2 w-full justify-start flex",children:[(0,r.jsx)("span",{className:"font-medium",children:i.title}),(0,r.jsx)("span",{className:"flex-1"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:i.data})]})}),className:a.className,placeholder:"Choose timezone",disabled:a.disabled})})},u=a=>{let e=[];for(let i of a){let a=(0,n.initDateWithTz)(new Date,i);if(!a){console.log({timezone:i});continue}let t=d(a);e.push({id:i,value:i,title:i,data:t,titleNode:(0,r.jsxs)("div",{className:"flex text-xs w-full gap-2",children:[(0,r.jsx)("span",{className:"font-semibold",children:i}),(0,r.jsx)("span",{className:"flex-1"}),(0,r.jsx)("span",{children:t})]})})}return e},d=a=>a.toLocaleString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0})},96246:function(a,e,i){"use strict";i.d(e,{F$:function(){return o},Q5:function(){return l},qE:function(){return s}});var r=i(57437),n=i(2265),t=i(61146),c=i(93448);let s=n.forwardRef((a,e)=>{let{className:i,...n}=a;return(0,r.jsx)(t.fC,{ref:e,className:(0,c.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",i),...n})});s.displayName=t.fC.displayName;let o=n.forwardRef((a,e)=>{let{className:i,...n}=a;return(0,r.jsx)(t.Ee,{ref:e,className:(0,c.cn)("aspect-square h-full w-full",i),...n})});o.displayName=t.Ee.displayName;let l=n.forwardRef((a,e)=>{let{className:i,...n}=a;return(0,r.jsx)(t.NY,{ref:e,className:(0,c.cn)("flex h-full w-full items-center justify-center rounded-none font-semibold bg-neutral-300 capitalize",i),...n})});l.displayName=t.NY.displayName},94589:function(a,e,i){"use strict";i.d(e,{r:function(){return s}});var r=i(57437),n=i(2265),t=i(50721),c=i(93448);let s=n.forwardRef((a,e)=>{let{className:i,thumbClassName:n,...s}=a;return(0,r.jsx)(t.fC,{className:(0,c.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",i),...s,ref:e,children:(0,r.jsx)(t.bU,{className:(0,c.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0",n)})})});s.displayName=t.fC.displayName},9175:function(a,e,i){"use strict";i.d(e,{J:function(){return n},MainContentLayout:function(){return d}});var r,n,t=i(57437);i(2265);var c=i(12381),s=i(40178),o=i(32060),l=i(40279),u=i(99376);(r=n||(n={})).Import="import",r.Export="export",r.ActivateMessaging="enableMessaging",r.ManageAccess="manageAccess",r.CopyLink="copyLink",r.ConfigureTitle="configureTitle";let d=a=>{let e=(0,u.useRouter)();return(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"w-full h-full overflow-hidden flex flex-col",children:[(0,t.jsx)("div",{className:"title w-full border-b border-neutral-300 h-12 flex items-center",children:(0,t.jsxs)("div",{className:"overflow-hidden w-full flex items-center p-2 gap-2 justify-start",children:[a.onBack&&(0,t.jsx)(c.z,{className:"text-xs font-semibold h-auto p-1.5 items-center hover:bg-transparent",onClick:()=>{var i;"string"==typeof a.onBack?e.push(a.onBack):null===(i=a.onBack)||void 0===i||i.call(a)},variant:"ghost",children:"←"}),(a.icon||a.emoji)&&(0,t.jsx)(c.z,{variant:"ghost",className:"text-xl hover:bg-neutral-300 p-1 size-6 rounded-full items-center justify-center",children:(0,t.jsx)("span",{className:"relative",children:a.icon?a.icon:a.emoji})}),"string"==typeof a.title?(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,t.jsx)(l.I,{className:"overflow-hidden truncate text-left font-semibold text-sm !border-0 pl-2 !shadow-none !outline-none !ring-0 text-black",readOnly:!a.editable,value:a.title||"Untitled"})})}):(0,t.jsx)("div",{className:"flex-1 overflow-hidden",children:a.title}),(0,t.jsx)("div",{children:a.titleRightContent&&(0,t.jsx)(t.Fragment,{children:a.titleRightContent})}),a.moreActions&&a.moreActions.onAction&&a.moreActions.actions.length>0&&(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)(o.h_,{children:[(0,t.jsx)(o.$F,{asChild:!0,children:(0,t.jsx)(c.z,{variant:"ghost",className:"mr-2 hover:bg-neutral-300 p-1 size-6 rounded-full",children:(0,t.jsx)(s.Z,{className:"size-4"})})}),(0,t.jsx)(o.AW,{className:"w-56 rounded-none p-1.5",align:"end",children:(0,t.jsx)(o.Qk,{children:a.moreActions.actions.map((e,i)=>(0,t.jsxs)(o.Xi,{className:"rounded-none text-xs font-semibold cursor-pointer truncate",onClick:i=>{var r;return null===(r=a.moreActions)||void 0===r?void 0:r.onAction(e.key)},children:[e.label,e.shortcut&&(0,t.jsx)(o.KM,{children:e.shortcut})]},i))})})]})})]})}),(0,t.jsx)("div",{className:"body flex-1 overflow-hidden",children:(0,t.jsx)("div",{className:"w-full h-full",children:a.children})})]})})}},19401:function(a,e,i){"use strict";i.d(e,{WorkspaceSettings:function(){return E}});var r=i(57437),n=i(90641),t=i(96246),c=i(75060),s=i(40279),o=i(2265),l=i(12381),u=i(47842),d=i(94589),m=i(20029),A=i(42212),f=i(39255),h=i(75744),p=i(6770),g=i(3163),x=i(14438),b=i(74291),v=i(99376),k=i(49971);let E=()=>{let{token:a}=(0,f.a)(),{workspace:e,updateWorkspace:i}=(0,A.cF)(),u=(0,o.useRef)(null),[b,v]=(0,o.useState)(!1),[E,y]=(0,o.useState)(0),[N,w]=(0,o.useState)(!1),[S,C]=(0,o.useState)(e.workspace.name),M=[p.eB.Admin,p.eB.Owner].includes(e.workspaceMember.role),P=async function(r){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!a)return;let{}=e,t=await (0,h.eA)(a.token,e.workspace.id,{name:r,timezone:n});if(t.error){x.Am.error(t.error);return}i({name:r,timezone:n}),x.Am.success("Workspace updated")},T=async r=>{if(!a)return;let n=await (0,h.Ng)(a.token,e.workspace.id,{enable:r});if(n.error){x.Am.error(n.error);return}i({isSupportAccessEnabled:r})};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(n.ScrollArea,{className:"size-full",children:(0,r.jsxs)("div",{className:"p-4 pb-20",children:[(0,r.jsx)("div",{className:"max-w-[500px]",children:(0,r.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,r.jsx)("div",{className:"col-span-full",children:(0,r.jsxs)("div",{className:"flex items-center gap-x-3",children:[(0,r.jsx)("div",{children:(0,r.jsxs)(t.qE,{className:"w-14 h-14 rounded",children:[(0,r.jsx)(t.F$,{src:e.workspace.logo}),(0,r.jsx)(t.Q5,{children:e.workspace.name[0]})]})}),M&&(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{children:[(0,r.jsxs)(l.z,{variant:"outline",type:"button",onClick:()=>{u&&u.current&&u.current.click()},disabled:b,className:"rounded-none px-2.5 py-1.5 text-xs h-auto font-semibold",children:[!b&&(0,r.jsx)(r.Fragment,{children:"Upload Logo"}),b&&(0,r.jsxs)(r.Fragment,{children:["Uploading (",E,"%)"]})]}),(0,r.jsx)("br",{}),(0,r.jsx)("br",{}),(0,r.jsx)("div",{className:"hidden",children:(0,r.jsx)("input",{type:"file",ref:u,multiple:!0,accept:"image/*",onChange:r=>{if(!a)return;let n=r.target.files;if(0===n.length)return;let t=n[0];(0,h.zn)(a.token,e.workspace.id,t,{onStart(){v(!0),y(0)},onComplete:a=>{if(v(!1),!a.isSuccess){let e=a.error||(0,g.E)();x.Am.error(e);return}let{logo:e}=a.data.data.workspace.workspace;i({logo:e})},onProgress:a=>{y(a)}})}})})]})})]})}),(0,r.jsxs)("div",{className:"flex flex-col flex-1 gap-1",children:[(0,r.jsx)(c._,{className:"block text-sm font-medium leading-6 text-gray-900",htmlFor:"name",children:"Name"}),(0,r.jsx)(s.I,{id:"name",type:"text",autoCapitalize:"none",autoCorrect:"off",value:S,readOnly:!M,className:"rounded-none ".concat(N&&"!ring-red-500"),onBlur:a=>{S.trim()&&S.trim()!==e.workspace.name&&P(S.trim()).then()},onChange:a=>{C(a.target.value),w(!a.target.value.trim())}})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col gap-1",children:[(0,r.jsx)(c._,{className:"block text-sm font-medium leading-6 text-gray-900",htmlFor:"domain",children:"Domain"}),(0,r.jsx)(s.I,{id:"domain",placeholder:"Domain",type:"text",value:e.workspace.domain,readOnly:!0,className:"rounded-none"})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col gap-1",children:[(0,r.jsx)(c._,{className:"block text-sm font-medium leading-6 text-gray-900",children:"Timezone"}),(0,r.jsx)(k.C,{timezone:e.workspace.timezone,onChange:a=>{P(e.workspace.name,a).then()},disabled:!M})]})]})}),M&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("h2",{className:"font-semibold my-8 mb-4",children:"Additional Settings"}),(0,r.jsxs)("div",{className:"max-w-[650px]",children:[(0,r.jsx)("div",{className:"flex flex-col gap-3",children:(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsx)(m.vyR,{className:"size-4"}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col gap-1",children:[(0,r.jsx)("h5",{className:"font-semibold text-sm",children:"Support Access"}),(0,r.jsx)("div",{className:"text-xs",children:"Temporarily grant Opendashboard support access to your workspace so we can troubleshoot problems on your behalf. You can revoke access at any time."})]}),(0,r.jsx)(d.r,{checked:e.workspace.isSupportAccessEnabled,onCheckedChange:T,"aria-readonly":!0})]})}),(0,r.jsx)(j,{})]})]})]})})})},j=()=>{let{token:a,removeWorkspace:e}=(0,f.a)(),{workspace:i}=(0,A.cF)(),[n,t]=(0,o.useState)(!1),[c,m]=(0,o.useState)(""),[g,k]=(0,o.useState)(!1),E=(0,v.useRouter)(),j=async()=>{if(!a)return;k(!0);let r=await (0,h.zl)(a.token,i.workspace.id,{reason:c});if(k(!1),r.error){x.Am.error(r.error);return}e(i),E.push("/welcome")};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex flex-col gap-3 mt-5",children:(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsx)(u.Z,{className:"size-4"}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col gap-1",children:[(0,r.jsx)("h5",{className:"font-semibold text-sm",children:"Delete Workspace"}),(0,r.jsx)("div",{className:"text-xs",children:"Permanently delete this workspace"})]}),(0,r.jsx)(d.r,{checked:n,disabled:i.workspaceMember.role!==p.eB.Owner,onCheckedChange:t,"aria-readonly":!0})]})}),(0,r.jsx)(b.Vq,{open:n,onOpenChange:t,children:(0,r.jsxs)(b.cZ,{className:"max-w-[600px] !rounded-none p-4",children:[(0,r.jsx)(b.fK,{children:(0,r.jsx)(b.$N,{className:"font-bold",children:"Delete Workspace"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex gap-2 py-2",children:[(0,r.jsx)(s.I,{value:c,onChange:a=>m(a.target.value),className:"flex-1 text-xs rounded-none font-medium"}),(0,r.jsx)(l.z,{disabled:g||!c.trim(),onClick:j,className:"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 bg-red-600",children:"Delete Workspace"})]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground pb-2",children:"This action cannot be undone. This will permanently delete the workspace, including all databases, pages and files. Please tell us the reason for deleting the workspace to confirm."})]})]})})]})}},47842:function(a,e,i){"use strict";var r=i(2265);let n=r.forwardRef(function(a,e){let{title:i,titleId:n,...t}=a;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},t),i?r.createElement("title",{id:n},i):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))});e.Z=n},61146:function(a,e,i){"use strict";i.d(e,{NY:function(){return y},Ee:function(){return j},fC:function(){return E}});var r=i(2265),n=i(73966),t=i(26606),c=i(61188),s=i(66840),o=i(82558);function l(){return()=>{}}var u=i(57437),d="Avatar",[m,A]=(0,n.b)(d),[f,h]=m(d),p=r.forwardRef((a,e)=>{let{__scopeAvatar:i,...n}=a,[t,c]=r.useState("idle");return(0,u.jsx)(f,{scope:i,imageLoadingStatus:t,onImageLoadingStatusChange:c,children:(0,u.jsx)(s.WV.span,{...n,ref:e})})});p.displayName=d;var g="AvatarImage",x=r.forwardRef((a,e)=>{let{__scopeAvatar:i,src:n,onLoadingStatusChange:d=()=>{},...m}=a,A=h(g,i),f=function(a,e){let{referrerPolicy:i,crossOrigin:n}=e,t=(0,o.useSyncExternalStore)(l,()=>!0,()=>!1),s=r.useRef(null),u=t?(s.current||(s.current=new window.Image),s.current):null,[d,m]=r.useState(()=>k(u,a));return(0,c.b)(()=>{m(k(u,a))},[u,a]),(0,c.b)(()=>{let a=a=>()=>{m(a)};if(!u)return;let e=a("loaded"),r=a("error");return u.addEventListener("load",e),u.addEventListener("error",r),i&&(u.referrerPolicy=i),"string"==typeof n&&(u.crossOrigin=n),()=>{u.removeEventListener("load",e),u.removeEventListener("error",r)}},[u,n,i]),d}(n,m),p=(0,t.W)(a=>{d(a),A.onImageLoadingStatusChange(a)});return(0,c.b)(()=>{"idle"!==f&&p(f)},[f,p]),"loaded"===f?(0,u.jsx)(s.WV.img,{...m,ref:e,src:n}):null});x.displayName=g;var b="AvatarFallback",v=r.forwardRef((a,e)=>{let{__scopeAvatar:i,delayMs:n,...t}=a,c=h(b,i),[o,l]=r.useState(void 0===n);return r.useEffect(()=>{if(void 0!==n){let a=window.setTimeout(()=>l(!0),n);return()=>window.clearTimeout(a)}},[n]),o&&"loaded"!==c.imageLoadingStatus?(0,u.jsx)(s.WV.span,{...t,ref:e}):null});function k(a,e){return a?e?(a.src!==e&&(a.src=e),a.complete&&a.naturalWidth>0?"loaded":"loading"):"error":"idle"}v.displayName=b;var E=p,j=x,y=v},50721:function(a,e,i){"use strict";i.d(e,{bU:function(){return j},fC:function(){return E}});var r=i(2265),n=i(6741),t=i(98575),c=i(73966),s=i(80886),o=i(6718),l=i(90420),u=i(66840),d=i(57437),m="Switch",[A,f]=(0,c.b)(m),[h,p]=A(m),g=r.forwardRef((a,e)=>{let{__scopeSwitch:i,name:c,checked:o,defaultChecked:l,required:A,disabled:f,value:p="on",onCheckedChange:g,form:x,...b}=a,[E,j]=r.useState(null),y=(0,t.e)(e,a=>j(a)),N=r.useRef(!1),w=!E||x||!!E.closest("form"),[S,C]=(0,s.T)({prop:o,defaultProp:null!=l&&l,onChange:g,caller:m});return(0,d.jsxs)(h,{scope:i,checked:S,disabled:f,children:[(0,d.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":A,"data-state":k(S),"data-disabled":f?"":void 0,disabled:f,value:p,...b,ref:y,onClick:(0,n.M)(a.onClick,a=>{C(a=>!a),w&&(N.current=a.isPropagationStopped(),N.current||a.stopPropagation())})}),w&&(0,d.jsx)(v,{control:E,bubbles:!N.current,name:c,value:p,checked:S,required:A,disabled:f,form:x,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var x="SwitchThumb",b=r.forwardRef((a,e)=>{let{__scopeSwitch:i,...r}=a,n=p(x,i);return(0,d.jsx)(u.WV.span,{"data-state":k(n.checked),"data-disabled":n.disabled?"":void 0,...r,ref:e})});b.displayName=x;var v=r.forwardRef((a,e)=>{let{__scopeSwitch:i,control:n,checked:c,bubbles:s=!0,...u}=a,m=r.useRef(null),A=(0,t.e)(m,e),f=(0,o.D)(c),h=(0,l.t)(n);return r.useEffect(()=>{let a=m.current;if(!a)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==c&&e){let i=new Event("click",{bubbles:s});e.call(a,c),a.dispatchEvent(i)}},[f,c,s]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:c,...u,tabIndex:-1,ref:A,style:{...u.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(a){return a?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var E=g,j=b},6718:function(a,e,i){"use strict";i.d(e,{D:function(){return n}});var r=i(2265);function n(a){let e=r.useRef({value:a,previous:a});return r.useMemo(()=>(e.current.value!==a&&(e.current.previous=e.current.value,e.current.value=a),e.current.previous),[a])}}},function(a){a.O(0,[6018,8310,6137,7648,311,2534,4451,1107,85,3493,3139,8107,5737,7900,2211,2212,991,2971,6577,1744],function(){return a(a.s=58368)}),_N_E=a.O()}]);