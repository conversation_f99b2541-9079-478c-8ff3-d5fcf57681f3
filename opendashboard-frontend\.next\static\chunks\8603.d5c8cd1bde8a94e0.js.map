{"version": 3, "file": "static/chunks/8603.d5c8cd1bde8a94e0.js", "mappings": "ubAEA,IAAAA,EAAA,gIAKAC,CAAAA,EAAAC,CAAgB,UAAAC,CAAA,EAEhB,IAAAA,GAGAA,EAAAC,MAAA,MAIA,CADAJ,EAAAK,IAAA,CAAAF,GALA,SAUA,IAAAG,EAAAH,EAAAI,KAAA,cACAD,CAAA,IAAAF,MAAA,KAIAI,CADA,IAAAD,KAAA,MACAE,IAAA,UAAAC,CAAA,EAAsC,OAAAA,EAAAN,MAAA,MAItC,uSCOO,IAAMO,EAAW,IACpB,GAAM,CAACC,YAAAA,CAAW,CAAEC,eAAAA,CAAc,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAChC,CAACC,EAAaC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAGzC,CAACC,cAAAA,CAAa,CAAC,CAAGJ,CAAAA,EAAAA,EAAAA,EAAAA,IAClB,CAACK,IAAAA,CAAG,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACR,CAACC,WAAAA,CAAU,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IAKf,CAACC,KAAAA,CAAI,CAAEC,mBAAAA,CAAkB,CAAEC,iBAAAA,CAAgB,CAAEC,mBAAAA,CAAkB,CAAEC,WAAAA,CAAU,CAAC,CAAGC,EACjF,CACAC,WAAAA,CAAU,CACVC,WAAAA,CAAU,CACVC,UAAAA,CAAS,CACTC,UAAAA,CAAS,CACTC,WAAAA,CAAU,CACVC,WAAAA,CAAU,CACb,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,EAAY,CACZC,GAAI,GAAcR,MAAAA,CAAXL,EAAKa,EAAE,CAAC,KAAkBC,MAAA,CAAfT,EAAMU,QAAQ,EAChCC,KAAM,CACFC,KAAM,OACNjB,KAAAA,EACAe,SAAUV,EAAMU,QAAQ,CAGhC,GAGMG,EAAQ,CACVR,WAAAA,EACAD,UAAWU,EAAAA,EAAGA,CAACC,SAAS,CAACC,QAAQ,CAACZ,EACtC,EAEMa,EAAQC,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBvB,EAAKwB,eAAe,CAACC,qBAAqB,CAACrB,EAAW,GAAK,WAE3F,MAAO,GAAAsB,EAAAC,GAAA,EAAAD,EAAAE,QAAA,WACH,GAAAF,EAAAG,IAAA,EAACC,MAAAA,CAAIC,UAAW,0CAAkFjB,MAAA,CAAxCH,EAAa,sBAAwB,IAC1FqB,IAAK1B,EACLY,MAAOA,EACPe,QAAS,IAAMnC,EAAWO,EAAML,IAAI,CAACa,EAAE,CAAER,EAAM6B,UAAU,EACxD,GAAG3B,CAAU,CACb,GAAGC,CAAS,WACd,GAAAkB,EAAAG,IAAA,EAACC,MAAAA,CAAIC,UAAU,gEACV,CAAC1B,EAAM8B,QAAQ,EAAI,GAAAT,EAAAC,GAAA,EAACS,EAAAA,CAAQA,CAAAA,CACzBH,QAASI,GAAKA,EAAEC,eAAe,GAC/BC,QAASlD,EAAYmD,QAAQ,CAACnC,EAAML,IAAI,CAACa,EAAE,EAC3C4B,gBAAiBC,IACTA,EAAGpD,EAAe,IAAID,EAAagB,EAAML,IAAI,CAACa,EAAE,CAAC,EAChDvB,EAAe,IAAIqD,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBtD,EAAagB,EAAML,IAAI,CAACa,EAAE,EAAE,CAC3E,IACJ,GAAAa,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,mCACVT,IAEJ,CAACjB,EAAM8B,QAAQ,EAAI9B,EAAM6B,UAAU,EAAI,GAAAR,EAAAG,IAAA,EAACe,EAAAA,EAAYA,CAAAA,WACjD,GAAAlB,EAAAC,GAAA,EAACkB,EAAAA,EAAmBA,CAAAA,CAACC,QAAO,YACxB,GAAApB,EAAAC,GAAA,EAACoB,EAAAA,CAAMA,CAAAA,CAACC,QAAS,QAASjB,UAAU,mCAA0B,GAAAL,EAAAC,GAAA,EAC1DsB,EAAAA,CAAsBA,CAAAA,CAAClB,UAAU,eAGzC,GAAAL,EAAAC,GAAA,EAACuB,EAAAA,EAAmBA,CAAAA,CAACnB,UAAU,oDAAoDoB,MAAM,eACrF,GAAAzB,EAAAC,GAAA,EAACyB,EAAAA,EAAgBA,CAAAA,CAACrB,UAAU,2BACVE,QAAS,KACLtC,EAAcU,EAAM6B,UAAU,CAAE,CAAC7B,EAAML,IAAI,CAACa,EAAE,CAAC,EAAEwC,IAAI,EACzD,WAAG,mBAMjC,GAAA3B,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,4CACV7B,EAAiBoD,GAAG,CAACC,GAElB,CADmC,CAACA,EAAM,CAC7BC,QAAQ,CAAS,KAEvB,GAAA9B,EAAAC,GAAA,EAACC,EAAAA,QAAQA,CAAAA,UACZ,GAAAF,EAAAC,GAAA,EAAC8B,EAAAA,CACGC,WAAYrD,EAAMJ,kBAAkB,CACpCc,SAAUwC,EACVvD,KAAMK,EAAML,IAAI,IAJFuD,UAU1C,EAUaE,EAAc,QAKnBE,EAJJ,GAAM,CAACC,cAAAA,CAAa,CAAEC,mBAAAA,CAAkB,CAAEC,QAAAA,CAAO,CAAC,CAAGjE,CAAAA,EAAAA,EAAAA,EAAAA,IAC/CkE,EAAS1D,EAAMqD,UAAU,CAACM,UAAU,CAAC3D,EAAMU,QAAQ,CAAC,CAE1D,GAAI,CAACgD,EAAQ,OAAO,KAGpB,IAAME,EAAW5D,EAAML,IAAI,CAACwB,eAAe,CAACC,qBAAqB,CAACpB,EAAMU,QAAQ,CAAC,EAAI,GACrF,GAAI,CAACkD,GACG,iBAAOA,GAAyB,CAACA,EAASC,IAAI,IAC9CC,MAAMC,OAAO,CAACH,IAAaA,IAAAA,EAASpF,MAAM,CAChD,OAAO,KAET,OAAQkF,EAAO9C,IAAI,EACf,KAAKoD,EAAAA,qBAAqBA,CAACC,IAAI,CAC/B,KAAKD,EAAAA,qBAAqBA,CAACE,IAAI,CAC/B,KAAKF,EAAAA,qBAAqBA,CAACG,MAAM,CACjC,KAAKH,EAAAA,qBAAqBA,CAACI,EAAE,CAC7B,KAAKJ,EAAAA,qBAAqBA,CAACK,SAAS,CACpC,KAAKL,EAAAA,qBAAqBA,CAACM,OAAO,CAC9B,IAAIC,EAAYC,OAAOZ,GACnBF,CAAAA,EAAO9C,IAAI,GAAKoD,EAAAA,qBAAqBA,CAACE,IAAI,EAAIR,EAAO9C,IAAI,GAAKoD,EAAAA,qBAAqBA,CAACG,MAAM,GAE1FI,CAAAA,EAAYE,CADFC,EAAAA,EAAAA,EAAAA,EAA8BhB,EAAQa,GAClCI,YAAY,EAE9BrB,EAAU,GAAAjC,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,wCAAgC6C,IACzD,KACJ,MAAKP,EAAAA,qBAAqBA,CAACY,MAAM,CAC7BtB,EAAU,GAAAjC,EAAAC,GAAA,EAACuD,EAAAA,EAAgBA,CAAAA,CAACnB,OAAQA,EAAwBoB,IAAK9E,EAAML,IAAI,GAC3E,KACJ,MAAKqE,EAAAA,qBAAqBA,CAACjC,QAAQ,CAC/BuB,EAAU,GAAAjC,EAAAC,GAAA,EAACS,EAAAA,CAAQA,CAAAA,CACfG,QAAS,CAAC,CAAC0B,IAEf,KACJ,MAAKI,EAAAA,qBAAqBA,CAACe,MAAM,CAC7BzB,EAAU,GAAAjC,EAAAC,GAAA,EAAC0D,EAAAA,EAAgBA,CAAAA,CACvBC,cAAejF,EAAML,IAAI,CAACuF,MAAM,CAACrD,UAAU,CAC3C6B,OAAQA,EACRoB,IAAK9E,EAAML,IAAI,CACf6D,mBAAoBA,EACpBD,cAAeA,IAEnB,KACJ,MAAKS,EAAAA,qBAAqBA,CAACmB,IAAI,CAC/B,KAAKnB,EAAAA,qBAAqBA,CAACoB,SAAS,CACpC,KAAKpB,EAAAA,qBAAqBA,CAACqB,SAAS,CAChC/B,EAAU,GAAAjC,EAAAC,GAAA,EAACgE,EAAAA,EAAcA,CAAAA,CACrB5B,OAAQA,EACRhC,UAAU,sBACVoD,IAAK9E,EAAML,IAAI,GAEnB,KACJ,MAAKqE,EAAAA,qBAAqBA,CAACuB,SAAS,CACpC,KAAKvB,EAAAA,qBAAqBA,CAACwB,SAAS,CACpC,KAAKxB,EAAAA,qBAAqBA,CAACyB,MAAM,CAC7BnC,EAAU,GAAAjC,EAAAC,GAAA,EAACoE,EAAAA,EAAgBA,CAAAA,CACvBhC,OAAQA,EACRoB,IAAK9E,EAAML,IAAI,CACf8D,QAASA,IAEb,KACJ,MAAKO,EAAAA,qBAAqBA,CAAC2B,KAAK,CAE5B,IAAIC,EAAQC,GAAU/B,MAAMC,OAAO,CADtBH,GAAAA,EAC0C,EAAE,CACzDN,EAAU,GAAAjC,EAAAG,IAAA,EAACC,MAAAA,CAAIC,UAAU,yCAAgCkE,EAAMpH,MAAM,CAAC,aAI9E,QACA,EAGQ,GAAA6C,EAAAG,IAAA,EAACC,MAAAA,CAAIC,UAAU,2DACnB,GAAAL,EAAAC,GAAA,EAACwE,EAAAA,CAAqBA,CAAAA,CAAClF,KAAM8C,EAAO9C,IAAI,CAAEc,UAAU,UACpD,GAAAL,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,kCACV4B,OANY,IASzB,sCC/KO,IAAMyC,EAAc,IACvB,GAAM,CAACC,MAAAA,CAAK,CAAEC,aAAAA,CAAY,CAAEvC,OAAAA,CAAM,CAAC,CAAG1D,EAKhC,CACFC,WAAAA,CAAU,CACVC,WAAAA,CAAU,CACVC,UAAAA,CAAS,CACTC,UAAAA,CAAS,CACTC,WAAAA,CAAU,CACVC,WAAAA,CAAU,CACb,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,EAAY,CACZC,GAAIkD,EAAOlD,EAAE,CACbG,KAAM,CACFC,KAAM,SACN8C,OAAAA,CACJ,EACA5B,SAAU,CAAC,CAAC9B,EAAMkG,WAAW,GAG3BrF,EAAQ,CACVR,WAAAA,EACAD,UAAWU,EAAAA,EAAGA,CAACC,SAAS,CAACC,QAAQ,CAACZ,EACtC,EAGA,MAAO,GAAAiB,EAAAC,GAAA,EAAAD,EAAAE,QAAA,WACH,GAAAF,EAAAG,IAAA,EAACC,MAAAA,CACGC,UAAW,6DAAqGjB,MAAA,CAAxCH,EAAa,sBAAwB,IAC7GqB,IAAK1B,EACLY,MAAOA,EACN,GAAGX,CAAU,CACb,GAAGC,CAAS,WACb,GAAAkB,EAAAC,GAAA,EAAC6E,EAAAA,CAAkBzC,OAAQA,EACRsC,MAAOA,EACPI,UAAWpG,EAAMoG,SAAS,CAC1BC,YAAarG,EAAMqG,WAAW,CAC9BvE,SAAU9B,EAAMkG,WAAW,EAAI,CAAClG,EAAMsG,gBAAgB,CACtDL,aAAcjG,EAAMiG,YAAY,GAInD,GAAA5E,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,kCACX,GAAAL,EAAAC,GAAA,EAACiF,EAAAA,UAAUA,CAAAA,CAAC7E,UAAU,6BAA6B8E,mBAAkB,oBACjE,GAAAnF,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,oFACX,GAAAL,EAAAC,GAAA,EAACmF,EAAAA,EAAeA,CAAAA,CAACT,MAAOA,EAAOlE,SAAU9B,EAAMkG,WAAW,UACrDF,EAAM/C,GAAG,CAAC,CAACtD,EAAM+G,IAAM,GAAArF,EAAAC,GAAA,EAACvC,EAAQA,CAE7Be,mBAAoBE,EAAMF,kBAAkB,CAC5CD,iBAAkBG,EAAMH,gBAAgB,CACxCD,mBAAoBI,EAAMJ,kBAAkB,CAC5CkC,SAAU9B,EAAMkG,WAAW,EAAI,CAAClG,EAAM2G,WAAW,CACjD5G,WAAYC,EAAMD,UAAU,CAC5BW,SAAUV,EAAM0D,MAAM,CAAClD,EAAE,CACzBqB,WAAY7B,EAAM6B,UAAU,CAC5BlC,KAAMA,GARDA,EAAKa,EAAE,EAAIkG,gBAkBhD,EAYaP,EAAoB,OAAC,CAACzC,OAAAA,CAAM,CAAEsC,MAAAA,CAAK,CAAEK,YAAAA,CAAW,CAAEJ,aAAAA,CAAY,CAAEG,UAAAA,CAAS,CAAEtE,SAAAA,CAAQ,CAAE,GAAG8E,EAA6B,CAAAC,EAE1HC,EAAmB,IACnBpD,CAAAA,EAAOqD,KAAK,EACZD,CAAAA,EAAOE,CAAAA,EAAAA,EAAAA,EAAAA,EAAUtD,EAAOqD,KAAK,GAEjC,IAAIlG,EAAuB,CAAC,EAO5B,OANIiG,GACAjG,CAAAA,EAAM,eAAkB,CAAG,GAAWJ,MAAA,CAARqG,EAAKG,EAAE,GAKlC,GAAA5F,EAAAC,GAAA,EAAAD,EAAAE,QAAA,WACH,GAAAF,EAAAG,IAAA,EAAC0F,SAAAA,CAAQ,GAAGN,CAAI,CAAElF,UAAU,yCACxB,GAAAL,EAAAG,IAAA,EAACC,MAAAA,CAAIC,UAAU,yEACX,GAAAL,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,0FACVb,MAAOA,WACP6C,EAAOzC,KAAK,GAEjB,GAAAI,EAAAC,GAAA,EAACoB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,QACRjB,UAAU,oFACbsE,EAAMxH,MAAM,MA6DpB,CAACsD,GAAY,GAAAT,EAAAG,IAAA,EAAAH,EAAAE,QAAA,YACT6E,GAAa,GAAA/E,EAAAC,GAAA,EAACoB,EAAAA,CAAMA,CAAAA,CACjBC,QAAQ,QACRf,QAAS,IAAMwE,MAAAA,EAAAA,KAAAA,EAAAA,EAAY1C,EAAOlD,EAAE,EACpCkB,UAAU,iFACV,GAAAL,EAAAC,GAAA,EAAC6F,EAAAA,GAAcA,CAAAA,CAACzF,UAAU,aAE9B,GAAAL,EAAAG,IAAA,EAACe,EAAAA,EAAYA,CAAAA,WACT,GAAAlB,EAAAC,GAAA,EAACkB,EAAAA,EAAmBA,CAAAA,CAACC,QAAO,YACxB,GAAApB,EAAAC,GAAA,EAACoB,EAAAA,CAAMA,CAAAA,CAACC,QAAS,QAASjB,UAAU,mCAA0B,GAAAL,EAAAC,GAAA,EAC1DsB,EAAAA,CAAsBA,CAAAA,CAAClB,UAAU,eAGzC,GAAAL,EAAAC,GAAA,EAACuB,EAAAA,EAAmBA,CAAAA,CAACnB,UAAU,oDAAoDoB,MAAM,eACrF,GAAAzB,EAAAC,GAAA,EAACyB,EAAAA,EAAgBA,CAAAA,CAACrB,UAAU,kCACVE,QAAS,IAAMqE,MAAAA,EAAAA,KAAAA,EAAAA,EAAevC,EAAOlD,EAAE,CAAE,CAAC2C,SAAU,CAACkD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAalD,QAAQ,aACvFkD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAalD,QAAQ,EAAG,OAAS,qBAe9D,EC3MaiE,EAAgB,IAEzB,GAAM,CACFC,WAAAA,CAAU,CAAEC,UAAAA,CAAS,CACrBC,QAAAA,CAAO,CAAEC,gBAAAA,CAAe,CAAEvB,aAAAA,CAAY,CACtCpG,iBAAAA,CAAgB,CAAEC,mBAAAA,CAAkB,CAAEF,mBAAAA,CAAkB,CACxDG,WAAAA,CAAU,CACb,CAAGC,QAIJ,CADAyH,QAAQC,GAAG,CAAC,CAACL,WAAAA,EAAYE,QAAAA,EAASC,gBAAAA,CAAe,GAC5CD,GACE,GAAAlG,EAAAC,GAAA,EAAAD,EAAAE,QAAA,WACH,GAAAF,EAAAG,IAAA,EAACC,MAAAA,CAAIC,UAAU,sDACX,GAAAL,EAAAC,GAAA,EAAC4F,SAAAA,CAAOxF,UAAU,2BACd,GAAAL,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,iCAAwB,oBAI3C,GAAAL,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,kCACX,GAAAL,EAAAC,GAAA,EAACiF,EAAAA,UAAUA,CAAAA,CAAC7E,UAAU,yBAClB,GAAAL,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,kEACV6F,EAAQtE,GAAG,CAAC0E,GACE,GAAAtG,EAAAC,GAAA,EAAC6E,EAAiBA,CAErBzC,OAAQiE,EACR1B,aAAcA,EACdnE,SAAU9B,EAAM8B,QAAQ,CACxBkE,MAAOqB,CAAU,CAACM,EAAInH,EAAE,CAAC,CAACyC,GAAG,CAACzC,GAAM8G,CAAS,CAAC9G,EAAG,EACjD6F,YAAamB,CAAe,CAACG,EAAInH,EAAE,CAAC,EAL/BmH,EAAInH,EAAE,cAbtB,IAyCzB,uCCpEO,IAAMoH,EAAY,IACrB,GAAM,CAACC,EAAMC,EAAQ,CAAGzI,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC3B0I,EAAWC,CAAAA,EAAAA,EAAAA,MAAAA,EAAgC,MAG3CC,EAASC,EAAAA,EAAY,CAACC,KAAKC,KAAK,CAACD,KAAKE,MAAM,GAAKH,EAAAA,EAAYA,CAAC1J,MAAM,EAAE,OAI5E8J,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACN,IAAMC,EAAWR,EAASS,OAAO,CACjC,GAAI,CAACD,EACD,OAEJA,EAASE,KAAK,GAEd,IAAMC,EAAS,KACXZ,EAAQ,GACZ,EAGA,OAFAS,EAASI,gBAAgB,CAAC,OAAQD,GAE3B,KACHH,EAASK,mBAAmB,CAAC,OAAQF,EACzC,CACJ,EAAG,CAACb,EAAK,EAUF,GAAAxG,EAAAC,GAAA,EAAAD,EAAAE,QAAA,WACH,GAAAF,EAAAG,IAAA,EAACC,MAAAA,CAAIC,UAAU,sDACV,CAACmG,GAAQ,GAAAxG,EAAAC,GAAA,EAAC4F,SAAAA,CAAOxF,UAAU,2BACxB,GAAAL,EAAAG,IAAA,EAACkB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UACRf,QAAS,IAGLkG,EAAQ,GACZ,EACApG,UAAU,kFAEd,GAAAL,EAAAC,GAAA,EAACuH,EAAAA,CAAcA,CAAAA,CAACnH,UAAU,YAAW,kBAI5CmG,GAAQ,GAAAxG,EAAAC,GAAA,EAAAD,EAAAE,QAAA,WACL,GAAAF,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,kBAEX,GAAAL,EAAAC,GAAA,EAACwH,EAAAA,CAAcA,CAAAA,CACXC,WAAU,GAACpH,IAAKoG,EAChBiB,SA5BH,IACbC,CAAAA,EAAQA,EAAMpF,IAAI,MAGlB7D,EAAMkJ,YAAY,CAACD,EAAOhB,EAAOlB,KAAK,EACtCe,EAAQ,IACZ,EAuBoBqB,iBAAiB,qBAQzC,+HCtCA,IAAMC,EAAe,YAaRC,EAAiB,IAG1B,GAAM,CAAC9F,cAAAA,CAAa,CAAC,CAAG/D,CAAAA,EAAAA,EAAAA,EAAAA,IAClB,CAAC6D,WAAAA,CAAU,CAAC,CAAGrD,SAKrB,CAH8B,CAACqD,EAAWxB,UAAU,CAAC,CACVyH,QAAQ,CAACjG,UAAU,CAACM,UAAU,CAACN,EAAWkG,UAAU,CAAC,EAAE,CAAC,CAI5F,GAAAlI,EAAAC,GAAA,EAACkI,EAAAA,CAAc,GAAGxJ,CAAK,GAFL,IAG7B,EAEMwJ,EAAe,QA0PTC,EAvPR,GAAM,CAAClG,cAAAA,CAAa,CAAEC,mBAAAA,CAAkB,CAAEC,QAAAA,CAAO,CAAEiG,UAAAA,CAAS,CAAC,CAAGlK,CAAAA,EAAAA,EAAAA,EAAAA,IAC1D,CAAC6D,WAAAA,CAAU,CAAC,CAAGrD,EACf,CAAC2J,qBAAAA,CAAoB,CAAEC,mBAAAA,CAAkB,CAAEC,qBAAAA,CAAoB,CAAEC,cAAAA,CAAa,CAAEC,MAAAA,CAAK,CAAC,CAAG7K,CAAAA,EAAAA,EAAAA,EAAAA,IACzF,CAAC8K,OAAQC,CAAU,CAAEC,OAAQC,CAAU,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAC3C,CAACpL,YAAAA,CAAW,CAAEC,eAAAA,CAAc,CAAC,CAAGoL,CAAAA,EAAAA,EAAAA,EAAAA,IAChC,CAAC5K,WAAAA,CAAU,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IACf,CAAC4K,QAAAA,CAAO,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAGZ,CAACC,EAAcC,EAAgB,CAAGpL,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC3C,CAACqL,EAAiBC,EAAmB,CAAGtL,CAAAA,EAAAA,EAAAA,QAAAA,EAAiB,IAG/DoI,QAAQC,GAAG,CAAC,2BAA4B,CAACkD,UADbb,EAAMc,QAAQ,CAACC,EAAAA,EAAiBA,CAACC,qBAAqB,CAAE,EAAE,CACpC,GAElD,IAAMzB,EAAW/F,CAAa,CAACF,EAAWxB,UAAU,CAAC,CAC/CmJ,EAA4B1B,EAASA,QAAQ,CAACjG,UAAU,CAACM,UAAU,CAACN,EAAWkG,UAAU,CAAC,EAAE,CAAC,CAG7F0B,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IAGTC,EAAW,CAAC9H,EAAW+H,WAAW,EAAI,CAACH,EAEvCI,EAAgBC,CAAAA,EAAAA,EAAAA,EAAAA,IAElBhF,EAAoB,CAAC+E,GAAiB,CANtBJ,GAMsC,CAAC5H,EAAW+H,WAAW,EAAID,EACjFxE,EAAwB,CAAC0E,GAAiB,CAP1BJ,GAO0C,CAAC5H,EAAW+H,WAAW,EAAID,CAEzF9H,CAAAA,EAAW2G,MAAM,CAAG3G,EAAW2G,MAAM,EAAI,CAACuB,WAAY,EAAE,CAAEC,MAAOC,EAAAA,KAAKA,CAACC,GAAG,EAC1ErI,EAAWsI,KAAK,CAAGtI,EAAWsI,KAAK,EAAI,EAAE,CACzCtI,EAAWuI,UAAU,CAAGvI,EAAWuI,UAAU,EAAI,EAAE,CACnDvI,EAAWmE,eAAe,CAAGnE,EAAWmE,eAAe,EAAI,CAAC,EAC5DnE,EAAWwI,YAAY,CAAGxI,EAAWwI,YAAY,EAAI,EAAE,CACvDxI,EAAWyI,cAAc,CAAGzI,EAAWyI,cAAc,EAAI,CAAC,EAK1D,IAAI/L,GAAauJ,EAASA,QAAQ,CAACjG,UAAU,CAAC0I,aAAa,EAAI,GAgC/DC,CA9B0B,KAEtB,GAAI,CAACjM,GACD,KAAK,IAAM2D,KAAUuI,OAAOC,MAAM,CAAC5C,EAASA,QAAQ,CAACjG,UAAU,CAACM,UAAU,EACtE,GAAID,EAAO9C,IAAI,GAAKoD,EAAAA,qBAAqBA,CAACE,IAAI,CAAE,CAC5CnE,GAAa2D,EAAOlD,EAAE,CACtB,KACJ,CACJ,CAECT,IAAYA,CAAAA,GAAauJ,EAASA,QAAQ,CAACjG,UAAU,CAAC8I,SAAS,CAAC,EAAE,EAEvE,GAAI,CAACN,aAAAA,CAAY,CAAEC,eAAAA,CAAc,CAAC,CAAGzI,EAIjC+I,EAAiBH,OAAOC,MAAM,CAACJ,GAAgB9B,MAAM,CAAChK,GAAS,CAACA,EAAMmD,QAAQ,EAAE3E,MAAM,CACpF6N,EAAgBD,EAEtB,IAAK,IAAM1I,KAAUuI,OAAOC,MAAM,CAAC5C,EAASA,QAAQ,CAACjG,UAAU,CAACM,UAAU,EAAG,CACzE,IAAMnD,EAAKkD,EAAOlD,EAAE,CACfqL,EAAa1J,QAAQ,CAAC3B,IAAKqL,EAAaS,IAAI,CAAC9L,GAC7CsL,CAAc,CAACtL,EAAG,EAAEsL,CAAAA,CAAc,CAACtL,EAAG,CAAG,CAAC2C,SAAU,EAAI,GACzD3C,IAAOT,IAAcsM,IAAAA,GAAuBD,EAAiB,IAC7DN,CAAc,CAACtL,EAAG,CAAG,CAAC2C,SAAU,EAAK,EACrCiJ,IAER,CACJ,KAmEA,IAAMG,GAAmB,MAAOC,IAC5B,MAAM7C,EAAqB3J,EAAMyM,IAAI,CAACjM,EAAE,CAAER,EAAMyM,IAAI,CAACC,MAAM,CAAEF,EACjE,EA0GMG,GAAeC,CAzGO,KACxB,IAAMC,EAA8B,EAAE,CACtCA,EAAYP,IAAI,CAAC,CAAC5L,SAAUoM,EAAAA,WAAWA,CAAC1H,SAAS,CAAE2H,MAAOC,EAAAA,IAAIA,CAACC,IAAI,GAEnE,GAAM,CAACxD,KAAAA,CAAI,CAAC,CAAGyD,CAAAA,EAAAA,EAAAA,oBAAAA,EACX5D,EACA7F,EACAF,EACAF,EAAW2G,MAAM,CA9GVC,EAgHP4C,EACAnD,EAAUyD,eAAe,CAACC,MAAM,EAIpC,OAAO3D,CACX,KAoGM,CAAClC,QAAAA,EAAO,CAAE8F,cAAAA,EAAa,CAAC,CAAGC,CAtLf,KACd,IAAM/F,EAAoB,EAAE,CACtB8F,EAA0B,EAAE,CAG5BzB,EAAa9H,MAAMC,OAAO,CAACV,EAAWuI,UAAU,EAAIvI,EAAWuI,UAAU,CAAG,EAAE,CAC9EpE,EAAkBnE,EAAWmE,eAAe,CAAGnE,EAAWmE,eAAe,CAAG,CAAC,EAEnF,IAAK,IAAM+F,KAAOvC,EAAYwC,SAAS,CAC9B5B,EAAWzJ,QAAQ,CAACoL,IAAM3B,EAAWU,IAAI,CAACiB,GAC1C/F,CAAe,CAAC+F,EAAI,EAAE/F,CAAAA,CAAe,CAAC+F,EAAI,CAAG,CAC9CpK,SAAU,GACVsK,WAAY,EAAE,CAClB,EAEJ,IAAIC,EAAgB,GAEpB,IAAK,IAAMlN,KAAMoL,EAAY,KACrBjE,EACJ,IAAMgG,EAAS3C,EAAY4C,UAAU,CAACpN,EAAG,CAGzC,GAAIA,IAAO4I,EACPzB,EAAM,CACFnH,GAAI4I,EACJnI,MAAO,YACPgI,MAAOG,CACX,EACA5B,CAAe,CAAC4B,EAAa,CAAG5B,CAAe,CAAChH,EAAG,EAAI,CACnD2C,SAAU,GACVsK,WAAY,EAAE,EAElBC,EAAgB,QACb,IAAIC,EAOJ,SANHhG,EAAM,CACFnH,GAAAA,EACAS,MAAO0M,EAAO1M,KAAK,CACnB8F,MAAO4G,EAAO5G,KAAK,CACnBkC,MAAOzI,CACX,EAEAgH,CAAe,CAAChH,EAAG,CAAC2C,QAAQ,CAAEkK,EAAcf,IAAI,CAAC3E,GAChDJ,EAAQ+E,IAAI,CAAC3E,EACtB,CAgBA,OAfK+F,IAMDnG,EAAQsG,OAAO,CALK,CAChBrN,GAAI4I,EACJnI,MAAO,YACPgI,MAAO,EACX,GAEAzB,CAAe,CAAC4B,EAAa,CAAG,CAC5BjG,SAAU,GACVsK,WAAY,EAAE,GAItBpK,EAAWuI,UAAU,CAAG,IAAIrE,EAAQtE,GAAG,CAACZ,GAAKA,EAAE7B,EAAE,KAAM6M,EAAcpK,GAAG,CAACZ,GAAKA,EAAE7B,EAAE,EAAE,CAE7E,CAAC+G,QAAAA,EAAS8F,cAAAA,CAAa,CAElC,KA0HMhG,IATEoC,EAAOkD,GAjNAxC,GAkNGD,EAAOrG,IAAI,IACrB4F,CAAAA,EAAOkD,GAAa3C,MAAM,CAAC8D,GAAKA,EAAE3M,eAAe,CAAC4M,UAAU,CAACC,WAAW,GAAG7L,QAAQ,CAAC+H,EAAOrG,IAAI,GAAGmK,WAAW,MAE1GC,CA9FgB,CAACtB,EAA6Bf,KACrD,IAAMvE,EAAyB,CAAC,EAKhC,IAAK,IAAI7G,KAHJ6C,EAAWmE,eAAe,EAAEnE,CAAAA,EAAWmE,eAAe,CAAG,CAAC,GAGhDoE,GACXvE,CAAU,CAAC7G,EAAG,CAAG,EAAE,CAGvB,IAAM0N,EAAalD,EAAYxK,EAAE,CAE3B2N,EAAyB,EAAE,CAE3B7G,EAEF,CAAC,EAGL,IAAK,IAAMxC,KAAO6H,EAAc,CAC5B,IACI1D,EAAQnE,EAAII,MAAM,CAACkJ,YAAY,CAACF,EAAW,CAC1CpK,MAAMC,OAAO,CAACkF,IAAQA,CAAAA,EAAQ,EAAE,EACrC,IAAIoF,EAAY,GAChB,GAAIpF,GAASA,EAAMzK,MAAM,CAAG,EACxB,IAAK,IAAMgC,KAAMoL,EACT3C,EAAM9G,QAAQ,CAAC3B,KACf6G,CAAU,CAAC7G,EAAG,CAAC8L,IAAI,CAACxH,EAAItE,EAAE,EAC1B6N,EAAY,IAMnBA,GACDF,EAAa7B,IAAI,CAACxH,EAAItE,EAAE,EAI5B8G,CAAS,CAACxC,EAAItE,EAAE,CAAC,CAAG,CAChBA,GAAIsE,EAAItE,EAAE,CACV0E,OAAQJ,EAAII,MAAM,CAClB/D,gBAAiB2D,EAAI3D,eAAe,CACpCmN,UAAWxJ,EAAIwJ,SAAS,CAGhC,CAGA,IAAMC,EAA8B,CAAC,EAErC,IAAK,IAAI/N,KAAMoL,EAAY,CACvB2C,CAAe,CAAC/N,EAAG,CAAG,EAAE,CAIxB,IAAMgO,EAAiBnL,EAAWmE,eAAe,CAAChH,EAAG,EAAI,CACrDiN,WAAY,EAAE,CAEbe,CAAAA,EAAef,UAAU,EAAEe,CAAAA,EAAef,UAAU,CAAG,EAAE,EAE9D,IAAMgB,EAAgB,IAAIC,IAAIrH,CAAU,CAAC7G,EAAG,EAG5C,IAAK,IAAMmO,KAAUH,EAAef,UAAU,CACtCgB,EAAcG,GAAG,CAACD,IAClBJ,CAAe,CAAC/N,EAAG,CAAC8L,IAAI,CAACqC,GAKjC,IAAK,IAAMA,KAAUtH,CAAU,CAAC7G,EAAG,CAC1BgO,EAAef,UAAU,CAACtL,QAAQ,CAACwM,IACpCJ,CAAe,CAAC/N,EAAG,CAAC8L,IAAI,CAACqC,EAGjCH,CAAAA,EAAef,UAAU,CAAGc,CAAe,CAAC/N,EAAG,CAOnD,OALA+N,CAAe,CAACnF,EAAa,CAAG+E,EAE3B9K,EAAWmE,eAAe,CAAC4B,EAAa,EAAE/F,CAAAA,EAAWmE,eAAe,CAAC4B,EAAa,CAAG,CAACjG,SAAU,GAAOsK,WAAY,EAAE,GAC1HpK,EAAWmE,eAAe,CAAC4B,EAAa,CAACqE,UAAU,CAAGU,EAE/C,CAAC9G,WAAYkH,EAAiBjH,UAAAA,CAAS,CAElD,GAS8BmC,EAAMpG,EAAWuI,UAAU,GAQnDiD,GAAYtH,GAAQtE,GAAG,CAACZ,GAAKA,EAAE7B,EAAE,EAIjC,CAACsO,GAAcC,GAAgB,CAAG1P,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,MAE1D,CAAC2P,GAAYC,GAAc,CAAG5P,CAAAA,EAAAA,EAAAA,QAAAA,EAA6B,MAE3D6P,GAAUC,CAAAA,EAAAA,EAAAA,EAAAA,EACZC,CAAAA,EAAAA,EAAAA,EAAAA,EAAUC,EAAAA,EAAaA,CAAE,CACrBC,qBAAsB,CAClBC,SAAU,EACd,CACJ,IA0CJ,SAAStJ,GAAazF,CAAU,CAAEgM,CAAoC,MAGjDnJ,EAFjB,GAAI,CAACA,EAAWmE,eAAe,CAAE,OAEjC,IAAIgI,EAAAA,OAAanM,CAAAA,EAAAA,EAAWmE,eAAe,GAA1BnE,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,CAA4B,CAAC7C,EAAG,CAC7CgP,IACAA,EAAa,CAAC,GAAGA,CAAU,CAAE,GAAGhD,CAAM,EACtCnJ,EAAWmE,eAAe,CAAChH,EAAG,CAAGgP,EACjCjD,GAAiB,CAAC/E,gBAAiBnE,EAAWmE,eAAe,GAAGxE,IAAI,GAE5E,CAEA,IAAMyM,GAAkB,IAEAnF,eAAAA,GAIhBK,EAAmB+E,GACnBjF,EAAgB,KAGhBkF,GAAqBD,EAE7B,EAEMC,GAAuB,MAAOD,IAChC,IAAMxD,EAAuB,CAAC,EAC1BwD,IAAYtG,GACZ8C,CAAAA,CAAM,CAAClB,EAAYxK,EAAE,CAAC,CAAG,CAACkP,EAAQ,EAGtC,MAAM5F,EAAcR,EAASA,QAAQ,CAAC9I,EAAE,CAAE,CAAC0L,EAAO,CACtD,EA2MA,MAAO,GAAA7K,EAAAC,GAAA,EAAAD,EAAAE,QAAA,WACH,GAAAF,EAAAG,IAAA,EAACC,MAAAA,CAAIC,UAAU,oDACV2B,EAAW+H,WAAW,EAAI,CAACH,GAAU,GAAA5J,EAAAC,GAAA,EAAAD,EAAAE,QAAA,WAClC,GAAAF,EAAAC,GAAA,EAACsO,EAAAA,EAAaA,CAAAA,CAAAA,KAElB,GAAAvO,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,8CACX,GAAAL,EAAAG,IAAA,EAACqO,EAAAA,EAAUA,CAAAA,CACPC,WAAY,CAACC,aAAc,GAAKC,UAAW,CAACC,EAAG,GAAIC,EAAG,EAAE,CAAC,EACzDhB,QAASA,GACTiB,YA1KhB,SAAqBC,CAAqB,MAClCA,EAKAA,EALJ,GAAIA,CAAAA,OAAAA,CAAAA,EAAAA,EAAMC,MAAM,CAAC1P,IAAI,CAAC6H,OAAO,GAAzB4H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA2BxP,IAAI,IAAK,SAAU,CAC9CmO,GAAgBqB,EAAMC,MAAM,CAAC1P,IAAI,CAAC6H,OAAO,CAAC9E,MAAM,EAChD,MACJ,CAEA,GAAI0M,CAAAA,OAAAA,CAAAA,EAAAA,EAAMC,MAAM,CAAC1P,IAAI,CAAC6H,OAAO,GAAzB4H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA2BxP,IAAI,IAAK,OAAQ,CAC5CqO,GAAcmB,EAAMC,MAAM,CAAC1P,IAAI,CAAC6H,OAAO,CAAC7I,IAAI,EAC5C,MACJ,CACJ,EAiKgB2Q,UA/JhB,SAAmBF,CAAmB,MAY9BC,EASOA,EACwBA,EACFE,EAA+BA,EAAAA,EAEvCF,EAAAA,EACFE,EAAAA,EAKOF,EAuBPhN,EArDvB0L,GAAgB,MAChBE,GAAc,MAEd,GAAM,CAACoB,OAAAA,CAAM,CAAEE,KAAAA,CAAI,CAAC,CAAGH,EACvB,GAAI,CAACG,EAAM,OAEX,IAAMC,EAAWH,EAAO7P,EAAE,CACpBiQ,EAASF,EAAK/P,EAAE,CAEtB,GAAIgQ,IAAaC,GAEjB,GAAIJ,CAAAA,OAAAA,CAAAA,EAAAA,EAAO1P,IAAI,CAAC6H,OAAO,GAAnB6H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAqBzP,IAAI,IAAK,SAAU,CACxC6G,QAAQC,GAAG,CAAC,yBAEZ,IAAMgJ,EAAoBnJ,GAAQoJ,SAAS,CAACtO,GAAKA,EAAE7B,EAAE,GAAKgQ,GACpDI,EAAkBrJ,GAAQoJ,SAAS,CAACtO,GAAKA,EAAE7B,EAAE,GAAKiQ,GAIxDlE,GAAiB,CAACX,WAFIiF,CAAAA,EAAAA,EAAAA,EAAAA,EAAUxN,EAAWuI,UAAU,CAAE8E,EAAmBE,EAE/B,GAAG5N,IAAI,EACtD,MAAO,GAAIqN,CAAAA,OAAAA,CAAAA,EAAAA,EAAO1P,IAAI,CAAC6H,OAAO,GAAnB6H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAqBzP,IAAI,IAAK,OAAQ,CAC7C,IAAMkQ,EAAyBT,CAAAA,OAAAA,CAAAA,EAAAA,EAAO1P,IAAI,CAAC6H,OAAO,GAAnB6H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAqB3P,QAAQ,GAAI,GAC1DqQ,EAAuBR,CAAAA,OAAAA,CAAAA,EAAAA,EAAK5P,IAAI,CAAC6H,OAAO,GAAjB+H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAmB7P,QAAQ,WAAI6P,CAAAA,EAAAA,EAAK5P,IAAI,CAAC6H,OAAO,GAAjB+H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAmB7M,MAAM,GAAzB6M,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA2B/P,EAAE,GAAI,GAEvFwQ,EAAeX,CAAAA,OAAAA,CAAAA,EAAAA,EAAO1P,IAAI,CAAC6H,OAAO,GAAnB6H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAqB1Q,IAAI,GAAzB0Q,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA2B7P,EAAE,GAAI,GAChDyQ,EAAaV,CAAAA,OAAAA,CAAAA,EAAAA,EAAK5P,IAAI,CAAC6H,OAAO,GAAjB+H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAmB5Q,IAAI,GAAvB4Q,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyB/P,EAAE,GAAI,GAK5Cb,EAAAA,OAAoB0Q,CAAAA,EAAAA,EAAO1P,IAAI,CAAC6H,OAAO,GAAnB6H,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAqB1Q,IAAI,CACnD,GAAI,CAACA,GACD,CAACoR,EADM,OAIX,GAAID,IAAmBC,EAAc,CAEjC,IAAIG,EAASvR,EAAKuF,MAAM,CAACkJ,YAAY,CAACpD,EAAYxK,EAAE,CAAC,CAGjD2Q,EAAmB,IAFMrN,MAAMC,OAAO,CAACmN,GAAUA,EAAS,EAAE,CAE1B,CACtCC,EAAS7O,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmB6O,EAAQL,GAChCC,GAAgBA,IAAiB3H,IACjC+H,EAAO7E,IAAI,CAACyE,GACZI,EAASC,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBD,IAE9B,IAAMjF,EAAuB,CAAC,CAE9BA,CAAAA,CAAM,CAAClB,EAAYxK,EAAE,CAAC,CAAG2Q,EAEzBvH,EAAmBN,EAASA,QAAQ,CAAC9I,EAAE,CAAE,CAACb,EAAKa,EAAE,CAAC,CAAE0L,EACxD,CAEA,IAAMsD,EAAAA,OAAanM,CAAAA,EAAAA,EAAWmE,eAAe,GAA1BnE,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,CAA4B,CAAC0N,EAAa,CACzDvB,IACAA,EAAW/B,UAAU,CAAG+B,EAAW/B,UAAU,EAAI,EAAE,CAC/CgD,EACAjB,EAAW/B,UAAU,CAAG4D,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB7B,EAAW/B,UAAU,CAAEwD,EAAYD,EAAc,UAGjGxB,EAAW/B,UAAU,CAACnB,IAAI,CAAC0E,GAC3BxB,EAAW/B,UAAU,CAAG2D,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiB5B,EAAW/B,UAAU,EAElElB,GAAiB,CAAC/E,gBAAiBnE,EAAWmE,eAAe,GAAGxE,IAAI,IAaxEyE,QAAQC,GAAG,CAAC,CAAC2I,OAAAA,EAAQE,KAAAA,EAAMO,eAAAA,EAAgBC,aAAAA,EAAcC,aAAAA,EAAcC,WAAAA,CAAU,EAiBrF,EAGJ,EA+DgBK,WA7DhB,SAAoBlB,CAAoB,EAgDxC,YAcgB,GAAA/O,EAAAG,IAAA,EAACC,MAAAA,CAAIC,UAAU,qFACX,GAAAL,EAAAC,GAAA,EAACmF,EAAAA,EAAeA,CAAAA,CAACT,MAAO6I,YACnBtH,GAAQtE,GAAG,CAAC0E,GACE,GAAAtG,EAAAC,GAAA,EAACyE,EAAWA,CACfrC,OAAQiE,EAGRtB,YAAahD,EAAWmE,eAAe,CAACG,EAAInH,EAAE,CAAC,CAC/CwF,MAAOqB,GAAWA,UAAU,CAACM,EAAInH,EAAE,CAAC,CAACyC,GAAG,CAACzC,GAAM6G,GAAWC,SAAS,CAAC9G,EAAG,EAEvEyF,aAAcA,GACdK,iBAAkBA,EAClBK,YAAaA,EACb/G,mBAAoB0J,EAASA,QAAQ,CAACjG,UAAU,CAChDvD,mBAAoBuD,EAAWyI,cAAc,CAC7CjM,iBAAkBwD,EAAWwI,YAAY,CACzC9L,WAAYA,GACZmG,YAAa,CAACiF,EACd/E,UAAWqJ,GACX5N,WAAYyH,EAASA,QAAQ,CAAC9I,EAAE,EAd3BmH,EAAInH,EAAE,KAmB1B8F,GAAoB,GAAAjF,EAAAC,GAAA,EAACsG,EAASA,CAACsB,aAtSpD,SAAyBqI,CAAY,CAAExK,CAAY,EAC/C,IAAM4G,EAAuB,CACzBnN,GAAIgR,CAAAA,EAAAA,EAAAA,YAAAA,IACJvQ,MAAOsQ,EACPxK,MAAAA,CACJ,EACAiE,EAAYwC,SAAS,CAAClB,IAAI,CAACqB,EAAOnN,EAAE,EACpCwK,EAAY4C,UAAU,CAACD,EAAOnN,EAAE,CAAC,CAAGmN,EAEpC9D,EAAqBP,EAASA,QAAQ,CAAC9I,EAAE,CAAEwK,EAAYxK,EAAE,CAAEwK,EAAYpK,IAAI,CAAE,CACzEgN,WAAY5C,EAAY4C,UAAU,CAClCJ,UAAWxC,EAAYwC,SAAS,EAExC,IA0RqBlH,GAAoB,GAAAjF,EAAAC,GAAA,EAAC8F,EAAaA,CAC/BC,WAAYA,GAAWA,UAAU,CACjCE,QAAS8F,GACT7F,gBAAiBnE,EAAWmE,eAAe,CAC3CF,UAAWD,GAAWC,SAAS,CAC/BrB,aAAcA,GACdrG,mBAAoB0J,EAASA,QAAQ,CAACjG,UAAU,CAChDvD,mBAAoBuD,EAAWyI,cAAc,CAC7CjM,iBAAkBwD,EAAWwI,YAAY,CACzC/J,SAAU,CAACqJ,EACXpL,WAAYA,QAInB0R,CAAAA,EAAAA,EAAAA,YAAAA,EACG,GAAApQ,EAAAG,IAAA,EAACkQ,EAAAA,EAAWA,CAAAA,WACP5C,IACG,GAAAzN,EAAAC,GAAA,EAACyE,EAAWA,CACRrC,OAAQoL,GAGRzI,YAAahD,EAAWmE,eAAe,CAACsH,GAAatO,EAAE,CAAC,CACxDwF,MAAOqB,GAAWA,UAAU,CAACyH,GAAatO,EAAE,CAAC,CAACyC,GAAG,CAACzC,GAAM6G,GAAWC,SAAS,CAAC9G,EAAG,EAEhFyF,aAAcA,GACdrG,mBAAoB0J,EAASA,QAAQ,CAACjG,UAAU,CAChDvD,mBAAoBuD,EAAWyI,cAAc,CAC7CjM,iBAAkBwD,EAAWwI,YAAY,CACzC9L,WAAYA,GACZmG,YAAa,CAACiF,EACdtJ,WAAYyH,EAASA,QAAQ,CAAC9I,EAAE,EAX3BsO,GAAatO,EAAE,EAc3BwO,IACG,GAAA3N,EAAAC,GAAA,EAACvC,EAAQA,CACLe,mBAAoBuD,EAAWyI,cAAc,CAC7CjM,iBAAkBwD,EAAWwI,YAAY,CACzCjM,mBAAoB0J,EAASA,QAAQ,CAACjG,UAAU,CAChDtD,WAAYA,GAEZW,SAAS,GACTmB,WAAYyH,EAASA,QAAQ,CAAC9I,EAAE,CAChCb,KAAMqP,IAHDA,GAAWxO,EAAE,KAM9BmR,SAASC,IAAI,OAMxBpH,GACG,GAAAnJ,EAAAC,GAAA,EAACuQ,EAAAA,CAAcA,CAAAA,CACXhK,KAAM2C,EACNsH,QAAS,IAAMrH,EAAgB,IAC/B5I,WAAYyH,EAASA,QAAQ,CAAC9I,EAAE,CAChCyJ,WAAY8H,CA/RL,KACnB,GAAI,GAAoBrH,IAAoBtB,EAI5C,MAAO,CACHmC,WAAY,CAAC,CACT7K,SAAUsK,EAAYxK,EAAE,CACxBwR,GAAIC,EAAAA,eAAeA,CAACC,MAAM,CAC1BjJ,MAAO,CAACyB,EAAgB,EAC1B,CACFc,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAExB,KAmRgByG,iBA9lBDlI,EA+lBCmI,gBAxSY,IAExB3S,EAAW4S,EAAU/I,EAASA,QAAQ,CAAC9I,EAAE,EACzCiK,EAAgB,GACpB,QAySJ,iBC/rBO,IAAM6H,EAAY,IACrB,GAAM,CAAC/O,cAAAA,CAAa,CAAEC,mBAAAA,CAAkB,CAAEC,QAAAA,CAAO,CAAEiG,UAAAA,CAAS,CAAC,CAAGlK,CAAAA,EAAAA,EAAAA,EAAAA,IAE1D,CAAC6D,WAAAA,CAAU,CAAC,CAAGrD,EACfsJ,EAAW/F,CAAa,CAACF,EAAWxB,UAAU,CAAC,CAC/CmJ,EAA0C1B,EAASA,QAAQ,CAACjG,UAAU,CAACM,UAAU,CAACN,EAAWkG,UAAU,CAAC,EAAE,CAAC,CAEjH,MAAQ,GAAAlI,EAAAG,IAAA,EAAAH,EAAAE,QAAA,YACF,EAAC+H,GAAY,CAAC0B,GAAeA,EAAYpK,IAAI,GAAKoD,EAAAA,qBAAqBA,CAACY,MAAM,GAAK,GAAAvD,EAAAC,GAAA,EAACiR,EAAAA,UAAUA,CAAAA,CAACC,KAAK,OAAOC,MAAM,wBAClHnJ,GAAY0B,GAAe,GAAA3J,EAAAC,GAAA,EAAC+H,EAAcA,CAACC,SAAUA,EAAW,GAAGtJ,CAAK,KAGjF,uCCAA,IAAM0S,EAA2BC,EAAAA,UAAgB,CAvBjD,SAA8B9L,CAI7B,CAAE+L,CAAM,KAJqB,CAC5B3R,MAAAA,CAAK,CACL4R,QAAAA,CAAO,CACP,GAAG7S,EACJ,CAJ6B6G,EAK5B,OAAoB8L,EAAAA,aAAmB,CAAC,MAAO1G,OAAO6G,MAAM,CAAC,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbxR,IAAKiR,EACL,kBAAmBC,CACrB,EAAG7S,GAAQiB,EAAqB0R,EAAAA,aAAmB,CAAC,QAAS,CAC3DnS,GAAIqS,CACN,EAAG5R,GAAS,KAAmB0R,EAAAA,aAAmB,CAAC,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,8IACL,GACF,EAEAC,CAAAA,EAAAC,CAAA,CAAed,uCCDf,IAAMA,EAA2BC,EAAAA,UAAgB,CAvBjD,SAAmC9L,CAIlC,CAAE+L,CAAM,KAJ0B,CACjC3R,MAAAA,CAAK,CACL4R,QAAAA,CAAO,CACP,GAAG7S,EACJ,CAJkC6G,EAKjC,OAAoB8L,EAAAA,aAAmB,CAAC,MAAO1G,OAAO6G,MAAM,CAAC,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbxR,IAAKiR,EACL,kBAAmBC,CACrB,EAAG7S,GAAQiB,EAAqB0R,EAAAA,aAAmB,CAAC,QAAS,CAC3DnS,GAAIqS,CACN,EAAG5R,GAAS,KAAmB0R,EAAAA,aAAmB,CAAC,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,gIACL,GACF,EAEAC,CAAAA,EAAAC,CAAA,CAAed", "sources": ["webpack://_N_E/./node_modules/email-validator/index.js", "webpack://_N_E/./src/components/workspace/main/views/board/components/itemCard.tsx", "webpack://_N_E/./src/components/workspace/main/views/board/components/boardColumn.tsx", "webpack://_N_E/./src/components/workspace/main/views/board/components/hiddenColumns.tsx", "webpack://_N_E/./src/components/workspace/main/views/board/components/newColumn.tsx", "webpack://_N_E/./src/components/workspace/main/views/board/components/boardContainer.tsx", "webpack://_N_E/./src/components/workspace/main/views/board/board.tsx", "webpack://_N_E/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js", "webpack://_N_E/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassCircleIcon.js"], "sourcesContent": ["\"use strict\";\r\n\r\nvar tester = /^[-!#$%&'*+\\/0-9=?A-Z^_a-z{|}~](\\.?[-!#$%&'*+\\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\\.?[a-zA-Z0-9])*\\.[a-zA-Z](-?[a-zA-Z0-9])+$/;\r\n// Thanks to:\r\n// http://fightingforalostcause.net/misc/2006/compare-email-regex.php\r\n// http://thedailywtf.com/Articles/Validating_Email_Addresses.aspx\r\n// http://stackoverflow.com/questions/201323/what-is-the-best-regular-expression-for-validating-email-addresses/201378#201378\r\nexports.validate = function(email)\r\n{\r\n\tif (!email)\r\n\t\treturn false;\r\n\t\t\r\n\tif(email.length>254)\r\n\t\treturn false;\r\n\r\n\tvar valid = tester.test(email);\r\n\tif(!valid)\r\n\t\treturn false;\r\n\r\n\t// Further checking of some things regex can't handle\r\n\tvar parts = email.split(\"@\");\r\n\tif(parts[0].length>64)\r\n\t\treturn false;\r\n\r\n\tvar domainParts = parts[1].split(\".\");\r\n\tif(domainParts.some(function(part) { return part.length>63; }))\r\n\t\treturn false;\r\n\r\n\treturn true;\r\n}", "import * as React from \"react\"\r\nimport {Fragment, ReactNode, useState} from \"react\"\r\nimport {EllipsisHorizontalIcon} from \"@heroicons/react/24/outline\";\r\nimport {Checkbox} from \"@/components/ui/checkbox\";\r\nimport {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from \"@/components/ui/dropdown-menu\";\r\nimport {Button} from \"@/components/ui/button\";\r\nimport {useSortable} from \"@dnd-kit/sortable\";\r\nimport {CSS} from \"@dnd-kit/utilities\";\r\nimport {DataViewRow} from \"@/components/workspace/main/views/table\";\r\nimport {ViewColumnCustomization} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {CreatedAtColumn, DatabaseDefinition, DatabaseFieldDataType, DateColumn, FilesColumnDbValue, LinkedColumn, SelectColumn, UpdatedAtColumn} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {recordValueToText} from \"opendb-app-db-utils/lib/utils/db\";\r\nimport {DatabaseFieldTypeIcon} from \"@/components/workspace/main/database/databaseFieldTypeIcon\";\r\nimport {getTextBasedColFormattedValue} from \"@/components/workspace/main/views/table/renderer/fields/text\";\r\nimport {RenderPureSelect} from \"@/components/workspace/main/views/table/renderer/fields/select\";\r\nimport {RenderPureLinked} from \"@/components/workspace/main/views/table/renderer/fields/linked\";\r\nimport {useWorkspace} from \"@/providers/workspace\";\r\nimport {RenderPureDate} from \"@/components/workspace/main/views/table/renderer/fields/date\";\r\nimport {RenderPurePerson} from \"@/components/workspace/main/views/table/renderer/fields/person\";\r\nimport {useViews} from \"@/providers/views\";\r\nimport {removeAllArrayItem} from \"opendb-app-db-utils/lib\";\r\nimport {useStackedPeek} from \"@/providers/stackedpeek\";\r\n\r\ninterface ItemCardProps {\r\n    item: DataViewRow\r\n    dataColumnsOrder: string[]\r\n    dataColumnPropsMap: {\r\n        [p: string]: ViewColumnCustomization\r\n    }\r\n    databaseDefinition: DatabaseDefinition\r\n    titleColId: string\r\n    databaseId: string\r\n    disabled?: boolean\r\n    columnId: string\r\n}\r\n\r\nexport const ItemCard = (props: ItemCardProps) => {\r\n    const {selectedIds, setSelectedIds} = useViews()\r\n    const [mouseIsOver, setMouseIsOver] = useState(false);\r\n\r\n\r\n    const {deleteRecords} = useViews()\r\n    const {url} = useWorkspace()\r\n    const {openRecord} = useStackedPeek()\r\n\r\n    // const itemUrl = url(`/databases/${props.databaseId}/records/${props.item.id}`)\r\n    // const router = useRouter()\r\n\r\n    const {item, databaseDefinition, dataColumnsOrder, dataColumnPropsMap, titleColId} = props\r\n    let {\r\n        setNodeRef,\r\n        attributes,\r\n        listeners,\r\n        transform,\r\n        transition,\r\n        isDragging,\r\n    } = useSortable({\r\n        id: `${item.id}|${props.columnId}`,\r\n        data: {\r\n            type: \"Item\",\r\n            item,\r\n            columnId: props.columnId\r\n        },\r\n        // disabled: editMode,\r\n    });\r\n\r\n    // isDragging = false\r\n    const style = {\r\n        transition,\r\n        transform: CSS.Transform.toString(transform),\r\n    };\r\n\r\n    const title = recordValueToText(item.processedRecord.processedRecordValues[titleColId]) || 'Untitled'\r\n\r\n    return <>\r\n        <div className={`w-full border bg-white overflow-hidden ${isDragging ? 'opacity-0 invisible' : ''}`}\r\n             ref={setNodeRef}\r\n             style={style}\r\n             onClick={() => openRecord(props.item.id, props.databaseId)}\r\n             {...attributes}\r\n             {...listeners}>\r\n            <div className=\"p-2.5 text-sm font-semibold flex gap-2 items-center\">\r\n                {!props.disabled && <Checkbox\r\n                    onClick={e => e.stopPropagation()}\r\n                    checked={selectedIds.includes(props.item.id)}\r\n                    onCheckedChange={c => {\r\n                        if (c) setSelectedIds([...selectedIds, props.item.id])\r\n                        else setSelectedIds([...removeAllArrayItem(selectedIds, props.item.id)])\r\n                    }}/>}\r\n                <div className=\"flex-1 text-xs truncate\">\r\n                    {title}\r\n                </div>\r\n                {!props.disabled && props.databaseId && <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                        <Button variant={\"ghost\"} className=\"rounded-full h-auto p-1\"><\r\n                            EllipsisHorizontalIcon className='size-4'/>\r\n                        </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent className=\"w-56  rounded-none text-neutral-800 font-semibold\" align=\"end\">\r\n                        <DropdownMenuItem className=\"text-xs rounded-none p-2\"\r\n                                          onClick={() => {\r\n                                              deleteRecords(props.databaseId, [props.item.id]).then()\r\n                                          }}>\r\n                            Delete\r\n                        </DropdownMenuItem>\r\n                    </DropdownMenuContent>\r\n                </DropdownMenu>}\r\n            </div>\r\n            <div className=\"flex flex-col gap-3 px-2.5  pb-4\">\r\n                {dataColumnsOrder.map(colId => {\r\n                    const colProps = dataColumnPropsMap[colId]\r\n                    if (colProps.isHidden) return null\r\n\r\n                    return <Fragment key={colId}>\r\n                        <RenderField\r\n                            definition={props.databaseDefinition}\r\n                            columnId={colId}\r\n                            item={props.item}/>\r\n                    </Fragment>\r\n                })}\r\n            </div>\r\n        </div>\r\n    </>\r\n};\r\n\r\n\r\ninterface RenderFieldProps {\r\n    // adjacentDatabases: AdjacentDatabases\r\n    definition: DatabaseDefinition\r\n    item: DataViewRow\r\n    columnId: string\r\n}\r\n\r\nexport const RenderField = (props: RenderFieldProps) => {\r\n    const {databaseStore, databaseErrorStore, members} = useWorkspace()\r\n    const column = props.definition.columnsMap[props.columnId];\r\n\r\n    if (!column) return null\r\n    let content: ReactNode;\r\n\r\n    const rowValue = props.item.processedRecord.processedRecordValues[props.columnId] || ''\r\n    if (!rowValue\r\n        || (typeof rowValue === 'string' && !rowValue.trim())\r\n        || (Array.isArray(rowValue) && rowValue.length === 0)\r\n    ) return null\r\n\r\n    switch (column.type) {\r\n        case DatabaseFieldDataType.UUID:\r\n        case DatabaseFieldDataType.Text:\r\n        case DatabaseFieldDataType.Number:\r\n        case DatabaseFieldDataType.AI:\r\n        case DatabaseFieldDataType.Summarize:\r\n        case DatabaseFieldDataType.Derived:\r\n            let textValue = String(rowValue)\r\n            if (column.type === DatabaseFieldDataType.Text || column.type === DatabaseFieldDataType.Number) {\r\n                const f = getTextBasedColFormattedValue(column, textValue)\r\n                textValue = f.displayValue\r\n            }\r\n            content = <div className='font-medium truncate text-xs'>{textValue}</div>\r\n            break;\r\n        case DatabaseFieldDataType.Select:\r\n            content = <RenderPureSelect column={column as SelectColumn} row={props.item}/>\r\n            break;\r\n        case DatabaseFieldDataType.Checkbox:\r\n            content = <Checkbox\r\n                checked={!!rowValue}\r\n            />\r\n            break;\r\n        case DatabaseFieldDataType.Linked:\r\n            content = <RenderPureLinked\r\n                refDatabaseId={props.item.record.databaseId}\r\n                column={column as LinkedColumn}\r\n                row={props.item}\r\n                databaseErrorStore={databaseErrorStore}\r\n                databaseStore={databaseStore}\r\n            />\r\n            break;\r\n        case DatabaseFieldDataType.Date:\r\n        case DatabaseFieldDataType.CreatedAt:\r\n        case DatabaseFieldDataType.UpdatedAt:\r\n            content = <RenderPureDate\r\n                column={column as (DateColumn | CreatedAtColumn | UpdatedAtColumn)}\r\n                className='text-xs font-medium'\r\n                row={props.item}\r\n            />\r\n            break;\r\n        case DatabaseFieldDataType.CreatedBy:\r\n        case DatabaseFieldDataType.UpdatedBy:\r\n        case DatabaseFieldDataType.Person:\r\n            content = <RenderPurePerson\r\n                column={column}\r\n                row={props.item}\r\n                members={members}\r\n            />\r\n            break;\r\n        case DatabaseFieldDataType.Files:\r\n            let rowVal = rowValue as (FilesColumnDbValue | undefined)\r\n            let files = rowVal && Array.isArray(rowVal) ? rowVal : []\r\n            content = <div className='font-medium truncate text-xs'>{files.length} file(s)</div>\r\n            break;\r\n\r\n\r\n    }\r\n    if (!content) return null;\r\n\r\n\r\n    return (<div className='flex gap-2 items-center w-full overflow-hidden'>\r\n        <DatabaseFieldTypeIcon type={column.type} className='col-3'/>\r\n        <div className='flex-1 overflow-hidden'>\r\n            {content}\r\n        </div>\r\n    </div>)\r\n}", "import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from \"@/components/ui/dropdown-menu\";\r\nimport {Button} from \"@/components/ui/button\";\r\nimport {EllipsisHorizontalIcon} from \"@heroicons/react/24/outline\";\r\nimport React, {CSSProperties} from \"react\";\r\nimport {ItemCard} from \"@/components/workspace/main/views/board/components/itemCard\";\r\nimport {Column} from \"@/components/workspace/main/views/board/components/boardContainer\";\r\nimport {SortableContext, useSortable} from \"@dnd-kit/sortable\";\r\nimport {CSS} from \"@dnd-kit/utilities\";\r\nimport {ScrollArea} from \"@/components/ui/scroll-area\";\r\nimport {DataViewRow} from \"@/components/workspace/main/views/table\";\r\nimport {BoardGroupItemProps, ViewColumnCustomization} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {DatabaseDefinition} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {ColorInfo} from \"@/utils/color\";\r\nimport {CirclePlusIcon} from \"@/components/icons/FontAwesomeRegular\";\r\n\r\nexport interface BoardColumnProps {\r\n    column: Column\r\n    items: DataViewRow[]\r\n    columnProps: BoardGroupItemProps\r\n\r\n    // deleteColumn: (id: Id) => void;\r\n    updateColumn?: (id: string, update: Partial<BoardGroupItemProps>) => void;\r\n\r\n    dataColumnsOrder: string[]\r\n    dataColumnPropsMap: { [p: string]: ViewColumnCustomization }\r\n    databaseDefinition: DatabaseDefinition\r\n\r\n    addRecord?: (id: string) => void\r\n\r\n    databaseId: string\r\n    titleColId: string\r\n    dndDisabled?: boolean\r\n\r\n    canEditStructure?: boolean\r\n    canEditData?: boolean\r\n}\r\n\r\nexport const BoardColumn = (props: BoardColumnProps) => {\r\n    const {items, updateColumn, column} = props\r\n    // const itemsIds = useMemo(() => {\r\n    //     return items.map((item) => item.id);\r\n    // }, [items]);\r\n\r\n    const {\r\n        setNodeRef,\r\n        attributes,\r\n        listeners,\r\n        transform,\r\n        transition,\r\n        isDragging,\r\n    } = useSortable({\r\n        id: column.id,\r\n        data: {\r\n            type: \"Column\",\r\n            column,\r\n        },\r\n        disabled: !!props.dndDisabled,\r\n    });\r\n\r\n    const style = {\r\n        transition,\r\n        transform: CSS.Transform.toString(transform),\r\n    };\r\n\r\n    // console.log({columnProps: props.columnProps})\r\n    return <>\r\n        <div\r\n            className={`flex-none flex flex-col gap-2 h-full overflow-hidden w-72 ${isDragging ? 'opacity-0 invisible' : ''}`}\r\n            ref={setNodeRef}\r\n            style={style}\r\n            {...attributes}\r\n            {...listeners}>\r\n            <BoardColumnHeader column={column}\r\n                               items={items}\r\n                               addRecord={props.addRecord}\r\n                               columnProps={props.columnProps}\r\n                               disabled={props.dndDisabled || !props.canEditStructure}\r\n                               updateColumn={props.updateColumn}\r\n                // deleteColumn={() => deleteColumn(column.id)}\r\n\r\n            />\r\n            <div className=\"flex-1 overflow-hidden\">\r\n                <ScrollArea className=\"size-full scrollBlockChild\" aria-orientation={\"vertical\"}>\r\n                    <div className=\"w-full h-full overflow-y-auto overflow-x-hidden flex flex-col gap-4 pb-4\">\r\n                        <SortableContext items={items} disabled={props.dndDisabled}>\r\n                            {items.map((item, i) => <ItemCard\r\n                                key={item.id || i}\r\n                                dataColumnPropsMap={props.dataColumnPropsMap}\r\n                                dataColumnsOrder={props.dataColumnsOrder}\r\n                                databaseDefinition={props.databaseDefinition}\r\n                                disabled={props.dndDisabled || !props.canEditData}\r\n                                titleColId={props.titleColId}\r\n                                columnId={props.column.id}\r\n                                databaseId={props.databaseId}\r\n                                item={item}/>)}\r\n                        </SortableContext>\r\n                    </div>\r\n                </ScrollArea>\r\n\r\n\r\n            </div>\r\n\r\n        </div>\r\n    </>\r\n}\r\n\r\ninterface BoardColumnHeaderProps extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> {\r\n    column: Column\r\n    items: DataViewRow[]\r\n    columnProps: BoardGroupItemProps\r\n    updateColumn?: (id: string, update: Partial<BoardGroupItemProps>) => void;\r\n    addRecord?: (id: string) => void\r\n    disabled?: boolean\r\n    // deleteColumn: () => void\r\n}\r\n\r\nexport const BoardColumnHeader = ({column, items, columnProps, updateColumn, addRecord, disabled, ...rest}: BoardColumnHeaderProps) => {\r\n\r\n    let info: null | any = null\r\n    if (column.color) {\r\n        info = ColorInfo(column.color)\r\n    }\r\n    let style: CSSProperties = {}\r\n    if (info) {\r\n        style['backgroundColor'] = `${info.bg}`\r\n    }\r\n\r\n    // console.log({columnProps: columnProps, column, items})\r\n\r\n    return <>\r\n        <header {...rest} className=\"py-2 flex gap-2 pr-0.5 group\">\r\n            <div className=\"flex-1 overflow-hidden flex gap-1 items-center justify-start\">\r\n                <div className=\"text-xs bg-neutral-200 rounded-full py-0.5 px-2 font-semibold w-min max-w-full truncate\"\r\n                     style={style}>\r\n                    {column.title}\r\n                </div>\r\n                <Button variant=\"ghost\"\r\n                        className=\"h-auto w-auto py-0.5 px-2 rounded-full items-center text-xs gap-1 mr-0.5\">\r\n                    {items.length}\r\n                    {/*<ChevronDownIcon className=\"h-3 w-3\"/>*/}\r\n                </Button>\r\n                {/*<DropdownMenu>*/}\r\n                {/*    <DropdownMenuTrigger asChild>*/}\r\n                {/*        <Button variant=\"ghost\"*/}\r\n                {/*                onClick={(e) => {*/}\r\n                {/*                    // setCollapse(!collapse)*/}\r\n                {/*                    e.stopPropagation()*/}\r\n                {/*                    e.preventDefault()*/}\r\n                {/*                    alert(\"Options\")*/}\r\n                {/*                }}*/}\r\n                {/*                className=\"h-auto w-auto py-0.5 px-2 rounded-full items-center text-xs gap-1 mr-0.5 hidden\">*/}\r\n                {/*            {items.length}*/}\r\n                {/*            <ChevronDownIcon className=\"h-3 w-3\"/>*/}\r\n                {/*        </Button>*/}\r\n                {/*    </DropdownMenuTrigger>*/}\r\n                {/*    <DropdownMenuContent className=\"w-56  rounded-none text-neutral-800 font-semibold\" align=\"end\">*/}\r\n                {/*        <DropdownMenuGroup className=\"p-1\">*/}\r\n                {/*            <DropdownMenuItem className=\"text-xs rounded-none p-2\">*/}\r\n                {/*                Hide*/}\r\n                {/*            </DropdownMenuItem>*/}\r\n                {/*            <DropdownMenuItem className=\"text-xs rounded-none p-2\">*/}\r\n                {/*                Delete*/}\r\n                {/*            </DropdownMenuItem>*/}\r\n                {/*            <DropdownMenuItem className=\"text-xs rounded-none p-2\">*/}\r\n                {/*                Sort 0 - 9*/}\r\n                {/*            </DropdownMenuItem>*/}\r\n                {/*            <DropdownMenuItem className=\"text-xs rounded-none p-2\">*/}\r\n                {/*                Sort 9 - 0*/}\r\n                {/*            </DropdownMenuItem>*/}\r\n                {/*            <DropdownMenuItem className=\"text-xs rounded-none p-2\"*/}\r\n                {/*                // onClick={deleteColumn}*/}\r\n                {/*            >*/}\r\n                {/*                Delete*/}\r\n                {/*            </DropdownMenuItem>*/}\r\n                {/*            <DropdownMenuItem className=\"text-xs rounded-none p-2\">Team</DropdownMenuItem>*/}\r\n                {/*            <DropdownMenuSub>*/}\r\n                {/*                <DropdownMenuSubTrigger className=\"text-xs rounded-none p-2\">Invite*/}\r\n                {/*                    users</DropdownMenuSubTrigger>*/}\r\n                {/*                <DropdownMenuPortal>*/}\r\n                {/*                    <DropdownMenuSubContent>*/}\r\n                {/*                        <DropdownMenuItem*/}\r\n                {/*                            className=\"text-xs rounded-none p-2\">Email</DropdownMenuItem>*/}\r\n                {/*                        <DropdownMenuItem*/}\r\n                {/*                            className=\"text-xs rounded-none p-2\">Message</DropdownMenuItem>*/}\r\n                {/*                        <DropdownMenuSeparator/>*/}\r\n                {/*                        <DropdownMenuItem*/}\r\n                {/*                            className=\"text-xs rounded-none p-2\">More...</DropdownMenuItem>*/}\r\n                {/*                    </DropdownMenuSubContent>*/}\r\n                {/*                </DropdownMenuPortal>*/}\r\n                {/*            </DropdownMenuSub>*/}\r\n                {/*            <DropdownMenuItem className=\"text-xs rounded-none p-2\">*/}\r\n                {/*                New Team*/}\r\n                {/*                <DropdownMenuShortcut>⌘+T</DropdownMenuShortcut>*/}\r\n                {/*            </DropdownMenuItem>*/}\r\n                {/*        </DropdownMenuGroup>*/}\r\n                {/*    </DropdownMenuContent>*/}\r\n                {/*</DropdownMenu>*/}\r\n\r\n            </div>\r\n            {!disabled && <>\r\n                {addRecord && <Button\r\n                    variant='ghost'\r\n                    onClick={() => addRecord?.(column.id)}\r\n                    className=\"rounded-full size-6 items-center p-1.5 -mr-1 hidden group-hover:block\">\r\n                    <CirclePlusIcon className='size-3'/>\r\n                </Button>}\r\n                <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                        <Button variant={\"ghost\"} className=\"rounded-full h-auto p-1\"><\r\n                            EllipsisHorizontalIcon className='size-4'/>\r\n                        </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent className=\"w-56  rounded-none text-neutral-800 font-semibold\" align=\"end\">\r\n                        <DropdownMenuItem className=\"text-xs rounded-none p-2 py-1.5\"\r\n                                          onClick={() => updateColumn?.(column.id, {isHidden: !columnProps?.isHidden})}>\r\n                            {columnProps?.isHidden ? 'Show' : 'Hide'}\r\n                        </DropdownMenuItem>\r\n\r\n                        {/*<DropdownMenuSeparator className=\"\"/>*/}\r\n                        {/*<DropdownMenuGroup className=\"p-1\">*/}\r\n                        {/*    <DropdownMenuItem className=\"text-xs rounded-none p-2\">*/}\r\n                        {/*        Delete*/}\r\n                        {/*    </DropdownMenuItem>*/}\r\n                        {/*</DropdownMenuGroup>*/}\r\n                    </DropdownMenuContent>\r\n                </DropdownMenu>\r\n            </>}\r\n\r\n        </header>\r\n    </>\r\n}", "import React from \"react\";\r\nimport {ScrollArea} from \"@/components/ui/scroll-area\";\r\nimport {BoardColumnHeader} from \"@/components/workspace/main/views/board/components/boardColumn\";\r\nimport {BoardItems, Column} from \"@/components/workspace/main/views/board/components/boardContainer\";\r\nimport {DataViewRow} from \"@/components/workspace/main/views/table\";\r\nimport {BoardGroupItemProps} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {ViewColumnCustomization} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {DatabaseDefinition} from \"opendb-app-db-utils/lib/typings/db\";\r\n\r\n\r\ninterface HiddenColumnsProps {\r\n    boardItems: BoardItems\r\n    rowIdsMap: { [id: string]: DataViewRow }\r\n    columns: Column[]\r\n    groupItemsProps: { [key: string]: BoardGroupItemProps }\r\n    updateColumn: (id: string, update: Partial<BoardGroupItemProps>) => void;\r\n\r\n    dataColumnsOrder: string[]\r\n    dataColumnPropsMap: { [p: string]: ViewColumnCustomization }\r\n    databaseDefinition: DatabaseDefinition\r\n\r\n    titleColId: string\r\n    disabled?: boolean\r\n\r\n\r\n}\r\n\r\nexport const HiddenColumns = (props: HiddenColumnsProps) => {\r\n\r\n    const {\r\n        boardItems, rowIdsMap,\r\n        columns, groupItemsProps, updateColumn,\r\n        dataColumnsOrder, dataColumnPropsMap, databaseDefinition,\r\n        titleColId\r\n    } = props\r\n\r\n\r\n    console.log({boardItems, columns, groupItemsProps})\r\n    if (!columns) return null;\r\n    return <>\r\n        <div className=\"flex-none flex flex-col gap-2 h-full w-64\">\r\n            <header className=\"py-2 flex gap-2\">\r\n                <div className=\"text-xs font-semibold\">\r\n                    Hidden column\r\n                </div>\r\n            </header>\r\n            <div className=\"flex-1 overflow-hidden\">\r\n                <ScrollArea className=\"w-full h-full\">\r\n                    <div className=\"h-full w-full overflow-y-auto flex flex-col gap-1 pb-4\">\r\n                        {columns.map(col => {\r\n                                return <BoardColumnHeader\r\n                                    key={col.id}\r\n                                    column={col}\r\n                                    updateColumn={updateColumn}\r\n                                    disabled={props.disabled}\r\n                                    items={boardItems[col.id].map(id => rowIdsMap[id])}\r\n                                    columnProps={groupItemsProps[col.id]}/>\r\n                                // return <BoardColumn\r\n                                //     column={col}\r\n                                //     key={col.id}\r\n                                //     // @ts-ignore\r\n                                //     columnProps={groupItemsProps[col.id]}\r\n                                //     items={boardItems[col.id].map(id => rowIdsMap[id])}\r\n                                //     // deleteColumn={deleteColumn}\r\n                                //     updateColumn={updateColumn}\r\n                                //     databaseDefinition={databaseDefinition}\r\n                                //     dataColumnPropsMap={dataColumnPropsMap}\r\n                                //     dataColumnsOrder={dataColumnsOrder}\r\n                                //     titleColId={titleColId}\r\n                                // />\r\n                            }\r\n                        )}\r\n                    </div>\r\n                </ScrollArea>\r\n\r\n\r\n            </div>\r\n        </div>\r\n    </>\r\n}", "import React, {useEffect, useRef, useState} from \"react\";\r\nimport {Button} from \"@/components/ui/button\";\r\nimport {PlusCircleIcon} from \"@heroicons/react/24/outline\";\r\nimport {Color} from \"opendb-app-db-utils/lib/typings/color\";\r\nimport {InputWithEnter} from \"@/components/custom-ui/inputWithEnter\";\r\nimport {ColorEntries} from \"@/utils/color\";\r\n\r\ninterface NewColumnProps {\r\n    createColumn: (name: string, color: Color) => void\r\n}\r\n\r\nexport const NewColumn = (props: NewColumnProps) => {\r\n    const [open, setOpen] = useState(false)\r\n    const inputRef = useRef<HTMLInputElement | null>(null)\r\n\r\n\r\n    const colour = ColorEntries[Math.floor(Math.random() * ColorEntries.length)]\r\n    // const defaultNewColor = colour.color;\r\n\r\n\r\n    useEffect(() => {\r\n        const inputEle = inputRef.current\r\n        if (!inputEle) {\r\n            return\r\n        }\r\n        inputEle.focus()\r\n\r\n        const onBlur = () => {\r\n            setOpen(false)\r\n        }\r\n        inputEle.addEventListener('blur', onBlur)\r\n\r\n        return () => {\r\n            inputEle.removeEventListener('blur', onBlur)\r\n        }\r\n    }, [open])\r\n\r\n    const onChange = (value: string) => {\r\n        value = value.trim()\r\n        if (!value) return\r\n\r\n        props.createColumn(value, colour.color)\r\n        setOpen(false)\r\n    }\r\n\r\n    return <>\r\n        <div className=\"flex-none flex flex-col gap-2 h-full w-64\">\r\n            {!open && <header className=\"py-2 flex gap-2\">\r\n                <Button variant=\"outline\"\r\n                        onClick={(e) => {\r\n                            // setCollapse(!collapse)\r\n                            // props.createColumn()\r\n                            setOpen(true)\r\n                        }}\r\n                        className=\"h-auto w-full p-2 py-1 rounded-full items-center text-xs gap-1 mr-0.5\">\r\n\r\n                    <PlusCircleIcon className=\"h-3 w-3\"/>\r\n                    New Column\r\n                </Button>\r\n            </header>}\r\n            {open && <>\r\n                <div className='w-full'>\r\n                    {/*<InputWithEnter shortEnter ref={ref => inputRef.current=autoFocus(ref)} wrapperClassname='h-8'/>*/}\r\n                    <InputWithEnter\r\n                        shortEnter ref={inputRef}\r\n                        onChange={onChange}\r\n                        wrapperClassname='h-8 pr-1'/>\r\n                </div>\r\n\r\n            </>}\r\n            {/*<div className=\"flex-1 overflow-hidden\">*/}\r\n            {/*</div>*/}\r\n        </div>\r\n    </>\r\n}", "import {BoardColumn} from \"@/components/workspace/main/views/board/components/boardColumn\";\r\nimport {HiddenColumns} from \"@/components/workspace/main/views/board/components/hiddenColumns\";\r\nimport React, {useState} from \"react\";\r\nimport {DndContext, DragEndEvent, DragOverEvent, DragOverlay, DragStartEvent, PointerSensor, useSensor, useSensors} from \"@dnd-kit/core\";\r\nimport {arrayMove, SortableContext} from \"@dnd-kit/sortable\";\r\nimport {NewColumn} from \"@/components/workspace/main/views/board/components/newColumn\";\r\nimport {createPortal} from \"react-dom\";\r\nimport {ItemCard} from \"@/components/workspace/main/views/board/components/itemCard\";\r\nimport {BoardViewRenderProps} from \"@/components/workspace/main/views/board/board\";\r\nimport {useWorkspace} from \"@/providers/workspace\";\r\nimport {DatabaseFieldDataType, DbRecordSort, MagicColumn, Match, RecordValues, SelectColumn, SelectOption, Sort} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {CompareOperator} from \"opendb-app-db-utils/lib/methods/compare\";\r\nimport {DatabaseRecordStoreItem} from \"@/typings/utilities\";\r\nimport {useViews, useViewFiltering, useViewSelection} from \"@/providers/views\";\r\nimport {Color} from \"opendb-app-db-utils/lib/typings/color\";\r\nimport {DataViewRow, filterAndSortRecords} from \"@/components/workspace/main/views/table\";\r\nimport {BoardGroupItemProps, BoardViewDefinition} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {ContentLocked} from \"@/components/workspace/main/views/common/contentLocked\";\r\nimport {arrayDeDuplicate, removeAllArrayItem} from \"opendb-app-db-utils/lib\";\r\nimport {arrayAddElementAdjacent} from \"opendb-app-db-utils/lib/methods/array\";\r\nimport {generateUUID} from \"opendb-app-db-utils/lib/methods/string\";\r\nimport {DatabaseConstants} from \"@/components/workspace/main/views/table/renderer/common/addColumn\";\r\nimport {useMaybeShared} from \"@/providers/shared\";\r\nimport {useMaybeTemplate} from \"@/providers/template\";\r\nimport {AddRecordModal} from \"@/components/workspace/main/views/AddRecordModal\";\r\nimport {useStackedPeek} from \"@/providers/stackedpeek\";\r\nimport {useViewContext} from \"@/components/workspace/main/views/ViewsRootLayout\";\r\n\r\nexport type Id = string | number;\r\nexport type Column = {\r\n    id: string;\r\n    title: string;\r\n    color?: Color\r\n    value: string\r\n};\r\n\r\nconst UngroupedKey = 'ungrouped'\r\n\r\nexport type Item = {\r\n    id: string | number;\r\n    columnId: Id;\r\n    content: string;\r\n};\r\n\r\nexport interface BoardItems {\r\n    [columnId: string]: string[]\r\n}\r\n\r\n\r\nexport const BoardContainer = (props: BoardViewRenderProps & {\r\n    database: DatabaseRecordStoreItem\r\n}) => {\r\n    const {databaseStore} = useWorkspace()\r\n    const {definition} = props\r\n\r\n    const database = databaseStore[definition.databaseId]\r\n    const groupColumn: SelectColumn = database.database.definition.columnsMap[definition.groupByIds[0]] as SelectColumn\r\n\r\n    if (!groupColumn) return null;\r\n\r\n    return <BoardContent {...props} />\r\n}\r\n\r\nconst BoardContent = (props: BoardViewRenderProps & {\r\n    database: DatabaseRecordStoreItem\r\n}) => {\r\n    const {databaseStore, databaseErrorStore, members, workspace} = useWorkspace()\r\n    const {definition} = props\r\n    const {updateViewDefinition, updateRecordValues, updateDatabaseColumn, createRecords, cache} = useViews()\r\n    const {filter: viewFilter, search: viewSearch} = useViewFiltering()\r\n    const {selectedIds, setSelectedIds} = useViewSelection()\r\n    const {openRecord} = useStackedPeek()\r\n    const {context} = useViewContext();\r\n\r\n    // State for AddRecordModal\r\n    const [showAddModal, setShowAddModal] = useState(false)\r\n    const [selectedGroupId, setSelectedGroupId] = useState<string>('')\r\n\r\n    const recordIds: string[] = cache.getCache(DatabaseConstants.NewlyCreatedColumnKey, [])\r\n    console.log('Newly created on render:', {recordIds})\r\n\r\n    const database = databaseStore[definition.databaseId]\r\n    const groupColumn: SelectColumn = database.database.definition.columnsMap[definition.groupByIds[0]] as SelectColumn\r\n\r\n    // if (!groupColumn) return null;\r\n    const shared = useMaybeShared()\r\n    const maybeShared = shared\r\n\r\n    const editable = !definition.lockContent && !shared\r\n\r\n    const maybeTemplate = useMaybeTemplate()\r\n\r\n    let canEditStructure = (!maybeTemplate && !maybeShared && !definition.lockContent && editable)\r\n    let canEditData: boolean = (!maybeTemplate && !maybeShared && !definition.lockContent && editable)\r\n\r\n    definition.filter = definition.filter || {conditions: [], match: Match.All}\r\n    definition.sorts = definition.sorts || []\r\n    definition.groupOrder = definition.groupOrder || []\r\n    definition.groupItemsProps = definition.groupItemsProps || {}\r\n    definition.columnsOrder = definition.columnsOrder || []\r\n    definition.columnPropsMap = definition.columnPropsMap || {}\r\n\r\n    // Use context-aware filter and search\r\n    const filter = viewFilter\r\n    const search = viewSearch\r\n    let titleColId = database.database.definition.titleColumnId || ''\r\n\r\n    const fixColumnPropsMap = () => {\r\n        // by default show the title column and 4 more and hide the rest\r\n        if (!titleColId) {\r\n            for (const column of Object.values(database.database.definition.columnsMap)) {\r\n                if (column.type === DatabaseFieldDataType.Text) {\r\n                    titleColId = column.id\r\n                    break\r\n                }\r\n            }\r\n        }\r\n        if (!titleColId) titleColId = database.database.definition.columnIds[0]\r\n\r\n        let {columnsOrder, columnPropsMap} = definition\r\n        // columnPropsMap = columnPropsMap || {}\r\n        // columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : []\r\n        // columnPropsMap = columnPropsMap || {}\r\n        let visibleColumns = Object.values(columnPropsMap).filter(props => !props.isHidden).length\r\n        const visibleOnLoad = visibleColumns\r\n\r\n        for (const column of Object.values(database.database.definition.columnsMap)) {\r\n            const id = column.id\r\n            if (!columnsOrder.includes(id)) columnsOrder.push(id)\r\n            if (!columnPropsMap[id]) columnPropsMap[id] = {isHidden: true}\r\n            if (id !== titleColId && visibleOnLoad === 0 && visibleColumns < 5) {\r\n                columnPropsMap[id] = {isHidden: false}\r\n                visibleColumns++\r\n            }\r\n        }\r\n    }\r\n\r\n    fixColumnPropsMap()\r\n\r\n    const buildCols = () => {\r\n        const columns: Column[] = []\r\n        const hiddenColumns: Column[] = []\r\n\r\n        // let {columnsOrder, columnPropsMap} = definition\r\n        const groupOrder = Array.isArray(definition.groupOrder) ? definition.groupOrder : []\r\n        const groupItemsProps = definition.groupItemsProps ? definition.groupItemsProps : {}\r\n\r\n        for (const key of groupColumn.optionIds) {\r\n            if (!groupOrder.includes(key)) groupOrder.push(key)\r\n            if (!groupItemsProps[key]) groupItemsProps[key] = {\r\n                isHidden: false,\r\n                itemsOrder: []\r\n            }\r\n        }\r\n        let ungroupExists = false\r\n\r\n        for (const id of groupOrder) {\r\n            let col: Column;\r\n            const option = groupColumn.optionsMap[id]\r\n\r\n\r\n            if (id === UngroupedKey) {\r\n                col = {\r\n                    id: UngroupedKey,\r\n                    title: 'Ungrouped',\r\n                    value: UngroupedKey\r\n                }\r\n                groupItemsProps[UngroupedKey] = groupItemsProps[id] || {\r\n                    isHidden: false,\r\n                    itemsOrder: []\r\n                }\r\n                ungroupExists = true\r\n            } else if (option) {\r\n                col = {\r\n                    id,\r\n                    title: option.title,\r\n                    color: option.color,\r\n                    value: id\r\n                }\r\n            } else continue\r\n            if (groupItemsProps[id].isHidden) hiddenColumns.push(col)\r\n            else columns.push(col)\r\n        }\r\n        if (!ungroupExists) {\r\n            const col: Column = {\r\n                id: UngroupedKey,\r\n                title: 'Ungrouped',\r\n                value: ''\r\n            }\r\n            columns.unshift(col)\r\n            groupItemsProps[UngroupedKey] = {\r\n                isHidden: false,\r\n                itemsOrder: []\r\n            }\r\n        }\r\n\r\n        definition.groupOrder = [...columns.map(c => c.id), ...hiddenColumns.map(c => c.id)]\r\n\r\n        return {columns, hiddenColumns}\r\n\r\n    }\r\n\r\n    const updateDefinition = async (update: Partial<BoardViewDefinition>) => {\r\n        await updateViewDefinition(props.view.id, props.view.pageId, update)\r\n    }\r\n    const getProcessedRecords = () => {\r\n        const sortOptions: DbRecordSort[] = []\r\n        sortOptions.push({columnId: MagicColumn.CreatedAt, order: Sort.Desc})\r\n\r\n        const {rows} = filterAndSortRecords(\r\n            database,\r\n            members,\r\n            databaseStore,\r\n            definition.filter,\r\n            filter,\r\n            sortOptions,\r\n            workspace.workspaceMember.userId\r\n        )\r\n\r\n\r\n        return rows\r\n    }\r\n\r\n    const getFinalBoardItems = (filteredRows: DataViewRow[], groupOrder: string[]) => {\r\n        const boardItems: BoardItems = {}\r\n\r\n        if (!definition.groupItemsProps) definition.groupItemsProps = {}\r\n\r\n        // Initialize boardItems for each column in columnsOrder with empty arrays\r\n        for (let id of groupOrder) {\r\n            boardItems[id] = [];\r\n        }\r\n\r\n        const groupColId = groupColumn.id;\r\n\r\n        const ungroupedIds: string[] = []\r\n\r\n        const rowIdsMap: {\r\n            [id: string]: DataViewRow\r\n        } = {}\r\n\r\n        // Populate boardItems with row ids based on the groupColId values found in each row\r\n        for (const row of filteredRows) {\r\n            let groupId = '';\r\n            let value = row.record.recordValues[groupColId] as (string[] | undefined)\r\n            if (!Array.isArray(value)) value = []\r\n            let isGrouped = false\r\n            if (value && value.length > 0) {\r\n                for (const id of groupOrder) {\r\n                    if (value.includes(id)) {\r\n                        boardItems[id].push(row.id)\r\n                        isGrouped = true\r\n\r\n                        groupId = id\r\n                    }\r\n                }\r\n            }\r\n            if (!isGrouped) {\r\n                ungroupedIds.push(row.id)\r\n                groupId = UngroupedKey\r\n            }\r\n\r\n            rowIdsMap[row.id] = {\r\n                id: row.id,\r\n                record: row.record,\r\n                processedRecord: row.processedRecord,\r\n                updatedAt: row.updatedAt\r\n            }\r\n\r\n        }\r\n\r\n        // Final boardItems that respects the order and content of groupItemsOrder\r\n        const finalBoardItems: BoardItems = {};\r\n\r\n        for (let id of groupOrder) {\r\n            finalBoardItems[id] = [];\r\n\r\n            // if (!definition.groupItemsProps[id])\r\n            // Retrieve the user-defined order for the current column\r\n            const groupItemProps = definition.groupItemsProps[id] || {\r\n                itemsOrder: []\r\n            };\r\n            if (!groupItemProps.itemsOrder) groupItemProps.itemsOrder = []\r\n\r\n            const currentIdsSet = new Set(boardItems[id]);\r\n\r\n            // Add items in the order from groupItemsOrder, respecting the existence of IDs in boardItems\r\n            for (const itemId of groupItemProps.itemsOrder) {\r\n                if (currentIdsSet.has(itemId)) {\r\n                    finalBoardItems[id].push(itemId);\r\n                }\r\n            }\r\n\r\n            // Add remaining items from boardItems that were not included but maintain the original order\r\n            for (const itemId of boardItems[id]) {\r\n                if (!groupItemProps.itemsOrder.includes(itemId)) {\r\n                    finalBoardItems[id].push(itemId);\r\n                }\r\n            }\r\n            groupItemProps.itemsOrder = finalBoardItems[id]\r\n        }\r\n        finalBoardItems[UngroupedKey] = ungroupedIds\r\n\r\n        if (!definition.groupItemsProps[UngroupedKey]) definition.groupItemsProps[UngroupedKey] = {isHidden: false, itemsOrder: []}\r\n        definition.groupItemsProps[UngroupedKey].itemsOrder = ungroupedIds\r\n\r\n        return {boardItems: finalBoardItems, rowIdsMap};\r\n\r\n    }\r\n\r\n    const filteredRows = getProcessedRecords()\r\n\r\n    const finalBoardItems = () => {\r\n        let rows = filteredRows\r\n        if (search && search.trim()) {\r\n            rows = filteredRows.filter(r => r.processedRecord.valuesText.toLowerCase().includes(search.trim().toLowerCase()))\r\n        }\r\n        return getFinalBoardItems(rows, definition.groupOrder)\r\n    }\r\n\r\n    // const columns =\r\n    const {columns, hiddenColumns} = buildCols()\r\n    const boardItems = finalBoardItems()\r\n\r\n    // const [columns, setColumns] = useState<Column[]>(board.cols);\r\n    const columnsId = columns.map(c => c.id);\r\n\r\n    // const [items, setItems] = useState<Item[]>(board.items);\r\n\r\n    const [activeColumn, setActiveColumn] = useState<Column | null>(null);\r\n\r\n    const [activeItem, setActiveItem] = useState<DataViewRow | null>(null);\r\n\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor, {\r\n            activationConstraint: {\r\n                distance: 10,\r\n            },\r\n        })\r\n    );\r\n\r\n    // function createItem(columnId: Id) {\r\n    //     const newItem: Item = {\r\n    //         id: generateId(),\r\n    //         columnId,\r\n    //         content: `Item ${items.length + 1}`,\r\n    //     };\r\n    //\r\n    //     setItems([...items, newItem]);\r\n    // }\r\n\r\n    // function deleteItem(id: Id) {\r\n    //     const newItems = items.filter((item) => item.id !== id);\r\n    //     setItems(newItems);\r\n    // }\r\n\r\n\r\n    function createNewColumn(name: string, color: Color) {\r\n        const option: SelectOption = {\r\n            id: generateUUID(),\r\n            title: name,\r\n            color\r\n        }\r\n        groupColumn.optionIds.push(option.id)\r\n        groupColumn.optionsMap[option.id] = option\r\n\r\n        updateDatabaseColumn(database.database.id, groupColumn.id, groupColumn.type, {\r\n            optionsMap: groupColumn.optionsMap,\r\n            optionIds: groupColumn.optionIds\r\n        })\r\n    }\r\n\r\n    function deleteColumn(id: Id) {\r\n        // const filteredColumns = columns.filter((col) => col.id !== id);\r\n        // setColumns(filteredColumns);\r\n        //\r\n        // const newItems = items.filter((t) => t.columnId !== id);\r\n        // setItems(newItems);\r\n    }\r\n\r\n    function updateColumn(id: string, update: Partial<BoardGroupItemProps>) {\r\n        if (!definition.groupItemsProps) return\r\n\r\n        let groupProps = definition.groupItemsProps?.[id]\r\n        if (groupProps) {\r\n            groupProps = {...groupProps, ...update}\r\n            definition.groupItemsProps[id] = groupProps\r\n            updateDefinition({groupItemsProps: definition.groupItemsProps}).then()\r\n        }\r\n    }\r\n\r\n    const handleAddRecord = (groupId: string) => {\r\n        // Check if we're in record tab context\r\n        const isRecordTab = context === 'record_tab'\r\n        \r\n        if (isRecordTab) {\r\n            // In record tabs: Use smart modal\r\n            setSelectedGroupId(groupId)\r\n            setShowAddModal(true)\r\n        } else {\r\n            // In main views: Direct record creation (original behavior)\r\n            handleDirectBoardAdd(groupId)\r\n        }\r\n    }\r\n\r\n    const handleDirectBoardAdd = async (groupId: string) => {\r\n        const values: RecordValues = {}\r\n        if (groupId !== UngroupedKey) {\r\n            values[groupColumn.id] = [groupId]\r\n        }\r\n        \r\n        await createRecords(database.database.id, [values])\r\n    }\r\n\r\n    const handleRecordCreated = (recordId: string) => {\r\n        // Automatically open the newly created record using our stacked peek logic\r\n        openRecord(recordId, database.database.id)\r\n        setShowAddModal(false)\r\n    }\r\n\r\n    // Create view filter based on selected group (for pre-population)\r\n    const getGroupFilter = () => {\r\n        if (!selectedGroupId || selectedGroupId === UngroupedKey) {\r\n            return undefined\r\n        }\r\n        \r\n        return {\r\n            conditions: [{\r\n                columnId: groupColumn.id,\r\n                op: CompareOperator.Equals,\r\n                value: [selectedGroupId]\r\n            }],\r\n            match: Match.All\r\n        }\r\n    }\r\n\r\n    // useEffect(() => {\r\n    //     const recordIds: string[] = cache.getCache(DatabaseConstants.NewlyCreatedColumnKey, [])\r\n    //     console.log('Newly created in effect:', {recordIds})\r\n    //     // boardItems.rowIdsMap\r\n    //     // const groupProps = definition.groupItemsProps?.[overColumnId]\r\n    //     // if (groupProps) {\r\n    //     //     groupProps.itemsOrder = groupProps.itemsOrder || []\r\n    //     //     if (overId) {\r\n    //     //         groupProps.itemsOrder = arrayAddElementAdjacent(groupProps.itemsOrder, overItemId, activeItemId, 'before')\r\n    //     //         groupProps.itemsOrder = arrayDeDuplicate(groupProps.itemsOrder)\r\n    //     //     } else {\r\n    //     //         groupProps.itemsOrder.push(activeItemId)\r\n    //     //         groupProps.itemsOrder = arrayDeDuplicate(groupProps.itemsOrder)\r\n    //     //     }\r\n    //     //     updateDefinition({groupItemsProps: definition.groupItemsProps}).then()\r\n    //     // }\r\n    // }, []);\r\n\r\n    function onDragStart(event: DragStartEvent) {\r\n        if (event.active.data.current?.type === \"Column\") {\r\n            setActiveColumn(event.active.data.current.column);\r\n            return;\r\n        }\r\n\r\n        if (event.active.data.current?.type === \"Item\") {\r\n            setActiveItem(event.active.data.current.item);\r\n            return;\r\n        }\r\n    }\r\n\r\n    function onDragEnd(event: DragEndEvent) {\r\n        setActiveColumn(null);\r\n        setActiveItem(null);\r\n\r\n        const {active, over} = event;\r\n        if (!over) return;\r\n\r\n        const activeId = active.id;\r\n        const overId = over.id;\r\n\r\n        if (activeId === overId) return;\r\n\r\n        if (active.data.current?.type === \"Column\") {\r\n            console.log(\"DRAG END for a column\");\r\n\r\n            const activeColumnIndex = columns.findIndex(c => c.id === activeId)\r\n            const overColumnIndex = columns.findIndex(c => c.id === overId)\r\n\r\n            const newGroupOrder = arrayMove(definition.groupOrder, activeColumnIndex, overColumnIndex);\r\n\r\n            updateDefinition({groupOrder: newGroupOrder}).then()\r\n        } else if (active.data.current?.type === \"Item\") {\r\n            const activeColumnId: string = active.data.current?.columnId || ''\r\n            const overColumnId: string = over.data.current?.columnId || over.data.current?.column?.id || ''\r\n\r\n            const activeItemId = active.data.current?.item?.id || ''\r\n            const overItemId = over.data.current?.item?.id || ''\r\n            // const overColumnId = over.data.current?.columnId || ''\r\n\r\n            // update the rowValue, update the record position in the column(remove from old column if necessary)\r\n\r\n            const item: DataViewRow = active.data.current?.item\r\n            if (!item) return\r\n            if (!overColumnId) return\r\n\r\n\r\n            if (activeColumnId !== overColumnId) {\r\n                // let currentVal = item.record.recordValues[groupColumn.id]\r\n                let rawVal = item.record.recordValues[groupColumn.id] as (string[] | undefined)\r\n                const currentVal: string[] = Array.isArray(rawVal) ? rawVal : []\r\n\r\n                let newVal: string[] = [...currentVal]\r\n                newVal = removeAllArrayItem(newVal, activeColumnId)\r\n                if (overColumnId && overColumnId !== UngroupedKey) {\r\n                    newVal.push(overColumnId)\r\n                    newVal = arrayDeDuplicate(newVal)\r\n                }\r\n                const values: RecordValues = {}\r\n\r\n                values[groupColumn.id] = newVal\r\n\r\n                updateRecordValues(database.database.id, [item.id], values)\r\n            }\r\n\r\n            const groupProps = definition.groupItemsProps?.[overColumnId]\r\n            if (groupProps) {\r\n                groupProps.itemsOrder = groupProps.itemsOrder || []\r\n                if (overId) {\r\n                    groupProps.itemsOrder = arrayAddElementAdjacent(groupProps.itemsOrder, overItemId, activeItemId, 'before')\r\n                    groupProps.itemsOrder = arrayDeDuplicate(groupProps.itemsOrder)\r\n                } else {\r\n                    groupProps.itemsOrder.push(activeItemId)\r\n                    groupProps.itemsOrder = arrayDeDuplicate(groupProps.itemsOrder)\r\n                }\r\n                updateDefinition({groupItemsProps: definition.groupItemsProps}).then()\r\n            }\r\n\r\n\r\n            // const valType = typeof currentVal\r\n            // if (valType === 'undefined') {\r\n            //     // item.record.recordValues[groupColumn.id] =\r\n            // } else if (valType === 'string' || valType === 'number' || valType === 'boolean') {\r\n            //     // if (['string'].includes(typeof currentVal)) {\r\n            //\r\n            //\r\n            // }\r\n\r\n            console.log({active, over, activeColumnId, overColumnId, activeItemId, overItemId})\r\n\r\n            // I'm dropping a Item over another Item\r\n            // setItems((items) => {\r\n            //     const activeIndex = items.findIndex((t) => t.id === activeId);\r\n            //     const overIndex = items.findIndex((t) => t.id === overId);\r\n            //\r\n            //     if (items[activeIndex].columnId != items[overIndex].columnId) {\r\n            //         // Fix introduced after video recording\r\n            //         items[activeIndex].columnId = items[overIndex].columnId;\r\n            //         return arrayMove(items, activeIndex, overIndex - 1);\r\n            //     }\r\n            //\r\n            //     return arrayMove(items, activeIndex, overIndex);\r\n            // });\r\n\r\n\r\n        }\r\n\r\n\r\n    }\r\n\r\n    function onDragOver(event: DragOverEvent) {\r\n        /**\r\n         const {active, over} = event;\r\n         if (!over) return;\r\n\r\n         const activeId = active.id;\r\n         const overId = over.id;\r\n\r\n         if (activeId === overId) return;\r\n\r\n         const isActiveItem = active.data.current?.type === \"Item\";\r\n         const isOverItem = over.data.current?.type === \"Item\";\r\n\r\n         const activeColumnId = active.data.current?.columnId || ''\r\n         const overColumnId = over.data.current?.columnId || ''\r\n\r\n         if (!isActiveItem) return;\r\n\r\n         console.log({active, over, isActiveItem, isOverItem, activeColumnId, overColumnId})\r\n\r\n         // I'm dropping a Item over another Item\r\n         if (isActiveItem && isOverItem) {\r\n         // setItems((items) => {\r\n         //     const activeIndex = items.findIndex((t) => t.id === activeId);\r\n         //     const overIndex = items.findIndex((t) => t.id === overId);\r\n         //\r\n         //     if (items[activeIndex].columnId != items[overIndex].columnId) {\r\n         //         // Fix introduced after video recording\r\n         //         items[activeIndex].columnId = items[overIndex].columnId;\r\n         //         return arrayMove(items, activeIndex, overIndex - 1);\r\n         //     }\r\n         //\r\n         //     return arrayMove(items, activeIndex, overIndex);\r\n         // });\r\n         }\r\n         //\r\n         // const isOverAColumn = over.data.current?.type === \"Column\";\r\n         //\r\n         // if (isActiveAItem && isOverAColumn) {\r\n         //     setItems((items) => {\r\n         //         const activeIndex = items.findIndex((t) => t.id === activeId);\r\n         //\r\n         //         items[activeIndex].columnId = overId;\r\n         //         console.log(\"DROPPING ITEM OVER COLUMN\", {activeIndex});\r\n         //         return arrayMove(items, activeIndex, activeIndex);\r\n         //     });\r\n         // }\r\n         */\r\n    }\r\n\r\n    return <>\r\n        <div className=\"size-full flex flex-col overflow-hidden\">\r\n            {definition.lockContent && !shared && <>\r\n                <ContentLocked/>\r\n            </>}\r\n            <div className='flex-1 overflow-hidden select-none'>\r\n                <DndContext\r\n                    autoScroll={{acceleration: 0.5, threshold: {x: 25, y: 10}}}\r\n                    sensors={sensors}\r\n                    onDragStart={onDragStart}\r\n                    onDragEnd={onDragEnd}\r\n                    onDragOver={onDragOver}>\r\n                    <div className=\"w-full h-full overflow-x-auto overflow-y-hidden gap-4 p-4 pt-2 pb-0 flex\">\r\n                        <SortableContext items={columnsId}>\r\n                            {columns.map(col => {\r\n                                    return <BoardColumn\r\n                                        column={col}\r\n                                        key={col.id}\r\n                                        // @ts-ignore\r\n                                        columnProps={definition.groupItemsProps[col.id]}\r\n                                        items={boardItems.boardItems[col.id].map(id => boardItems.rowIdsMap[id])}\r\n                                        // deleteColumn={deleteColumn}\r\n                                        updateColumn={updateColumn}\r\n                                        canEditStructure={canEditStructure}\r\n                                        canEditData={canEditData}\r\n                                        databaseDefinition={database.database.definition}\r\n                                        dataColumnPropsMap={definition.columnPropsMap}\r\n                                        dataColumnsOrder={definition.columnsOrder}\r\n                                        titleColId={titleColId}\r\n                                        dndDisabled={!editable}\r\n                                        addRecord={handleAddRecord}\r\n                                        databaseId={database.database.id}\r\n                                    />\r\n                                }\r\n                            )}\r\n                        </SortableContext>\r\n                        {canEditStructure && <NewColumn createColumn={createNewColumn}/>}\r\n                        {canEditStructure && <HiddenColumns\r\n                            boardItems={boardItems.boardItems}\r\n                            columns={hiddenColumns}\r\n                            groupItemsProps={definition.groupItemsProps}\r\n                            rowIdsMap={boardItems.rowIdsMap}\r\n                            updateColumn={updateColumn}\r\n                            databaseDefinition={database.database.definition}\r\n                            dataColumnPropsMap={definition.columnPropsMap}\r\n                            dataColumnsOrder={definition.columnsOrder}\r\n                            disabled={!editable}\r\n                            titleColId={titleColId}\r\n                        />}\r\n                    </div>\r\n\r\n                    {createPortal(\r\n                        <DragOverlay>\r\n                            {activeColumn && (\r\n                                <BoardColumn\r\n                                    column={activeColumn}\r\n                                    key={activeColumn.id}\r\n                                    // @ts-ignore\r\n                                    columnProps={definition.groupItemsProps[activeColumn.id]}\r\n                                    items={boardItems.boardItems[activeColumn.id].map(id => boardItems.rowIdsMap[id])}\r\n                                    // deleteColumn={deleteColumn}\r\n                                    updateColumn={updateColumn}\r\n                                    databaseDefinition={database.database.definition}\r\n                                    dataColumnPropsMap={definition.columnPropsMap}\r\n                                    dataColumnsOrder={definition.columnsOrder}\r\n                                    titleColId={titleColId}\r\n                                    dndDisabled={!editable}\r\n                                    databaseId={database.database.id}\r\n                                />\r\n                            )}\r\n                            {activeItem && (\r\n                                <ItemCard\r\n                                    dataColumnPropsMap={definition.columnPropsMap}\r\n                                    dataColumnsOrder={definition.columnsOrder}\r\n                                    databaseDefinition={database.database.definition}\r\n                                    titleColId={titleColId}\r\n                                    key={activeItem.id}\r\n                                    columnId=''\r\n                                    databaseId={database.database.id}\r\n                                    item={activeItem}/>\r\n                            )}\r\n                        </DragOverlay>,\r\n                        document.body\r\n                    )}\r\n                </DndContext>\r\n            </div>\r\n            \r\n            {/* AddRecordModal for intelligent board record creation */}\r\n            {showAddModal && (\r\n                <AddRecordModal\r\n                    open={showAddModal}\r\n                    onClose={() => setShowAddModal(false)}\r\n                    databaseId={database.database.id}\r\n                    viewFilter={getGroupFilter()}\r\n                    contextualFilter={filter}\r\n                    onRecordCreated={handleRecordCreated}\r\n                />\r\n            )}\r\n        </div>\r\n    </>\r\n}", "import {BoardContainer} from \"@/components/workspace/main/views/board/components/boardContainer\";\r\nimport {BoardViewDefinition} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {ViewRenderProps} from \"@/components/workspace/main/views/table\";\r\nimport {DatabaseColumn, DatabaseFieldDataType} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {useWorkspace} from \"@/providers/workspace\";\r\nimport {PageLoader} from \"@/components/custom-ui/loader\";\r\n\r\n\r\nexport interface BoardViewRenderProps extends ViewRenderProps {\r\n    definition: BoardViewDefinition\r\n}\r\n\r\nexport const BoardView = (props: BoardViewRenderProps) => {\r\n    const {databaseStore, databaseErrorStore, members, workspace} = useWorkspace()\r\n\r\n    const {definition} = props\r\n    const database = databaseStore[definition.databaseId]\r\n    const groupColumn: DatabaseColumn | undefined = database.database.definition.columnsMap[definition.groupByIds[0]]\r\n\r\n    return (<>\r\n        {(!database || !groupColumn || groupColumn.type !== DatabaseFieldDataType.Select) && <PageLoader size='full' error='Error loading board'/>}\r\n        {database && groupColumn && <BoardContainer database={database} {...props}/>}\r\n\r\n    </>)\r\n}", "import * as React from \"react\";\nfunction EllipsisVerticalIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EllipsisVerticalIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction MagnifyingGlassCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassCircleIcon);\nexport default ForwardRef;"], "names": ["tester", "exports", "G", "email", "length", "test", "parts", "split", "domainParts", "some", "part", "ItemCard", "selectedIds", "setSelectedIds", "useViews", "mouseIsOver", "setMouseIsOver", "useState", "deleteRecords", "url", "useWorkspace", "openRecord", "useStackedPeek", "item", "databaseDefinition", "dataColumnsOrder", "dataColumnPropsMap", "titleColId", "props", "setNodeRef", "attributes", "listeners", "transform", "transition", "isDragging", "useSortable", "id", "concat", "columnId", "data", "type", "style", "CSS", "Transform", "toString", "title", "recordValueToText", "processedRecord", "processedRecordValues", "jsx_runtime", "jsx", "Fragment", "jsxs", "div", "className", "ref", "onClick", "databaseId", "disabled", "Checkbox", "e", "stopPropagation", "checked", "includes", "onCheckedChange", "c", "removeAllArrayItem", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "EllipsisHorizontalIcon", "DropdownMenuContent", "align", "DropdownMenuItem", "then", "map", "colId", "isHidden", "RenderField", "definition", "content", "databaseStore", "databaseErrorStore", "members", "column", "columnsMap", "rowValue", "trim", "Array", "isArray", "DatabaseFieldDataType", "UUID", "Text", "Number", "AI", "Summarize", "Derived", "textValue", "String", "f", "getTextBasedColFormattedValue", "displayValue", "Select", "RenderPureSelect", "row", "Linked", "RenderPureLinked", "refDatabaseId", "record", "Date", "CreatedAt", "UpdatedAt", "RenderPureDate", "CreatedBy", "UpdatedBy", "Person", "RenderPure<PERSON>erson", "Files", "files", "rowVal", "DatabaseFieldTypeIcon", "BoardColumn", "items", "updateColumn", "dndDisabled", "BoardColumnHeader", "addRecord", "columnProps", "canEditStructure", "ScrollArea", "aria-orientation", "SortableContext", "i", "canEditData", "rest", "param", "info", "color", "ColorInfo", "bg", "header", "CirclePlusIcon", "HiddenColumns", "boardItems", "rowIdsMap", "columns", "groupItemsProps", "console", "log", "col", "NewColumn", "open", "<PERSON><PERSON><PERSON>", "inputRef", "useRef", "colour", "ColorEntries", "Math", "floor", "random", "useEffect", "inputEle", "current", "focus", "onBlur", "addEventListener", "removeEventListener", "PlusCircleIcon", "InputWithEnter", "shortEnter", "onChange", "value", "createColumn", "wrapperClassname", "UngroupedKey", "BoardContainer", "database", "groupByIds", "Board<PERSON>ontent", "rows", "workspace", "updateViewDefinition", "updateRecordValues", "updateDatabaseColumn", "createRecords", "cache", "filter", "viewFilter", "search", "viewSearch", "useViewFiltering", "useViewSelection", "context", "useViewContext", "showAddModal", "setShowAddModal", "selectedGroupId", "setSelectedGroupId", "recordIds", "getCache", "DatabaseConstants", "NewlyCreatedColumnKey", "groupColumn", "shared", "useMaybeShared", "editable", "lock<PERSON><PERSON><PERSON>", "maybeTemplate", "useMaybeTemplate", "conditions", "match", "Match", "All", "sorts", "groupOrder", "columnsOrder", "columnPropsMap", "titleColumnId", "fixColumnPropsMap", "Object", "values", "columnIds", "visibleColumns", "visibleOnLoad", "push", "updateDefinition", "update", "view", "pageId", "filteredRows", "getProcessedRecords", "sortOptions", "MagicColumn", "order", "Sort", "Desc", "filterAndSortRecords", "workspaceMember", "userId", "hiddenColumns", "buildCols", "key", "optionIds", "itemsOrder", "ungroupExists", "option", "optionsMap", "unshift", "r", "valuesText", "toLowerCase", "getFinalBoardItems", "groupColId", "ungroupedIds", "recordValues", "isGrouped", "updatedAt", "finalBoardItems", "groupItemProps", "currentIdsSet", "Set", "itemId", "has", "columnsId", "activeColumn", "setActiveColumn", "activeItem", "setActiveItem", "sensors", "useSensors", "useSensor", "PointerSensor", "activationConstraint", "distance", "groupProps", "handleAddRecord", "groupId", "handleDirectBoardAdd", "ContentLocked", "DndContext", "autoScroll", "acceleration", "threshold", "x", "y", "onDragStart", "event", "active", "onDragEnd", "over", "activeId", "overId", "activeColumnIndex", "findIndex", "overColumnIndex", "arrayMove", "activeColumnId", "overColumnId", "activeItemId", "overItemId", "rawVal", "newVal", "arrayDeDuplicate", "arrayAddElementAdjacent", "onDragOver", "name", "generateUUID", "createPortal", "DragOverlay", "document", "body", "AddRecordModal", "onClose", "getGroupFilter", "op", "CompareOperator", "Equals", "contextualFilter", "onRecordCreated", "recordId", "BoardView", "<PERSON><PERSON><PERSON><PERSON>", "size", "error", "ForwardRef", "React", "svgRef", "titleId", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "d", "__webpack_exports__", "Z"], "sourceRoot": ""}