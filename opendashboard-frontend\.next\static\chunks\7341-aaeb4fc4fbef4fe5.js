!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f77392a9-9ca2-4e23-96c6-50cac6e76186",e._sentryDebugIdIdentifier="sentry-dbid-f77392a9-9ca2-4e23-96c6-50cac6e76186")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7341],{64784:function(e,t,n){e.exports={graphlib:n(44774),layout:n(33790),debug:n(56070),util:{time:n(28212).time,notime:n(28212).notime},version:n(61870)}},29882:function(e,t,n){"use strict";let r=n(98785),o=n(28212).uniqueId;e.exports={run:function(e){let t,n,i;("greedy"===e.graph().acyclicer?r(e,t=>e.edge(t).weight):(t=[],n={},i={},e.nodes().forEach(function r(o){Object.hasOwn(i,o)||(i[o]=!0,n[o]=!0,e.outEdges(o).forEach(e=>{Object.hasOwn(n,e.w)?t.push(e):r(e.w)}),delete n[o])}),t)).forEach(t=>{let n=e.edge(t);e.removeEdge(t),n.forwardName=t.name,n.reversed=!0,e.setEdge(t.w,t.v,n,o("rev"))})},undo:function(e){e.edges().forEach(t=>{let n=e.edge(t);if(n.reversed){e.removeEdge(t);let r=n.forwardName;delete n.reversed,delete n.forwardName,e.setEdge(t.w,t.v,n,r)}})}}},55008:function(e,t,n){let r=n(28212);function o(e,t,n,o,i,a){let u=i[t][a-1],s=r.addDummyNode(e,"border",{width:0,height:0,rank:a,borderType:t},n);i[t][a]=s,e.setParent(s,o),u&&e.setEdge(u,s,{weight:1})}e.exports=function(e){e.children().forEach(function t(n){let r=e.children(n),i=e.node(n);if(r.length&&r.forEach(t),Object.hasOwn(i,"minRank")){i.borderLeft=[],i.borderRight=[];for(let t=i.minRank,r=i.maxRank+1;t<r;++t)o(e,"borderLeft","_bl",n,i,t),o(e,"borderRight","_br",n,i,t)}})}},78806:function(e){"use strict";function t(e){e.nodes().forEach(t=>n(e.node(t))),e.edges().forEach(t=>n(e.edge(t)))}function n(e){let t=e.width;e.width=e.height,e.height=t}function r(e){e.y=-e.y}function o(e){let t=e.x;e.x=e.y,e.y=t}e.exports={adjust:function(e){let n=e.graph().rankdir.toLowerCase();("lr"===n||"rl"===n)&&t(e)},undo:function(e){let n=e.graph().rankdir.toLowerCase();("bt"===n||"rl"===n)&&(e.nodes().forEach(t=>r(e.node(t))),e.edges().forEach(t=>{let n=e.edge(t);n.points.forEach(r),Object.hasOwn(n,"y")&&r(n)})),("lr"===n||"rl"===n)&&(e.nodes().forEach(t=>o(e.node(t))),e.edges().forEach(t=>{let n=e.edge(t);n.points.forEach(o),Object.hasOwn(n,"x")&&o(n)}),t(e))}}},64986:function(e){class t{constructor(){let e={};e._next=e._prev=e,this._sentinel=e}dequeue(){let e=this._sentinel,t=e._prev;if(t!==e)return n(t),t}enqueue(e){let t=this._sentinel;e._prev&&e._next&&n(e),e._next=t._next,t._next._prev=e,t._next=e,e._prev=t}toString(){let e=[],t=this._sentinel,n=t._prev;for(;n!==t;)e.push(JSON.stringify(n,r)),n=n._prev;return"["+e.join(", ")+"]"}}function n(e){e._prev._next=e._next,e._next._prev=e._prev,delete e._next,delete e._prev}function r(e,t){if("_next"!==e&&"_prev"!==e)return t}e.exports=t},56070:function(e,t,n){let r=n(28212),o=n(44774).Graph;e.exports={debugOrdering:function(e){let t=r.buildLayerMatrix(e),n=new o({compound:!0,multigraph:!0}).setGraph({});return e.nodes().forEach(t=>{n.setNode(t,{label:t}),n.setParent(t,"layer"+e.node(t).rank)}),e.edges().forEach(e=>n.setEdge(e.v,e.w,{},e.name)),t.forEach((e,t)=>{n.setNode("layer"+t,{rank:"same"}),e.reduce((e,t)=>(n.setEdge(e,t,{style:"invis"}),t))}),n}}},98785:function(e,t,n){let r=n(44774).Graph,o=n(64986);e.exports=function(e,t){var n;let s,l,c,d,h;if(1>=e.nodeCount())return[];let f=(n=t||i,s=new r,l=0,c=0,e.nodes().forEach(e=>{s.setNode(e,{v:e,in:0,out:0})}),e.edges().forEach(e=>{let t=s.edge(e.v,e.w)||0,r=n(e);s.setEdge(e.v,e.w,t+r),c=Math.max(c,s.node(e.v).out+=r),l=Math.max(l,s.node(e.w).in+=r)}),d=(function(e){let t=[];for(let n=0;n<e;n++)t.push(n);return t})(c+l+3).map(()=>new o),h=l+1,s.nodes().forEach(e=>{u(d,h,s.node(e))}),{graph:s,buckets:d,zeroIdx:h});return(function(e,t,n){let r,o=[],i=t[t.length-1],u=t[0];for(;e.nodeCount();){for(;r=u.dequeue();)a(e,t,n,r);for(;r=i.dequeue();)a(e,t,n,r);if(e.nodeCount()){for(let i=t.length-2;i>0;--i)if(r=t[i].dequeue()){o=o.concat(a(e,t,n,r,!0));break}}}return o})(f.graph,f.buckets,f.zeroIdx).flatMap(t=>e.outEdges(t.v,t.w))};let i=()=>1;function a(e,t,n,r,o){let i=o?[]:void 0;return e.inEdges(r.v).forEach(r=>{let a=e.edge(r),s=e.node(r.v);o&&i.push({v:r.v,w:r.w}),s.out-=a,u(t,n,s)}),e.outEdges(r.v).forEach(r=>{let o=e.edge(r),i=r.w,a=e.node(i);a.in-=o,u(t,n,a)}),e.removeNode(r.v),i}function u(e,t,n){n.out?n.in?e[n.out-n.in+t].enqueue(n):e[e.length-1].enqueue(n):e[0].enqueue(n)}},33790:function(e,t,n){"use strict";let r=n(29882),o=n(34097),i=n(76401),a=n(28212).normalizeRanks,u=n(61312),s=n(28212).removeEmptyRanks,l=n(95079),c=n(55008),d=n(78806),h=n(32204),f=n(85503),p=n(28212),g=n(44774).Graph;e.exports=function(e,t){let n=t&&t.debugTiming?p.time:p.notime;n("layout",()=>{let M=n("  buildLayoutGraph",()=>{let t,n;return t=new g({multigraph:!0,compound:!0}),n=O(e.graph()),t.setGraph(Object.assign({},v,k(n,y),p.pick(n,m))),e.nodes().forEach(n=>{let r=k(O(e.node(n)),w);Object.keys(b).forEach(e=>{void 0===r[e]&&(r[e]=b[e])}),t.setNode(n,r),t.setParent(n,e.parent(n))}),e.edges().forEach(n=>{let r=O(e.edge(n));t.setEdge(n,Object.assign({},x,k(r,_),p.pick(r,E)))}),t});n("  runLayout",()=>{n("    makeSpaceForEdgeLabels",()=>{let e;return e=M.graph(),void(e.ranksep/=2,M.edges().forEach(t=>{let n=M.edge(t);n.minlen*=2,"c"!==n.labelpos.toLowerCase()&&("TB"===e.rankdir||"BT"===e.rankdir?n.width+=n.labeloffset:n.height+=n.labeloffset)}))}),n("    removeSelfEdges",()=>(function(e){e.edges().forEach(t=>{if(t.v===t.w){var n=e.node(t.v);n.selfEdges||(n.selfEdges=[]),n.selfEdges.push({e:t,label:e.edge(t)}),e.removeEdge(t)}})})(M)),n("    acyclic",()=>r.run(M)),n("    nestingGraph.run",()=>l.run(M)),n("    rank",()=>i(p.asNonCompoundGraph(M))),n("    injectEdgeLabelProxies",()=>(function(e){e.edges().forEach(t=>{let n=e.edge(t);if(n.width&&n.height){let n=e.node(t.v),r={rank:(e.node(t.w).rank-n.rank)/2+n.rank,e:t};p.addDummyNode(e,"edge-proxy",r,"_ep")}})})(M)),n("    removeEmptyRanks",()=>s(M)),n("    nestingGraph.cleanup",()=>l.cleanup(M)),n("    normalizeRanks",()=>a(M)),n("    assignRankMinMax",()=>{let e;return e=0,void(M.nodes().forEach(t=>{let n=M.node(t);n.borderTop&&(n.minRank=M.node(n.borderTop).rank,n.maxRank=M.node(n.borderBottom).rank,e=Math.max(e,n.maxRank))}),M.graph().maxRank=e)}),n("    removeEdgeLabelProxies",()=>(function(e){e.nodes().forEach(t=>{let n=e.node(t);"edge-proxy"===n.dummy&&(e.edge(n.e).labelRank=n.rank,e.removeNode(t))})})(M)),n("    normalize.run",()=>o.run(M)),n("    parentDummyChains",()=>u(M)),n("    addBorderSegments",()=>c(M)),n("    order",()=>h(M,t)),n("    insertSelfEdges",()=>(function(e){p.buildLayerMatrix(e).forEach(t=>{var n=0;t.forEach((t,r)=>{var o=e.node(t);o.order=r+n,(o.selfEdges||[]).forEach(t=>{p.addDummyNode(e,"selfedge",{width:t.label.width,height:t.label.height,rank:o.rank,order:r+ ++n,e:t.e,label:t.label},"_se")}),delete o.selfEdges})})})(M)),n("    adjustCoordinateSystem",()=>d.adjust(M)),n("    position",()=>f(M)),n("    positionSelfEdges",()=>(function(e){e.nodes().forEach(t=>{var n=e.node(t);if("selfedge"===n.dummy){var r=e.node(n.e.v),o=r.x+r.width/2,i=r.y,a=n.x-o,u=r.height/2;e.setEdge(n.e,n.label),e.removeNode(t),n.label.points=[{x:o+2*a/3,y:i-u},{x:o+5*a/6,y:i-u},{x:o+a,y:i},{x:o+5*a/6,y:i+u},{x:o+2*a/3,y:i+u}],n.label.x=n.x,n.label.y=n.y}})})(M)),n("    removeBorderNodes",()=>{M.nodes().forEach(e=>{if(M.children(e).length){let t=M.node(e),n=M.node(t.borderTop),r=M.node(t.borderBottom),o=M.node(t.borderLeft[t.borderLeft.length-1]),i=M.node(t.borderRight[t.borderRight.length-1]);t.width=Math.abs(i.x-o.x),t.height=Math.abs(r.y-n.y),t.x=o.x+t.width/2,t.y=n.y+t.height/2}}),M.nodes().forEach(e=>{"border"===M.node(e).dummy&&M.removeNode(e)})}),n("    normalize.undo",()=>o.undo(M)),n("    fixupEdgeLabelCoords",()=>(function(e){e.edges().forEach(t=>{let n=e.edge(t);if(Object.hasOwn(n,"x"))switch(("l"===n.labelpos||"r"===n.labelpos)&&(n.width-=n.labeloffset),n.labelpos){case"l":n.x-=n.width/2+n.labeloffset;break;case"r":n.x+=n.width/2+n.labeloffset}})})(M)),n("    undoCoordinateSystem",()=>d.undo(M)),n("    translateGraph",()=>(function(e){let t=Number.POSITIVE_INFINITY,n=0,r=Number.POSITIVE_INFINITY,o=0,i=e.graph(),a=i.marginx||0,u=i.marginy||0;function s(e){let i=e.x,a=e.y,u=e.width,s=e.height;t=Math.min(t,i-u/2),n=Math.max(n,i+u/2),r=Math.min(r,a-s/2),o=Math.max(o,a+s/2)}e.nodes().forEach(t=>s(e.node(t))),e.edges().forEach(t=>{let n=e.edge(t);Object.hasOwn(n,"x")&&s(n)}),t-=a,r-=u,e.nodes().forEach(n=>{let o=e.node(n);o.x-=t,o.y-=r}),e.edges().forEach(n=>{let o=e.edge(n);o.points.forEach(e=>{e.x-=t,e.y-=r}),Object.hasOwn(o,"x")&&(o.x-=t),Object.hasOwn(o,"y")&&(o.y-=r)}),i.width=n-t+a,i.height=o-r+u})(M)),n("    assignNodeIntersects",()=>(function(e){e.edges().forEach(t=>{let n,r,o=e.edge(t),i=e.node(t.v),a=e.node(t.w);o.points?(n=o.points[0],r=o.points[o.points.length-1]):(o.points=[],n=a,r=i),o.points.unshift(p.intersectRect(i,n)),o.points.push(p.intersectRect(a,r))})})(M)),n("    reversePoints",()=>(function(e){e.edges().forEach(t=>{let n=e.edge(t);n.reversed&&n.points.reverse()})})(M)),n("    acyclic.undo",()=>r.undo(M))}),n("  updateInputGraph",()=>{e.nodes().forEach(t=>{let n=e.node(t),r=M.node(t);n&&(n.x=r.x,n.y=r.y,n.rank=r.rank,M.children(t).length&&(n.width=r.width,n.height=r.height))}),e.edges().forEach(t=>{let n=e.edge(t),r=M.edge(t);n.points=r.points,Object.hasOwn(r,"x")&&(n.x=r.x,n.y=r.y)}),e.graph().width=M.graph().width,e.graph().height=M.graph().height})})};let y=["nodesep","edgesep","ranksep","marginx","marginy"],v={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},m=["acyclicer","ranker","rankdir","align"],w=["width","height"],b={width:0,height:0},_=["minlen","weight","width","height","labeloffset"],x={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},E=["labelpos"];function k(e,t){return p.mapValues(p.pick(e,t),Number)}function O(e){var t={};return e&&Object.entries(e).forEach(([e,n])=>{"string"==typeof e&&(e=e.toLowerCase()),t[e]=n}),t}},95079:function(e,t,n){let r=n(28212);e.exports={run:function(e){var t;let n=r.addDummyNode(e,"root",{},"_root"),o=(t={},e.children().forEach(n=>(function n(r,o){var i=e.children(r);i&&i.length&&i.forEach(e=>n(e,o+1)),t[r]=o})(n,1)),t),i=Object.values(o),a=r.applyWithChunking(Math.max,i)-1,u=2*a+1;e.graph().nestingRoot=n,e.edges().forEach(t=>e.edge(t).minlen*=u);let s=e.edges().reduce((t,n)=>t+e.edge(n).weight,0)+1;e.children().forEach(t=>(function e(t,n,o,i,a,u,s){let l=t.children(s);if(!l.length){s!==n&&t.setEdge(n,s,{weight:0,minlen:o});return}let c=r.addBorderNode(t,"_bt"),d=r.addBorderNode(t,"_bb"),h=t.node(s);t.setParent(c,s),h.borderTop=c,t.setParent(d,s),h.borderBottom=d,l.forEach(r=>{e(t,n,o,i,a,u,r);let l=t.node(r),h=l.borderTop?l.borderTop:r,f=l.borderBottom?l.borderBottom:r,p=l.borderTop?i:2*i,g=h!==f?1:a-u[s]+1;t.setEdge(c,h,{weight:p,minlen:g,nestingEdge:!0}),t.setEdge(f,d,{weight:p,minlen:g,nestingEdge:!0})}),t.parent(s)||t.setEdge(n,c,{weight:0,minlen:a+u[s]})})(e,n,u,s,a,o,t)),e.graph().nodeRankFactor=u},cleanup:function(e){var t=e.graph();e.removeNode(t.nestingRoot),delete t.nestingRoot,e.edges().forEach(t=>{e.edge(t).nestingEdge&&e.removeEdge(t)})}}},34097:function(e,t,n){"use strict";let r=n(28212);e.exports={run:function(e){e.graph().dummyChains=[],e.edges().forEach(t=>(function(e,t){let n,o,i,a=t.v,u=e.node(a).rank,s=t.w,l=e.node(s).rank,c=t.name,d=e.edge(t),h=d.labelRank;if(l!==u+1){for(e.removeEdge(t),i=0,++u;u<l;++i,++u)d.points=[],o={width:0,height:0,edgeLabel:d,edgeObj:t,rank:u},n=r.addDummyNode(e,"edge",o,"_d"),u===h&&(o.width=d.width,o.height=d.height,o.dummy="edge-label",o.labelpos=d.labelpos),e.setEdge(a,n,{weight:d.weight},c),0===i&&e.graph().dummyChains.push(n),a=n;e.setEdge(a,s,{weight:d.weight},c)}})(e,t))},undo:function(e){e.graph().dummyChains.forEach(t=>{let n,r=e.node(t),o=r.edgeLabel;for(e.setEdge(r.edgeObj,o);r.dummy;)n=e.successors(t)[0],e.removeNode(t),o.points.push({x:r.x,y:r.y}),"edge-label"===r.dummy&&(o.x=r.x,o.y=r.y,o.width=r.width,o.height=r.height),t=n,r=e.node(t)})}}},97550:function(e){e.exports=function(e,t,n){let r={},o;n.forEach(n=>{let i=e.parent(n),a,u;for(;i;){if((a=e.parent(i))?(u=r[a],r[a]=i):(u=o,o=i),u&&u!==i){t.setEdge(u,i);return}i=a}})}},1337:function(e){e.exports=function(e,t=[]){return t.map(t=>{let n=e.inEdges(t);if(!n.length)return{v:t};{let r=n.reduce((t,n)=>{let r=e.edge(n),o=e.node(n.v);return{sum:t.sum+r.weight*o.order,weight:t.weight+r.weight}},{sum:0,weight:0});return{v:t,barycenter:r.sum/r.weight,weight:r.weight}}})}},47956:function(e,t,n){let r=n(44774).Graph,o=n(28212);e.exports=function(e,t,n){let i=function(e){for(var t;e.hasNode(t=o.uniqueId("_root")););return t}(e),a=new r({compound:!0}).setGraph({root:i}).setDefaultNodeLabel(t=>e.node(t));return e.nodes().forEach(r=>{let o=e.node(r),u=e.parent(r);(o.rank===t||o.minRank<=t&&t<=o.maxRank)&&(a.setNode(r),a.setParent(r,u||i),e[n](r).forEach(t=>{let n=t.v===r?t.w:t.v,o=a.edge(n,r),i=void 0!==o?o.weight:0;a.setEdge(n,r,{weight:e.edge(t).weight+i})}),Object.hasOwn(o,"minRank")&&a.setNode(r,{borderLeft:o.borderLeft[t],borderRight:o.borderRight[t]}))}),a}},67828:function(e,t,n){"use strict";let r=n(28212).zipObject;e.exports=function(e,t){let n=0;for(let o=1;o<t.length;++o)n+=function(e,t,n){let o=r(n,n.map((e,t)=>t)),i=t.flatMap(t=>e.outEdges(t).map(t=>({pos:o[t.w],weight:e.edge(t).weight})).sort((e,t)=>e.pos-t.pos)),a=1;for(;a<n.length;)a<<=1;let u=2*a-1;a-=1;let s=Array(u).fill(0),l=0;return i.forEach(e=>{let t=e.pos+a;s[t]+=e.weight;let n=0;for(;t>0;)t%2&&(n+=s[t+1]),t=t-1>>1,s[t]+=e.weight;l+=e.weight*n}),l}(e,t[o-1],t[o]);return n}},32204:function(e,t,n){"use strict";let r=n(97387),o=n(67828),i=n(76509),a=n(47956),u=n(97550),s=n(44774).Graph,l=n(28212);function c(e,t,n){return t.map(function(t){return a(e,t,n)})}function d(e,t){Object.values(t).forEach(t=>t.forEach((t,n)=>e.node(t).order=n))}e.exports=function e(t,n){if(n&&"function"==typeof n.customOrder){n.customOrder(t,e);return}let a=l.maxRank(t),h=c(t,l.range(1,a+1),"inEdges"),f=c(t,l.range(a-1,-1,-1),"outEdges"),p=r(t);if(d(t,p),n&&n.disableOptimalOrderHeuristic)return;let g=Number.POSITIVE_INFINITY,y;for(let e=0,n=0;n<4;++e,++n){(function(e,t){let n=new s;e.forEach(function(e){let r=e.graph().root,o=i(e,r,n,t);o.vs.forEach((t,n)=>e.node(t).order=n),u(e,n,o.vs)})})(e%2?h:f,e%4>=2),p=l.buildLayerMatrix(t);let r=o(t,p);r<g&&(n=0,y=Object.assign({},p),g=r)}d(t,y)}},97387:function(e,t,n){"use strict";let r=n(28212);e.exports=function(e){let t={},n=e.nodes().filter(t=>!e.children(t).length),o=n.map(t=>e.node(t).rank),i=r.applyWithChunking(Math.max,o),a=r.range(i+1).map(()=>[]);return n.sort((t,n)=>e.node(t).rank-e.node(n).rank).forEach(function n(r){t[r]||(t[r]=!0,a[e.node(r).rank].push(r),e.successors(r).forEach(n))}),a}},36342:function(e,t,n){"use strict";let r=n(28212);e.exports=function(e,t){let n={};return e.forEach((e,t)=>{let r=n[e.v]={indegree:0,in:[],out:[],vs:[e.v],i:t};void 0!==e.barycenter&&(r.barycenter=e.barycenter,r.weight=e.weight)}),t.edges().forEach(e=>{let t=n[e.v],r=n[e.w];void 0!==t&&void 0!==r&&(r.indegree++,t.out.push(n[e.w]))}),function(e){let t=[];for(;e.length;){let n=e.pop();t.push(n),n.in.reverse().forEach(function(e){return t=>{!t.merged&&(void 0===t.barycenter||void 0===e.barycenter||t.barycenter>=e.barycenter)&&function(e,t){let n=0,r=0;e.weight&&(n+=e.barycenter*e.weight,r+=e.weight),t.weight&&(n+=t.barycenter*t.weight,r+=t.weight),e.vs=t.vs.concat(e.vs),e.barycenter=n/r,e.weight=r,e.i=Math.min(t.i,e.i),t.merged=!0}(e,t)}}(n)),n.out.forEach(function(t){return n=>{n.in.push(t),0==--n.indegree&&e.push(n)}}(n))}return t.filter(e=>!e.merged).map(e=>r.pick(e,["vs","i","barycenter","weight"]))}(Object.values(n).filter(e=>!e.indegree))}},76509:function(e,t,n){let r=n(1337),o=n(36342),i=n(91251);e.exports=function e(t,n,a,u){let s=t.children(n),l=t.node(n),c=l?l.borderLeft:void 0,d=l?l.borderRight:void 0,h={};c&&(s=s.filter(e=>e!==c&&e!==d));let f=r(t,s);f.forEach(n=>{if(t.children(n.v).length){let r=e(t,n.v,a,u);h[n.v]=r,Object.hasOwn(r,"barycenter")&&(void 0!==n.barycenter?(n.barycenter=(n.barycenter*n.weight+r.barycenter*r.weight)/(n.weight+r.weight),n.weight+=r.weight):(n.barycenter=r.barycenter,n.weight=r.weight))}});let p=o(f,a);(function(e,t){e.forEach(e=>{e.vs=e.vs.flatMap(e=>t[e]?t[e].vs:e)})})(p,h);let g=i(p,u);if(c&&(g.vs=[c,g.vs,d].flat(!0),t.predecessors(c).length)){let e=t.node(t.predecessors(c)[0]),n=t.node(t.predecessors(d)[0]);Object.hasOwn(g,"barycenter")||(g.barycenter=0,g.weight=0),g.barycenter=(g.barycenter*g.weight+e.order+n.order)/(g.weight+2),g.weight+=2}return g}},91251:function(e,t,n){let r=n(28212);function o(e,t,n){let r;for(;t.length&&(r=t[t.length-1]).i<=n;)t.pop(),e.push(r.vs),n++;return n}e.exports=function(e,t){var n;let i=r.partition(e,e=>Object.hasOwn(e,"barycenter")),a=i.lhs,u=i.rhs.sort((e,t)=>t.i-e.i),s=[],l=0,c=0,d=0;a.sort((n=!!t,(e,t)=>e.barycenter<t.barycenter?-1:e.barycenter>t.barycenter?1:n?t.i-e.i:e.i-t.i)),d=o(s,u,d),a.forEach(e=>{d+=e.vs.length,s.push(e.vs),l+=e.barycenter*e.weight,c+=e.weight,d=o(s,u,d)});let h={vs:s.flat(!0)};return c&&(h.barycenter=l/c,h.weight=c),h}},61312:function(e){e.exports=function(e){let t,n;let r=(t={},n=0,e.children().forEach(function r(o){let i=n;e.children(o).forEach(r),t[o]={low:i,lim:n++}}),t);e.graph().dummyChains.forEach(t=>{let n=e.node(t),o=n.edgeObj,i=function(e,t,n,r){let o,i,a=[],u=[],s=Math.min(t[n].low,t[r].low),l=Math.max(t[n].lim,t[r].lim);o=n;do a.push(o=e.parent(o));while(o&&(t[o].low>s||l>t[o].lim));for(i=o,o=r;(o=e.parent(o))!==i;)u.push(o);return{path:a.concat(u.reverse()),lca:i}}(e,r,o.v,o.w),a=i.path,u=i.lca,s=0,l=a[0],c=!0;for(;t!==o.w;){if(n=e.node(t),c){for(;(l=a[s])!==u&&e.node(l).maxRank<n.rank;)s++;l===u&&(c=!1)}if(!c){for(;s<a.length-1&&e.node(l=a[s+1]).minRank<=n.rank;)s++;l=a[s]}e.setParent(t,l),t=e.successors(t)[0]}})}},78621:function(e,t,n){"use strict";let r=n(44774).Graph,o=n(28212);function i(e,t){let n={};return t.length&&t.reduce(function(t,r){let o=0,i=0,a=t.length,s=r[r.length-1];return r.forEach((t,l)=>{let c=function(e,t){if(e.node(t).dummy)return e.predecessors(t).find(t=>e.node(t).dummy)}(e,t),d=c?e.node(c).order:a;(c||t===s)&&(r.slice(i,l+1).forEach(t=>{e.predecessors(t).forEach(r=>{let i=e.node(r),a=i.order;(a<o||d<a)&&!(i.dummy&&e.node(t).dummy)&&u(n,r,t)})}),i=l+1,o=d)}),r}),n}function a(e,t){let n={};function r(t,r,i,a,s){let l;o.range(r,i).forEach(r=>{l=t[r],e.node(l).dummy&&e.predecessors(l).forEach(t=>{let r=e.node(t);r.dummy&&(r.order<a||r.order>s)&&u(n,t,l)})})}return t.length&&t.reduce(function(t,n){let o=-1,i,a=0;return n.forEach((u,s)=>{if("border"===e.node(u).dummy){let t=e.predecessors(u);t.length&&(i=e.node(t[0]).order,r(n,a,s,o,i),a=s,o=i)}r(n,a,n.length,i,t.length)}),n}),n}function u(e,t,n){if(t>n){let e=t;t=n,n=e}let r=e[t];r||(e[t]=r={}),r[n]=!0}function s(e,t,n){if(t>n){let e=t;t=n,n=e}return!!e[t]&&Object.hasOwn(e[t],n)}function l(e,t,n,r){let o={},i={},a={};return t.forEach(e=>{e.forEach((e,t)=>{o[e]=e,i[e]=e,a[e]=t})}),t.forEach(e=>{let t=-1;e.forEach(e=>{let u=r(e);if(u.length){let r=((u=u.sort((e,t)=>a[e]-a[t])).length-1)/2;for(let l=Math.floor(r),c=Math.ceil(r);l<=c;++l){let r=u[l];i[e]===e&&t<a[r]&&!s(n,e,r)&&(i[r]=e,i[e]=o[e]=o[r],t=a[r])}}})}),{root:o,align:i}}function c(e,t,n,o,i){var a,u;let s,l,c;let d={},h=(s=new r,a=(l=e.graph()).nodesep,u=l.edgesep,c=(e,t,n)=>{let r,o,s=e.node(t),l=e.node(n);if(r=0+s.width/2,Object.hasOwn(s,"labelpos"))switch(s.labelpos.toLowerCase()){case"l":o=-s.width/2;break;case"r":o=s.width/2}if(o&&(r+=i?o:-o),o=0,r+=(s.dummy?u:a)/2+(l.dummy?u:a)/2+l.width/2,Object.hasOwn(l,"labelpos"))switch(l.labelpos.toLowerCase()){case"l":o=l.width/2;break;case"r":o=-l.width/2}return o&&(r+=i?o:-o),o=0,r},t.forEach(t=>{let r;t.forEach(t=>{let o=n[t];if(s.setNode(o),r){var i=n[r],a=s.edge(i,o);s.setEdge(i,o,Math.max(c(e,t,r),a||0))}r=t})}),s),f=i?"borderLeft":"borderRight";function p(e,t){let n=h.nodes(),r=n.pop(),o={};for(;r;)o[r]?e(r):(o[r]=!0,n.push(r),n=n.concat(t(r))),r=n.pop()}return p(function(e){d[e]=h.inEdges(e).reduce((e,t)=>Math.max(e,d[t.v]+h.edge(t)),0)},h.predecessors.bind(h)),p(function(t){let n=h.outEdges(t).reduce((e,t)=>Math.min(e,d[t.w]-h.edge(t)),Number.POSITIVE_INFINITY),r=e.node(t);n!==Number.POSITIVE_INFINITY&&r.borderType!==f&&(d[t]=Math.max(d[t],n))},h.successors.bind(h)),Object.keys(o).forEach(e=>d[e]=d[n[e]]),d}function d(e,t){return Object.values(t).reduce((t,n)=>{let r=Number.NEGATIVE_INFINITY,o=Number.POSITIVE_INFINITY;Object.entries(n).forEach(([t,n])=>{let i=e.node(t).width/2;r=Math.max(n+i,r),o=Math.min(n-i,o)});let i=r-o;return i<t[0]&&(t=[i,n]),t},[Number.POSITIVE_INFINITY,null])[1]}function h(e,t){let n=Object.values(t),r=o.applyWithChunking(Math.min,n),i=o.applyWithChunking(Math.max,n);["u","d"].forEach(n=>{["l","r"].forEach(a=>{let u=n+a,s=e[u];if(s===t)return;let l=Object.values(s),c=r-o.applyWithChunking(Math.min,l);"l"!==a&&(c=i-o.applyWithChunking(Math.max,l)),c&&(e[u]=o.mapValues(s,e=>e+c))})})}function f(e,t){return o.mapValues(e.ul,(n,r)=>{if(t)return e[t.toLowerCase()][r];{let t=Object.values(e).map(e=>e[r]).sort((e,t)=>e-t);return(t[1]+t[2])/2}})}e.exports={positionX:function(e){let t,n=o.buildLayerMatrix(e),r=Object.assign(i(e,n),a(e,n)),u={};["u","d"].forEach(i=>{t="u"===i?n:Object.values(n).reverse(),["l","r"].forEach(n=>{"r"===n&&(t=t.map(e=>Object.values(e).reverse()));let a=("u"===i?e.predecessors:e.successors).bind(e),s=l(e,t,r,a),d=c(e,t,s.root,s.align,"r"===n);"r"===n&&(d=o.mapValues(d,e=>-e)),u[i+n]=d})});let s=d(e,u);return h(u,s),f(u,e.graph().align)},findType1Conflicts:i,findType2Conflicts:a,addConflict:u,hasConflict:s,verticalAlignment:l,horizontalCompaction:c,alignCoordinates:h,findSmallestWidthAlignment:d,balance:f}},85503:function(e,t,n){"use strict";let r=n(28212),o=n(78621).positionX;e.exports=function(e){var t;let n,i,a;t=e=r.asNonCompoundGraph(e),n=r.buildLayerMatrix(t),i=t.graph().ranksep,a=0,n.forEach(e=>{let n=e.reduce((e,n)=>{let r=t.node(n).height;return e>r?e:r},0);e.forEach(e=>t.node(e).y=a+n/2),a+=n+i}),Object.entries(o(e)).forEach(([t,n])=>e.node(t).x=n)}},79276:function(e,t,n){"use strict";var r=n(44774).Graph,o=n(77963).slack;e.exports=function(e){var t,n,i=new r({directed:!1}),a=e.nodes()[0],u=e.nodeCount();for(i.setNode(a,{});i.nodes().forEach(function t(n){e.nodeEdges(n).forEach(r=>{var a=r.v,u=n===a?r.w:a;i.hasNode(u)||o(e,r)||(i.setNode(u,{}),i.setEdge(n,u,{}),t(u))})}),i.nodeCount()<u;)t=function(e,t){return t.edges().reduce((n,r)=>{let i=Number.POSITIVE_INFINITY;return(e.hasNode(r.v)!==e.hasNode(r.w)&&(i=o(t,r)),i<n[0])?[i,r]:n},[Number.POSITIVE_INFINITY,null])[1]}(i,e),n=i.hasNode(t.v)?o(e,t):-o(e,t),function(e,t,n){e.nodes().forEach(e=>t.node(e).rank+=n)}(i,e,n);return i}},76401:function(e,t,n){"use strict";var r=n(77963).longestPath,o=n(79276),i=n(49037);e.exports=function(e){switch(e.graph().ranker){case"network-simplex":default:i(e);break;case"tight-tree":r(e),o(e);break;case"longest-path":a(e)}};var a=r},49037:function(e,t,n){"use strict";var r=n(79276),o=n(77963).slack,i=n(77963).longestPath,a=n(44774).alg.preorder,u=n(44774).alg.postorder,s=n(28212).simplify;function l(e){i(e=s(e));var t,n,o=r(e);for(h(o),c(o,e);t=f(o);)n=p(o,e,t),g(o,e,t,n)}function c(e,t){var n=u(e,e.nodes());(n=n.slice(0,n.length-1)).forEach(n=>{var r;return r=e.node(n).parent,void(e.edge(n,r).cutvalue=d(e,t,n))})}function d(e,t,n){var r=e.node(n).parent,o=!0,i=t.edge(n,r),a=0;return i||(o=!1,i=t.edge(r,n)),a=i.weight,t.nodeEdges(n).forEach(i=>{var u=i.v===n,s=u?i.w:i.v;if(s!==r){var l=u===o,c=t.edge(i).weight;if(a+=l?c:-c,e.hasEdge(n,s)){var d=e.edge(n,s).cutvalue;a+=l?-d:d}}}),a}function h(e,t){arguments.length<2&&(t=e.nodes()[0]),function e(t,n,r,o,i){var a=r,u=t.node(o);return n[o]=!0,t.neighbors(o).forEach(i=>{Object.hasOwn(n,i)||(r=e(t,n,r,i,o))}),u.low=a,u.lim=r++,i?u.parent=i:delete u.parent,r}(e,{},1,t)}function f(e){return e.edges().find(t=>e.edge(t).cutvalue<0)}function p(e,t,n){var r=n.v,i=n.w;t.hasEdge(r,i)||(r=n.w,i=n.v);var a=e.node(r),u=e.node(i),s=a,l=!1;return a.lim>u.lim&&(s=u,l=!0),t.edges().filter(t=>l===y(e,e.node(t.v),s)&&l!==y(e,e.node(t.w),s)).reduce((e,n)=>o(t,n)<o(t,e)?n:e)}function g(e,t,n,r){var o,i,u=n.v,s=n.w;e.removeEdge(u,s),e.setEdge(r.v,r.w,{}),h(e),c(e,t),o=e.nodes().find(e=>!t.node(e).parent),a(e,o).slice(1).forEach(n=>{var r=e.node(n).parent,o=t.edge(n,r),i=!1;o||(o=t.edge(r,n),i=!0),t.node(n).rank=t.node(r).rank+(i?o.minlen:-o.minlen)})}function y(e,t,n){return n.low<=t.lim&&t.lim<=n.lim}e.exports=l,l.initLowLimValues=h,l.initCutValues=c,l.calcCutValue=d,l.leaveEdge=f,l.enterEdge=p,l.exchangeEdges=g},77963:function(e,t,n){"use strict";let{applyWithChunking:r}=n(28212);e.exports={longestPath:function(e){var t={};e.sources().forEach(function n(o){var i=e.node(o);if(Object.hasOwn(t,o))return i.rank;t[o]=!0;var a=r(Math.min,e.outEdges(o).map(t=>null==t?Number.POSITIVE_INFINITY:n(t.w)-e.edge(t).minlen));return a===Number.POSITIVE_INFINITY&&(a=0),i.rank=a})},slack:function(e,t){return e.node(t.w).rank-e.node(t.v).rank-e.edge(t).minlen}}},28212:function(e,t,n){"use strict";let r=n(44774).Graph;function o(e,t,n,r){let o;do o=s(r);while(e.hasNode(o));return n.dummy=t,e.setNode(o,n),o}function i(e,t){if(!(t.length>65535))return e.apply(null,t);{let n=function(e,t=65535){let n=[];for(let r=0;r<e.length;r+=t){let o=e.slice(r,r+t);n.push(o)}return n}(t);return e.apply(null,n.map(t=>e.apply(null,t)))}}function a(e){return i(Math.max,e.nodes().map(t=>{let n=e.node(t).rank;return void 0===n?Number.MIN_VALUE:n}))}e.exports={addBorderNode:function(e,t,n,r){let i={width:0,height:0};return arguments.length>=4&&(i.rank=n,i.order=r),o(e,"border",i,t)},addDummyNode:o,applyWithChunking:i,asNonCompoundGraph:function(e){let t=new r({multigraph:e.isMultigraph()}).setGraph(e.graph());return e.nodes().forEach(n=>{e.children(n).length||t.setNode(n,e.node(n))}),e.edges().forEach(n=>{t.setEdge(n,e.edge(n))}),t},buildLayerMatrix:function(e){let t=l(a(e)+1).map(()=>[]);return e.nodes().forEach(n=>{let r=e.node(n),o=r.rank;void 0!==o&&(t[o][r.order]=n)}),t},intersectRect:function(e,t){let n,r,o=e.x,i=e.y,a=t.x-o,u=t.y-i,s=e.width/2,l=e.height/2;if(!a&&!u)throw Error("Not possible to find intersection inside of the rectangle");return Math.abs(u)*s>Math.abs(a)*l?(u<0&&(l=-l),n=l*a/u,r=l):(a<0&&(s=-s),n=s,r=s*u/a),{x:o+n,y:i+r}},mapValues:function(e,t){let n=t;return"string"==typeof t&&(n=e=>e[t]),Object.entries(e).reduce((e,[t,r])=>(e[t]=n(r,t),e),{})},maxRank:a,normalizeRanks:function(e){let t=i(Math.min,e.nodes().map(t=>{let n=e.node(t).rank;return void 0===n?Number.MAX_VALUE:n}));e.nodes().forEach(n=>{let r=e.node(n);Object.hasOwn(r,"rank")&&(r.rank-=t)})},notime:function(e,t){return t()},partition:function(e,t){let n={lhs:[],rhs:[]};return e.forEach(e=>{t(e)?n.lhs.push(e):n.rhs.push(e)}),n},pick:function(e,t){let n={};for(let r of t)void 0!==e[r]&&(n[r]=e[r]);return n},predecessorWeights:function(e){let t=e.nodes().map(t=>{let n={};return e.inEdges(t).forEach(t=>{n[t.v]=(n[t.v]||0)+e.edge(t).weight}),n});return c(e.nodes(),t)},range:l,removeEmptyRanks:function(e){let t=i(Math.min,e.nodes().map(t=>e.node(t).rank)),n=[];e.nodes().forEach(r=>{let o=e.node(r).rank-t;n[o]||(n[o]=[]),n[o].push(r)});let r=0,o=e.graph().nodeRankFactor;Array.from(n).forEach((t,n)=>{void 0===t&&n%o!=0?--r:void 0!==t&&r&&t.forEach(t=>e.node(t).rank+=r)})},simplify:function(e){let t=new r().setGraph(e.graph());return e.nodes().forEach(n=>t.setNode(n,e.node(n))),e.edges().forEach(n=>{let r=t.edge(n.v,n.w)||{weight:0,minlen:1},o=e.edge(n);t.setEdge(n.v,n.w,{weight:r.weight+o.weight,minlen:Math.max(r.minlen,o.minlen)})}),t},successorWeights:function(e){let t=e.nodes().map(t=>{let n={};return e.outEdges(t).forEach(t=>{n[t.w]=(n[t.w]||0)+e.edge(t).weight}),n});return c(e.nodes(),t)},time:function(e,t){let n=Date.now();try{return t()}finally{console.log(e+" time: "+(Date.now()-n)+"ms")}},uniqueId:s,zipObject:c};let u=0;function s(e){var t=++u;return toString(e)+t}function l(e,t,n=1){null==t&&(t=e,e=0);let r=e=>e<t;n<0&&(r=e=>t<e);let o=[];for(let t=e;r(t);t+=n)o.push(t);return o}function c(e,t){return e.reduce((e,n,r)=>(e[n]=t[r],e),{})}},61870:function(e){e.exports="1.1.4"},44774:function(e,t,n){var r=n(81504);e.exports={Graph:r.Graph,json:n(13195),alg:n(30400),version:r.version}},32775:function(e){e.exports=function(e){var t,n={},r=[];return e.nodes().forEach(function(o){t=[],function r(o){Object.hasOwn(n,o)||(n[o]=!0,t.push(o),e.successors(o).forEach(r),e.predecessors(o).forEach(r))}(o),t.length&&r.push(t)}),r}},9903:function(e){function t(e,t,n,o){for(var i=[[e,!1]];i.length>0;){var a=i.pop();a[1]?o.push(a[0]):Object.hasOwn(n,a[0])||(n[a[0]]=!0,i.push([a[0],!0]),r(t(a[0]),e=>i.push([e,!1])))}}function n(e,t,n,o){for(var i=[e];i.length>0;){var a=i.pop();Object.hasOwn(n,a)||(n[a]=!0,o.push(a),r(t(a),e=>i.push(e)))}}function r(e,t){for(var n=e.length;n--;)t(e[n],n,e);return e}e.exports=function(e,r,o){Array.isArray(r)||(r=[r]);var i=e.isDirected()?t=>e.successors(t):t=>e.neighbors(t),a="post"===o?t:n,u=[],s={};return r.forEach(t=>{if(!e.hasNode(t))throw Error("Graph does not have node: "+t);a(t,i,s,u)}),u}},8171:function(e,t,n){var r=n(56533);e.exports=function(e,t,n){return e.nodes().reduce(function(o,i){return o[i]=r(e,i,t,n),o},{})}},56533:function(e,t,n){var r=n(15801);e.exports=function(e,t,n,i){return function(e,t,n,o){var i,a,u={},s=new r,l=function(e){var t=e.v!==i?e.v:e.w,r=u[t],o=n(e),l=a.distance+o;if(o<0)throw Error("dijkstra does not allow negative edge weights. Bad edge: "+e+" Weight: "+o);l<r.distance&&(r.distance=l,r.predecessor=i,s.decrease(t,l))};for(e.nodes().forEach(function(e){var n=e===t?0:Number.POSITIVE_INFINITY;u[e]={distance:n},s.add(e,n)});s.size()>0&&(a=u[i=s.removeMin()]).distance!==Number.POSITIVE_INFINITY;)o(i).forEach(l);return u}(e,String(t),n||o,i||function(t){return e.outEdges(t)})};var o=()=>1},38681:function(e,t,n){var r=n(74951);e.exports=function(e){return r(e).filter(function(t){return t.length>1||1===t.length&&e.hasEdge(t[0],t[0])})}},39745:function(e){e.exports=function(e,n,r){var o,i,a,u;return o=n||t,i=r||function(t){return e.outEdges(t)},a={},(u=e.nodes()).forEach(function(e){a[e]={},a[e][e]={distance:0},u.forEach(function(t){e!==t&&(a[e][t]={distance:Number.POSITIVE_INFINITY})}),i(e).forEach(function(t){var n=t.v===e?t.w:t.v,r=o(t);a[e][n]={distance:r,predecessor:e}})}),u.forEach(function(e){var t=a[e];u.forEach(function(n){var r=a[n];u.forEach(function(n){var o=r[e],i=t[n],a=r[n],u=o.distance+i.distance;u<a.distance&&(a.distance=u,a.predecessor=i.predecessor)})})}),a};var t=()=>1},30400:function(e,t,n){e.exports={components:n(32775),dijkstra:n(56533),dijkstraAll:n(8171),findCycles:n(38681),floydWarshall:n(39745),isAcyclic:n(30604),postorder:n(33684),preorder:n(62185),prim:n(37702),tarjan:n(74951),topsort:n(52938)}},30604:function(e,t,n){var r=n(52938);e.exports=function(e){try{r(e)}catch(e){if(e instanceof r.CycleException)return!1;throw e}return!0}},33684:function(e,t,n){var r=n(9903);e.exports=function(e,t){return r(e,t,"post")}},62185:function(e,t,n){var r=n(9903);e.exports=function(e,t){return r(e,t,"pre")}},37702:function(e,t,n){var r=n(31245),o=n(15801);e.exports=function(e,t){var n,i=new r,a={},u=new o;function s(e){var r=e.v===n?e.w:e.v,o=u.priority(r);if(void 0!==o){var i=t(e);i<o&&(a[r]=n,u.decrease(r,i))}}if(0===e.nodeCount())return i;e.nodes().forEach(function(e){u.add(e,Number.POSITIVE_INFINITY),i.setNode(e)}),u.decrease(e.nodes()[0],0);for(var l=!1;u.size()>0;){if(Object.hasOwn(a,n=u.removeMin()))i.setEdge(n,a[n]);else if(l)throw Error("Input graph is not connected: "+e);else l=!0;e.nodeEdges(n).forEach(s)}return i}},74951:function(e){e.exports=function(e){var t=0,n=[],r={},o=[];return e.nodes().forEach(function(i){Object.hasOwn(r,i)||function i(a){var u=r[a]={onStack:!0,lowlink:t,index:t++};if(n.push(a),e.successors(a).forEach(function(e){Object.hasOwn(r,e)?r[e].onStack&&(u.lowlink=Math.min(u.lowlink,r[e].index)):(i(e),u.lowlink=Math.min(u.lowlink,r[e].lowlink))}),u.lowlink===u.index){var s,l=[];do r[s=n.pop()].onStack=!1,l.push(s);while(a!==s);o.push(l)}}(i)}),o}},52938:function(e){function t(e){var t={},r={},o=[];if(e.sinks().forEach(function i(a){if(Object.hasOwn(r,a))throw new n;Object.hasOwn(t,a)||(r[a]=!0,t[a]=!0,e.predecessors(a).forEach(i),delete r[a],o.push(a))}),Object.keys(t).length!==e.nodeCount())throw new n;return o}class n extends Error{constructor(){super(...arguments)}}e.exports=t,t.CycleException=n},15801:function(e){class t{_arr=[];_keyIndices={};size(){return this._arr.length}keys(){return this._arr.map(function(e){return e.key})}has(e){return Object.hasOwn(this._keyIndices,e)}priority(e){var t=this._keyIndices[e];if(void 0!==t)return this._arr[t].priority}min(){if(0===this.size())throw Error("Queue underflow");return this._arr[0].key}add(e,t){var n=this._keyIndices;if(!Object.hasOwn(n,e=String(e))){var r=this._arr,o=r.length;return n[e]=o,r.push({key:e,priority:t}),this._decrease(o),!0}return!1}removeMin(){this._swap(0,this._arr.length-1);var e=this._arr.pop();return delete this._keyIndices[e.key],this._heapify(0),e.key}decrease(e,t){var n=this._keyIndices[e];if(t>this._arr[n].priority)throw Error("New priority is greater than current priority. Key: "+e+" Old: "+this._arr[n].priority+" New: "+t);this._arr[n].priority=t,this._decrease(n)}_heapify(e){var t=this._arr,n=2*e,r=n+1,o=e;n<t.length&&(o=t[n].priority<t[o].priority?n:o,r<t.length&&(o=t[r].priority<t[o].priority?r:o),o!==e&&(this._swap(e,o),this._heapify(o)))}_decrease(e){for(var t,n=this._arr,r=n[e].priority;0!==e&&!(n[t=e>>1].priority<r);)this._swap(e,t),e=t}_swap(e,t){var n=this._arr,r=this._keyIndices,o=n[e],i=n[t];n[e]=i,n[t]=o,r[i.key]=e,r[o.key]=t}}e.exports=t},31245:function(e){"use strict";class t{_isDirected=!0;_isMultigraph=!1;_isCompound=!1;_label;_defaultNodeLabelFn=()=>void 0;_defaultEdgeLabelFn=()=>void 0;_nodes={};_in={};_preds={};_out={};_sucs={};_edgeObjs={};_edgeLabels={};_nodeCount=0;_edgeCount=0;_parent;_children;constructor(e){e&&(this._isDirected=!Object.hasOwn(e,"directed")||e.directed,this._isMultigraph=!!Object.hasOwn(e,"multigraph")&&e.multigraph,this._isCompound=!!Object.hasOwn(e,"compound")&&e.compound),this._isCompound&&(this._parent={},this._children={},this._children["\0"]={})}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(e){return this._label=e,this}graph(){return this._label}setDefaultNodeLabel(e){return this._defaultNodeLabelFn=e,"function"!=typeof e&&(this._defaultNodeLabelFn=()=>e),this}nodeCount(){return this._nodeCount}nodes(){return Object.keys(this._nodes)}sources(){var e=this;return this.nodes().filter(t=>0===Object.keys(e._in[t]).length)}sinks(){var e=this;return this.nodes().filter(t=>0===Object.keys(e._out[t]).length)}setNodes(e,t){var n=arguments,r=this;return e.forEach(function(e){n.length>1?r.setNode(e,t):r.setNode(e)}),this}setNode(e,t){return Object.hasOwn(this._nodes,e)?arguments.length>1&&(this._nodes[e]=t):(this._nodes[e]=arguments.length>1?t:this._defaultNodeLabelFn(e),this._isCompound&&(this._parent[e]="\0",this._children[e]={},this._children["\0"][e]=!0),this._in[e]={},this._preds[e]={},this._out[e]={},this._sucs[e]={},++this._nodeCount),this}node(e){return this._nodes[e]}hasNode(e){return Object.hasOwn(this._nodes,e)}removeNode(e){var t=this;if(Object.hasOwn(this._nodes,e)){var n=e=>t.removeEdge(t._edgeObjs[e]);delete this._nodes[e],this._isCompound&&(this._removeFromParentsChildList(e),delete this._parent[e],this.children(e).forEach(function(e){t.setParent(e)}),delete this._children[e]),Object.keys(this._in[e]).forEach(n),delete this._in[e],delete this._preds[e],Object.keys(this._out[e]).forEach(n),delete this._out[e],delete this._sucs[e],--this._nodeCount}return this}setParent(e,t){if(!this._isCompound)throw Error("Cannot set parent in a non-compound graph");if(void 0===t)t="\0";else{t+="";for(var n=t;void 0!==n;n=this.parent(n))if(n===e)throw Error("Setting "+t+" as parent of "+e+" would create a cycle");this.setNode(t)}return this.setNode(e),this._removeFromParentsChildList(e),this._parent[e]=t,this._children[t][e]=!0,this}_removeFromParentsChildList(e){delete this._children[this._parent[e]][e]}parent(e){if(this._isCompound){var t=this._parent[e];if("\0"!==t)return t}}children(e="\0"){if(this._isCompound){var t=this._children[e];if(t)return Object.keys(t)}else if("\0"===e)return this.nodes();else if(this.hasNode(e))return[]}predecessors(e){var t=this._preds[e];if(t)return Object.keys(t)}successors(e){var t=this._sucs[e];if(t)return Object.keys(t)}neighbors(e){var t=this.predecessors(e);if(t){let r=new Set(t);for(var n of this.successors(e))r.add(n);return Array.from(r.values())}}isLeaf(e){return 0===(this.isDirected()?this.successors(e):this.neighbors(e)).length}filterNodes(e){var t=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});t.setGraph(this.graph());var n=this;Object.entries(this._nodes).forEach(function([n,r]){e(n)&&t.setNode(n,r)}),Object.values(this._edgeObjs).forEach(function(e){t.hasNode(e.v)&&t.hasNode(e.w)&&t.setEdge(e,n.edge(e))});var r={};return this._isCompound&&t.nodes().forEach(e=>t.setParent(e,function e(o){var i=n.parent(o);return void 0===i||t.hasNode(i)?(r[o]=i,i):i in r?r[i]:e(i)}(e))),t}setDefaultEdgeLabel(e){return this._defaultEdgeLabelFn=e,"function"!=typeof e&&(this._defaultEdgeLabelFn=()=>e),this}edgeCount(){return this._edgeCount}edges(){return Object.values(this._edgeObjs)}setPath(e,t){var n=this,r=arguments;return e.reduce(function(e,o){return r.length>1?n.setEdge(e,o,t):n.setEdge(e,o),o}),this}setEdge(){var e,t,r,i,a=!1,u=arguments[0];"object"==typeof u&&null!==u&&"v"in u?(e=u.v,t=u.w,r=u.name,2==arguments.length&&(i=arguments[1],a=!0)):(e=u,t=arguments[1],r=arguments[3],arguments.length>2&&(i=arguments[2],a=!0)),e=""+e,t=""+t,void 0!==r&&(r=""+r);var s=o(this._isDirected,e,t,r);if(Object.hasOwn(this._edgeLabels,s))return a&&(this._edgeLabels[s]=i),this;if(void 0!==r&&!this._isMultigraph)throw Error("Cannot set a named edge when isMultigraph = false");this.setNode(e),this.setNode(t),this._edgeLabels[s]=a?i:this._defaultEdgeLabelFn(e,t,r);var l=function(e,t,n,r){var o=""+t,i=""+n;if(!e&&o>i){var a=o;o=i,i=a}var u={v:o,w:i};return r&&(u.name=r),u}(this._isDirected,e,t,r);return e=l.v,t=l.w,Object.freeze(l),this._edgeObjs[s]=l,n(this._preds[t],e),n(this._sucs[e],t),this._in[t][s]=l,this._out[e][s]=l,this._edgeCount++,this}edge(e,t,n){var r=1==arguments.length?i(this._isDirected,arguments[0]):o(this._isDirected,e,t,n);return this._edgeLabels[r]}edgeAsObj(){let e=this.edge(...arguments);return"object"!=typeof e?{label:e}:e}hasEdge(e,t,n){var r=1==arguments.length?i(this._isDirected,arguments[0]):o(this._isDirected,e,t,n);return Object.hasOwn(this._edgeLabels,r)}removeEdge(e,t,n){var a=1==arguments.length?i(this._isDirected,arguments[0]):o(this._isDirected,e,t,n),u=this._edgeObjs[a];return u&&(e=u.v,t=u.w,delete this._edgeLabels[a],delete this._edgeObjs[a],r(this._preds[t],e),r(this._sucs[e],t),delete this._in[t][a],delete this._out[e][a],this._edgeCount--),this}inEdges(e,t){var n=this._in[e];if(n){var r=Object.values(n);return t?r.filter(e=>e.v===t):r}}outEdges(e,t){var n=this._out[e];if(n){var r=Object.values(n);return t?r.filter(e=>e.w===t):r}}nodeEdges(e,t){var n=this.inEdges(e,t);if(n)return n.concat(this.outEdges(e,t))}}function n(e,t){e[t]?e[t]++:e[t]=1}function r(e,t){--e[t]||delete e[t]}function o(e,t,n,r){var o=""+t,i=""+n;if(!e&&o>i){var a=o;o=i,i=a}return o+"\x01"+i+"\x01"+(void 0===r?"\0":r)}function i(e,t){return o(e,t.v,t.w,t.name)}e.exports=t},81504:function(e,t,n){e.exports={Graph:n(31245),version:n(75559)}},13195:function(e,t,n){var r=n(31245);e.exports={write:function(e){var t={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:e.nodes().map(function(t){var n=e.node(t),r=e.parent(t),o={v:t};return void 0!==n&&(o.value=n),void 0!==r&&(o.parent=r),o}),edges:e.edges().map(function(t){var n=e.edge(t),r={v:t.v,w:t.w};return void 0!==t.name&&(r.name=t.name),void 0!==n&&(r.value=n),r})};return void 0!==e.graph()&&(t.value=structuredClone(e.graph())),t},read:function(e){var t=new r(e.options).setGraph(e.value);return e.nodes.forEach(function(e){t.setNode(e.v,e.value),e.parent&&t.setParent(e.v,e.parent)}),e.edges.forEach(function(e){t.setEdge({v:e.v,w:e.w,name:e.name},e.value)}),t}}},75559:function(e){e.exports="2.2.4"},68799:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FieldList=void 0;var r=n(57437),o=n(21775),i=n(97061);t.FieldList=function(){var e=(0,i.useFieldRendererContext)(),t=e.definition,n=e.components;return(0,r.jsx)("div",{className:"field-list",children:Object.entries(t).map(function(e){var t=e[0],i=e[1];return(0,r.jsx)("div",{className:"field-wrapper",children:(0,r.jsx)(o.FieldRenderer,{propKey:t,prop:i,components:n})},t)})})}},21775:function(e,t,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.FieldRenderer=void 0;var i=n(57437),a=n(69131),u=n(97061);t.FieldRenderer=function(e){var t,n=e.propKey,s=e.prop,l=e.components,c=(0,u.useFieldRendererContext)(),d=c.values,h=c.setValue,f=c.dropdownOptions,p=c.refreshOptions;if(!(0,a.evaluateVisibleIf)(s.visibleIf,d))return null;var g=d[n],y=function(e){return h(n,e)},v=f[n],m=null==l?void 0:l[s.type];return m?(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(m,{propKey:n,prop:s,value:g,onChange:y,options:null==v?void 0:v.options,loading:null==v?void 0:v.loading,error:null==v?void 0:v.error})}):(0,i.jsxs)("div",{className:"form-field",children:[(0,i.jsx)("label",{children:s.displayName}),"SHORT_TEXT"===s.type&&(0,i.jsx)("input",{className:"input",type:"text",value:g||"",onChange:function(e){return y(e.target.value)}}),"LONG_TEXT"===s.type&&(0,i.jsx)("textarea",{className:"input",rows:4,value:g||"",onChange:function(e){return y(e.target.value)}}),"STATIC_DROPDOWN"===s.type&&(0,i.jsxs)("select",{className:"input",multiple:s.multiple,value:g||[],onChange:function(e){s.multiple?y(Array.from(e.target.selectedOptions).map(function(e){return e.value})):y(e.target.value)},children:[(0,i.jsx)("option",{disabled:!0,value:"",children:s.placeholder||"Select..."}),s.options.map(function(e){return(0,i.jsx)("option",{value:e.value,children:e.label},e.value)})]}),"DROPDOWN"===s.type&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("select",{className:"input",multiple:s.multiple,disabled:null==v?void 0:v.loading,value:g||[],onChange:function(e){s.multiple?y(Array.from(e.target.selectedOptions).map(function(e){return e.value})):y(e.target.value)},children:[(0,i.jsx)("option",{disabled:!0,value:"",children:s.placeholder||"Select..."}),null===(t=null==v?void 0:v.options)||void 0===t?void 0:t.map(function(e){return(0,i.jsx)("option",{value:e.value,children:e.label},e.value)})]}),(0,i.jsx)("button",{type:"button",onClick:function(){return p(n)},style:{fontSize:12},children:"Retry"})]}),"SWITCH"===s.type&&(0,i.jsx)("input",{type:"checkbox",checked:!!g,onChange:function(e){return y(e.target.checked)}}),"NUMBER"===s.type&&(0,i.jsx)("input",{className:"input",type:"number",value:g||"",onChange:function(e){return y(parseFloat(e.target.value))}}),"DATE"===s.type&&(0,i.jsx)("input",{className:"input",type:"date",value:g||"",onChange:function(e){return y(e.target.value)}}),"FILE"===s.type&&(0,i.jsx)("input",{className:"input",type:"file",onChange:function(e){var t,n=null===(t=e.target.files)||void 0===t?void 0:t[0];n&&y(n.name)}}),"JSON"===s.type&&(0,i.jsx)("textarea",{style:{padding:"6px 8px",border:"1px solid #ccc",borderRadius:4,width:"100%"},placeholder:"Enter JSON",rows:6,value:"string"==typeof g?g:void 0!==g?JSON.stringify(g,null,2):"",onChange:function(e){y(e.target.value)}}),"KEY_VALUE_ARRAY"===s.type&&(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:8},children:[(g||[]).map(function(e,t){return(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8},children:[(0,i.jsx)("input",{placeholder:"Key",value:e.key,onChange:function(e){var n=o([],g||[],!0);n[t]=r(r({},n[t]),{key:e.target.value}),y(n)},style:{padding:"6px 8px",border:"1px solid #ccc",borderRadius:4,flex:1}}),(0,i.jsx)("input",{placeholder:"Value",value:e.value,onChange:function(e){var n=o([],g||[],!0);n[t]=r(r({},n[t]),{value:e.target.value}),y(n)},style:{padding:"6px 8px",border:"1px solid #ccc",borderRadius:4,flex:1}}),(0,i.jsx)("button",{type:"button",onClick:function(){var e=o([],g||[],!0);e.splice(t,1),y(e)},style:{padding:"6px 10px",border:"1px solid #ccc",borderRadius:4,backgroundColor:"#f9f9f9",cursor:"pointer"},children:"Remove"})]},t)}),(0,i.jsx)("button",{type:"button",onClick:function(){return y(o(o([],g||[],!0),[{key:"",value:""}],!1))},style:{marginTop:6,padding:"6px 10px",border:"1px solid #ccc",borderRadius:4,backgroundColor:"#f3f4f6",alignSelf:"flex-start",cursor:"pointer"},children:"+ Add Pair"})]}),"ARRAY"===s.type&&(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:8},children:[(g||[]).map(function(e,t){return(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8},children:[(0,i.jsx)("input",{type:"text",value:e,onChange:function(e){var n=o([],g,!0);n[t]=e.target.value,y(n)},style:{padding:"6px 8px",border:"1px solid #ccc",borderRadius:4,flex:1}}),(0,i.jsx)("button",{type:"button",onClick:function(){var e=o([],g,!0);e.splice(t,1),y(e)},style:{padding:"6px 10px",border:"1px solid #ccc",borderRadius:4,backgroundColor:"#f3f4f6",cursor:"pointer"},children:"Remove"})]},t)}),(0,i.jsx)("button",{type:"button",onClick:function(){return y(o(o([],g||[],!0),[""],!1))},style:{padding:"6px 12px",border:"1px solid #ccc",borderRadius:4,backgroundColor:"#f3f4f6",alignSelf:"flex-start",cursor:"pointer"},children:"+ Add Item"})]}),s.description&&(0,i.jsx)("div",{className:"helper-text",style:{fontSize:12,color:"#6b7280"},children:s.description})]})}},97061:function(e,t,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function u(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,u)}s((r=r.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){var n,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=u(0),a.throw=u(1),a.return=u(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(u){return function(s){return function(u){if(n)throw TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return i.label++,{value:u[1],done:!1};case 5:i.label++,r=u[1],u=[0];continue;case 7:u=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){i=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){i.label=u[1];break}if(6===u[0]&&i.label<o[1]){i.label=o[1],o=u;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(u);break}o[2]&&i.ops.pop(),i.trys.pop();continue}u=t.call(e,i)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,s])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.FieldRendererProvider=void 0,t.useFieldRendererContext=function(){var e=(0,u.useContext)(l);if(!e)throw Error("useFieldRendererContext must be used within a FieldRendererProvider");return e};var a=n(57437),u=n(2265),s=n(69131),l=(0,u.createContext)(void 0);t.FieldRendererProvider=function(e){var t=e.children,n=e.definition,c=e.initialValues,d=e.onChange,h=e.components,f=e.getOptions,p=e.requiresAuth,g=void 0!==p&&p,y=e.selectedConnectionId,v=r(r({},Object.fromEntries(Object.entries(n).filter(function(e){return e[0],void 0!==e[1].defaultValue}).map(function(e){return[e[0],e[1].defaultValue]}))),void 0===c?{}:c),m=(0,u.useState)(v),w=m[0],b=m[1],_=(0,u.useState)({}),x=_[0],E=_[1],k=(0,u.useRef)(v),O=(0,s.areRequiredFieldsFilled)(n,w),M=(0,u.useRef)(!1),j=function(e){return o(void 0,void 0,void 0,function(){var t,o,a;return i(this,function(i){switch(i.label){case 0:if(!(t=n[e])||"DROPDOWN"!==t.type)return[2];if(g&&!y)return E(function(t){var n;return r(r({},t),((n={})[e]={options:[],loading:!1},n))}),[2];E(function(t){var n;return r(r({},t),((n={})[e]={options:[],loading:!0},n))}),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,f(e,{values:w,connectionId:y})];case 2:return o=i.sent(),E(function(t){var n;return r(r({},t),((n={})[e]={options:o,loading:!1},n))}),[3,4];case 3:return a=i.sent(),E(function(t){var n;return r(r({},t),((n={})[e]={options:[],loading:!1,error:a.message||"Failed to load options"},n))}),[3,4];case 4:return[2]}})})};return(0,u.useEffect)(function(){for(var e=0,t=Object.entries(n);e<t.length;e++){var r=t[e],o=r[0],i=r[1];if("DROPDOWN"===i.type){var a=(i.refreshers||[]).some(function(e){return w[e]!==k.current[e]});(!x[o]||a)&&j(o)}}k.current=w},[w,n]),(0,u.useEffect)(function(){if(g&&!M.current&&y){for(var e=0,t=Object.entries(n);e<t.length;e++){var r=t[e],o=r[0];"DROPDOWN"===r[1].type&&j(o)}M.current=!0}},[y,g]),(0,a.jsx)(l.Provider,{value:{values:w,setValue:function(e,t){b(function(o){var i,a=r(r({},o),((i={})[e]=t,i)),u=(0,s.areRequiredFieldsFilled)(n,a);return null==d||d(a,u),a})},dropdownOptions:x,refreshOptions:j,isRequiredFilled:O,definition:n,components:h},children:t})}},37471:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(21775),t),o(n(68799),t),o(n(97061),t)},19794:function(e,t,n){"use strict";n.d(t,{t8:function(){return p},jD:function(){return d},QZ:function(){return g},IY:function(){return h},Ly:function(){return y},pB:function(){return v},oW:function(){return f},oC:function(){return nZ},Ql:function(){return n1},FD:function(){return n2},X6:function(){return re},Cz:function(){return rs},dw:function(){return rt},Kl:function(){return rn},Z_:function(){return nb},yF:function(){return nF},tj:function(){return tk},OL:function(){return nt},q7:function(){return tz},n3:function(){return nS},Ki:function(){return t0},wQ:function(){return t_},Qj:function(){return tw},ZB:function(){return ne},Eg:function(){return tL},lM:function(){return nd},OQ:function(){return np},oI:function(){return tZ},my:function(){return t$},Zp:function(){return tM},t_:function(){return no},Pp:function(){return ng},JU:function(){return nN},WD:function(){return tF},xx:function(){return ny},wv:function(){return nl},S2:function(){return ni},vG:function(){return tA},W0:function(){return tP},dW:function(){return nC},Rf:function(){return t8},ZJ:function(){return nP},RX:function(){return tS},f5:function(){return tR},xA:function(){return tT},lp:function(){return tK},OW:function(){return nM},Hm:function(){return nx},$i:function(){return t4},WI:function(){return tO},so:function(){return nW},k5:function(){return tb},Ky:function(){return tx},J3:function(){return tj},RY:function(){return nv},s$:function(){return nu},Q5:function(){return t6},N5:function(){return ns},Vt:function(){return tN},kE:function(){return tJ},J$:function(){return tQ},nb:function(){return t9},PS:function(){return tG},hO:function(){return nH},m:function(){return t2},Kz:function(){return n_},oj:function(){return t5},BT:function(){return nX},_2:function(){return t1},VV:function(){return nz},be:function(){return nq},B1:function(){return nV},nu:function(){return nn}});var r,o,i,a,u,s,l,c,d,h,f,p,g,y,v,m={value:()=>{}};function w(){for(var e,t=0,n=arguments.length,r={};t<n;++t){if(!(e=arguments[t]+"")||e in r||/[\s.]/.test(e))throw Error("illegal type: "+e);r[e]=[]}return new b(r)}function b(e){this._=e}function _(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=m,e=e.slice(0,r).concat(e.slice(r+1));break}return null!=n&&e.push({name:t,value:n}),e}function x(){}function E(e){return null==e?x:function(){return this.querySelector(e)}}function k(){return[]}function O(e){return null==e?k:function(){return this.querySelectorAll(e)}}function M(e){return function(){return this.matches(e)}}function j(e){return function(t){return t.matches(e)}}b.prototype=w.prototype={constructor:b,on:function(e,t){var n,r=this._,o=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!r.hasOwnProperty(e))throw Error("unknown type: "+e);return{type:e,name:t}}),i=-1,a=o.length;if(arguments.length<2){for(;++i<a;)if((n=(e=o[i]).type)&&(n=function(e,t){for(var n,r=0,o=e.length;r<o;++r)if((n=e[r]).name===t)return n.value}(r[n],e.name)))return n;return}if(null!=t&&"function"!=typeof t)throw Error("invalid callback: "+t);for(;++i<a;)if(n=(e=o[i]).type)r[n]=_(r[n],e.name,t);else if(null==t)for(n in r)r[n]=_(r[n],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new b(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,r,o=Array(n),i=0;i<n;++i)o[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(r=this._[e],i=0,n=r.length;i<n;++i)r[i].value.apply(t,o)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};var N=Array.prototype.find;function I(){return this.firstElementChild}var T=Array.prototype.filter;function A(){return Array.from(this.children)}function C(e){return Array(e.length)}function S(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function P(e,t,n,r,o,i){for(var a,u=0,s=t.length,l=i.length;u<l;++u)(a=t[u])?(a.__data__=i[u],r[u]=a):n[u]=new S(e,i[u]);for(;u<s;++u)(a=t[u])&&(o[u]=a)}function R(e,t,n,r,o,i,a){var u,s,l,c=new Map,d=t.length,h=i.length,f=Array(d);for(u=0;u<d;++u)(s=t[u])&&(f[u]=l=a.call(s,s.__data__,u,t)+"",c.has(l)?o[u]=s:c.set(l,s));for(u=0;u<h;++u)l=a.call(e,i[u],u,i)+"",(s=c.get(l))?(r[u]=s,s.__data__=i[u],c.delete(l)):n[u]=new S(e,i[u]);for(u=0;u<d;++u)(s=t[u])&&c.get(f[u])===s&&(o[u]=s)}function $(e){return e.__data__}function L(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}S.prototype={constructor:S,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};var z="http://www.w3.org/1999/xhtml",F={svg:"http://www.w3.org/2000/svg",xhtml:z,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function D(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),F.hasOwnProperty(t)?{space:F[t],local:e}:e}function B(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function W(e,t){return e.style.getPropertyValue(t)||B(e).getComputedStyle(e,null).getPropertyValue(t)}function V(e){return e.trim().split(/^|\s+/)}function H(e){return e.classList||new Y(e)}function Y(e){this._node=e,this._names=V(e.getAttribute("class")||"")}function q(e,t){for(var n=H(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function X(e,t){for(var n=H(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function G(){this.textContent=""}function U(){this.innerHTML=""}function Z(){this.nextSibling&&this.parentNode.appendChild(this)}function K(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Q(e){var t=D(e);return(t.local?function(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}:function(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===z&&t.documentElement.namespaceURI===z?t.createElement(e):t.createElementNS(n,e)}})(t)}function J(){return null}function ee(){var e=this.parentNode;e&&e.removeChild(this)}function et(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function en(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function er(e){return function(){var t=this.__on;if(t){for(var n,r=0,o=-1,i=t.length;r<i;++r)(n=t[r],e.type&&n.type!==e.type||n.name!==e.name)?t[++o]=n:this.removeEventListener(n.type,n.listener,n.options);++o?t.length=o:delete this.__on}}}function eo(e,t,n){return function(){var r,o=this.__on,i=function(e){t.call(this,e,this.__data__)};if(o){for(var a=0,u=o.length;a<u;++a)if((r=o[a]).type===e.type&&r.name===e.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=i,r.options=n),r.value=t;return}}this.addEventListener(e.type,i,n),r={type:e.type,name:e.name,value:t,listener:i,options:n},o?o.push(r):this.__on=[r]}}function ei(e,t,n){var r=B(e),o=r.CustomEvent;"function"==typeof o?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}Y.prototype={add:function(e){0>this._names.indexOf(e)&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var ea=[null];function eu(e,t){this._groups=e,this._parents=t}function es(){return new eu([[document.documentElement]],ea)}function el(e){return"string"==typeof e?new eu([[document.querySelector(e)]],[document.documentElement]):new eu([[e]],ea)}function ec(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}eu.prototype=es.prototype={constructor:eu,select:function(e){"function"!=typeof e&&(e=E(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a,u=t[o],s=u.length,l=r[o]=Array(s),c=0;c<s;++c)(i=u[c])&&(a=e.call(i,i.__data__,c,u))&&("__data__"in i&&(a.__data__=i.__data__),l[c]=a);return new eu(r,this._parents)},selectAll:function(e){if("function"==typeof e){var t;t=e,e=function(){var e;return e=t.apply(this,arguments),null==e?[]:Array.isArray(e)?e:Array.from(e)}}else e=O(e);for(var n=this._groups,r=n.length,o=[],i=[],a=0;a<r;++a)for(var u,s=n[a],l=s.length,c=0;c<l;++c)(u=s[c])&&(o.push(e.call(u,u.__data__,c,s)),i.push(u));return new eu(o,i)},selectChild:function(e){var t;return this.select(null==e?I:(t="function"==typeof e?e:j(e),function(){return N.call(this.children,t)}))},selectChildren:function(e){var t;return this.selectAll(null==e?A:(t="function"==typeof e?e:j(e),function(){return T.call(this.children,t)}))},filter:function(e){"function"!=typeof e&&(e=M(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a=t[o],u=a.length,s=r[o]=[],l=0;l<u;++l)(i=a[l])&&e.call(i,i.__data__,l,a)&&s.push(i);return new eu(r,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,$);var n=t?R:P,r=this._parents,o=this._groups;"function"!=typeof e&&(m=e,e=function(){return m});for(var i=o.length,a=Array(i),u=Array(i),s=Array(i),l=0;l<i;++l){var c=r[l],d=o[l],h=d.length,f="object"==typeof(v=e.call(c,c&&c.__data__,l,r))&&"length"in v?v:Array.from(v),p=f.length,g=u[l]=Array(p),y=a[l]=Array(p);n(c,d,g,y,s[l]=Array(h),f,t);for(var v,m,w,b,_=0,x=0;_<p;++_)if(w=g[_]){for(_>=x&&(x=_+1);!(b=y[x])&&++x<p;);w._next=b||null}}return(a=new eu(a,r))._enter=u,a._exit=s,a},enter:function(){return new eu(this._enter||this._groups.map(C),this._parents)},exit:function(){return new eu(this._exit||this._groups.map(C),this._parents)},join:function(e,t,n){var r=this.enter(),o=this,i=this.exit();return"function"==typeof e?(r=e(r))&&(r=r.selection()):r=r.append(e+""),null!=t&&(o=t(o))&&(o=o.selection()),null==n?i.remove():n(i),r&&o?r.merge(o).order():o},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,a=Math.min(o,i),u=Array(o),s=0;s<a;++s)for(var l,c=n[s],d=r[s],h=c.length,f=u[s]=Array(h),p=0;p<h;++p)(l=c[p]||d[p])&&(f[p]=l);for(;s<o;++s)u[s]=n[s];return new eu(u,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r,o=e[t],i=o.length-1,a=o[i];--i>=0;)(r=o[i])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=L);for(var n=this._groups,r=n.length,o=Array(r),i=0;i<r;++i){for(var a,u=n[i],s=u.length,l=o[i]=Array(s),c=0;c<s;++c)(a=u[c])&&(l[c]=a);l.sort(t)}return new eu(o,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var a=r[o];if(a)return a}return null},size:function(){let e=0;for(let t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o,i=t[n],a=0,u=i.length;a<u;++a)(o=i[a])&&e.call(o,o.__data__,a,i);return this},attr:function(e,t){var n=D(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==t?n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}}:"function"==typeof t?n.local?function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}:function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}:n.local?function(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}:function(e,t){return function(){this.setAttribute(e,t)}})(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?function(e){return function(){this.style.removeProperty(e)}}:"function"==typeof t?function(e,t,n){return function(){var r=t.apply(this,arguments);null==r?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}:function(e,t,n){return function(){this.style.setProperty(e,t,n)}})(e,t,null==n?"":n)):W(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?function(e){return function(){delete this[e]}}:"function"==typeof t?function(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}:function(e,t){return function(){this[e]=t}})(e,t)):this.node()[e]},classed:function(e,t){var n=V(e+"");if(arguments.length<2){for(var r=H(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each(("function"==typeof t?function(e,t){return function(){(t.apply(this,arguments)?q:X)(this,e)}}:t?function(e){return function(){q(this,e)}}:function(e){return function(){X(this,e)}})(n,t))},text:function(e){return arguments.length?this.each(null==e?G:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}:function(e){return function(){this.textContent=e}})(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?U:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}:function(e){return function(){this.innerHTML=e}})(e)):this.node().innerHTML},raise:function(){return this.each(Z)},lower:function(){return this.each(K)},append:function(e){var t="function"==typeof e?e:Q(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})},insert:function(e,t){var n="function"==typeof e?e:Q(e),r=null==t?J:"function"==typeof t?t:E(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(ee)},clone:function(e){return this.select(e?en:et)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var r,o,i=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}),a=i.length;if(arguments.length<2){var u=this.node().__on;if(u){for(var s,l=0,c=u.length;l<c;++l)for(r=0,s=u[l];r<a;++r)if((o=i[r]).type===s.type&&o.name===s.name)return s.value}return}for(r=0,u=t?eo:er;r<a;++r)this.each(u(i[r],t,n));return this},dispatch:function(e,t){return this.each(("function"==typeof t?function(e,t){return function(){return ei(this,e,t.apply(this,arguments))}}:function(e,t){return function(){return ei(this,e,t)}})(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r,o=e[t],i=0,a=o.length;i<a;++i)(r=o[i])&&(yield r)}};let ed={passive:!1},eh={capture:!0,passive:!1};function ef(e){e.stopImmediatePropagation()}function ep(e){e.preventDefault(),e.stopImmediatePropagation()}function eg(e){var t=e.document.documentElement,n=el(e).on("dragstart.drag",ep,eh);"onselectstart"in t?n.on("selectstart.drag",ep,eh):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function ey(e,t){var n=e.document.documentElement,r=el(e).on("dragstart.drag",null);t&&(r.on("click.drag",ep,eh),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var ev=e=>()=>e;function em(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:i,x:a,y:u,dx:s,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}function ew(e){return!e.ctrlKey&&!e.button}function eb(){return this.parentNode}function e_(e,t){return null==t?{x:e.x,y:e.y}:t}function ex(){return navigator.maxTouchPoints||"ontouchstart"in this}function eE(){var e,t,n,r,o=ew,i=eb,a=e_,u=ex,s={},l=w("start","drag","end"),c=0,d=0;function h(e){e.on("mousedown.drag",f).filter(u).on("touchstart.drag",y).on("touchmove.drag",v,ed).on("touchend.drag touchcancel.drag",m).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function f(a,u){if(!r&&o.call(this,a,u)){var s=b(this,i.call(this,a,u),a,u,"mouse");s&&(el(a.view).on("mousemove.drag",p,eh).on("mouseup.drag",g,eh),eg(a.view),ef(a),n=!1,e=a.clientX,t=a.clientY,s("start",a))}}function p(r){if(ep(r),!n){var o=r.clientX-e,i=r.clientY-t;n=o*o+i*i>d}s.mouse("drag",r)}function g(e){el(e.view).on("mousemove.drag mouseup.drag",null),ey(e.view,n),ep(e),s.mouse("end",e)}function y(e,t){if(o.call(this,e,t)){var n,r,a=e.changedTouches,u=i.call(this,e,t),s=a.length;for(n=0;n<s;++n)(r=b(this,u,e,t,a[n].identifier,a[n]))&&(ef(e),r("start",e,a[n]))}}function v(e){var t,n,r=e.changedTouches,o=r.length;for(t=0;t<o;++t)(n=s[r[t].identifier])&&(ep(e),n("drag",e,r[t]))}function m(e){var t,n,o=e.changedTouches,i=o.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),t=0;t<i;++t)(n=s[o[t].identifier])&&(ef(e),n("end",e,o[t]))}function b(e,t,n,r,o,i){var u,d,f,p=l.copy(),g=ec(i||n,t);if(null!=(f=a.call(e,new em("beforestart",{sourceEvent:n,target:h,identifier:o,active:c,x:g[0],y:g[1],dx:0,dy:0,dispatch:p}),r)))return u=f.x-g[0]||0,d=f.y-g[1]||0,function n(i,a,l){var y,v=g;switch(i){case"start":s[o]=n,y=c++;break;case"end":delete s[o],--c;case"drag":g=ec(l||a,t),y=c}p.call(i,e,new em(i,{sourceEvent:a,subject:f,target:h,identifier:o,active:y,x:g[0]+u,y:g[1]+d,dx:g[0]-v[0],dy:g[1]-v[1],dispatch:p}),r)}}return h.filter=function(e){return arguments.length?(o="function"==typeof e?e:ev(!!e),h):o},h.container=function(e){return arguments.length?(i="function"==typeof e?e:ev(e),h):i},h.subject=function(e){return arguments.length?(a="function"==typeof e?e:ev(e),h):a},h.touchable=function(e){return arguments.length?(u="function"==typeof e?e:ev(!!e),h):u},h.on=function(){var e=l.on.apply(l,arguments);return e===l?h:e},h.clickDistance=function(e){return arguments.length?(d=(e=+e)*e,h):Math.sqrt(d)},h}function ek(e){return((e=Math.exp(e))+1/e)/2}em.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var eO,eM,ej=function e(t,n,r){function o(e,o){var i,a,u=e[0],s=e[1],l=e[2],c=o[0],d=o[1],h=o[2],f=c-u,p=d-s,g=f*f+p*p;if(g<1e-12)a=Math.log(h/l)/t,i=function(e){return[u+e*f,s+e*p,l*Math.exp(t*e*a)]};else{var y=Math.sqrt(g),v=(h*h-l*l+r*g)/(2*l*n*y),m=(h*h-l*l-r*g)/(2*h*n*y),w=Math.log(Math.sqrt(v*v+1)-v);a=(Math.log(Math.sqrt(m*m+1)-m)-w)/t,i=function(e){var r,o,i=e*a,c=ek(w),d=l/(n*y)*(((r=Math.exp(2*(r=t*i+w)))-1)/(r+1)*c-((o=Math.exp(o=w))-1/o)/2);return[u+d*f,s+d*p,l*c/ek(t*i+w)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return o.rho=function(t){var n=Math.max(.001,+t),r=n*n;return e(n,r,r*r)},o}(Math.SQRT2,2,4),eN=0,eI=0,eT=0,eA=0,eC=0,eS=0,eP="object"==typeof performance&&performance.now?performance:Date,eR="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function e$(){return eC||(eR(eL),eC=eP.now()+eS)}function eL(){eC=0}function ez(){this._call=this._time=this._next=null}function eF(e,t,n){var r=new ez;return r.restart(e,t,n),r}function eD(){eC=(eA=eP.now())+eS,eN=eI=0;try{!function(){e$(),++eN;for(var e,t=eO;t;)(e=eC-t._time)>=0&&t._call.call(void 0,e),t=t._next;--eN}()}finally{eN=0,function(){for(var e,t,n=eO,r=1/0;n;)n._call?(r>n._time&&(r=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:eO=t);eM=e,eW(r)}(),eC=0}}function eB(){var e=eP.now(),t=e-eA;t>1e3&&(eS-=t,eA=e)}function eW(e){!eN&&(eI&&(eI=clearTimeout(eI)),e-eC>24?(e<1/0&&(eI=setTimeout(eD,e-eP.now()-eS)),eT&&(eT=clearInterval(eT))):(eT||(eA=eP.now(),eT=setInterval(eB,1e3)),eN=1,eR(eD)))}function eV(e,t,n){var r=new ez;return t=null==t?0:+t,r.restart(n=>{r.stop(),e(n+t)},t,n),r}ez.prototype=eF.prototype={constructor:ez,restart:function(e,t,n){if("function"!=typeof e)throw TypeError("callback is not a function");n=(null==n?e$():+n)+(null==t?0:+t),this._next||eM===this||(eM?eM._next=this:eO=this,eM=this),this._call=e,this._time=n,eW()},stop:function(){this._call&&(this._call=null,this._time=1/0,eW())}};var eH=w("start","end","cancel","interrupt"),eY=[];function eq(e,t,n,r,o,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};(function(e,t,n){var r,o=e.__transition;function i(s){var l,c,d,h;if(1!==n.state)return u();for(l in o)if((h=o[l]).name===n.name){if(3===h.state)return eV(i);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete o[l]):+l<t&&(h.state=6,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete o[l])}if(eV(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(s))}),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(l=0,n.state=3,r=Array(d=n.tween.length),c=-1;l<d;++l)(h=n.tween[l].value.call(e,e.__data__,n.index,n.group))&&(r[++c]=h);r.length=c+1}}function a(t){for(var o=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(u),n.state=5,1),i=-1,a=r.length;++i<a;)r[i].call(e,o);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),u())}function u(){for(var r in n.state=6,n.timer.stop(),delete o[t],o)return;delete e.__transition}o[t]=n,n.timer=eF(function(e){n.state=1,n.timer.restart(i,n.delay,n.time),n.delay<=e&&i(e-n.delay)},0,n.time)})(e,n,{name:t,index:r,group:o,on:eH,tween:eY,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function eX(e,t){var n=eU(e,t);if(n.state>0)throw Error("too late; already scheduled");return n}function eG(e,t){var n=eU(e,t);if(n.state>3)throw Error("too late; already running");return n}function eU(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw Error("transition not found");return n}function eZ(e,t){var n,r,o,i=e.__transition,a=!0;if(i){for(o in t=null==t?null:t+"",i){if((n=i[o]).name!==t){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[o]}a&&delete e.__transition}}var eK=n(44193),eQ=180/Math.PI,eJ={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function e0(e,t,n,r,o,i){var a,u,s;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(s=e*n+t*r)&&(n-=e*s,r-=t*s),(u=Math.sqrt(n*n+r*r))&&(n/=u,r/=u,s/=u),e*r<t*n&&(e=-e,t=-t,s=-s,a=-a),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*eQ,skewX:Math.atan(s)*eQ,scaleX:a,scaleY:u}}function e1(e,t,n,r){function o(e){return e.length?e.pop()+" ":""}return function(i,a){var u,s,l,c,d=[],h=[];return i=e(i),a=e(a),function(e,r,o,i,a,u){if(e!==o||r!==i){var s=a.push("translate(",null,t,null,n);u.push({i:s-4,x:(0,eK.Z)(e,o)},{i:s-2,x:(0,eK.Z)(r,i)})}else(o||i)&&a.push("translate("+o+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,d,h),(u=i.rotate)!==(s=a.rotate)?(u-s>180?s+=360:s-u>180&&(u+=360),h.push({i:d.push(o(d)+"rotate(",null,r)-2,x:(0,eK.Z)(u,s)})):s&&d.push(o(d)+"rotate("+s+r),(l=i.skewX)!==(c=a.skewX)?h.push({i:d.push(o(d)+"skewX(",null,r)-2,x:(0,eK.Z)(l,c)}):c&&d.push(o(d)+"skewX("+c+r),function(e,t,n,r,i,a){if(e!==n||t!==r){var u=i.push(o(i)+"scale(",null,",",null,")");a.push({i:u-4,x:(0,eK.Z)(e,n)},{i:u-2,x:(0,eK.Z)(t,r)})}else(1!==n||1!==r)&&i.push(o(i)+"scale("+n+","+r+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,d,h),i=a=null,function(e){for(var t,n=-1,r=h.length;++n<r;)d[(t=h[n]).i]=t.x(e);return d.join("")}}}var e2=e1(function(e){let t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?eJ:e0(t.a,t.b,t.c,t.d,t.e,t.f)},"px, ","px)","deg)"),e5=e1(function(e){return null==e?eJ:(c||(c=document.createElementNS("http://www.w3.org/2000/svg","g")),c.setAttribute("transform",e),e=c.transform.baseVal.consolidate())?e0((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):eJ},", ",")",")");function e7(e,t,n){var r=e._id;return e.each(function(){var e=eG(this,r);(e.value||(e.value={}))[t]=n.apply(this,arguments)}),function(e){return eU(e,r).value[t]}}var e4=n(26910),e6=n(16064),e3=n(19729);function e8(e,t){var n;return("number"==typeof t?eK.Z:t instanceof e4.ZP?e6.ZP:(n=(0,e4.ZP)(t))?(t=n,e6.ZP):e3.Z)(e,t)}var e9=es.prototype.constructor;function te(e){return function(){this.style.removeProperty(e)}}var tt=0;function tn(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}var tr=es.prototype;tn.prototype=(function(e){return es().transition(e)}).prototype={constructor:tn,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=E(e));for(var r=this._groups,o=r.length,i=Array(o),a=0;a<o;++a)for(var u,s,l=r[a],c=l.length,d=i[a]=Array(c),h=0;h<c;++h)(u=l[h])&&(s=e.call(u,u.__data__,h,l))&&("__data__"in u&&(s.__data__=u.__data__),d[h]=s,eq(d[h],t,n,h,d,eU(u,n)));return new tn(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=O(e));for(var r=this._groups,o=r.length,i=[],a=[],u=0;u<o;++u)for(var s,l=r[u],c=l.length,d=0;d<c;++d)if(s=l[d]){for(var h,f=e.call(s,s.__data__,d,l),p=eU(s,n),g=0,y=f.length;g<y;++g)(h=f[g])&&eq(h,t,n,g,f,p);i.push(f),a.push(s)}return new tn(i,a,t,n)},selectChild:tr.selectChild,selectChildren:tr.selectChildren,filter:function(e){"function"!=typeof e&&(e=M(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a=t[o],u=a.length,s=r[o]=[],l=0;l<u;++l)(i=a[l])&&e.call(i,i.__data__,l,a)&&s.push(i);return new tn(r,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw Error();for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),a=Array(r),u=0;u<i;++u)for(var s,l=t[u],c=n[u],d=l.length,h=a[u]=Array(d),f=0;f<d;++f)(s=l[f]||c[f])&&(h[f]=s);for(;u<r;++u)a[u]=t[u];return new tn(a,this._parents,this._name,this._id)},selection:function(){return new e9(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=++tt,r=this._groups,o=r.length,i=0;i<o;++i)for(var a,u=r[i],s=u.length,l=0;l<s;++l)if(a=u[l]){var c=eU(a,t);eq(a,e,n,l,u,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new tn(r,this._parents,e,n)},call:tr.call,nodes:tr.nodes,node:tr.node,size:tr.size,empty:tr.empty,each:tr.each,on:function(e,t){var n,r,o,i=this._id;return arguments.length<2?eU(this.node(),i).on.on(e):this.each((o=(e+"").trim().split(/^|\s+/).every(function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e})?eX:eG,function(){var a=o(this,i),u=a.on;u!==n&&(r=(n=u).copy()).on(e,t),a.on=r}))},attr:function(e,t){var n=D(e),r="transform"===n?e5:e8;return this.attrTween(e,"function"==typeof t?(n.local?function(e,t,n){var r,o,i;return function(){var a,u,s=n(this);return null==s?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local))===(u=s+"")?null:a===r&&u===o?i:(o=u,i=t(r=a,s))}}:function(e,t,n){var r,o,i;return function(){var a,u,s=n(this);return null==s?void this.removeAttribute(e):(a=this.getAttribute(e))===(u=s+"")?null:a===r&&u===o?i:(o=u,i=t(r=a,s))}})(n,r,e7(this,"attr."+e,t)):null==t?(n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}})(n):(n.local?function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===r?o:o=t(r=a,n)}}:function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===r?o:o=t(r=a,n)}})(n,r,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();var r=D(e);return this.tween(n,(r.local?function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttributeNS(e.space,e.local,o.call(this,t))}),n}return o._value=t,o}:function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttribute(e,o.call(this,t))}),n}return o._value=t,o})(r,t))},style:function(e,t,n){var r,o,i,a,u,s,l,c,d,h,f,p,g,y,v,m,w,b,_,x,E,k="transform"==(e+="")?e2:e8;return null==t?this.styleTween(e,(r=e,function(){var e=W(this,r),t=(this.style.removeProperty(r),W(this,r));return e===t?null:e===o&&t===i?a:a=k(o=e,i=t)})).on("end.style."+e,te(e)):"function"==typeof t?this.styleTween(e,(u=e,s=e7(this,"style."+e,t),function(){var e=W(this,u),t=s(this),n=t+"";return null==t&&(this.style.removeProperty(u),n=t=W(this,u)),e===n?null:e===l&&n===c?d:(c=n,d=k(l=e,t))})).each((h=this._id,w="end."+(m="style."+(f=e)),function(){var e=eG(this,h),t=e.on,n=null==e.value[m]?v||(v=te(f)):void 0;(t!==p||y!==n)&&(g=(p=t).copy()).on(w,y=n),e.on=g})):this.styleTween(e,(b=e,E=t+"",function(){var e=W(this,b);return e===E?null:e===_?x:x=k(_=e,t)}),n).on("end.style."+e,null)},styleTween:function(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==t)return this.tween(r,null);if("function"!=typeof t)throw Error();return this.tween(r,function(e,t,n){var r,o;function i(){var i=t.apply(this,arguments);return i!==o&&(r=(o=i)&&function(t){this.style.setProperty(e,i.call(this,t),n)}),r}return i._value=t,i}(e,t,null==n?"":n))},text:function(e){var t,n;return this.tween("text","function"==typeof e?(t=e7(this,"text",e),function(){var e=t(this);this.textContent=null==e?"":e}):(n=null==e?"":e+"",function(){this.textContent=n}))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw Error();return this.tween(t,function(e){var t,n;function r(){var r=e.apply(this,arguments);return r!==n&&(t=(n=r)&&function(e){this.textContent=r.call(this,e)}),t}return r._value=e,r}(e))},remove:function(){var e;return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r,o=eU(this.node(),n).tween,i=0,a=o.length;i<a;++i)if((r=o[i]).name===e)return r.value;return null}return this.each((null==t?function(e,t){var n,r;return function(){var o=eG(this,e),i=o.tween;if(i!==n){r=n=i;for(var a=0,u=r.length;a<u;++a)if(r[a].name===t){(r=r.slice()).splice(a,1);break}}o.tween=r}}:function(e,t,n){var r,o;if("function"!=typeof n)throw Error();return function(){var i=eG(this,e),a=i.tween;if(a!==r){o=(r=a).slice();for(var u={name:t,value:n},s=0,l=o.length;s<l;++s)if(o[s].name===t){o[s]=u;break}s===l&&o.push(u)}i.tween=o}})(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){eX(this,e).delay=+t.apply(this,arguments)}}:function(e,t){return t=+t,function(){eX(this,e).delay=t}})(t,e)):eU(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){eG(this,e).duration=+t.apply(this,arguments)}}:function(e,t){return t=+t,function(){eG(this,e).duration=t}})(t,e)):eU(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw Error();return function(){eG(this,e).ease=t}}(t,e)):eU(this.node(),t).ease},easeVarying:function(e){var t;if("function"!=typeof e)throw Error();return this.each((t=this._id,function(){var n=e.apply(this,arguments);if("function"!=typeof n)throw Error();eG(this,t).ease=n}))},end:function(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,a){var u={value:a},s={value:function(){0==--o&&i()}};n.each(function(){var n=eG(this,r),o=n.on;o!==e&&((t=(e=o).copy())._.cancel.push(u),t._.interrupt.push(u),t._.end.push(s)),n.on=t}),0===o&&i()})},[Symbol.iterator]:tr[Symbol.iterator]};var to={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};es.prototype.interrupt=function(e){return this.each(function(){eZ(this,e)})},es.prototype.transition=function(e){var t,n;e instanceof tn?(t=e._id,e=e._name):(t=++tt,(n=to).time=e$(),e=null==e?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var a,u=r[i],s=u.length,l=0;l<s;++l)(a=u[l])&&eq(a,e,t,l,u,n||function(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw Error(`transition ${t} not found`);return n}(a,t));return new tn(r,this._parents,e,t)};var ti=e=>()=>e;function ta(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function tu(e,t,n){this.k=e,this.x=t,this.y=n}tu.prototype={constructor:tu,scale:function(e){return 1===e?this:new tu(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new tu(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var ts=new tu(1,0,0);function tl(e){for(;!e.__zoom;)if(!(e=e.parentNode))return ts;return e.__zoom}function tc(e){e.stopImmediatePropagation()}function td(e){e.preventDefault(),e.stopImmediatePropagation()}function th(e){return(!e.ctrlKey||"wheel"===e.type)&&!e.button}function tf(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function tp(){return this.__zoom||ts}function tg(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function ty(){return navigator.maxTouchPoints||"ontouchstart"in this}function tv(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function tm(){var e,t,n,r=th,o=tf,i=tv,a=tg,u=ty,s=[0,1/0],l=[[-1/0,-1/0],[1/0,1/0]],c=250,d=ej,h=w("start","zoom","end"),f=0,p=10;function g(e){e.property("__zoom",tp).on("wheel.zoom",E,{passive:!1}).on("mousedown.zoom",k).on("dblclick.zoom",O).filter(u).on("touchstart.zoom",M).on("touchmove.zoom",j).on("touchend.zoom touchcancel.zoom",N).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(e,t){return(t=Math.max(s[0],Math.min(s[1],t)))===e.k?e:new tu(t,e.x,e.y)}function v(e,t,n){var r=t[0]-n[0]*e.k,o=t[1]-n[1]*e.k;return r===e.x&&o===e.y?e:new tu(e.k,r,o)}function m(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function b(e,t,n,r){e.on("start.zoom",function(){_(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){_(this,arguments).event(r).end()}).tween("zoom",function(){var e=arguments,i=_(this,e).event(r),a=o.apply(this,e),u=null==n?m(a):"function"==typeof n?n.apply(this,e):n,s=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),l=this.__zoom,c="function"==typeof t?t.apply(this,e):t,h=d(l.invert(u).concat(s/l.k),c.invert(u).concat(s/c.k));return function(e){if(1===e)e=c;else{var t=h(e),n=s/t[2];e=new tu(n,u[0]-t[0]*n,u[1]-t[1]*n)}i.zoom(null,e)}})}function _(e,t,n){return!n&&e.__zooming||new x(e,t)}function x(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=o.apply(e,t),this.taps=0}function E(e,...t){if(r.apply(this,arguments)){var n=_(this,t).event(e),o=this.__zoom,u=Math.max(s[0],Math.min(s[1],o.k*Math.pow(2,a.apply(this,arguments)))),c=ec(e);if(n.wheel)(n.mouse[0][0]!==c[0]||n.mouse[0][1]!==c[1])&&(n.mouse[1]=o.invert(n.mouse[0]=c)),clearTimeout(n.wheel);else{if(o.k===u)return;n.mouse=[c,o.invert(c)],eZ(this),n.start()}td(e),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",i(v(y(o,u),n.mouse[0],n.mouse[1]),n.extent,l))}}function k(e,...t){if(!n&&r.apply(this,arguments)){var o=e.currentTarget,a=_(this,t,!0).event(e),u=el(e.view).on("mousemove.zoom",function(e){if(td(e),!a.moved){var t=e.clientX-c,n=e.clientY-d;a.moved=t*t+n*n>f}a.event(e).zoom("mouse",i(v(a.that.__zoom,a.mouse[0]=ec(e,o),a.mouse[1]),a.extent,l))},!0).on("mouseup.zoom",function(e){u.on("mousemove.zoom mouseup.zoom",null),ey(e.view,a.moved),td(e),a.event(e).end()},!0),s=ec(e,o),c=e.clientX,d=e.clientY;eg(e.view),tc(e),a.mouse=[s,this.__zoom.invert(s)],eZ(this),a.start()}}function O(e,...t){if(r.apply(this,arguments)){var n=this.__zoom,a=ec(e.changedTouches?e.changedTouches[0]:e,this),u=n.invert(a),s=n.k*(e.shiftKey?.5:2),d=i(v(y(n,s),a,u),o.apply(this,t),l);td(e),c>0?el(this).transition().duration(c).call(b,d,a,e):el(this).call(g.transform,d,a,e)}}function M(n,...o){if(r.apply(this,arguments)){var i,a,u,s,l=n.touches,c=l.length,d=_(this,o,n.changedTouches.length===c).event(n);for(tc(n),a=0;a<c;++a)s=[s=ec(u=l[a],this),this.__zoom.invert(s),u.identifier],d.touch0?d.touch1||d.touch0[2]===s[2]||(d.touch1=s,d.taps=0):(d.touch0=s,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=s[0],e=setTimeout(function(){e=null},500)),eZ(this),d.start())}}function j(e,...t){if(this.__zooming){var n,r,o,a,u=_(this,t).event(e),s=e.changedTouches,c=s.length;for(td(e),n=0;n<c;++n)o=ec(r=s[n],this),u.touch0&&u.touch0[2]===r.identifier?u.touch0[0]=o:u.touch1&&u.touch1[2]===r.identifier&&(u.touch1[0]=o);if(r=u.that.__zoom,u.touch1){var d=u.touch0[0],h=u.touch0[1],f=u.touch1[0],p=u.touch1[1],g=(g=f[0]-d[0])*g+(g=f[1]-d[1])*g,m=(m=p[0]-h[0])*m+(m=p[1]-h[1])*m;r=y(r,Math.sqrt(g/m)),o=[(d[0]+f[0])/2,(d[1]+f[1])/2],a=[(h[0]+p[0])/2,(h[1]+p[1])/2]}else{if(!u.touch0)return;o=u.touch0[0],a=u.touch0[1]}u.zoom("touch",i(v(r,o,a),u.extent,l))}}function N(e,...r){if(this.__zooming){var o,i,a=_(this,r).event(e),u=e.changedTouches,s=u.length;for(tc(e),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),o=0;o<s;++o)i=u[o],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=ec(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<p)){var l=el(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return g.transform=function(e,t,n,r){var o=e.selection?e.selection():e;o.property("__zoom",tp),e!==o?b(e,t,n,r):o.interrupt().each(function(){_(this,arguments).event(r).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()})},g.scaleBy=function(e,t,n,r){g.scaleTo(e,function(){var e=this.__zoom.k,n="function"==typeof t?t.apply(this,arguments):t;return e*n},n,r)},g.scaleTo=function(e,t,n,r){g.transform(e,function(){var e=o.apply(this,arguments),r=this.__zoom,a=null==n?m(e):"function"==typeof n?n.apply(this,arguments):n,u=r.invert(a),s="function"==typeof t?t.apply(this,arguments):t;return i(v(y(r,s),a,u),e,l)},n,r)},g.translateBy=function(e,t,n,r){g.transform(e,function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),o.apply(this,arguments),l)},null,r)},g.translateTo=function(e,t,n,r,a){g.transform(e,function(){var e=o.apply(this,arguments),a=this.__zoom,u=null==r?m(e):"function"==typeof r?r.apply(this,arguments):r;return i(ts.translate(u[0],u[1]).scale(a.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,l)},r,a)},x.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=el(this.that).datum();h.call(e,this.that,new ta(e,{sourceEvent:this.sourceEvent,target:g,type:e,transform:this.that.__zoom,dispatch:h}),t)}},g.wheelDelta=function(e){return arguments.length?(a="function"==typeof e?e:ti(+e),g):a},g.filter=function(e){return arguments.length?(r="function"==typeof e?e:ti(!!e),g):r},g.touchable=function(e){return arguments.length?(u="function"==typeof e?e:ti(!!e),g):u},g.extent=function(e){return arguments.length?(o="function"==typeof e?e:ti([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),g):o},g.scaleExtent=function(e){return arguments.length?(s[0]=+e[0],s[1]=+e[1],g):[s[0],s[1]]},g.translateExtent=function(e){return arguments.length?(l[0][0]=+e[0][0],l[1][0]=+e[1][0],l[0][1]=+e[0][1],l[1][1]=+e[1][1],g):[[l[0][0],l[0][1]],[l[1][0],l[1][1]]]},g.constrain=function(e){return arguments.length?(i=e,g):i},g.duration=function(e){return arguments.length?(c=+e,g):c},g.interpolate=function(e){return arguments.length?(d=e,g):d},g.on=function(){var e=h.on.apply(h,arguments);return e===h?g:e},g.clickDistance=function(e){return arguments.length?(f=(e=+e)*e,g):Math.sqrt(f)},g.tapDistance=function(e){return arguments.length?(p=+e,g):p},g}tl.prototype=tu.prototype;let tw={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:e=>`The old edge with id=${e} does not exist.`,error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,{id:t,sourceHandle:n,targetHandle:r})=>`Couldn't create edge for ${e} handle id: "${"source"===e?n:r}", edge id: ${t}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error013:(e="react")=>`It seems that you haven't loaded the styles. Please import '@xyflow/${e}/dist/style.css' or base.css to make sure everything is working properly.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},tb=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],t_=["Enter"," ","Escape"];(r=d||(d={})).Strict="strict",r.Loose="loose",(o=h||(h={})).Free="free",o.Vertical="vertical",o.Horizontal="horizontal",(i=f||(f={})).Partial="partial",i.Full="full";let tx={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};(a=p||(p={})).Bezier="default",a.Straight="straight",a.Step="step",a.SmoothStep="smoothstep",a.SimpleBezier="simplebezier",(u=g||(g={})).Arrow="arrow",u.ArrowClosed="arrowclosed",(s=y||(y={})).Left="left",s.Top="top",s.Right="right",s.Bottom="bottom";let tE={[y.Left]:y.Right,[y.Right]:y.Left,[y.Top]:y.Bottom,[y.Bottom]:y.Top};function tk(e,t){if(!e&&!t)return!0;if(!e||!t||e.size!==t.size)return!1;if(!e.size&&!t.size)return!0;for(let n of e.keys())if(!t.has(n))return!1;return!0}function tO(e,t,n){if(!n)return;let r=[];e.forEach((e,n)=>{t?.has(n)||r.push(e)}),r.length&&n(r)}function tM(e){return null===e?null:e?"valid":"invalid"}let tj=e=>"id"in e&&"source"in e&&"target"in e,tN=e=>"id"in e&&"position"in e&&!("source"in e)&&!("target"in e),tI=e=>"id"in e&&"internals"in e&&!("source"in e)&&!("target"in e),tT=(e,t,n)=>{if(!e.id)return[];let r=new Set;return n.forEach(t=>{t.source===e.id&&r.add(t.target)}),t.filter(e=>r.has(e.id))},tA=(e,t,n)=>{if(!e.id)return[];let r=new Set;return n.forEach(t=>{t.target===e.id&&r.add(t.source)}),t.filter(e=>r.has(e.id))},tC=(e,t=[0,0])=>{let{width:n,height:r}=t8(e),o=e.origin??t,i=n*o[0],a=r*o[1];return{x:e.position.x-i,y:e.position.y-a}},tS=(e,t={nodeOrigin:[0,0]})=>0===e.length?{x:0,y:0,width:0,height:0}:tX(e.reduce((e,n)=>{let r="string"==typeof n,o=t.nodeLookup||r?void 0:n;return t.nodeLookup&&(o=r?t.nodeLookup.get(n):tI(n)?n:t.nodeLookup.get(n.id)),tY(e,o?tU(o,t.nodeOrigin):{x:0,y:0,x2:0,y2:0})},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})),tP=(e,t={})=>{if(0===e.size)return{x:0,y:0,width:0,height:0};let n={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return e.forEach(e=>{if(void 0===t.filter||t.filter(e)){let t=tU(e);n=tY(n,t)}}),tX(n)},tR=(e,t,[n,r,o]=[0,0,1],i=!1,a=!1)=>{let u={...t2(t,[n,r,o]),width:t.width/o,height:t.height/o},s=[];for(let t of e.values()){let{measured:e,selectable:n=!0,hidden:r=!1}=t;if(a&&!n||r)continue;let o=e.width??t.width??t.initialWidth??null,l=e.height??t.height??t.initialHeight??null,c=tK(u,tG(t)),d=(o??0)*(l??0),h=i&&c>0;(!t.internals.handleBounds||h||c>=d||t.dragging)&&s.push(t)}return s},t$=(e,t)=>{let n=new Set;return e.forEach(e=>{n.add(e.id)}),t.filter(e=>n.has(e.source)||n.has(e.target))};async function tL({nodes:e,width:t,height:n,panZoom:r,minZoom:o,maxZoom:i},a){if(0===e.size)return Promise.resolve(!0);let u=t4(tP(function(e,t){let n=new Map,r=t?.nodes?new Set(t.nodes.map(e=>e.id)):null;return e.forEach(e=>{e.measured.width&&e.measured.height&&(t?.includeHiddenNodes||!e.hidden)&&(!r||r.has(e.id))&&n.set(e.id,e)}),n}(e,a)),t,n,a?.minZoom??o,a?.maxZoom??i,a?.padding??.1);return await r.setViewport(u,{duration:a?.duration}),Promise.resolve(!0)}function tz({nodeId:e,nextPosition:t,nodeLookup:n,nodeOrigin:r=[0,0],nodeExtent:o,onError:i}){let a=n.get(e),u=a.parentId?n.get(a.parentId):void 0,{x:s,y:l}=u?u.internals.positionAbsolute:{x:0,y:0},c=a.origin??r,d=o;if("parent"!==a.extent||a.expandParent)u&&t3(a.extent)&&(d=[[a.extent[0][0]+s,a.extent[0][1]+l],[a.extent[1][0]+s,a.extent[1][1]+l]]);else if(u){let e=u.measured.width,t=u.measured.height;e&&t&&(d=[[s,l],[s+e,l+t]])}else i?.("005",tw.error005());let h=t3(d)?tB(t,d,a.measured):t;return(void 0===a.measured.width||void 0===a.measured.height)&&i?.("015",tw.error015()),{position:{x:h.x-s+(a.measured.width??0)*c[0],y:h.y-l+(a.measured.height??0)*c[1]},positionAbsolute:h}}async function tF({nodesToRemove:e=[],edgesToRemove:t=[],nodes:n,edges:r,onBeforeDelete:o}){let i=new Set(e.map(e=>e.id)),a=[];for(let e of n){if(!1===e.deletable)continue;let t=i.has(e.id),n=!t&&e.parentId&&a.find(t=>t.id===e.parentId);(t||n)&&a.push(e)}let u=new Set(t.map(e=>e.id)),s=r.filter(e=>!1!==e.deletable),l=t$(a,s);for(let e of s)u.has(e.id)&&!l.find(t=>t.id===e.id)&&l.push(e);if(!o)return{edges:l,nodes:a};let c=await o({nodes:a,edges:l});return"boolean"==typeof c?c?{edges:l,nodes:a}:{edges:[],nodes:[]}:c}let tD=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),tB=(e={x:0,y:0},t,n)=>({x:tD(e.x,t[0][0],t[1][0]-(n?.width??0)),y:tD(e.y,t[0][1],t[1][1]-(n?.height??0))});function tW(e,t,n){let{width:r,height:o}=t8(n),{x:i,y:a}=n.internals.positionAbsolute;return tB(e,[[i,a],[i+r,a+o]],t)}let tV=(e,t,n)=>e<t?tD(Math.abs(e-t),1,t)/t:e>n?-tD(Math.abs(e-n),1,t)/t:0,tH=(e,t,n=15,r=40)=>[tV(e.x,r,t.width-r)*n,tV(e.y,r,t.height-r)*n],tY=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),tq=({x:e,y:t,width:n,height:r})=>({x:e,y:t,x2:e+n,y2:t+r}),tX=({x:e,y:t,x2:n,y2:r})=>({x:e,y:t,width:n-e,height:r-t}),tG=(e,t=[0,0])=>{let{x:n,y:r}=tI(e)?e.internals.positionAbsolute:tC(e,t);return{x:n,y:r,width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}},tU=(e,t=[0,0])=>{let{x:n,y:r}=tI(e)?e.internals.positionAbsolute:tC(e,t);return{x:n,y:r,x2:n+(e.measured?.width??e.width??e.initialWidth??0),y2:r+(e.measured?.height??e.height??e.initialHeight??0)}},tZ=(e,t)=>tX(tY(tq(e),tq(t))),tK=(e,t)=>Math.ceil(Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x))*Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y))),tQ=e=>tJ(e.width)&&tJ(e.height)&&tJ(e.x)&&tJ(e.y),tJ=e=>!isNaN(e)&&isFinite(e),t0=(e,t)=>{},t1=(e,t=[1,1])=>({x:t[0]*Math.round(e.x/t[0]),y:t[1]*Math.round(e.y/t[1])}),t2=({x:e,y:t},[n,r,o],i=!1,a=[1,1])=>{let u={x:(e-n)/o,y:(t-r)/o};return i?t1(u,a):u},t5=({x:e,y:t},[n,r,o])=>({x:e*o+n,y:t*o+r});function t7(e,t){if("number"==typeof e)return Math.floor((t-t/(1+e))*.5);if("string"==typeof e&&e.endsWith("px")){let t=parseFloat(e);if(!Number.isNaN(t))return Math.floor(t)}if("string"==typeof e&&e.endsWith("%")){let n=parseFloat(e);if(!Number.isNaN(n))return Math.floor(t*n*.01)}return console.error(`[React Flow] The padding value "${e}" is invalid. Please provide a number or a string with a valid unit (px or %).`),0}let t4=(e,t,n,r,o,i)=>{let a=function(e,t,n){if("string"==typeof e||"number"==typeof e){let r=t7(e,n),o=t7(e,t);return{top:r,right:o,bottom:r,left:o,x:2*o,y:2*r}}if("object"==typeof e){let r=t7(e.top??e.y??0,n),o=t7(e.bottom??e.y??0,n),i=t7(e.left??e.x??0,t),a=t7(e.right??e.x??0,t);return{top:r,right:a,bottom:o,left:i,x:i+a,y:r+o}}return{top:0,right:0,bottom:0,left:0,x:0,y:0}}(i,t,n),u=tD(Math.min((t-a.x)/e.width,(n-a.y)/e.height),r,o),s=e.x+e.width/2,l=e.y+e.height/2,c=t/2-s*u,d=n/2-l*u,h=function(e,t,n,r,o,i){let{x:a,y:u}=t5(e,[t,n,r]),{x:s,y:l}=t5({x:e.x+e.width,y:e.y+e.height},[t,n,r]);return{left:Math.floor(a),top:Math.floor(u),right:Math.floor(o-s),bottom:Math.floor(i-l)}}(e,c,d,u,t,n),f={left:Math.min(h.left-a.left,0),top:Math.min(h.top-a.top,0),right:Math.min(h.right-a.right,0),bottom:Math.min(h.bottom-a.bottom,0)};return{x:c-f.left+f.right,y:d-f.top+f.bottom,zoom:u}},t6=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0;function t3(e){return void 0!==e&&"parent"!==e}function t8(e){return{width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}}function t9(e){return(e.measured?.width??e.width??e.initialWidth)!==void 0&&(e.measured?.height??e.height??e.initialHeight)!==void 0}function ne(e,t={width:0,height:0},n,r,o){let i={...e},a=r.get(n);if(a){let e=a.origin||o;i.x+=a.internals.positionAbsolute.x-(t.width??0)*e[0],i.y+=a.internals.positionAbsolute.y-(t.height??0)*e[1]}return i}function nt(e,t){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}function nn(){let e,t;return{promise:new Promise((n,r)=>{e=n,t=r}),resolve:e,reject:t}}function nr(e,{snapGrid:t=[0,0],snapToGrid:n=!1,transform:r,containerBounds:o}){let{x:i,y:a}=nl(e),u=t2({x:i-(o?.left??0),y:a-(o?.top??0)},r),{x:s,y:l}=n?t1(u,t):u;return{xSnapped:s,ySnapped:l,...u}}let no=e=>({width:e.offsetWidth,height:e.offsetHeight}),ni=e=>e?.getRootNode?.()||window?.document,na=["INPUT","SELECT","TEXTAREA"];function nu(e){let t=e.composedPath?.()?.[0]||e.target;return t?.nodeType===1&&(na.includes(t.nodeName)||t.hasAttribute("contenteditable")||!!t.closest(".nokey"))}let ns=e=>"clientX"in e,nl=(e,t)=>{let n=ns(e),r=n?e.clientX:e.touches?.[0].clientX,o=n?e.clientY:e.touches?.[0].clientY;return{x:r-(t?.left??0),y:o-(t?.top??0)}},nc=(e,t,n,r,o)=>{let i=t.querySelectorAll(`.${e}`);return i&&i.length?Array.from(i).map(t=>{let i=t.getBoundingClientRect();return{id:t.getAttribute("data-handleid"),type:e,nodeId:o,position:t.getAttribute("data-handlepos"),x:(i.left-n.left)/r,y:(i.top-n.top)/r,...no(t)}}):null};function nd({sourceX:e,sourceY:t,targetX:n,targetY:r,sourceControlX:o,sourceControlY:i,targetControlX:a,targetControlY:u}){let s=.125*e+.375*o+.375*a+.125*n,l=.125*t+.375*i+.375*u+.125*r;return[s,l,Math.abs(s-e),Math.abs(l-t)]}function nh(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function nf({pos:e,x1:t,y1:n,x2:r,y2:o,c:i}){switch(e){case y.Left:return[t-nh(t-r,i),n];case y.Right:return[t+nh(r-t,i),n];case y.Top:return[t,n-nh(n-o,i)];case y.Bottom:return[t,n+nh(o-n,i)]}}function np({sourceX:e,sourceY:t,sourcePosition:n=y.Bottom,targetX:r,targetY:o,targetPosition:i=y.Top,curvature:a=.25}){let[u,s]=nf({pos:n,x1:e,y1:t,x2:r,y2:o,c:a}),[l,c]=nf({pos:i,x1:r,y1:o,x2:e,y2:t,c:a}),[d,h,f,p]=nd({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:u,sourceControlY:s,targetControlX:l,targetControlY:c});return[`M${e},${t} C${u},${s} ${l},${c} ${r},${o}`,d,h,f,p]}function ng({sourceX:e,sourceY:t,targetX:n,targetY:r}){let o=Math.abs(n-e)/2,i=Math.abs(r-t)/2;return[n<e?n+o:n-o,r<t?r+i:r-i,o,i]}function ny({sourceNode:e,targetNode:t,selected:n=!1,zIndex:r=0,elevateOnSelect:o=!1}){if(!o)return r;let i=n||t.selected||e.selected,a=Math.max(e.internals.z||0,t.internals.z||0,1e3);return r+(i?a:0)}function nv({sourceNode:e,targetNode:t,width:n,height:r,transform:o}){let i=tY(tU(e),tU(t));return i.x===i.x2&&(i.x2+=1),i.y===i.y2&&(i.y2+=1),tK({x:-o[0]/o[2],y:-o[1]/o[2],width:n/o[2],height:r/o[2]},tX(i))>0}let nm=({source:e,sourceHandle:t,target:n,targetHandle:r})=>`xy-edge__${e}${t||""}-${n}${r||""}`,nw=(e,t)=>t.some(t=>t.source===e.source&&t.target===e.target&&(t.sourceHandle===e.sourceHandle||!t.sourceHandle&&!e.sourceHandle)&&(t.targetHandle===e.targetHandle||!t.targetHandle&&!e.targetHandle)),nb=(e,t)=>{let n;return e.source&&e.target?nw(n=tj(e)?{...e}:{...e,id:nm(e)},t)?t:(null===n.sourceHandle&&delete n.sourceHandle,null===n.targetHandle&&delete n.targetHandle,t.concat(n)):(t0("006",tw.error006()),t)},n_=(e,t,n,r={shouldReplaceId:!0})=>{let{id:o,...i}=e;if(!t.source||!t.target)return t0("006",tw.error006()),n;if(!n.find(t=>t.id===e.id))return t0("007",tw.error007(o)),n;let a={...i,id:r.shouldReplaceId?nm(t):o,source:t.source,target:t.target,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle};return n.filter(e=>e.id!==o).concat(a)};function nx({sourceX:e,sourceY:t,targetX:n,targetY:r}){let[o,i,a,u]=ng({sourceX:e,sourceY:t,targetX:n,targetY:r});return[`M ${e},${t}L ${n},${r}`,o,i,a,u]}let nE={[y.Left]:{x:-1,y:0},[y.Right]:{x:1,y:0},[y.Top]:{x:0,y:-1},[y.Bottom]:{x:0,y:1}},nk=({source:e,sourcePosition:t=y.Bottom,target:n})=>t===y.Left||t===y.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},nO=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function nM({sourceX:e,sourceY:t,sourcePosition:n=y.Bottom,targetX:r,targetY:o,targetPosition:i=y.Top,borderRadius:a=5,centerX:u,centerY:s,offset:l=20}){let[c,d,h,f,p]=function({source:e,sourcePosition:t=y.Bottom,target:n,targetPosition:r=y.Top,center:o,offset:i}){let a,u;let s=nE[t],l=nE[r],c={x:e.x+s.x*i,y:e.y+s.y*i},d={x:n.x+l.x*i,y:n.y+l.y*i},h=nk({source:c,sourcePosition:t,target:d}),f=0!==h.x?"x":"y",p=h[f],g=[],v={x:0,y:0},m={x:0,y:0},[w,b,_,x]=ng({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(s[f]*l[f]==-1){a=o.x??w,u=o.y??b;let e=[{x:a,y:c.y},{x:a,y:d.y}],t=[{x:c.x,y:u},{x:d.x,y:u}];g=s[f]===p?"x"===f?e:t:"x"===f?t:e}else{let o=[{x:c.x,y:d.y}],h=[{x:d.x,y:c.y}];if(g="x"===f?s.x===p?h:o:s.y===p?o:h,t===r){let t=Math.abs(e[f]-n[f]);if(t<=i){let r=Math.min(i-1,i-t);s[f]===p?v[f]=(c[f]>e[f]?-1:1)*r:m[f]=(d[f]>n[f]?-1:1)*r}}if(t!==r){let e="x"===f?"y":"x",t=s[f]===l[e],n=c[e]>d[e],r=c[e]<d[e];(1===s[f]&&(!t&&n||t&&r)||1!==s[f]&&(!t&&r||t&&n))&&(g="x"===f?o:h)}let y={x:c.x+v.x,y:c.y+v.y},w={x:d.x+m.x,y:d.y+m.y};Math.max(Math.abs(y.x-g[0].x),Math.abs(w.x-g[0].x))>=Math.max(Math.abs(y.y-g[0].y),Math.abs(w.y-g[0].y))?(a=(y.x+w.x)/2,u=g[0].y):(a=g[0].x,u=(y.y+w.y)/2)}return[[e,{x:c.x+v.x,y:c.y+v.y},...g,{x:d.x+m.x,y:d.y+m.y},n],a,u,_,x]}({source:{x:e,y:t},sourcePosition:n,target:{x:r,y:o},targetPosition:i,center:{x:u,y:s},offset:l});return[c.reduce((e,t,n)=>e+(n>0&&n<c.length-1?function(e,t,n,r){let o=Math.min(nO(e,t)/2,nO(t,n)/2,r),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a){let t=e.x<n.x?-1:1,r=e.y<n.y?1:-1;return`L ${i+o*t},${a}Q ${i},${a} ${i},${a+o*r}`}let u=e.x<n.x?1:-1,s=e.y<n.y?-1:1;return`L ${i},${a+o*s}Q ${i},${a} ${i+o*u},${a}`}(c[n-1],t,c[n+1],a):`${0===n?"M":"L"}${t.x} ${t.y}`),""),d,h,f,p]}function nj(e){return e&&!!(e.internals.handleBounds||e.handles?.length)&&!!(e.measured.width||e.width||e.initialWidth)}function nN(e){let{sourceNode:t,targetNode:n}=e;if(!nj(t)||!nj(n))return null;let r=t.internals.handleBounds||nI(t.handles),o=n.internals.handleBounds||nI(n.handles),i=nA(r?.source??[],e.sourceHandle),a=nA(e.connectionMode===d.Strict?o?.target??[]:(o?.target??[]).concat(o?.source??[]),e.targetHandle);if(!i||!a)return e.onError?.("008",tw.error008(i?"target":"source",{id:e.id,sourceHandle:e.sourceHandle,targetHandle:e.targetHandle})),null;let u=i?.position||y.Bottom,s=a?.position||y.Top,l=nT(t,i,u),c=nT(n,a,s);return{sourceX:l.x,sourceY:l.y,targetX:c.x,targetY:c.y,sourcePosition:u,targetPosition:s}}function nI(e){if(!e)return null;let t=[],n=[];for(let r of e)r.width=r.width??1,r.height=r.height??1,"source"===r.type?t.push(r):"target"===r.type&&n.push(r);return{source:t,target:n}}function nT(e,t,n=y.Left,r=!1){let o=(t?.x??0)+e.internals.positionAbsolute.x,i=(t?.y??0)+e.internals.positionAbsolute.y,{width:a,height:u}=t??t8(e);if(r)return{x:o+a/2,y:i+u/2};switch(t?.position??n){case y.Top:return{x:o+a/2,y:i};case y.Right:return{x:o+a,y:i+u/2};case y.Bottom:return{x:o+a/2,y:i+u};case y.Left:return{x:o,y:i+u/2}}}function nA(e,t){return e&&(t?e.find(e=>e.id===t):e[0])||null}function nC(e,t){if(!e)return"";if("string"==typeof e)return e;let n=t?`${t}__`:"";return`${n}${Object.keys(e).sort().map(t=>`${t}=${e[t]}`).join("&")}`}function nS(e,{id:t,defaultColor:n,defaultMarkerStart:r,defaultMarkerEnd:o}){let i=new Set;return e.reduce((e,a)=>([a.markerStart||r,a.markerEnd||o].forEach(r=>{if(r&&"object"==typeof r){let o=nC(r,t);i.has(o)||(e.push({id:o,color:r.color||n,...r}),i.add(o))}}),e),[]).sort((e,t)=>e.id.localeCompare(t.id))}function nP(e,t,n,r,o){let i=.5;"start"===o?i=0:"end"===o&&(i=1);let a=[(e.x+e.width*i)*t.zoom+t.x,e.y*t.zoom+t.y-r],u=[-100*i,-100];switch(n){case y.Right:a=[(e.x+e.width)*t.zoom+t.x+r,(e.y+e.height*i)*t.zoom+t.y],u=[0,-100*i];break;case y.Bottom:a[1]=(e.y+e.height)*t.zoom+t.y+r,u[1]=0;break;case y.Left:a=[e.x*t.zoom+t.x-r,(e.y+e.height*i)*t.zoom+t.y],u=[-100,-100*i]}return`translate(${a[0]}px, ${a[1]}px) translate(${u[0]}%, ${u[1]}%)`}let nR={nodeOrigin:[0,0],nodeExtent:tb,elevateNodesOnSelect:!0,defaults:{}},n$={...nR,checkEquality:!0};function nL(e,t){let n={...e};for(let e in t)void 0!==t[e]&&(n[e]=t[e]);return n}function nz(e,t,n){let r=nL(nR,n);for(let n of e.values())if(n.parentId)nD(n,e,t,r);else{let e=tB(tC(n,r.nodeOrigin),t3(n.extent)?n.extent:r.nodeExtent,t8(n));n.internals.positionAbsolute=e}}function nF(e,t,n,r){let o=nL(n$,r),i=e.length>0,a=new Map(t),u=o?.elevateNodesOnSelect?1e3:0;for(let s of(t.clear(),n.clear(),e)){let e=a.get(s.id);if(o.checkEquality&&s===e?.internals.userNode)t.set(s.id,e);else{let n=tB(tC(s,o.nodeOrigin),t3(s.extent)?s.extent:o.nodeExtent,t8(s));e={...o.defaults,...s,measured:{width:s.measured?.width,height:s.measured?.height},internals:{positionAbsolute:n,handleBounds:s.measured?e?.internals.handleBounds:void 0,z:nB(s,u),userNode:s}},t.set(s.id,e)}void 0!==e.measured&&void 0!==e.measured.width&&void 0!==e.measured.height||e.hidden||(i=!1),s.parentId&&nD(e,t,n,r)}return i}function nD(e,t,n,r){let{elevateNodesOnSelect:o,nodeOrigin:i,nodeExtent:a}=nL(nR,r),u=e.parentId,s=t.get(u);if(!s){console.warn(`Parent node ${u} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);return}!function(e,t){if(!e.parentId)return;let n=t.get(e.parentId);n?n.set(e.id,e):t.set(e.parentId,new Map([[e.id,e]]))}(e,n);let{x:l,y:c,z:d}=function(e,t,n,r,o){let{x:i,y:a}=t.internals.positionAbsolute,u=t8(e),s=tC(e,n),l=t3(e.extent)?tB(s,e.extent,u):s,c=tB({x:i+l.x,y:a+l.y},r,u);"parent"===e.extent&&(c=tW(c,u,t));let d=nB(e,o),h=t.internals.z??0;return{x:c.x,y:c.y,z:h>d?h:d}}(e,s,i,a,o?1e3:0),{positionAbsolute:h}=e.internals,f=l!==h.x||c!==h.y;(f||d!==e.internals.z)&&t.set(e.id,{...e,internals:{...e.internals,positionAbsolute:f?{x:l,y:c}:h,z:d}})}function nB(e,t){return(tJ(e.zIndex)?e.zIndex:0)+(e.selected?t:0)}function nW(e,t,n,r=[0,0]){let o=[],i=new Map;for(let n of e){let e=t.get(n.parentId);if(!e)continue;let r=tZ(i.get(n.parentId)?.expandedRect??tG(e),n.rect);i.set(n.parentId,{expandedRect:r,parent:e})}return i.size>0&&i.forEach(({expandedRect:t,parent:i},a)=>{let u=i.internals.positionAbsolute,s=t8(i),l=i.origin??r,c=t.x<u.x?Math.round(Math.abs(u.x-t.x)):0,d=t.y<u.y?Math.round(Math.abs(u.y-t.y)):0,h=Math.max(s.width,Math.round(t.width)),f=Math.max(s.height,Math.round(t.height)),p=(h-s.width)*l[0],g=(f-s.height)*l[1];(c>0||d>0||p||g)&&(o.push({id:a,type:"position",position:{x:i.position.x-c+p,y:i.position.y-d+g}}),n.get(a)?.forEach(t=>{e.some(e=>e.id===t.id)||o.push({id:t.id,type:"position",position:{x:t.position.x+c,y:t.position.y+d}})})),(s.width<t.width||s.height<t.height||c||d)&&o.push({id:a,type:"dimensions",setAttributes:!0,dimensions:{width:h+(c?l[0]*c-p:0),height:f+(d?l[1]*d-g:0)}})}),o}function nV(e,t,n,r,o,i){let a=r?.querySelector(".xyflow__viewport"),u=!1;if(!a)return{changes:[],updatedInternals:u};let s=[],l=window.getComputedStyle(a),{m22:c}=new window.DOMMatrixReadOnly(l.transform),d=[];for(let r of e.values()){let e=t.get(r.id);if(!e)continue;if(e.hidden){t.set(e.id,{...e,internals:{...e.internals,handleBounds:void 0}}),u=!0;continue}let a=no(r.nodeElement),l=e.measured.width!==a.width||e.measured.height!==a.height;if(a.width&&a.height&&(l||!e.internals.handleBounds||r.force)){let h=r.nodeElement.getBoundingClientRect(),f=t3(e.extent)?e.extent:i,{positionAbsolute:p}=e.internals;e.parentId&&"parent"===e.extent?p=tW(p,a,t.get(e.parentId)):f&&(p=tB(p,f,a));let g={...e,measured:a,internals:{...e.internals,positionAbsolute:p,handleBounds:{source:nc("source",r.nodeElement,h,c,e.id),target:nc("target",r.nodeElement,h,c,e.id)}}};t.set(e.id,g),e.parentId&&nD(g,t,n,{nodeOrigin:o}),u=!0,l&&(s.push({id:e.id,type:"dimensions",dimensions:a}),e.expandParent&&e.parentId&&d.push({id:e.id,parentId:e.parentId,rect:tG(g,o)}))}}if(d.length>0){let e=nW(d,t,n,o);s.push(...e)}return{changes:s,updatedInternals:u}}async function nH({delta:e,panZoom:t,transform:n,translateExtent:r,width:o,height:i}){if(!t||!e.x&&!e.y)return Promise.resolve(!1);let a=await t.setViewportConstrained({x:n[0]+e.x,y:n[1]+e.y,zoom:n[2]},[[0,0],[o,i]],r);return Promise.resolve(!!a&&(a.x!==n[0]||a.y!==n[1]||a.k!==n[2]))}function nY(e,t,n,r,o,i){let a=o,u=r.get(a)||new Map;r.set(a,u.set(n,t)),a=`${o}-${e}`;let s=r.get(a)||new Map;if(r.set(a,s.set(n,t)),i){a=`${o}-${e}-${i}`;let u=r.get(a)||new Map;r.set(a,u.set(n,t))}}function nq(e,t,n){for(let r of(e.clear(),t.clear(),n)){let{source:n,target:o,sourceHandle:i=null,targetHandle:a=null}=r,u={edgeId:r.id,source:n,target:o,sourceHandle:i,targetHandle:a},s=`${n}-${i}--${o}-${a}`;nY("source",u,`${o}-${a}--${n}-${i}`,e,n,i),nY("target",u,s,e,o,a),t.set(r.id,r)}}function nX(e,t){if(null===e||null===t)return!1;let n=Array.isArray(e)?e:[e],r=Array.isArray(t)?t:[t];if(n.length!==r.length)return!1;for(let e=0;e<n.length;e++)if(n[e].id!==r[e].id||n[e].type!==r[e].type||!Object.is(n[e].data,r[e].data))return!1;return!0}function nG(e,t,n){let r=e;do{if(r?.matches?.(t))return!0;if(r===n)break;r=r?.parentElement}while(r);return!1}function nU({nodeId:e,dragItems:t,nodeLookup:n,dragging:r=!0}){let o=[];for(let[e,i]of t){let t=n.get(e)?.internals.userNode;t&&o.push({...t,position:i.position,dragging:r})}if(!e)return[o[0],o];let i=n.get(e)?.internals.userNode;return[i?{...i,position:t.get(e)?.position||i.position,dragging:r}:o[0],o]}function nZ({onNodeMouseDown:e,getStoreItems:t,onDragStart:n,onDrag:r,onDragStop:o}){let i={x:null,y:null},a=0,u=new Map,s=!1,l={x:0,y:0},c=null,d=!1,h=null,f=!1;return{update:function({noDragClassName:p,handleSelector:g,domNode:y,isSelectable:v,nodeId:m,nodeClickDistance:w=0}){function b({x:e,y:n},o){let{nodeLookup:a,nodeExtent:s,snapGrid:l,snapToGrid:c,nodeOrigin:d,onNodeDrag:h,onSelectionDrag:f,onError:p,updateNodePositions:g}=t();i={x:e,y:n};let y=!1,v={x:0,y:0,x2:0,y2:0};for(let[t,r]of(u.size>1&&s&&(v=tq(tP(u))),u)){if(!a.has(t))continue;let o={x:e-r.distance.x,y:n-r.distance.y};c&&(o=t1(o,l));let i=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];if(u.size>1&&s&&!r.extent){let{positionAbsolute:e}=r.internals,t=e.x-v.x+s[0][0],n=e.x+r.measured.width-v.x2+s[1][0];i=[[t,e.y-v.y+s[0][1]],[n,e.y+r.measured.height-v.y2+s[1][1]]]}let{position:h,positionAbsolute:f}=tz({nodeId:t,nextPosition:o,nodeLookup:a,nodeExtent:i,nodeOrigin:d,onError:p});y=y||r.position.x!==h.x||r.position.y!==h.y,r.position=h,r.internals.positionAbsolute=f}if(y&&(g(u,!0),o&&(r||h||!m&&f))){let[e,t]=nU({nodeId:m,dragItems:u,nodeLookup:a});r?.(o,u,e,t),h?.(o,e,t),m||f?.(o,t)}}async function _(){if(!c)return;let{transform:e,panBy:n,autoPanSpeed:r,autoPanOnNodeDrag:o}=t();if(!o){s=!1,cancelAnimationFrame(a);return}let[u,d]=tH(l,c,r);(0!==u||0!==d)&&(i.x=(i.x??0)-u/e[2],i.y=(i.y??0)-d/e[2],await n({x:u,y:d})&&b(i,null)),a=requestAnimationFrame(_)}function x(r){let{nodeLookup:o,multiSelectionActive:a,nodesDraggable:s,transform:l,snapGrid:h,snapToGrid:f,selectNodesOnDrag:p,onNodeDragStart:g,onSelectionDragStart:y,unselectNodesAndEdges:w}=t();d=!0,p&&v||a||!m||o.get(m)?.selected||w(),v&&p&&m&&e?.(m);let b=nr(r.sourceEvent,{transform:l,snapGrid:h,snapToGrid:f,containerBounds:c});if(i=b,(u=function(e,t,n,r){let o=new Map;for(let[i,a]of e)if((a.selected||a.id===r)&&(!a.parentId||!function e(t,n){if(!t.parentId)return!1;let r=n.get(t.parentId);return!!r&&(!!r.selected||e(r,n))}(a,e))&&(a.draggable||t&&void 0===a.draggable)){let t=e.get(i);t&&o.set(i,{id:i,position:t.position||{x:0,y:0},distance:{x:n.x-t.internals.positionAbsolute.x,y:n.y-t.internals.positionAbsolute.y},extent:t.extent,parentId:t.parentId,origin:t.origin,expandParent:t.expandParent,internals:{positionAbsolute:t.internals.positionAbsolute||{x:0,y:0}},measured:{width:t.measured.width??0,height:t.measured.height??0}})}return o}(o,s,b,m)).size>0&&(n||g||!m&&y)){let[e,t]=nU({nodeId:m,dragItems:u,nodeLookup:o});n?.(r.sourceEvent,u,e,t),g?.(r.sourceEvent,e,t),m||y?.(r.sourceEvent,t)}}h=el(y);let E=eE().clickDistance(w).on("start",e=>{let{domNode:n,nodeDragThreshold:r,transform:o,snapGrid:a,snapToGrid:u}=t();c=n?.getBoundingClientRect()||null,f=!1,0===r&&x(e),i=nr(e.sourceEvent,{transform:o,snapGrid:a,snapToGrid:u,containerBounds:c}),l=nl(e.sourceEvent,c)}).on("drag",e=>{let{autoPanOnNodeDrag:n,transform:r,snapGrid:o,snapToGrid:a,nodeDragThreshold:h,nodeLookup:p}=t(),g=nr(e.sourceEvent,{transform:r,snapGrid:o,snapToGrid:a,containerBounds:c});if(("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1||m&&!p.has(m))&&(f=!0),!f){if(!s&&n&&d&&(s=!0,_()),!d){let t=g.xSnapped-(i.x??0),n=g.ySnapped-(i.y??0);Math.sqrt(t*t+n*n)>h&&x(e)}(i.x!==g.xSnapped||i.y!==g.ySnapped)&&u&&d&&(l=nl(e.sourceEvent,c),b(g,e.sourceEvent))}}).on("end",e=>{if(d&&!f&&(s=!1,d=!1,cancelAnimationFrame(a),u.size>0)){let{nodeLookup:n,updateNodePositions:r,onNodeDragStop:i,onSelectionDragStop:a}=t();if(r(u,!1),o||i||!m&&a){let[t,r]=nU({nodeId:m,dragItems:u,nodeLookup:n,dragging:!1});o?.(e.sourceEvent,u,t,r),i?.(e.sourceEvent,t,r),m||a?.(e.sourceEvent,r)}}}).filter(e=>{let t=e.target;return!e.button&&(!p||!nG(t,`.${p}`,y))&&(!g||nG(t,g,y))});h.call(E)},destroy:function(){h?.on(".drag",null)}}}function nK(e,t,n,r,o,i=!1){let a=r.get(e);if(!a)return null;let u="strict"===o?a.internals.handleBounds?.[t]:[...a.internals.handleBounds?.source??[],...a.internals.handleBounds?.target??[]],s=(n?u?.find(e=>e.id===n):u?.[0])??null;return s&&i?{...s,...nT(a,s,s.position,!0)}:s}function nQ(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}let nJ=()=>!0;function n0(e,{handle:t,connectionMode:n,fromNodeId:r,fromHandleId:o,fromType:i,doc:a,lib:u,flowId:s,isValidConnection:l=nJ,nodeLookup:c}){let h="target"===i,f=t?a.querySelector(`.${u}-flow__handle[data-id="${s}-${t?.nodeId}-${t?.id}-${t?.type}"]`):null,{x:p,y:g}=nl(e),y=a.elementFromPoint(p,g),v=y?.classList.contains(`${u}-flow__handle`)?y:f,m={handleDomNode:v,isValid:!1,connection:null,toHandle:null};if(v){let e=nQ(void 0,v),t=v.getAttribute("data-nodeid"),i=v.getAttribute("data-handleid"),a=v.classList.contains("connectable"),u=v.classList.contains("connectableend");if(!t||!e)return m;let s={source:h?t:r,sourceHandle:h?i:o,target:h?r:t,targetHandle:h?o:i};m.connection=s;let f=a&&u&&(n===d.Strict?h&&"source"===e||!h&&"target"===e:t!==r||i!==o);m.isValid=f&&l(s),m.toHandle=nK(t,e,i,c,n,!0)}return m}let n1={onPointerDown:function(e,{connectionMode:t,connectionRadius:n,handleId:r,nodeId:o,edgeUpdaterType:i,isTarget:a,domNode:u,nodeLookup:s,lib:l,autoPanOnConnect:c,flowId:d,panBy:h,cancelConnection:f,onConnectStart:p,onConnect:g,onConnectEnd:v,isValidConnection:m=nJ,onReconnectEnd:w,updateConnection:b,getTransform:_,getFromHandle:x,autoPanSpeed:E}){let k;let O=ni(e.target),M=0,{x:j,y:N}=nl(e),I=nQ(i,O?.elementFromPoint(j,N)),T=u?.getBoundingClientRect();if(!T||!I)return;let A=nK(o,I,r,s,t);if(!A)return;let C=nl(e,T),S=!1,P=null,R=!1,$=null,L={...A,nodeId:o,type:I,position:A.position},z=s.get(o),F={inProgress:!0,isValid:null,from:nT(z,L,y.Left,!0),fromHandle:L,fromPosition:L.position,fromNode:z,to:C,toHandle:null,toPosition:tE[L.position],toNode:null};b(F);let D=F;function B(e){var i,u;let f;if(!x()||!L){W(e);return}let p=_();k=function(e,t,n,r){let o=[],i=1/0;for(let a of function(e,t,n){let r=[],o={x:e.x-n,y:e.y-n,width:2*n,height:2*n};for(let e of t.values())tK(o,tG(e))>0&&r.push(e);return r}(e,n,t+250))for(let n of[...a.internals.handleBounds?.source??[],...a.internals.handleBounds?.target??[]]){if(r.nodeId===n.nodeId&&r.type===n.type&&r.id===n.id)continue;let{x:u,y:s}=nT(a,n,n.position,!0),l=Math.sqrt(Math.pow(u-e.x,2)+Math.pow(s-e.y,2));l>t||(l<i?(o=[{...n,x:u,y:s}],i=l):l===i&&o.push({...n,x:u,y:s}))}if(!o.length)return null;if(o.length>1){let e="source"===r.type?"target":"source";return o.find(t=>t.type===e)??o[0]}return o[0]}(t2(C=nl(e,T),p,!1,[1,1]),n,s,L),S||(function e(){if(!c||!T)return;let[t,n]=tH(C,T,E);h({x:t,y:n}),M=requestAnimationFrame(e)}(),S=!0);let g=n0(e,{handle:k,connectionMode:t,fromNodeId:o,fromHandleId:r,fromType:a?"target":"source",isValidConnection:m,doc:O,lib:l,flowId:d,nodeLookup:s});$=g.handleDomNode,P=g.connection,i=!!k,u=g.isValid,f=null,u?f=!0:i&&!u&&(f=!1),R=f;let y={...D,isValid:R,to:g.toHandle&&R?t5({x:g.toHandle.x,y:g.toHandle.y},p):C,toHandle:g.toHandle,toPosition:R&&g.toHandle?g.toHandle.position:tE[L.position],toNode:g.toHandle?s.get(g.toHandle.nodeId):null};R&&k&&D.toHandle&&y.toHandle&&D.toHandle.type===y.toHandle.type&&D.toHandle.nodeId===y.toHandle.nodeId&&D.toHandle.id===y.toHandle.id&&D.to.x===y.to.x&&D.to.y===y.to.y||(b(y),D=y)}function W(e){(k||$)&&P&&R&&g?.(P);let{inProgress:t,...n}=D,r={...n,toPosition:D.toHandle?D.toPosition:null};v?.(e,r),i&&w?.(e,r),f(),cancelAnimationFrame(M),S=!1,R=!1,P=null,$=null,O.removeEventListener("mousemove",B),O.removeEventListener("mouseup",W),O.removeEventListener("touchmove",B),O.removeEventListener("touchend",W)}p?.(e,{nodeId:o,handleId:r,handleType:I}),O.addEventListener("mousemove",B),O.addEventListener("mouseup",W),O.addEventListener("touchmove",B),O.addEventListener("touchend",W)},isValid:n0};function n2({domNode:e,panZoom:t,getTransform:n,getViewScale:r}){let o=el(e);return{update:function({translateExtent:e,width:i,height:a,zoomStep:u=10,pannable:s=!0,zoomable:l=!0,inversePan:c=!1}){let d=[0,0],h=tm().on("start",e=>{("mousedown"===e.sourceEvent.type||"touchstart"===e.sourceEvent.type)&&(d=[e.sourceEvent.clientX??e.sourceEvent.touches[0].clientX,e.sourceEvent.clientY??e.sourceEvent.touches[0].clientY])}).on("zoom",s?o=>{let u=n();if("mousemove"!==o.sourceEvent.type&&"touchmove"!==o.sourceEvent.type||!t)return;let s=[o.sourceEvent.clientX??o.sourceEvent.touches[0].clientX,o.sourceEvent.clientY??o.sourceEvent.touches[0].clientY],l=[s[0]-d[0],s[1]-d[1]];d=s;let h=r()*Math.max(u[2],Math.log(u[2]))*(c?-1:1),f={x:u[0]-l[0]*h,y:u[1]-l[1]*h};t.setViewportConstrained({x:f.x,y:f.y,zoom:u[2]},[[0,0],[i,a]],e)}:null).on("zoom.wheel",l?e=>{let r=n();if("wheel"!==e.sourceEvent.type||!t)return;let o=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*u,i=r[2]*Math.pow(2,o);t.scaleTo(i)}:null);o.call(h,{})},destroy:function(){o.on("zoom",null)},pointer:ec}}let n5=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,n7=e=>({x:e.x,y:e.y,zoom:e.k}),n4=({x:e,y:t,zoom:n})=>ts.translate(e,t).scale(n),n6=(e,t)=>e.target.closest(`.${t}`),n3=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),n8=(e,t=0,n=()=>{})=>{let r="number"==typeof t&&t>0;return r||n(),r?e.transition().duration(t).on("end",n):e},n9=e=>{let t=e.ctrlKey&&t6()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t};function re({domNode:e,minZoom:t,maxZoom:n,paneClickDistance:r,translateExtent:o,viewport:i,onPanZoom:a,onPanZoomStart:u,onPanZoomEnd:s,onDraggingChange:l}){let c={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},d=e.getBoundingClientRect(),f=tm().clickDistance(!tJ(r)||r<0?0:r).scaleExtent([t,n]).translateExtent(o),p=el(e).call(f);w({x:i.x,y:i.y,zoom:tD(i.zoom,t,n)},[[0,0],[d.width,d.height]],o);let g=p.on("wheel.zoom"),y=p.on("dblclick.zoom");function v(e,t){return p?new Promise(n=>{f?.transform(n8(p,t?.duration,()=>n(!0)),e)}):Promise.resolve(!1)}function m(){f.on("zoom",null)}async function w(e,t,n){let r=n4(e),o=f?.constrain()(r,t,n);return o&&await v(o),new Promise(e=>e(o))}return f.wheelDelta(n9),{update:function({noWheelClassName:e,noPanClassName:t,onPaneContextMenu:n,userSelectionActive:r,panOnScroll:o,panOnDrag:i,panOnScrollMode:d,panOnScrollSpeed:v,preventScrolling:w,zoomOnPinch:b,zoomOnScroll:_,zoomOnDoubleClick:x,zoomActivationKeyPressed:E,lib:k,onTransformChange:O}){r&&!c.isZoomingOrPanning&&m();let M=!o||E||r?function({noWheelClassName:e,preventScrolling:t,d3ZoomHandler:n}){return function(r,o){let i="wheel"===r.type,a=!t&&i&&!r.ctrlKey,u=n6(r,e);if(r.ctrlKey&&i&&u&&r.preventDefault(),a||u)return null;r.preventDefault(),n.call(this,r,o)}}({noWheelClassName:e,preventScrolling:w,d3ZoomHandler:g}):function({zoomPanValues:e,noWheelClassName:t,d3Selection:n,d3Zoom:r,panOnScrollMode:o,panOnScrollSpeed:i,zoomOnPinch:a,onPanZoomStart:u,onPanZoom:s,onPanZoomEnd:l}){return c=>{if(n6(c,t))return!1;c.preventDefault(),c.stopImmediatePropagation();let d=n.property("__zoom").k||1;if(c.ctrlKey&&a){let e=ec(c),t=n9(c);r.scaleTo(n,d*Math.pow(2,t),e,c);return}let f=1===c.deltaMode?20:1,p=o===h.Vertical?0:c.deltaX*f,g=o===h.Horizontal?0:c.deltaY*f;!t6()&&c.shiftKey&&o!==h.Vertical&&(p=c.deltaY*f,g=0),r.translateBy(n,-(p/d)*i,-(g/d)*i,{internal:!0});let y=n7(n.property("__zoom"));clearTimeout(e.panScrollTimeout),e.isPanScrolling||(e.isPanScrolling=!0,u?.(c,y)),e.isPanScrolling&&(s?.(c,y),e.panScrollTimeout=setTimeout(()=>{l?.(c,y),e.isPanScrolling=!1},150))}}({zoomPanValues:c,noWheelClassName:e,d3Selection:p,d3Zoom:f,panOnScrollMode:d,panOnScrollSpeed:v,zoomOnPinch:b,onPanZoomStart:u,onPanZoom:a,onPanZoomEnd:s});if(p.on("wheel.zoom",M,{passive:!1}),!r){let e=function({zoomPanValues:e,onDraggingChange:t,onPanZoomStart:n}){return r=>{if(r.sourceEvent?.internal)return;let o=n7(r.transform);e.mouseButton=r.sourceEvent?.button||0,e.isZoomingOrPanning=!0,e.prevViewport=o,r.sourceEvent?.type==="mousedown"&&t(!0),n&&n?.(r.sourceEvent,o)}}({zoomPanValues:c,onDraggingChange:l,onPanZoomStart:u});f.on("start",e);let t=function({zoomPanValues:e,panOnDrag:t,onPaneContextMenu:n,onTransformChange:r,onPanZoom:o}){return i=>{e.usedRightMouseButton=!!(n&&n3(t,e.mouseButton??0)),i.sourceEvent?.sync||r([i.transform.x,i.transform.y,i.transform.k]),o&&!i.sourceEvent?.internal&&o?.(i.sourceEvent,n7(i.transform))}}({zoomPanValues:c,panOnDrag:i,onPaneContextMenu:!!n,onPanZoom:a,onTransformChange:O});f.on("zoom",t);let r=function({zoomPanValues:e,panOnDrag:t,panOnScroll:n,onDraggingChange:r,onPanZoomEnd:o,onPaneContextMenu:i}){return a=>{if(!a.sourceEvent?.internal&&(e.isZoomingOrPanning=!1,i&&n3(t,e.mouseButton??0)&&!e.usedRightMouseButton&&a.sourceEvent&&i(a.sourceEvent),e.usedRightMouseButton=!1,r(!1),o&&n5(e.prevViewport,a.transform))){let t=n7(a.transform);e.prevViewport=t,clearTimeout(e.timerId),e.timerId=setTimeout(()=>{o?.(a.sourceEvent,t)},n?150:0)}}}({zoomPanValues:c,panOnDrag:i,panOnScroll:o,onPaneContextMenu:n,onPanZoomEnd:s,onDraggingChange:l});f.on("end",r)}let j=function({zoomActivationKeyPressed:e,zoomOnScroll:t,zoomOnPinch:n,panOnDrag:r,panOnScroll:o,zoomOnDoubleClick:i,userSelectionActive:a,noWheelClassName:u,noPanClassName:s,lib:l}){return c=>{let d=e||t,h=n&&c.ctrlKey;if(1===c.button&&"mousedown"===c.type&&(n6(c,`${l}-flow__node`)||n6(c,`${l}-flow__edge`)))return!0;if(!r&&!d&&!o&&!i&&!n||a||n6(c,u)&&"wheel"===c.type||n6(c,s)&&("wheel"!==c.type||o&&"wheel"===c.type&&!e)||!n&&c.ctrlKey&&"wheel"===c.type)return!1;if(!n&&"touchstart"===c.type&&c.touches?.length>1)return c.preventDefault(),!1;if(!d&&!o&&!h&&"wheel"===c.type||!r&&("mousedown"===c.type||"touchstart"===c.type)||Array.isArray(r)&&!r.includes(c.button)&&"mousedown"===c.type)return!1;let f=Array.isArray(r)&&r.includes(c.button)||!c.button||c.button<=1;return(!c.ctrlKey||"wheel"===c.type)&&f}}({zoomActivationKeyPressed:E,panOnDrag:i,zoomOnScroll:_,panOnScroll:o,zoomOnDoubleClick:x,zoomOnPinch:b,userSelectionActive:r,noPanClassName:t,noWheelClassName:e,lib:k});f.filter(j),x?p.on("dblclick.zoom",y):p.on("dblclick.zoom",null)},destroy:m,setViewport:async function(e,t){let n=n4(e);return await v(n,t),new Promise(e=>e(n))},setViewportConstrained:w,getViewport:function(){let e=p?tl(p.node()):{x:0,y:0,k:1};return{x:e.x,y:e.y,zoom:e.k}},scaleTo:function(e,t){return p?new Promise(n=>{f?.scaleTo(n8(p,t?.duration,()=>n(!0)),e)}):Promise.resolve(!1)},scaleBy:function(e,t){return p?new Promise(n=>{f?.scaleBy(n8(p,t?.duration,()=>n(!0)),e)}):Promise.resolve(!1)},setScaleExtent:function(e){f?.scaleExtent(e)},setTranslateExtent:function(e){f?.translateExtent(e)},syncViewport:function(e){if(p){let t=n4(e),n=p.property("__zoom");(n.k!==e.zoom||n.x!==e.x||n.y!==e.y)&&f?.transform(p,t,null,{sync:!0})}},setClickDistance:function(e){let t=!tJ(e)||e<0?0:e;f?.clickDistance(t)}}}(l=v||(v={})).Line="line",l.Handle="handle";let rt=["top-left","top-right","bottom-left","bottom-right"],rn=["top","right","bottom","left"];function rr(e,t){return Math.max(0,t-e)}function ro(e,t){return Math.max(0,e-t)}function ri(e,t,n){return Math.max(0,t-e,e-n)}let ra={width:0,height:0,x:0,y:0},ru={...ra,pointerX:0,pointerY:0,aspectRatio:1};function rs({domNode:e,nodeId:t,getStoreItems:n,onChange:r,onEnd:o}){let i=el(e);return{update:function({controlPosition:e,boundaries:a,keepAspectRatio:u,resizeDirection:s,onResizeStart:l,onResize:c,onResizeEnd:d,shouldResize:h}){let f,p,g,y,v={...ra},m={...ru},w=function(e){let t=e.includes("right")||e.includes("left");return{isHorizontal:t,isVertical:e.includes("bottom")||e.includes("top"),affectsX:e.includes("left"),affectsY:e.includes("top")}}(e),b=null,_=[],x=eE().on("start",e=>{let{nodeLookup:r,transform:o,snapGrid:i,snapToGrid:a,nodeOrigin:u,paneDomNode:s}=n();if(!(f=r.get(t)))return;b=s?.getBoundingClientRect()??null;let{xSnapped:c,ySnapped:d}=nr(e.sourceEvent,{transform:o,snapGrid:i,snapToGrid:a,containerBounds:b});if(m={...v={width:f.measured.width??0,height:f.measured.height??0,x:f.position.x??0,y:f.position.y??0},pointerX:c,pointerY:d,aspectRatio:v.width/v.height},p=void 0,f.parentId&&("parent"===f.extent||f.expandParent)){var h;g=(p=r.get(f.parentId))&&"parent"===f.extent?[[0,0],[(h=p).measured.width,h.measured.height]]:void 0}for(let[e,n]of(_=[],y=void 0,r))if(n.parentId===t&&(_.push({id:e,position:{...n.position},extent:n.extent}),"parent"===n.extent||n.expandParent)){let e=function(e,t,n){let r=t.position.x+e.position.x,o=t.position.y+e.position.y,i=e.measured.width??0,a=e.measured.height??0,u=n[0]*i,s=n[1]*a;return[[r-u,o-s],[r+i-u,o+a-s]]}(n,f,n.origin??u);y=y?[[Math.min(e[0][0],y[0][0]),Math.min(e[0][1],y[0][1])],[Math.max(e[1][0],y[1][0]),Math.max(e[1][1],y[1][1])]]:e}l?.(e,{...v})}).on("drag",e=>{let{transform:t,snapGrid:o,snapToGrid:i,nodeOrigin:l}=n(),d=nr(e.sourceEvent,{transform:t,snapGrid:o,snapToGrid:i,containerBounds:b}),x=[];if(!f)return;let{x:E,y:k,width:O,height:M}=v,j={},N=f.origin??l,{width:I,height:T,x:A,y:C}=function(e,t,n,r,o,i,a,u){let{affectsX:s,affectsY:l}=t,{isHorizontal:c,isVertical:d}=t,h=c&&d,{xSnapped:f,ySnapped:p}=n,{minWidth:g,maxWidth:y,minHeight:v,maxHeight:m}=r,{x:w,y:b,width:_,height:x,aspectRatio:E}=e,k=Math.floor(c?f-e.pointerX:0),O=Math.floor(d?p-e.pointerY:0),M=_+(s?-k:k),j=x+(l?-O:O),N=-i[0]*_,I=-i[1]*x,T=ri(M,g,y),A=ri(j,v,m);if(a){let e=0,t=0;s&&k<0?e=rr(w+k+N,a[0][0]):!s&&k>0&&(e=ro(w+M+N,a[1][0])),l&&O<0?t=rr(b+O+I,a[0][1]):!l&&O>0&&(t=ro(b+j+I,a[1][1])),T=Math.max(T,e),A=Math.max(A,t)}if(u){let e=0,t=0;s&&k>0?e=ro(w+k,u[0][0]):!s&&k<0&&(e=rr(w+M,u[1][0])),l&&O>0?t=ro(b+O,u[0][1]):!l&&O<0&&(t=rr(b+j,u[1][1])),T=Math.max(T,e),A=Math.max(A,t)}if(o&&(c&&(T=Math.max(T,ri(M/E,v,m)*E),a&&(T=Math.max(T,(s||l)&&(!s||l||!h)?rr(b+I+(s?k:-k)/E,a[0][1])*E:ro(b+I+M/E,a[1][1])*E)),u&&(T=Math.max(T,(s||l)&&(!s||l||!h)?ro(b+(s?k:-k)/E,u[0][1])*E:rr(b+M/E,u[1][1])*E))),d&&(A=Math.max(A,ri(j*E,g,y)/E),a&&(A=Math.max(A,(s||l)&&(!l||s||!h)?rr(w+(l?O:-O)*E+N,a[0][0])/E:ro(w+j*E+N,a[1][0])/E)),u&&(A=Math.max(A,(s||l)&&(!l||s||!h)?ro(w+(l?O:-O)*E,u[0][0])/E:rr(w+j*E,u[1][0])/E)))),O+=O<0?A:-A,k+=k<0?T:-T,o){if(h){var C,S,P,R;M>j*E?O=((C=s,S=l,C?!S:S)?-k:k)/E:k=((P=s,R=l,P?!R:R)?-O:O)*E}else c?(O=k/E,l=s):(k=O*E,s=l)}let $=s?w+k:w,L=l?b+O:b;return{width:_+(s?-k:k),height:x+(l?-O:O),x:i[0]*k*(s?-1:1)+$,y:i[1]*O*(l?-1:1)+L}}(m,w,d,a,u,N,g,y),S=I!==O,P=T!==M,R=A!==E&&S,$=C!==k&&P;if(!R&&!$&&!S&&!P)return;if((R||$||1===N[0]||1===N[1])&&(j.x=R?A:v.x,j.y=$?C:v.y,v.x=j.x,v.y=j.y,_.length>0)){let e=A-E,t=C-k;for(let n of _)n.position={x:n.position.x-e+N[0]*(I-O),y:n.position.y-t+N[1]*(T-M)},x.push(n)}if((S||P)&&(j.width=S&&(!s||"horizontal"===s)?I:v.width,j.height=P&&(!s||"vertical"===s)?T:v.height,v.width=j.width,v.height=j.height),p&&f.expandParent){let e=N[0]*(j.width??0);j.x&&j.x<e&&(v.x=e,m.x=m.x-(j.x-e));let t=N[1]*(j.height??0);j.y&&j.y<t&&(v.y=t,m.y=m.y-(j.y-t))}let L=function({width:e,prevWidth:t,height:n,prevHeight:r,affectsX:o,affectsY:i}){let a=e-t,u=n-r,s=[a>0?1:a<0?-1:0,u>0?1:u<0?-1:0];return a&&o&&(s[0]=-1*s[0]),u&&i&&(s[1]=-1*s[1]),s}({width:v.width,prevWidth:O,height:v.height,prevHeight:M,affectsX:w.affectsX,affectsY:w.affectsY}),z={...v,direction:L};!1!==h?.(e,z)&&(c?.(e,z),r(j,x))}).on("end",e=>{d?.(e,{...v}),o?.({...v})});i.call(x)},destroy:function(){i.on(".drag",null)}}}},31958:function(e){var t;t=function(){return(function e(t,n,r){function o(a,u){if(!n[a]){if(!t[a]){if(i)return i(a,!0);var s=Error("Cannot find module '"+a+"'");throw s.code="MODULE_NOT_FOUND",s}var l=n[a]={exports:{}};t[a][0].call(l.exports,function(e){return o(t[a][1][e]||e)},l,l.exports,e,t,n,r)}return n[a].exports}for(var i=void 0,a=0;a<r.length;a++)o(r[a]);return o})({1:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function o(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}var i=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.defaultLayoutOptions,i=n.algorithms,u=n.workerFactory,s=n.workerUrl;if(o(this,e),this.defaultLayoutOptions=void 0===r?{}:r,this.initialized=!1,void 0===s&&void 0===u)throw Error("Cannot construct an ELK without both 'workerUrl' and 'workerFactory'.");var l=u;void 0!==s&&void 0===u&&(l=function(e){return new Worker(e)});var c=l(s);if("function"!=typeof c.postMessage)throw TypeError("Created worker does not provide the required 'postMessage' function.");this.worker=new a(c),this.worker.postMessage({cmd:"register",algorithms:void 0===i?["layered","stress","mrtree","radial","force","disco","sporeOverlap","sporeCompaction","rectpacking"]:i}).then(function(e){return t.initialized=!0}).catch(console.err)}return r(e,[{key:"layout",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.layoutOptions,r=void 0===n?this.defaultLayoutOptions:n,o=t.logging,i=t.measureExecutionTime;return e?this.worker.postMessage({cmd:"layout",graph:e,layoutOptions:r,options:{logging:void 0!==o&&o,measureExecutionTime:void 0!==i&&i}}):Promise.reject(Error("Missing mandatory parameter 'graph'."))}},{key:"knownLayoutAlgorithms",value:function(){return this.worker.postMessage({cmd:"algorithms"})}},{key:"knownLayoutOptions",value:function(){return this.worker.postMessage({cmd:"options"})}},{key:"knownLayoutCategories",value:function(){return this.worker.postMessage({cmd:"categories"})}},{key:"terminateWorker",value:function(){this.worker&&this.worker.terminate()}}]),e}();n.default=i;var a=function(){function e(t){var n=this;if(o(this,e),void 0===t)throw Error("Missing mandatory parameter 'worker'.");this.resolvers={},this.worker=t,this.worker.onmessage=function(e){setTimeout(function(){n.receive(n,e)},0)}}return r(e,[{key:"postMessage",value:function(e){var t=this.id||0;this.id=t+1,e.id=t;var n=this;return new Promise(function(r,o){n.resolvers[t]=function(e,t){e?(n.convertGwtStyleError(e),o(e)):r(t)},n.worker.postMessage(e)})}},{key:"receive",value:function(e,t){var n=t.data,r=e.resolvers[n.id];r&&(delete e.resolvers[n.id],n.error?r(n.error):r(null,n.data))}},{key:"terminate",value:function(){this.worker&&this.worker.terminate()}},{key:"convertGwtStyleError",value:function(e){if(e){var t=e.__java$exception;t&&(t.cause&&t.cause.backingJsObject&&(e.cause=t.cause.backingJsObject,this.convertGwtStyleError(e.cause)),delete e.__java$exception)}}}]),e}()},{}],2:[function(e,t,n){"use strict";var r=e("./elk-api.js").default;Object.defineProperty(t.exports,"__esModule",{value:!0}),t.exports=r,r.default=r},{"./elk-api.js":1}]},{},[2])(2)},e.exports=t()},97491:function(e,t,n){"use strict";var r=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,t);var r=Object.assign({},e),o=!1;try{o=!0}catch(e){}if(e.workerUrl){if(o){var i=n(70847);r.workerFactory=function(e){return new i(e)}}else console.warn("Web worker requested but 'web-worker' package not installed. \nConsider installing the package or pass your own 'workerFactory' to ELK's constructor.\n... Falling back to non-web worker version.")}if(!r.workerFactory){var a=n(58831).Worker;r.workerFactory=function(e){return new a(e)}}return function(e,t){if(!e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&("object"==typeof t||"function"==typeof t)?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,r))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(n(31958).default);Object.defineProperty(e.exports,"__esModule",{value:!0}),e.exports=r,r.default=r},34357:function(e,t,n){e=n.nmd(e);var r,o="__lodash_hash_undefined__",i="[object Arguments]",a="[object Boolean]",u="[object Date]",s="[object Function]",l="[object GeneratorFunction]",c="[object Map]",d="[object Number]",h="[object Object]",f="[object Promise]",p="[object RegExp]",g="[object Set]",y="[object String]",v="[object Symbol]",m="[object WeakMap]",w="[object ArrayBuffer]",b="[object DataView]",_="[object Float32Array]",x="[object Float64Array]",E="[object Int8Array]",k="[object Int16Array]",O="[object Int32Array]",M="[object Uint8Array]",j="[object Uint8ClampedArray]",N="[object Uint16Array]",I="[object Uint32Array]",T=/\w*$/,A=/^\[object .+?Constructor\]$/,C=/^(?:0|[1-9]\d*)$/,S={};S[i]=S["[object Array]"]=S[w]=S[b]=S[a]=S[u]=S[_]=S[x]=S[E]=S[k]=S[O]=S[c]=S[d]=S[h]=S[p]=S[g]=S[y]=S[v]=S[M]=S[j]=S[N]=S[I]=!0,S["[object Error]"]=S[s]=S[m]=!1;var P="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,R="object"==typeof self&&self&&self.Object===Object&&self,$=P||R||Function("return this")(),L=t&&!t.nodeType&&t,z=L&&e&&!e.nodeType&&e,F=z&&z.exports===L;function D(e,t){return e.set(t[0],t[1]),e}function B(e,t){return e.add(t),e}function W(e,t,n,r){var o=-1,i=e?e.length:0;for(r&&i&&(n=e[++o]);++o<i;)n=t(n,e[o],o,e);return n}function V(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}function H(e){var t=-1,n=Array(e.size);return e.forEach(function(e,r){n[++t]=[r,e]}),n}function Y(e,t){return function(n){return e(t(n))}}function q(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}var X=Array.prototype,G=Function.prototype,U=Object.prototype,Z=$["__core-js_shared__"],K=(r=/[^.]+$/.exec(Z&&Z.keys&&Z.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"",Q=G.toString,J=U.hasOwnProperty,ee=U.toString,et=RegExp("^"+Q.call(J).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),en=F?$.Buffer:void 0,er=$.Symbol,eo=$.Uint8Array,ei=Y(Object.getPrototypeOf,Object),ea=Object.create,eu=U.propertyIsEnumerable,es=X.splice,el=Object.getOwnPropertySymbols,ec=en?en.isBuffer:void 0,ed=Y(Object.keys,Object),eh=eP($,"DataView"),ef=eP($,"Map"),ep=eP($,"Promise"),eg=eP($,"Set"),ey=eP($,"WeakMap"),ev=eP(Object,"create"),em=ez(eh),ew=ez(ef),eb=ez(ep),e_=ez(eg),ex=ez(ey),eE=er?er.prototype:void 0,ek=eE?eE.valueOf:void 0;function eO(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function eM(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function ej(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function eN(e){this.__data__=new eM(e)}function eI(e,t,n){var r=e[t];J.call(e,t)&&eF(r,n)&&(void 0!==n||t in e)||(e[t]=n)}function eT(e,t){for(var n=e.length;n--;)if(eF(e[n][0],t))return n;return -1}function eA(e){var t=new e.constructor(e.byteLength);return new eo(t).set(new eo(e)),t}function eC(e,t,n,r){n||(n={});for(var o=-1,i=t.length;++o<i;){var a=t[o],u=r?r(n[a],e[a],a,n,e):void 0;eI(n,a,void 0===u?e[a]:u)}return n}function eS(e,t){var n,r=e.__data__;return("string"==(n=typeof t)||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==t:null===t)?r["string"==typeof t?"string":"hash"]:r.map}function eP(e,t){var n=null==e?void 0:e[t];return!(!eH(n)||K&&K in n)&&(eV(n)||V(n)?et:A).test(ez(n))?n:void 0}eO.prototype.clear=function(){this.__data__=ev?ev(null):{}},eO.prototype.delete=function(e){return this.has(e)&&delete this.__data__[e]},eO.prototype.get=function(e){var t=this.__data__;if(ev){var n=t[e];return n===o?void 0:n}return J.call(t,e)?t[e]:void 0},eO.prototype.has=function(e){var t=this.__data__;return ev?void 0!==t[e]:J.call(t,e)},eO.prototype.set=function(e,t){return this.__data__[e]=ev&&void 0===t?o:t,this},eM.prototype.clear=function(){this.__data__=[]},eM.prototype.delete=function(e){var t=this.__data__,n=eT(t,e);return!(n<0)&&(n==t.length-1?t.pop():es.call(t,n,1),!0)},eM.prototype.get=function(e){var t=this.__data__,n=eT(t,e);return n<0?void 0:t[n][1]},eM.prototype.has=function(e){return eT(this.__data__,e)>-1},eM.prototype.set=function(e,t){var n=this.__data__,r=eT(n,e);return r<0?n.push([e,t]):n[r][1]=t,this},ej.prototype.clear=function(){this.__data__={hash:new eO,map:new(ef||eM),string:new eO}},ej.prototype.delete=function(e){return eS(this,e).delete(e)},ej.prototype.get=function(e){return eS(this,e).get(e)},ej.prototype.has=function(e){return eS(this,e).has(e)},ej.prototype.set=function(e,t){return eS(this,e).set(e,t),this},eN.prototype.clear=function(){this.__data__=new eM},eN.prototype.delete=function(e){return this.__data__.delete(e)},eN.prototype.get=function(e){return this.__data__.get(e)},eN.prototype.has=function(e){return this.__data__.has(e)},eN.prototype.set=function(e,t){var n=this.__data__;if(n instanceof eM){var r=n.__data__;if(!ef||r.length<199)return r.push([e,t]),this;n=this.__data__=new ej(r)}return n.set(e,t),this};var eR=el?Y(el,Object):function(){return[]},e$=function(e){return ee.call(e)};function eL(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||U)}function ez(e){if(null!=e){try{return Q.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function eF(e,t){return e===t||e!=e&&t!=t}(eh&&e$(new eh(new ArrayBuffer(1)))!=b||ef&&e$(new ef)!=c||ep&&e$(ep.resolve())!=f||eg&&e$(new eg)!=g||ey&&e$(new ey)!=m)&&(e$=function(e){var t=ee.call(e),n=t==h?e.constructor:void 0,r=n?ez(n):void 0;if(r)switch(r){case em:return b;case ew:return c;case eb:return f;case e_:return g;case ex:return m}return t});var eD=Array.isArray;function eB(e){var t;return null!=e&&"number"==typeof(t=e.length)&&t>-1&&t%1==0&&t<=9007199254740991&&!eV(e)}var eW=ec||function(){return!1};function eV(e){var t=eH(e)?ee.call(e):"";return t==s||t==l}function eH(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function eY(e){return eB(e)?function(e,t){var n,r=eD(e)||e&&"object"==typeof e&&eB(e)&&J.call(e,"callee")&&(!eu.call(e,"callee")||ee.call(e)==i)?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],o=r.length,a=!!o;for(var u in e)J.call(e,u)&&!(a&&("length"==u||(n=null==(n=o)?9007199254740991:n)&&("number"==typeof u||C.test(u))&&u>-1&&u%1==0&&u<n))&&r.push(u);return r}(e):function(e){if(!eL(e))return ed(e);var t=[];for(var n in Object(e))J.call(e,n)&&"constructor"!=n&&t.push(n);return t}(e)}e.exports=function(e){return function e(t,n,r,o,f,m,A){if(o&&(C=m?o(t,f,m,A):o(t)),void 0!==C)return C;if(!eH(t))return t;var C,P=eD(t);if(P){if(R=t.length,$=t.constructor(R),R&&"string"==typeof t[0]&&J.call(t,"index")&&($.index=t.index,$.input=t.input),C=$,!n)return function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}(t,C)}else{var R,$,L,z,F,Y,X=e$(t),G=X==s||X==l;if(eW(t))return function(e,t){if(t)return e.slice();var n=new e.constructor(e.length);return e.copy(n),n}(t,n);if(X==h||X==i||G&&!m){if(V(t))return m?t:{};if(C="function"!=typeof(L=G?{}:t).constructor||eL(L)?{}:eH(z=ei(L))?ea(z):{},!n)return F=(Y=C)&&eC(t,eY(t),Y),eC(t,eR(t),F)}else{if(!S[X])return m?t:{};C=function(e,t,n,r){var o,i,s,l=e.constructor;switch(t){case w:return eA(e);case a:case u:return new l(+e);case b:return o=r?eA(e.buffer):e.buffer,new e.constructor(o,e.byteOffset,e.byteLength);case _:case x:case E:case k:case O:case M:case j:case N:case I:return i=r?eA(e.buffer):e.buffer,new e.constructor(i,e.byteOffset,e.length);case c:return W(r?n(H(e),!0):H(e),D,new e.constructor);case d:case y:return new l(e);case p:return(s=new e.constructor(e.source,T.exec(e))).lastIndex=e.lastIndex,s;case g:return W(r?n(q(e),!0):q(e),B,new e.constructor);case v:return ek?Object(ek.call(e)):{}}}(t,X,e,n)}}A||(A=new eN);var U=A.get(t);if(U)return U;if(A.set(t,C),!P){var Z,K,Q=r?(K=eY(Z=t),eD(Z)?K:function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}(K,eR(Z))):eY(t)}return function(e,t){for(var n=-1,r=e?e.length:0;++n<r&&!1!==t(e[n],n,e););}(Q||t,function(i,a){Q&&(i=t[a=i]),eI(C,a,e(i,n,r,o,a,t,A))}),C}(e,!0,!0)}},38837:function(e,t,n){"use strict";var r,o;t.c$=t.Lw=t.fW=t.Cl=t.xV=t.w9=t.Qy=t.At=t.uF=void 0;let i=n(97062),a=n(36109),u=n(78068);function s(e,t=""){return null==e?[]:Array.isArray(e)?[{id:t,title:t.charAt(0).toUpperCase()+t.slice(1),description:`Definition for ${t}`,type:r.List,children:e.length>0&&"object"==typeof e[0]?s(e[0],t):void 0}]:"object"!=typeof e?[{id:t,title:t.charAt(0).toUpperCase()+t.slice(1),description:`Definition for ${t}`,type:r.Text}]:Object.entries(e).map(([e,t])=>{let n="object"!=typeof t?r.Text:Array.isArray(t)?r.List:r.Object,o={id:e,title:e.charAt(0).toUpperCase()+e.slice(1),description:`Definition for ${e}`,type:n};return n===r.List&&Array.isArray(t)&&t.length>0&&"object"==typeof t[0]?o.children=s(t[0],e):n===r.Object&&(o.children=s(t,e)),o})}t.uF=s,t.At=(e,n)=>{let r=(0,a.generatePseudoDbRecords)(e.definition,1,n)[0],o=(0,a.transformRawRecords)(e.definition,[r],n)[0];return(0,t.w9)(e,[o],n)},(o=r=t.Qy||(t.Qy={})).Text="text",o.List="list",o.Object="object";let l=[{id:"id",title:"Id",description:"Id of the person",type:r.Text},{id:"title",title:"Title",description:"Fullname of the person",type:r.Text},{id:"image",title:"Image",description:"Image of the person",type:r.Text},{id:"email",title:"Email",description:"Email of the person",type:r.Text},{id:"firstName",title:"First name",description:"First name of the person",type:r.Text},{id:"lastName",title:"Last name",description:"Last name of the person",type:r.Text}];t.w9=(e,t,n)=>{let o=[];for(let n of t){let t={id:n.id,createdAt:new Date(n.createdAt).toISOString(),updatedAt:new Date(n.updatedAt).toISOString(),createdBy:n.createdBy,updatedBy:n.updatedBy,database:{id:e.id,name:e.name},values:n.processedRecordValues};o.push(t)}let{valueDefinition:i,sample:u}=(()=>{let t=(0,a.generatePseudoDbRecords)(e.definition,1,n)[0],i=(0,a.transformRawRecords)(e.definition,[t],n)[0],u={id:i.id,createdAt:new Date(i.createdAt).toISOString(),updatedAt:new Date(i.updatedAt).toISOString(),createdBy:i.createdBy,updatedBy:i.updatedBy,database:{id:e.id,name:e.name},values:i.processedRecordValues},l=[];for(let t of Object.values(e.definition.columnsMap)){let e=u.values[t.id]||"",n=t.title,r=s(e,t.id);r&&0!==r.length&&(r[0].title=n,r[0].description=`Value for ${n}`,l.push(r[0]))}return{valueDefinition:{id:"values",title:"Values",description:"Values of the record",type:r.Object,children:l},sample:u,recordsInWorkflow:o}})();return{sample:u,outputs:[{description:"Record Id",id:"id",title:"Id"},{description:"Record Created Time in ISO string",id:"createdAt",title:"Created At"},{description:"Record Updated Time in ISO string",id:"updatedAt",title:"Updated At"},{description:"Record's author",id:"createdBy",title:"Created By",type:r.Object,children:l},{description:"Record's author of last update",id:"updatedBy",title:"Updated By",type:r.Object,children:l},{description:"Database the record belongs to",id:"database",title:"Database",children:[{description:"Id of the database",id:"id",title:"Id"},{description:"Name of the database",id:"name",title:"Name"}]},i],recordsInWorkflow:o}},t.xV=[i.WorkflowFlowControlType.Loop_Break,i.WorkflowFlowControlType.Loop_Continue,i.WorkflowFlowControlType.Loop_StartOfLoop,i.WorkflowFlowControlType.Loop_EndOfLoop,i.WorkflowFlowControlType.Branching_EndOfBranching,i.WorkflowFlowControlType.Empty_Placeholder],t.Cl=e=>{let t={};for(let n of Object.values(e.map))t[n.id]={node:n,outgoingEdges:[],incomingEdges:[]};for(let n of e.edges)t[n.source].outgoingEdges.push(n),t[n.target].incomingEdges.push(n);return t},t.fW=(e,n,r,o)=>{o||(o=(0,t.Cl)(e));let a=o[n],u=o[r];if(!a)throw Error("Start step not found");if(!u)throw Error("End step not found");let s=[],l=[],c=new Set,d=!1,h=e=>{if(e.node.category!==i.WorkflowNodeCategory.TERMINAL)for(let t of(c.add(e.node.id),e.outgoingEdges)){let e=o[t.target];if(!e)throw Error(`Target in depth search not found: ${t.target}`);if(l.push(t),e.node.id===r){d=!0;continue}c.has(e.node.id)||(s.push(e.node),h(e))}};if(h(a),!d)throw console.log({nodes:s,edges:l,nodeEdgeMap:o}),Error("End step not reachable");return{nodes:s,edges:l,nodeEdgeMap:o}};let c={},d=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&!!n.databaseId};t.Lw=e=>!!(e.field&&String(e.field).trim())&&!!e.op&&(!!u.SingleOperandOperators.includes(e.op)||!!(e.value&&String(e.value).trim())),c[i.WorkflowTriggerType.Opendashboard_OnRecordCreated]=d,c[i.WorkflowTriggerType.Opendashboard_OnRecordUpdated]=d,c[i.WorkflowTriggerType.Opendashboard_OnRecordDeleted]=d,c[i.WorkflowTriggerType.Webhook_OnWebhook]=e=>{var t,n,r,o,a,u,s,l,c;let d=null===(t=e.data)||void 0===t?void 0:t.configs;return!!d&&(!!(d.authType===i.WebhookAuthenticationType.Basic&&(null===(r=null===(n=d.basicAuth)||void 0===n?void 0:n.username)||void 0===r?void 0:r.trim())&&(null===(a=null===(o=d.basicAuth)||void 0===o?void 0:o.password)||void 0===a?void 0:a.trim()))||!!(d.authType===i.WebhookAuthenticationType.Header&&(null===(s=null===(u=d.headerAuth)||void 0===u?void 0:u.key)||void 0===s?void 0:s.trim())&&(null===(c=null===(l=d.headerAuth)||void 0===l?void 0:l.value)||void 0===c?void 0:c.trim()))||void 0)},c[i.WorkflowTriggerType.Schedule_OnSchedule]=e=>{var t,n,r;let o=null===(t=e.data)||void 0===t?void 0:t.configs;return!!o&&(o.type===i.ScheduleType.OneTime&&!!o.date||o.frequency===i.ScheduleFrequency.Hourly||o.frequency===i.ScheduleFrequency.Daily&&!!o.timeOfDay||o.frequency===i.ScheduleFrequency.Weekly&&!!o.timeOfDay&&null!==(n=o.daysOfWeek)&&void 0!==n&&!!n.length||o.frequency===i.ScheduleFrequency.Monthly&&!!o.timeOfDay&&null!==(r=o.datesOfMonth)&&void 0!==r&&!!r.length||void 0)},c[i.WorkflowFlowControlType.Delay]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!(n.type===i.DelayType.DELAY_FOR&&n.unitValue&&n.unit||n.type===i.DelayType.DELAY_UNTIL_DAY_TIME&&n.timeOfDay&&Object.values(n.days).some(e=>e))||n.type===i.DelayType.DELAY_UNTIL_DATE&&!!n.date||void 0)},c[i.WorkflowFlowControlType.Branching]=e=>{var n;let r=null===(n=e.data)||void 0===n?void 0:n.configs;if(!r)return!1;let o=e=>{let n=e.groups||[],r=n.length>0;for(let e of n){if(0===e.conditions.length){r=!1;break}for(let n of e.conditions)if(!(0,t.Lw)(n)){r=!1;break}if(!r)break}e.isReady=r},i=r.branches,a=i.length>0;for(let e of i)o(e),a=a&&!!e.isReady;return a},c[i.WorkflowFlowControlType.LoopOnItems]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!n.items&&!!e.sampleOutput&&!!e.outputFields||void 0)},c[i.WorkflowActionType.HTTP_SendHTTPRequest]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!n.url&&!!n.method||void 0)},c[i.WorkflowActionType.OnDemand_CallWorkflow]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!n.workflowId||void 0)},c[i.WorkflowActionType.Opendashboard_FindRecords]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!n.databaseId||void 0)},c[i.WorkflowActionType.Opendashboard_FindRecordsById]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!(n.databaseId&&n.recordIds&&n.recordIds.trim())||void 0)},c[i.WorkflowActionType.Opendashboard_FindRecord]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!(n.databaseId&&n.recordId&&n.recordId.trim())||void 0)},c[i.WorkflowActionType.Opendashboard_CreateRecord]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!n.authorId&&!!n.databaseId&&!!n.values||void 0)},c[i.WorkflowActionType.Opendashboard_DeleteRecords]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!(n.authorId&&n.databaseId&&n.recordIds&&n.recordIds.trim())||void 0)},c[i.WorkflowActionType.Opendashboard_UpdateRecords]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!(n.authorId&&n.databaseId&&n.recordIds&&n.recordIds.trim())||void 0)},c[i.WorkflowActionType.Opendashboard_SendEmail]=e=>{var t,n,r,o,i;let a=null===(t=e.data)||void 0===t?void 0:t.configs;return!!a&&(!!(a.senderId&&(null===(n=a.sendTo)||void 0===n?void 0:n.email)&&(null===(r=a.sendTo)||void 0===r?void 0:r.name)&&(null===(o=a.subject)||void 0===o?void 0:o.trim())&&(null===(i=a.contentText)||void 0===i?void 0:i.trim()))||void 0)},c[i.WorkflowActionType.Opendashboard_CreateReminder]=e=>{var t,n;let r=null===(t=e.data)||void 0===t?void 0:t.configs;return!!r&&(r.authorId&&(null===(n=r.title)||void 0===n?void 0:n.trim())&&r.notifyAt&&Array.isArray(r.notifyAt)&&r.notifyAt.length>0&&r.assignedToUserIds&&Array.isArray(r.assignedToUserIds)&&r.assignedToUserIds.length>0?!r.associateWithRecord||!!(r.recordId&&r.recordId.trim()&&r.databaseId):void 0)},c[i.WorkflowActionType.Opendashboard_GenerateAIContent]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!n.userPrompt&&!!n.maxWords||void 0)},c[i.WorkflowActionType.Utility_ExecuteFormula]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!(n.formula&&n.formula.trim())||void 0)},c[i.WorkflowActionType.Input_RequestInput]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!n.inputFields&&Object.keys(n.inputFields).length>0||void 0)},c[i.WorkflowFlowControlType.Input_WaitForInput]=e=>{var t;let n=null===(t=e.data)||void 0===t?void 0:t.configs;return!!n&&(!!n.inputNodeId||void 0)};let h=[i.WorkflowTriggerType.OnDemand_Callable,i.WorkflowFlowControlType.Approval_WaitForApproval,i.WorkflowFlowControlType.Approval_CreateApprovalLink,i.WorkflowFlowControlType.Terminal_EndWorkflow];t.c$=e=>{var n;return!!(e.data&&t.xV.includes(null===(n=e.data)||void 0===n?void 0:n.type))||e.category===i.WorkflowNodeCategory.TERMINAL||!!e.data&&!!e.data.type&&(!!h.includes(e.data.type)||!!c[e.data.type]&&!!c[e.data.type](e))}},92860:function(e,t,n){"use strict";var r=n(2265),o=n(82558),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,u=r.useRef,s=r.useEffect,l=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=u(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;var f=a(e,(d=l(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&h.hasValue){var t=h.value;if(o(t,e))return u=t}return u=e}if(t=u,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,u=n)}var a,u,s=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,n,r,o]))[0],d[1]);return s(function(){h.hasValue=!0,h.value=f},[f]),c(f),f}},35195:function(e,t,n){"use strict";e.exports=n(92860)},36190:function(){},83568:function(e,t,n){"use strict";n.d(t,{Z:function(){return function e(t){if("string"==typeof t||"number"==typeof t)return""+t;let n="";if(Array.isArray(t))for(let r=0,o;r<t.length;r++)""!==(o=e(t[r]))&&(n+=(n&&" ")+o);else for(let e in t)t[e]&&(n+=(n&&" ")+e);return n}}})},26910:function(e,t,n){"use strict";function r(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function o(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function i(){}n.d(t,{ZP:function(){return w},B8:function(){return x}});var a="\\s*([+-]?\\d+)\\s*",u="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",s="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",l=/^#([0-9a-f]{3,8})$/,c=RegExp(`^rgb\\(${a},${a},${a}\\)$`),d=RegExp(`^rgb\\(${s},${s},${s}\\)$`),h=RegExp(`^rgba\\(${a},${a},${a},${u}\\)$`),f=RegExp(`^rgba\\(${s},${s},${s},${u}\\)$`),p=RegExp(`^hsl\\(${u},${s},${s}\\)$`),g=RegExp(`^hsla\\(${u},${s},${s},${u}\\)$`),y={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function v(){return this.rgb().formatHex()}function m(){return this.rgb().formatRgb()}function w(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=l.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?b(t):3===n?new E(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?_(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?_(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=c.exec(e))?new E(t[1],t[2],t[3],1):(t=d.exec(e))?new E(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=h.exec(e))?_(t[1],t[2],t[3],t[4]):(t=f.exec(e))?_(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=p.exec(e))?I(t[1],t[2]/100,t[3]/100,1):(t=g.exec(e))?I(t[1],t[2]/100,t[3]/100,t[4]):y.hasOwnProperty(e)?b(y[e]):"transparent"===e?new E(NaN,NaN,NaN,0):null}function b(e){return new E(e>>16&255,e>>8&255,255&e,1)}function _(e,t,n,r){return r<=0&&(e=t=n=NaN),new E(e,t,n,r)}function x(e,t,n,r){var o;return 1==arguments.length?((o=e)instanceof i||(o=w(o)),o)?new E((o=o.rgb()).r,o.g,o.b,o.opacity):new E:new E(e,t,n,null==r?1:r)}function E(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}function k(){return`#${N(this.r)}${N(this.g)}${N(this.b)}`}function O(){let e=M(this.opacity);return`${1===e?"rgb(":"rgba("}${j(this.r)}, ${j(this.g)}, ${j(this.b)}${1===e?")":`, ${e})`}`}function M(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function j(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function N(e){return((e=j(e))<16?"0":"")+e.toString(16)}function I(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new A(e,t,n,r)}function T(e){if(e instanceof A)return new A(e.h,e.s,e.l,e.opacity);if(e instanceof i||(e=w(e)),!e)return new A;if(e instanceof A)return e;var t=(e=e.rgb()).r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),a=Math.max(t,n,r),u=NaN,s=a-o,l=(a+o)/2;return s?(u=t===a?(n-r)/s+(n<r)*6:n===a?(r-t)/s+2:(t-n)/s+4,s/=l<.5?a+o:2-a-o,u*=60):s=l>0&&l<1?0:u,new A(u,s,l,e.opacity)}function A(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}function C(e){return(e=(e||0)%360)<0?e+360:e}function S(e){return Math.max(0,Math.min(1,e||0))}function P(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}r(i,w,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:v,formatHex:v,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return T(this).formatHsl()},formatRgb:m,toString:m}),r(E,x,o(i,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new E(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new E(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new E(j(this.r),j(this.g),j(this.b),M(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:k,formatHex:k,formatHex8:function(){return`#${N(this.r)}${N(this.g)}${N(this.b)}${N((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:O,toString:O})),r(A,function(e,t,n,r){return 1==arguments.length?T(e):new A(e,t,n,null==r?1:r)},o(i,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new A(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new A(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new E(P(e>=240?e-240:e+120,o,r),P(e,o,r),P(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new A(C(this.h),S(this.s),S(this.l),M(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=M(this.opacity);return`${1===e?"hsl(":"hsla("}${C(this.h)}, ${100*S(this.s)}%, ${100*S(this.l)}%${1===e?")":`, ${e})`}`}}))},7097:function(e,t){"use strict";t.Z=e=>()=>e},44193:function(e,t,n){"use strict";function r(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}n.d(t,{Z:function(){return r}})},16064:function(e,t,n){"use strict";n.d(t,{ZP:function(){return u}});var r=n(26910);function o(e,t,n,r,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*n+(1+3*e+3*i-3*a)*r+a*o)/6}var i=n(7097);function a(e,t){var n=t-e;return n?function(t){return e+t*n}:(0,i.Z)(isNaN(e)?t:e)}var u=function e(t){var n,o=1==(n=+(n=t))?a:function(e,t){var r,o,a;return t-e?(r=e,o=t,r=Math.pow(r,a=n),o=Math.pow(o,a)-r,a=1/a,function(e){return Math.pow(r+e*o,a)}):(0,i.Z)(isNaN(e)?t:e)};function u(e,t){var n=o((e=(0,r.B8)(e)).r,(t=(0,r.B8)(t)).r),i=o(e.g,t.g),u=o(e.b,t.b),s=a(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=i(t),e.b=u(t),e.opacity=s(t),e+""}}return u.gamma=e,u}(1);function s(e){return function(t){var n,o,i=t.length,a=Array(i),u=Array(i),s=Array(i);for(n=0;n<i;++n)o=(0,r.B8)(t[n]),a[n]=o.r||0,u[n]=o.g||0,s[n]=o.b||0;return a=e(a),u=e(u),s=e(s),o.opacity=1,function(e){return o.r=a(e),o.g=u(e),o.b=s(e),o+""}}}s(function(e){var t=e.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),i=e[r],a=e[r+1],u=r>0?e[r-1]:2*i-a,s=r<t-1?e[r+2]:2*a-i;return o((n-r/t)*t,u,i,a,s)}}),s(function(e){var t=e.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*t),i=e[(r+t-1)%t],a=e[r%t],u=e[(r+1)%t],s=e[(r+2)%t];return o((n-r/t)*t,i,a,u,s)}})},19729:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(44193),o=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,i=RegExp(o.source,"g");function a(e,t){var n,a,u,s,l,c=o.lastIndex=i.lastIndex=0,d=-1,h=[],f=[];for(e+="",t+="";(u=o.exec(e))&&(s=i.exec(t));)(l=s.index)>c&&(l=t.slice(c,l),h[d]?h[d]+=l:h[++d]=l),(u=u[0])===(s=s[0])?h[d]?h[d]+=s:h[++d]=s:(h[++d]=null,f.push({i:d,x:(0,r.Z)(u,s)})),c=i.lastIndex;return c<t.length&&(l=t.slice(c),h[d]?h[d]+=l:h[++d]=l),h.length<2?f[0]?(n=f[0].x,function(e){return n(e)+""}):(a=t,function(){return a}):(t=f.length,function(e){for(var n,r=0;r<t;++r)h[(n=f[r]).i]=n.x(e);return h.join("")})}},70847:function(e,t,n){"use strict";n.r(t),t.default="undefined"!=typeof Worker?Worker:void 0},85854:function(e,t,n){"use strict";function r(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}n.d(t,{X:function(){return r}})},69829:function(e,t,n){"use strict";n.d(t,{F:function(){return h},s:function(){return c}});var r=n(2265),o=n(35195);let i=e=>{let t;let n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,o,i);return i},a=e=>e?i(e):i,{useDebugValue:u}=r,{useSyncExternalStoreWithSelector:s}=o,l=e=>e;function c(e,t=l,n){let r=s(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return u(r),r}let d=(e,t)=>{let n=a(e),r=(e,r=t)=>c(n,e,r);return Object.assign(r,n),r},h=(e,t)=>e?d(e,t):d}}]);