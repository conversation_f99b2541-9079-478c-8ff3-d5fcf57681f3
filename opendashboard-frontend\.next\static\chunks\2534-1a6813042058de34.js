!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="11bac67d-d59b-49c9-b0ea-c4a8dc35890b",e._sentryDebugIdIdentifier="sentry-dbid-11bac67d-d59b-49c9-b0ea-c4a8dc35890b")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2534],{5478:function(e,t,n){n.d(t,{Ry:function(){return l}});var r=new WeakMap,o=new WeakMap,i={},u=0,a=function(e){return e&&(e.host||a(e.parentNode))},c=function(e,t,n,c){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=a(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,v=new Set(l),p=function(e){!e||f.has(e)||(f.add(e),p(e.parentNode))};l.forEach(p);var m=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(c),i=null!==t&&"false"!==t,u=(r.get(e)||0)+1,a=(s.get(e)||0)+1;r.set(e,u),s.set(e,a),d.push(e),1===u&&i&&o.set(e,!0),1===a&&e.setAttribute(n,"true"),i||e.setAttribute(c,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),u++,function(){d.forEach(function(e){var t=r.get(e)-1,i=s.get(e)-1;r.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(c),o.delete(e)),i||e.removeAttribute(n)}),--u||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},l=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),c(r,o,n,"aria-hidden")):function(){return null}}},85770:function(e,t,n){n.d(t,{Av:function(){return u},pF:function(){return r},xv:function(){return i},zi:function(){return o}});var r="right-scroll-bar-position",o="width-before-scroll-bar",i="with-scroll-bars-hidden",u="--removed-body-scroll-bar-size"},5517:function(e,t,n){n.d(t,{jp:function(){return m}});var r=n(2265),o=n(18704),i=n(85770),u={left:0,top:0,right:0,gap:0},a=function(e){return parseInt(e||"",10)||0},c=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[a(n),a(r),a(o)]},l=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return u;var t=c(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},s=(0,o.Ws)(),d="data-scroll-locked",f=function(e,t,n,r){var o=e.left,u=e.top,a=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat(i.xv," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(d,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(u,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(i.pF," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(i.zi," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(i.pF," .").concat(i.pF," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(i.zi," .").concat(i.zi," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(d,"] {\n    ").concat(i.Av,": ").concat(c,"px;\n  }\n")},v=function(){var e=parseInt(document.body.getAttribute(d)||"0",10);return isFinite(e)?e:0},p=function(){r.useEffect(function(){return document.body.setAttribute(d,(v()+1).toString()),function(){var e=v()-1;e<=0?document.body.removeAttribute(d):document.body.setAttribute(d,e.toString())}},[])},m=function(e){var t=e.noRelative,n=e.noImportant,o=e.gapMode,i=void 0===o?"margin":o;p();var u=r.useMemo(function(){return l(i)},[i]);return r.createElement(s,{styles:f(u,!t,i,n?"":"!important")})}},60703:function(e,t,n){n.d(t,{Z:function(){return L}});var r=n(5853),o=n(2265),i=n(85770),u=n(17325),a=(0,n(31412)._)(),c=function(){},l=o.forwardRef(function(e,t){var n=o.useRef(null),i=o.useState({onScrollCapture:c,onWheelCapture:c,onTouchMoveCapture:c}),l=i[0],s=i[1],d=e.forwardProps,f=e.children,v=e.className,p=e.removeScrollBar,m=e.enabled,h=e.shards,y=e.sideCar,b=e.noRelative,g=e.noIsolation,E=e.inert,w=e.allowPinchZoom,S=e.as,N=e.gapMode,C=(0,r._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(0,u.q)([n,t]),T=(0,r.pi)((0,r.pi)({},C),l);return o.createElement(o.Fragment,null,m&&o.createElement(y,{sideCar:a,removeScrollBar:p,shards:h,noRelative:b,noIsolation:g,inert:E,setCallbacks:s,allowPinchZoom:!!w,lockRef:n,gapMode:N}),d?o.cloneElement(o.Children.only(f),(0,r.pi)((0,r.pi)({},T),{ref:R})):o.createElement(void 0===S?"div":S,(0,r.pi)({},T,{className:v,ref:R}),f))});l.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},l.classNames={fullWidth:i.zi,zeroRight:i.pF};var s=n(49085),d=n(5517),f=n(18704),v=!1;if("undefined"!=typeof window)try{var p=Object.defineProperty({},"passive",{get:function(){return v=!0,!0}});window.addEventListener("test",p,p),window.removeEventListener("test",p,p)}catch(e){v=!1}var m=!!v&&{passive:!1},h=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},y=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),b(e,r)){var o=g(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},b=function(e,t){return"v"===e?h(t,"overflowY"):h(t,"overflowX")},g=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},E=function(e,t,n,r,o){var i,u=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=u*r,c=n.target,l=t.contains(c),s=!1,d=a>0,f=0,v=0;do{var p=g(e,c),m=p[0],h=p[1]-p[2]-u*m;(m||h)&&b(e,c)&&(f+=h,v+=m),c=c.parentNode.host||c.parentNode}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-a>v)&&(s=!0),s},w=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},S=function(e){return[e.deltaX,e.deltaY]},N=function(e){return e&&"current"in e?e.current:e},C=0,R=[],T=(0,s.L)(a,function(e){var t=o.useRef([]),n=o.useRef([0,0]),i=o.useRef(),u=o.useState(C++)[0],a=o.useState(f.Ws)[0],c=o.useRef(e);o.useEffect(function(){c.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(u));var t=(0,r.ev)([e.lockRef.current],(e.shards||[]).map(N),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(u))}),function(){document.body.classList.remove("block-interactivity-".concat(u)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(u))})}}},[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var r,o=w(e),u=n.current,a="deltaX"in e?e.deltaX:u[0]-o[0],l="deltaY"in e?e.deltaY:u[1]-o[1],s=e.target,d=Math.abs(a)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=y(d,s);if(!f)return!0;if(f?r=d:(r="v"===d?"h":"v",f=y(d,s)),!f)return!1;if(!i.current&&"changedTouches"in e&&(a||l)&&(i.current=r),!r)return!0;var v=i.current||r;return E(v,t,e,"h"===v?a:l,!0)},[]),s=o.useCallback(function(e){if(R.length&&R[R.length-1]===a){var n="deltaY"in e?S(e):w(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(N).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),v=o.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),p=o.useCallback(function(e){n.current=w(e),i.current=void 0},[]),h=o.useCallback(function(t){v(t.type,S(t),t.target,l(t,e.lockRef.current))},[]),b=o.useCallback(function(t){v(t.type,w(t),t.target,l(t,e.lockRef.current))},[]);o.useEffect(function(){return R.push(a),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:b}),document.addEventListener("wheel",s,m),document.addEventListener("touchmove",s,m),document.addEventListener("touchstart",p,m),function(){R=R.filter(function(e){return e!==a}),document.removeEventListener("wheel",s,m),document.removeEventListener("touchmove",s,m),document.removeEventListener("touchstart",p,m)}},[]);var g=e.removeScrollBar,T=e.inert;return o.createElement(o.Fragment,null,T?o.createElement(a,{styles:"\n  .block-interactivity-".concat(u," {pointer-events: none;}\n  .allow-interactivity-").concat(u," {pointer-events: all;}\n")}):null,g?o.createElement(d.jp,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),x=o.forwardRef(function(e,t){return o.createElement(l,(0,r.pi)({},e,{ref:t,sideCar:T}))});x.classNames=l.classNames;var L=x},18704:function(e,t,n){n.d(t,{Ws:function(){return a}});var r,o=n(2265),i=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,u;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),u=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(u)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},u=function(){var e=i();return function(t,n){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},a=function(){var e=u();return function(t){return e(t.styles,t.dynamic),null}}},17325:function(e,t,n){n.d(t,{q:function(){return a}});var r=n(2265);function o(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var i="undefined"!=typeof window?r.useLayoutEffect:r.useEffect,u=new WeakMap;function a(e,t){var n,a,c,l=(n=t||null,a=function(t){return e.forEach(function(e){return o(e,t)})},(c=(0,r.useState)(function(){return{value:n,callback:a,facade:{get current(){return c.value},set current(value){var e=c.value;e!==value&&(c.value=value,c.callback(value,e))}}}})[0]).callback=a,c.facade);return i(function(){var t=u.get(l);if(t){var n=new Set(t),r=new Set(e),i=l.current;n.forEach(function(e){r.has(e)||o(e,null)}),r.forEach(function(e){n.has(e)||o(e,i)})}u.set(l,e)},[e]),l}},49085:function(e,t,n){n.d(t,{L:function(){return u}});var r=n(5853),o=n(2265),i=function(e){var t=e.sideCar,n=(0,r._T)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var i=t.read();if(!i)throw Error("Sidecar medium not found");return o.createElement(i,(0,r.pi)({},n))};function u(e,t){return e.useMedium(t),i}i.isSideCarExport=!0},31412:function(e,t,n){n.d(t,{_:function(){return i}});var r=n(5853);function o(e){return e}function i(e){void 0===e&&(e={});var t,n,i,u=(void 0===t&&(t=o),n=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var r=t(e,i);return n.push(r),function(){n=n.filter(function(e){return e!==r})}},assignSyncMedium:function(e){for(i=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){i=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var o=function(){var n=t;t=[],n.forEach(e)},u=function(){return Promise.resolve().then(o)};u(),n={push:function(e){t.push(e),u()},filter:function(e){return t=t.filter(e),n}}}});return u.options=(0,r.pi)({async:!0,ssr:!1},e),u}},6741:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},98575:function(e,t,n){n.d(t,{F:function(){return i},e:function(){return u}});var r=n(2265);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function u(...e){return r.useCallback(i(...e),e)}},73966:function(e,t,n){n.d(t,{b:function(){return u},k:function(){return i}});var r=n(2265),o=n(57437);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,u=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:u,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function u(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let u=r.createContext(i),a=n.length;n=[...n,i];let c=t=>{let{scope:n,children:i,...c}=t,l=n?.[e]?.[a]||u,s=r.useMemo(()=>c,Object.values(c));return(0,o.jsx)(l.Provider,{value:s,children:i})};return c.displayName=t+"Provider",[c,function(n,o){let c=o?.[e]?.[a]||u,l=r.useContext(c);if(l)return l;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},15278:function(e,t,n){n.d(t,{XB:function(){return f}});var r,o=n(2265),i=n(6741),u=n(66840),a=n(98575),c=n(26606),l=n(57437),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:h,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:g,onDismiss:E,...w}=e,S=o.useContext(d),[N,C]=o.useState(null),R=null!==(f=null==N?void 0:N.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,T]=o.useState({}),x=(0,a.e)(t,e=>C(e)),L=Array.from(S.layers),[M]=[...S.layersWithOutsidePointerEventsDisabled].slice(-1),O=L.indexOf(M),A=N?L.indexOf(N):-1,P=S.layersWithOutsidePointerEventsDisabled.size>0,k=A>=O,W=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,c.W)(e),i=o.useRef(!1),u=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){p("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",u.current),u.current=t,n.addEventListener("click",u.current,{once:!0})):t()}else n.removeEventListener("click",u.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",u.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...S.branches].some(e=>e.contains(t));!k||n||(null==y||y(e),null==g||g(e),e.defaultPrevented||null==E||E())},R),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,c.W)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...S.branches].some(e=>e.contains(t))||(null==b||b(e),null==g||g(e),e.defaultPrevented||null==E||E())},R);return!function(e,t=globalThis?.document){let n=(0,c.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{A!==S.layers.size-1||(null==h||h(e),!e.defaultPrevented&&E&&(e.preventDefault(),E()))},R),o.useEffect(()=>{if(N)return m&&(0===S.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),S.layersWithOutsidePointerEventsDisabled.add(N)),S.layers.add(N),v(),()=>{m&&1===S.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[N,R,m,S]),o.useEffect(()=>()=>{N&&(S.layers.delete(N),S.layersWithOutsidePointerEventsDisabled.delete(N),v())},[N,S]),o.useEffect(()=>{let e=()=>T({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,l.jsx)(u.WV.div,{...w,ref:x,style:{pointerEvents:P?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,W.onPointerDownCapture)})});function v(){let e=new CustomEvent(s);document.dispatchEvent(e)}function p(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,u.jH)(i,a):i.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(u.WV.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},86097:function(e,t,n){n.d(t,{EW:function(){return i}});var r=n(2265),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:u()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:u()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function u(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},99103:function(e,t,n){let r;n.d(t,{M:function(){return f}});var o=n(2265),i=n(98575),u=n(66840),a=n(26606),c=n(57437),l="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},f=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:f,onUnmountAutoFocus:y,...b}=e,[g,E]=o.useState(null),w=(0,a.W)(f),S=(0,a.W)(y),N=o.useRef(null),C=(0,i.e)(t,e=>E(e)),R=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(R.paused||!g)return;let t=e.target;g.contains(t)?N.current=t:m(N.current,{select:!0})},t=function(e){if(R.paused||!g)return;let t=e.relatedTarget;null===t||g.contains(t)||m(N.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(g)});return g&&n.observe(g,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,g,R.paused]),o.useEffect(()=>{if(g){h.add(R);let e=document.activeElement;if(!g.contains(e)){let t=new CustomEvent(l,d);g.addEventListener(l,w),g.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(v(g).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(g))}return()=>{g.removeEventListener(l,w),setTimeout(()=>{let t=new CustomEvent(s,d);g.addEventListener(s,S),g.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),g.removeEventListener(s,S),h.remove(R)},0)}}},[g,w,S,R]);let T=o.useCallback(e=>{if(!n&&!r||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=v(e);return[p(t,e),p(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,R.paused]);return(0,c.jsx)(u.WV.div,{tabIndex:-1,...b,ref:C,onKeyDown:T})});function v(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var h=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=y(r,e)).unshift(e)},remove(e){var t;null===(t=(r=y(r,e))[0])||void 0===t||t.resume()}});function y(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},99255:function(e,t,n){n.d(t,{M:function(){return c}});var r,o=n(2265),i=n(61188),u=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function c(e){let[t,n]=o.useState(u());return(0,i.b)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},83832:function(e,t,n){n.d(t,{h:function(){return c}});var r=n(2265),o=n(54887),i=n(66840),u=n(61188),a=n(57437),c=r.forwardRef((e,t)=>{var n,c;let{container:l,...s}=e,[d,f]=r.useState(!1);(0,u.b)(()=>f(!0),[]);let v=l||d&&(null===(c=globalThis)||void 0===c?void 0:null===(n=c.document)||void 0===n?void 0:n.body);return v?o.createPortal((0,a.jsx)(i.WV.div,{...s,ref:t}),v):null});c.displayName="Portal"},71599:function(e,t,n){n.d(t,{z:function(){return u}});var r=n(2265),o=n(98575),i=n(61188),u=e=>{var t,n;let u,c;let{present:l,children:s}=e,d=function(e){var t,n;let[o,u]=r.useState(),c=r.useRef(null),l=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(c.current);s.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=c.current,n=l.current;if(n!==e){let r=s.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),l.current=e}},[e,f]),(0,i.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=a(c.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!l.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=a(c.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{c.current=e?getComputedStyle(e):null,u(e)},[])}}(l),f="function"==typeof s?s({present:d.isPresent}):r.Children.only(s),v=(0,o.e)(d.ref,(u=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in u&&u.isReactWarning?f.ref:(u=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in u&&u.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof s||d.isPresent?r.cloneElement(f,{ref:v}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"},66840:function(e,t,n){n.d(t,{WV:function(){return a},jH:function(){return c}});var r=n(2265),o=n(54887),i=n(37053),u=n(57437),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(a,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},37053:function(e,t,n){n.d(t,{Slot:function(){return a},Z8:function(){return u}});var r=n(2265),o=n(98575),i=n(57437);function u(e){let t=function(e){let t=r.forwardRef((e,t)=>{var n,i;let u,a;let{children:c,...l}=e,s=r.isValidElement(c)?(u=null===(n=Object.getOwnPropertyDescriptor(c.props,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in u&&u.isReactWarning?c.ref:(u=null===(i=Object.getOwnPropertyDescriptor(c,"ref"))||void 0===i?void 0:i.get)&&"isReactWarning"in u&&u.isReactWarning?c.props.ref:c.props.ref||c.ref:void 0,d=(0,o.e)(s,t);if(r.isValidElement(c)){let e=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=i(...t);return o(...t),r}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(l,c.props);return c.type!==r.Fragment&&(e.ref=d),r.cloneElement(c,e)}return r.Children.count(c)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=r.forwardRef((e,n)=>{let{children:o,...u}=e,a=r.Children.toArray(o),c=a.find(l);if(c){let e=c.props.children,o=a.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...u,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...u,ref:n,children:o})});return n.displayName="".concat(e,".Slot"),n}var a=u("Slot"),c=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}},26606:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},80886:function(e,t,n){n.d(t,{T:function(){return a}});var r,o=n(2265),i=n(61188),u=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.b;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,c]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return u(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),l=void 0!==e,s=l?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[s,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&c.current?.(n)}else a(t)},[l,e,a,c])]}Symbol("RADIX:SYNC_STATE")},61188:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}},90535:function(e,t,n){n.d(t,{j:function(){return u}});var r=n(61994);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.W,u=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:u,defaultVariants:a}=t,c=Object.keys(u).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let i=o(t)||o(r);return u[e][i]}),l=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,c,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...l}[t]):({...a,...l})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},5853:function(e,t,n){n.d(t,{CR:function(){return a},Jh:function(){return u},_T:function(){return o},ev:function(){return c},mG:function(){return i},pi:function(){return r}});var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function i(e,t,n,r){return new(n||(n=Promise))(function(o,i){function u(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(u,a)}c((r=r.apply(e,t||[])).next())})}function u(e,t){var n,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},u=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return u.next=a(0),u.throw=a(1),u.return=a(2),"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function a(a){return function(c){return function(a){if(n)throw TypeError("Generator is already executing.");for(;u&&(u=0,a[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function a(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}function c(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError}}]);