!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ef747ebb-6584-4267-933f-df014c5eacc0",e._sentryDebugIdIdentifier="sentry-dbid-ef747ebb-6584-4267-933f-df014c5eacc0")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8008],{7223:function(e,t,n){Promise.resolve().then(n.bind(n,64367))},30166:function(e,t,n){"use strict";n.d(t,{default:function(){return i.a}});var r=n(55775),i=n.n(r)},99376:function(e,t,n){"use strict";var r=n(35475);n.o(r,"redirect")&&n.d(t,{redirect:function(){return r.redirect}}),n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},55775:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=n(47043);n(57437),n(2265);let i=r._(n(15602));function a(e,t){var n;let r={loading:e=>{let{error:t,isLoading:n,pastDelay:r}=e;return null}};"function"==typeof e&&(r.loader=e);let a={...r,...t};return(0,i.default)({...a,modules:null==(n=a.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81523:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return i}});let r=n(18993);function i(e){let{reason:t,children:n}=e;if("undefined"==typeof window)throw new r.BailoutToCSRError(t);return n}},15602:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let r=n(57437),i=n(2265),a=n(81523),l=n(70049);function o(e){return{default:e&&"default"in e?e.default:e}}let d={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},u=function(e){let t={...d,...e},n=(0,i.lazy)(()=>t.loader().then(o)),u=t.loading;function s(e){let o=u?(0,r.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,d=t.ssr?(0,r.jsxs)(r.Fragment,{children:["undefined"==typeof window?(0,r.jsx)(l.PreloadCss,{moduleIds:t.modules}):null,(0,r.jsx)(n,{...e})]}):(0,r.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(n,{...e})});return(0,r.jsx)(i.Suspense,{fallback:o,children:d})}return s.displayName="LoadableComponent",s}},70049:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return a}});let r=n(57437),i=n(20544);function a(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let n=(0,i.getExpectedRequestStore)("next/dynamic css"),a=[];if(n.reactLoadableManifest&&t){let e=n.reactLoadableManifest;for(let n of t){if(!e[n])continue;let t=e[n].files.filter(e=>e.endsWith(".css"));a.push(...t)}}return 0===a.length?null:(0,r.jsx)(r.Fragment,{children:a.map(e=>(0,r.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:n.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},23500:function(e,t){"use strict";var n,r,i;t.Bq=t.Ly=t.bW=void 0,(n=t.bW||(t.bW={})).Table="table",n.Board="board",n.Form="form",n.Document="document",n.Dashboard="dashboard",n.SummaryTable="summary-table",n.ListView="list-view",n.Calendar="calendar",(r=t.Ly||(t.Ly={})).Left="left",r.Right="right",(i=t.Bq||(t.Bq={})).Infobox="infobox",i.LineChart="lineChart",i.BarChart="barChart",i.PieChart="pieChart",i.FunnelChart="funnelChart",i.Embed="embed",i.Image="image",i.Text="text"},64367:function(e,t,n){"use strict";n.r(t);var r=n(57437),i=n(99376),a=n(54921);t.default=()=>{let e=(0,i.useParams)().viewId;return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(a.o,{id:e})})}},54921:function(e,t,n){"use strict";n.d(t,{o:function(){return w}});var r=n(57437),i=n(23500),a=n(2265),l=n(30166),o=n(29119);let d=(0,l.default)(()=>Promise.all([n.e(1092),n.e(6018),n.e(8025),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(6462),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(7674),n.e(1506),n.e(7561),n.e(663),n.e(3879),n.e(4376),n.e(4239),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3139),n.e(7515),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(8267),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(6240)]).then(n.bind(n,26240)).then(e=>e.DashboardView),{loadableGenerated:{webpack:()=>[26240]},ssr:!1}),u=(0,l.default)(()=>Promise.all([n.e(8025),n.e(6018),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818)]).then(n.bind(n,97017)).then(e=>e.TableView),{loadableGenerated:{webpack:()=>[97017]},ssr:!1}),s=(0,l.default)(()=>Promise.all([n.e(6018),n.e(7360),n.e(696),n.e(8025),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(6640),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(6326),n.e(4805),n.e(8849),n.e(8603)]).then(n.bind(n,89034)).then(e=>e.BoardView),{loadableGenerated:{webpack:()=>[89034]},ssr:!1}),f=(0,l.default)(()=>Promise.all([n.e(6018),n.e(7360),n.e(696),n.e(8025),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(631)]).then(n.bind(n,60631)).then(e=>e.FormView),{loadableGenerated:{webpack:()=>[60631]},ssr:!1}),c=(0,l.default)(()=>Promise.all([n.e(6018),n.e(2191),n.e(7190),n.e(696),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(8107),n.e(5737),n.e(794),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(7918)]).then(n.bind(n,87918)).then(e=>e.DocumentView),{loadableGenerated:{webpack:()=>[87918]},ssr:!1}),b=(0,l.default)(()=>Promise.all([n.e(8025),n.e(6018),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(9625)]).then(n.bind(n,79625)).then(e=>e.SummaryTableView),{loadableGenerated:{webpack:()=>[79625]},ssr:!1}),m=(0,l.default)(()=>Promise.all([n.e(6018),n.e(8025),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(826),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(8792)]).then(n.bind(n,68792)).then(e=>e.ListView),{loadableGenerated:{webpack:()=>[68792]},ssr:!1}),h=(0,l.default)(()=>Promise.all([n.e(6018),n.e(8025),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(6640),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(6326),n.e(4805),n.e(8849),n.e(2949)]).then(n.bind(n,39910)).then(e=>e.CalendarView),{loadableGenerated:{webpack:()=>[39910]},ssr:!1}),w=e=>{let{viewsMap:t}=(0,o.qt)(),n=e.id,l=e.view||t[n],w=l.type;return(0,a.useEffect)(()=>{document.title=(null==l?void 0:l.name)||"Untitled"},[]),(0,r.jsx)(r.Fragment,{children:w===i.bW.Table?(0,r.jsx)(u,{view:l,definition:l.definition}):w===i.bW.Board?(0,r.jsx)(s,{view:l,definition:l.definition}):w===i.bW.Form?(0,r.jsx)(f,{view:l,definition:l.definition}):w===i.bW.Document?(0,r.jsx)(c,{view:l,definition:l.definition}):w===i.bW.SummaryTable?(0,r.jsx)(b,{view:l,definition:l.definition}):w===i.bW.Dashboard?(0,r.jsx)(d,{view:l,definition:l.definition}):w===i.bW.ListView?(0,r.jsx)(m,{view:l,definition:l.definition}):w===i.bW.Calendar?(0,r.jsx)(h,{view:l,definition:l.definition}):(0,r.jsx)(r.Fragment,{})})}},29119:function(e,t,n){"use strict";n.d(t,{Ti:function(){return a},ol:function(){return o},qt:function(){return d}});var r=n(57437),i=n(2265);let a=e=>{let{page:t,views:n,accessLevel:i,permissions:a}=e.permissiblePage,o={};for(let e of n)o[e.id]=e;return(0,r.jsx)(l.Provider,{value:{page:t,views:n,accessLevel:i,permissions:a,viewsMap:o},children:e.children})},l=(0,i.createContext)(void 0),o=()=>(0,i.useContext)(l)||null,d=()=>{let e=(0,i.useContext)(l);if(!e)throw Error("usePage must be used within a PageProvider");return e}}},function(e){e.O(0,[991,2971,6577,1744],function(){return e(e.s=7223)}),_N_E=e.O()}]);