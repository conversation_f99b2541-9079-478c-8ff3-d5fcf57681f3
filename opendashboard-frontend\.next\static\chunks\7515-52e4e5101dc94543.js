!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="64036958-3912-4609-b931-12e8c8eec942",e._sentryDebugIdIdentifier="sentry-dbid-64036958-3912-4609-b931-12e8c8eec942")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7515],{96200:function(e,t){t.Z={locale:"en",long:{year:{previous:"last year",current:"this year",next:"next year",past:{one:"{0} year ago",other:"{0} years ago"},future:{one:"in {0} year",other:"in {0} years"}},quarter:{previous:"last quarter",current:"this quarter",next:"next quarter",past:{one:"{0} quarter ago",other:"{0} quarters ago"},future:{one:"in {0} quarter",other:"in {0} quarters"}},month:{previous:"last month",current:"this month",next:"next month",past:{one:"{0} month ago",other:"{0} months ago"},future:{one:"in {0} month",other:"in {0} months"}},week:{previous:"last week",current:"this week",next:"next week",past:{one:"{0} week ago",other:"{0} weeks ago"},future:{one:"in {0} week",other:"in {0} weeks"}},day:{previous:"yesterday",current:"today",next:"tomorrow",past:{one:"{0} day ago",other:"{0} days ago"},future:{one:"in {0} day",other:"in {0} days"}},hour:{current:"this hour",past:{one:"{0} hour ago",other:"{0} hours ago"},future:{one:"in {0} hour",other:"in {0} hours"}},minute:{current:"this minute",past:{one:"{0} minute ago",other:"{0} minutes ago"},future:{one:"in {0} minute",other:"in {0} minutes"}},second:{current:"now",past:{one:"{0} second ago",other:"{0} seconds ago"},future:{one:"in {0} second",other:"in {0} seconds"}}},short:{year:{previous:"last yr.",current:"this yr.",next:"next yr.",past:"{0} yr. ago",future:"in {0} yr."},quarter:{previous:"last qtr.",current:"this qtr.",next:"next qtr.",past:{one:"{0} qtr. ago",other:"{0} qtrs. ago"},future:{one:"in {0} qtr.",other:"in {0} qtrs."}},month:{previous:"last mo.",current:"this mo.",next:"next mo.",past:"{0} mo. ago",future:"in {0} mo."},week:{previous:"last wk.",current:"this wk.",next:"next wk.",past:"{0} wk. ago",future:"in {0} wk."},day:{previous:"yesterday",current:"today",next:"tomorrow",past:{one:"{0} day ago",other:"{0} days ago"},future:{one:"in {0} day",other:"in {0} days"}},hour:{current:"this hour",past:"{0} hr. ago",future:"in {0} hr."},minute:{current:"this minute",past:"{0} min. ago",future:"in {0} min."},second:{current:"now",past:"{0} sec. ago",future:"in {0} sec."}},narrow:{year:{previous:"last yr.",current:"this yr.",next:"next yr.",past:"{0}y ago",future:"in {0}y"},quarter:{previous:"last qtr.",current:"this qtr.",next:"next qtr.",past:"{0}q ago",future:"in {0}q"},month:{previous:"last mo.",current:"this mo.",next:"next mo.",past:"{0}mo ago",future:"in {0}mo"},week:{previous:"last wk.",current:"this wk.",next:"next wk.",past:"{0}w ago",future:"in {0}w"},day:{previous:"yesterday",current:"today",next:"tomorrow",past:"{0}d ago",future:"in {0}d"},hour:{current:"this hour",past:"{0}h ago",future:"in {0}h"},minute:{current:"this minute",past:"{0}m ago",future:"in {0}m"},second:{current:"now",past:"{0}s ago",future:"in {0}s"}},now:{now:{current:"now",future:"in a moment",past:"just now"}},mini:{year:"{0}yr",month:"{0}mo",week:"{0}wk",day:"{0}d",hour:"{0}h",minute:"{0}m",second:"{0}s",now:"now"},"short-time":{year:"{0} yr.",month:"{0} mo.",week:"{0} wk.",day:{one:"{0} day",other:"{0} days"},hour:"{0} hr.",minute:"{0} min.",second:"{0} sec."},"long-time":{year:{one:"{0} year",other:"{0} years"},month:{one:"{0} month",other:"{0} months"},week:{one:"{0} week",other:"{0} weeks"},day:{one:"{0} day",other:"{0} days"},hour:{one:"{0} hour",other:"{0} hours"},minute:{one:"{0} minute",other:"{0} minutes"},second:{one:"{0} second",other:"{0} seconds"}}}},16598:function(e,t,r){r.d(t,{Z:function(){return ek}});var n,o="en",a={},i={};function u(e){return a[e]?e:i[e.toLowerCase()]?i[e.toLowerCase()]:void 0}function c(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.localeMatcher||"lookup";switch(r){case"lookup":case"best fit":return function(e){var t=u(e);if(t)return t;for(var r=e.split("-");e.length>1;){r.pop();var n=u(e=r.join("-"));if(n)return n}}(e);default:throw RangeError('Invalid "localeMatcher" option: '.concat(r))}}var s={af:function(e){return 1==e?"one":"other"},am:function(e){return e>=0&&e<=1?"one":"other"},ar:function(e){var t=String(e).split("."),r=Number(t[0])==e&&t[0].slice(-2);return 0==e?"zero":1==e?"one":2==e?"two":r>=3&&r<=10?"few":r>=11&&r<=99?"many":"other"},ast:function(e){var t=!String(e).split(".")[1];return 1==e&&t?"one":"other"},be:function(e){var t=String(e).split("."),r=Number(t[0])==e,n=r&&t[0].slice(-1),o=r&&t[0].slice(-2);return 1==n&&11!=o?"one":n>=2&&n<=4&&(o<12||o>14)?"few":r&&0==n||n>=5&&n<=9||o>=11&&o<=14?"many":"other"},br:function(e){var t=String(e).split("."),r=Number(t[0])==e,n=r&&t[0].slice(-1),o=r&&t[0].slice(-2),a=r&&t[0].slice(-6);return 1==n&&11!=o&&71!=o&&91!=o?"one":2==n&&12!=o&&72!=o&&92!=o?"two":(3==n||4==n||9==n)&&(o<10||o>19)&&(o<70||o>79)&&(o<90||o>99)?"few":0!=e&&r&&0==a?"many":"other"},bs:function(e){var t=String(e).split("."),r=t[0],n=t[1]||"",o=!t[1],a=r.slice(-1),i=r.slice(-2),u=n.slice(-1),c=n.slice(-2);return o&&1==a&&11!=i||1==u&&11!=c?"one":o&&a>=2&&a<=4&&(i<12||i>14)||u>=2&&u<=4&&(c<12||c>14)?"few":"other"},ca:function(e){var t=String(e).split("."),r=t[0],n=!t[1],o=r.slice(-6);return 1==e&&n?"one":0!=r&&0==o&&n?"many":"other"},ceb:function(e){var t=String(e).split("."),r=t[0],n=t[1]||"",o=!t[1],a=r.slice(-1),i=n.slice(-1);return o&&(1==r||2==r||3==r)||o&&4!=a&&6!=a&&9!=a||!o&&4!=i&&6!=i&&9!=i?"one":"other"},cs:function(e){var t=String(e).split("."),r=t[0],n=!t[1];return 1==e&&n?"one":r>=2&&r<=4&&n?"few":n?"other":"many"},cy:function(e){return 0==e?"zero":1==e?"one":2==e?"two":3==e?"few":6==e?"many":"other"},da:function(e){var t=String(e).split("."),r=t[0],n=Number(t[0])==e;return 1!=e&&(n||0!=r&&1!=r)?"other":"one"},dsb:function(e){var t=String(e).split("."),r=t[0],n=t[1]||"",o=!t[1],a=r.slice(-2),i=n.slice(-2);return o&&1==a||1==i?"one":o&&2==a||2==i?"two":o&&(3==a||4==a)||3==i||4==i?"few":"other"},dz:function(e){return"other"},es:function(e){var t=String(e).split("."),r=t[0],n=!t[1],o=r.slice(-6);return 1==e?"one":0!=r&&0==o&&n?"many":"other"},ff:function(e){return e>=0&&e<2?"one":"other"},fr:function(e){var t=String(e).split("."),r=t[0],n=!t[1],o=r.slice(-6);return e>=0&&e<2?"one":0!=r&&0==o&&n?"many":"other"},ga:function(e){var t=Number(String(e).split(".")[0])==e;return 1==e?"one":2==e?"two":t&&e>=3&&e<=6?"few":t&&e>=7&&e<=10?"many":"other"},gd:function(e){var t=Number(String(e).split(".")[0])==e;return 1==e||11==e?"one":2==e||12==e?"two":t&&e>=3&&e<=10||t&&e>=13&&e<=19?"few":"other"},he:function(e){var t=String(e).split("."),r=t[0],n=!t[1];return 1==r&&n||0==r&&!n?"one":2==r&&n?"two":"other"},is:function(e){var t=String(e).split("."),r=t[0],n=(t[1]||"").replace(/0+$/,""),o=Number(t[0])==e,a=r.slice(-1),i=r.slice(-2);return o&&1==a&&11!=i||n%10==1&&n%100!=11?"one":"other"},ksh:function(e){return 0==e?"zero":1==e?"one":"other"},lt:function(e){var t=String(e).split("."),r=t[1]||"",n=Number(t[0])==e,o=n&&t[0].slice(-1),a=n&&t[0].slice(-2);return 1==o&&(a<11||a>19)?"one":o>=2&&o<=9&&(a<11||a>19)?"few":0!=r?"many":"other"},lv:function(e){var t=String(e).split("."),r=t[1]||"",n=r.length,o=Number(t[0])==e,a=o&&t[0].slice(-1),i=o&&t[0].slice(-2),u=r.slice(-2),c=r.slice(-1);return o&&0==a||i>=11&&i<=19||2==n&&u>=11&&u<=19?"zero":1==a&&11!=i||2==n&&1==c&&11!=u||2!=n&&1==c?"one":"other"},mk:function(e){var t=String(e).split("."),r=t[0],n=t[1]||"",o=!t[1],a=r.slice(-1),i=r.slice(-2),u=n.slice(-1),c=n.slice(-2);return o&&1==a&&11!=i||1==u&&11!=c?"one":"other"},mt:function(e){var t=String(e).split("."),r=Number(t[0])==e&&t[0].slice(-2);return 1==e?"one":2==e?"two":0==e||r>=3&&r<=10?"few":r>=11&&r<=19?"many":"other"},pa:function(e){return 0==e||1==e?"one":"other"},pl:function(e){var t=String(e).split("."),r=t[0],n=!t[1],o=r.slice(-1),a=r.slice(-2);return 1==e&&n?"one":n&&o>=2&&o<=4&&(a<12||a>14)?"few":n&&1!=r&&(0==o||1==o)||n&&o>=5&&o<=9||n&&a>=12&&a<=14?"many":"other"},pt:function(e){var t=String(e).split("."),r=t[0],n=!t[1],o=r.slice(-6);return 0==r||1==r?"one":0!=r&&0==o&&n?"many":"other"},ro:function(e){var t=String(e).split("."),r=!t[1],n=Number(t[0])==e&&t[0].slice(-2);return 1==e&&r?"one":!r||0==e||1!=e&&n>=1&&n<=19?"few":"other"},ru:function(e){var t=String(e).split("."),r=t[0],n=!t[1],o=r.slice(-1),a=r.slice(-2);return n&&1==o&&11!=a?"one":n&&o>=2&&o<=4&&(a<12||a>14)?"few":n&&0==o||n&&o>=5&&o<=9||n&&a>=11&&a<=14?"many":"other"},se:function(e){return 1==e?"one":2==e?"two":"other"},si:function(e){var t=String(e).split("."),r=t[0],n=t[1]||"";return 0==e||1==e||0==r&&1==n?"one":"other"},sl:function(e){var t=String(e).split("."),r=t[0],n=!t[1],o=r.slice(-2);return n&&1==o?"one":n&&2==o?"two":n&&(3==o||4==o)||!n?"few":"other"}};function l(e){return"pt-PT"===e?e:function(e){var t=e.match(f);if(!t)throw TypeError("Invalid locale: ".concat(e));return t[1]}(e)}s.as=s.am,s.az=s.af,s.bg=s.af,s.bn=s.am,s.brx=s.af,s.ce=s.af,s.chr=s.af,s.de=s.ast,s.ee=s.af,s.el=s.af,s.en=s.ast,s.et=s.ast,s.eu=s.af,s.fa=s.am,s.fi=s.ast,s.fil=s.ceb,s.fo=s.af,s.fur=s.af,s.fy=s.ast,s.gl=s.ast,s.gu=s.am,s.ha=s.af,s.hi=s.am,s.hr=s.bs,s.hsb=s.dsb,s.hu=s.af,s.hy=s.ff,s.ia=s.ast,s.id=s.dz,s.ig=s.dz,s.it=s.ca,s.ja=s.dz,s.jgo=s.af,s.jv=s.dz,s.ka=s.af,s.kea=s.dz,s.kk=s.af,s.kl=s.af,s.km=s.dz,s.kn=s.am,s.ko=s.dz,s.ks=s.af,s.ku=s.af,s.ky=s.af,s.lb=s.af,s.lkt=s.dz,s.lo=s.dz,s.ml=s.af,s.mn=s.af,s.mr=s.af,s.ms=s.dz,s.my=s.dz,s.nb=s.af,s.ne=s.af,s.nl=s.ast,s.nn=s.af,s.no=s.af,s.or=s.af,s.pcm=s.am,s.ps=s.af,s.rm=s.af,s.sah=s.dz,s.sc=s.ast,s.sd=s.af,s.sk=s.cs,s.so=s.af,s.sq=s.af,s.sr=s.bs,s.su=s.dz,s.sv=s.ast,s.sw=s.ast,s.ta=s.af,s.te=s.af,s.th=s.dz,s.ti=s.pa,s.tk=s.af,s.to=s.dz,s.tr=s.af,s.ug=s.af,s.uk=s.ru,s.ur=s.ast,s.uz=s.af,s.vi=s.dz,s.wae=s.af,s.wo=s.dz,s.xh=s.af,s.yi=s.ast,s.yo=s.dz,s.yue=s.dz,s.zh=s.dz,s.zu=s.am;var f=/^([a-z0-9]+)/i;function p(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var y=function(){var e,t;function r(e,t){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r);var n=r.supportedLocalesOf(e);if(0===n.length)throw RangeError("Unsupported locale: "+e);if(t&&"cardinal"!==t.type)throw RangeError('Only "cardinal" "type" is supported');this.$=s[l(n[0])]}return e=[{key:"select",value:function(e){return this.$(e)}}],t=[{key:"supportedLocalesOf",value:function(e){return"string"==typeof e&&(e=[e]),e.filter(function(e){return s[l(e)]})}}],e&&p(r.prototype,e),t&&p(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function m(e){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r,n,o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var a=[],i=!0,u=!1;try{for(o=o.call(e);!(i=(r=o.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,n=e}finally{try{i||null==o.return||o.return()}finally{if(u)throw n}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return v(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return v(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var g=["second","minute","hour","day","week","month","quarter","year"],w=["auto","always"],O=["long","short","narrow"],j=["lookup","best fit"],S=function(){var e;function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,t);var n=r.numeric,a=r.style,i=r.localeMatcher;if(this.numeric="always",this.style="long",this.localeMatcher="lookup",void 0!==n){if(0>w.indexOf(n))throw RangeError('Invalid "numeric" option: '.concat(n));this.numeric=n}if(void 0!==a){if(0>O.indexOf(a))throw RangeError('Invalid "style" option: '.concat(a));this.style=a}if(void 0!==i){if(0>j.indexOf(i))throw RangeError('Invalid "localeMatcher" option: '.concat(i));this.localeMatcher=i}if("string"==typeof e&&(e=[e]),e.push(o),this.locale=t.supportedLocalesOf(e,{localeMatcher:this.localeMatcher})[0],!this.locale)throw Error("No supported locale was found");y.supportedLocalesOf(this.locale).length>0?this.pluralRules=new y(this.locale):console.warn('"'.concat(this.locale,'" locale is not supported')),"undefined"!=typeof Intl&&Intl.NumberFormat?(this.numberFormat=new Intl.NumberFormat(this.locale),this.numberingSystem=this.numberFormat.resolvedOptions().numberingSystem):this.numberingSystem="latn",this.locale=c(this.locale,{localeMatcher:this.localeMatcher})}return e=[{key:"format",value:function(){var e=k(arguments),t=d(e,2),r=t[0],n=t[1];return this.getRule(r,n).replace("{0}",this.formatNumber(Math.abs(r)))}},{key:"formatToParts",value:function(){var e=k(arguments),t=d(e,2),r=t[0],n=t[1],o=this.getRule(r,n),a=o.indexOf("{0}");if(a<0)return[{type:"literal",value:o}];var i=[];return a>0&&i.push({type:"literal",value:o.slice(0,a)}),i=i.concat(this.formatNumberToParts(Math.abs(r)).map(function(e){return b(b({},e),{},{unit:n})})),a+3<o.length-1&&i.push({type:"literal",value:o.slice(a+3)}),i}},{key:"getRule",value:function(e,t){var r=a[this.locale][this.style][t];if("string"==typeof r)return r;if("auto"===this.numeric){if(-2===e||-1===e){var n=r["previous".concat(-1===e?"":"-"+Math.abs(e))];if(n)return n}else if(1===e||2===e){var o=r["next".concat(1===e?"":"-"+Math.abs(e))];if(o)return o}else if(0===e&&r.current)return r.current}var i=r[e<0||0===e&&1/e==-1/0?"past":"future"];return"string"==typeof i?i:i[this.pluralRules&&this.pluralRules.select(Math.abs(e))||"other"]||i.other}},{key:"formatNumber",value:function(e){return this.numberFormat?this.numberFormat.format(e):String(e)}},{key:"formatNumberToParts",value:function(e){return this.numberFormat&&this.numberFormat.formatToParts?this.numberFormat.formatToParts(e):[{type:"integer",value:this.formatNumber(e)}]}},{key:"resolvedOptions",value:function(){return{locale:this.locale,style:this.style,numeric:this.numeric,numberingSystem:this.numberingSystem}}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();S.supportedLocalesOf=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"==typeof e)e=[e];else if(!Array.isArray(e))throw TypeError('Invalid "locales" argument');return e.filter(function(e){return c(e,t)})},S.addLocale=function(e){if(!e)throw Error("No locale data passed");a[e.locale]=e,i[e.locale.toLowerCase()]=e.locale},S.setDefaultLocale=function(e){o=e},S.getDefaultLocale=function(){return o},S.PluralRules=y;var P='Invalid "unit" argument';function k(e){if(e.length<2)throw TypeError('"unit" argument is required');return[function(e){if(e=Number(e),Number.isFinite&&!Number.isFinite(e))throw RangeError("".concat('Invalid "number" argument',": ").concat(e));return e}(e[0]),function(e){if("symbol"===m(e))throw TypeError(P);if("string"!=typeof e||("s"===e[e.length-1]&&(e=e.slice(0,e.length-1)),0>g.indexOf(e)))throw RangeError("".concat(P,": ").concat(e));return e}(e[1])]}function A(e){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var D=function(){var e;function t(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,t),this.cache={}}return e=[{key:"get",value:function(){for(var e=this.cache,t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];for(var o=0;o<r.length;o++){var a=r[o];if("object"!==A(e))return;e=e[a]}return e}},{key:"put",value:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n=t.pop(),o=t.pop(),a=this.cache,i=0;i<t.length;i++){var u=t[i];"object"!==A(a[u])&&(a[u]={}),a=a[u]}return a[o]=n}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function T(e){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function E(e){return(E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var I={}.constructor;function N(e){switch(e){case"second":return 1;case"minute":return 60;case"hour":return 3600;case"day":return 86400;case"week":return 604800;case"month":return 2630016;case"year":return 31556952}}function F(e){return void 0!==e.factor?e.factor:N(e.unit||e.formatAs)||1}function M(e){return"floor"===e?Math.floor:Math.round}function R(e){return"floor"===e?1:.5}function z(e){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function L(e,t){var r,n=t.prevStep,o=t.timestamp,a=t.now,i=t.future,u=t.round;return n&&(n.id||n.unit)&&(r=e["threshold_for_".concat(n.id||n.unit)]),void 0===r&&void 0!==e.threshold&&"function"==typeof(r=e.threshold)&&(r=r(a,i)),void 0===r&&(r=e.minTime),"object"===z(r)&&(r=n&&n.id&&void 0!==r[n.id]?r[n.id]:r.default),"function"==typeof r&&(r=r(o,{future:i,getMinTimeForUnit:function(e,t){return q(e,t||n&&n.formatAs,{round:u})}})),void 0===r&&e.test&&(r=e.test(o,{now:a,future:i})?0:9007199254740991),void 0===r&&(n?e.formatAs&&n.formatAs&&(r=q(e.formatAs,n.formatAs,{round:u})):r=0),void 0===r&&console.warn("[javascript-time-ago] A step should specify `minTime`:\n"+JSON.stringify(e,null,2)),r}function q(e,t,r){var n,o=r.round,a=N(e);if(n="now"===t?N(e):N(t),void 0!==a&&void 0!==n)return a-n*(1-R(o))}function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var _={};function U(e){return _[e]}function $(e){if(!e)throw Error("[javascript-time-ago] No locale data passed.");_[e.locale]=e}var Y={steps:[{formatAs:"now"},{formatAs:"second"},{formatAs:"minute"},{formatAs:"hour"},{formatAs:"day"},{formatAs:"week"},{formatAs:"month"},{formatAs:"year"}],labels:"long"};function V(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function J(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?V(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):V(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var Z=J(J({},Y),{},{steps:Y.steps.filter(function(e){return"second"!==e.formatAs})}),B=[{factor:1,unit:"now"},{threshold:1,threshold_for_now:45.5,factor:1,unit:"second"},{threshold:45.5,factor:60,unit:"minute"},{threshold:150,granularity:5,factor:60,unit:"minute"},{threshold:1350,factor:1800,unit:"half-hour"},{threshold:2550,threshold_for_minute:3150,factor:3600,unit:"hour"},{threshold:20.5/24*86400,factor:86400,unit:"day"},{threshold:475200,factor:604800,unit:"week"},{threshold:2116800,factor:2630016,unit:"month"},{threshold:27615168,factor:31556952,unit:"year"}],G={gradation:B,flavour:"long",units:["now","minute","hour","day","week","month","year"]},H={gradation:B,flavour:"long-time",units:["now","minute","hour","day","week","month","year"]};function K(e){return e instanceof Date?e:new Date(e)}var Q=[{formatAs:"second"},{formatAs:"minute"},{formatAs:"hour"}],W={};("undefined"==typeof Intl?"undefined":T(Intl))==="object"&&"function"==typeof Intl.DateTimeFormat?Q.push({minTime:function(e,t){return t.future,(0,t.getMinTimeForUnit)("day")},format:function(e,t){return W[t]||(W[t]={}),W[t].dayMonth||(W[t].dayMonth=new Intl.DateTimeFormat(t,{month:"short",day:"numeric"})),W[t].dayMonth.format(K(e))}},{minTime:function(e,t){if(!t.future)return(new Date(new Date(e).getFullYear()+1,0).getTime()-e)/1e3;var r=new Date(new Date(e).getFullYear(),0).getTime()-1;return(e-r)/1e3},format:function(e,t){return W[t]||(W[t]={}),W[t].dayMonthYear||(W[t].dayMonthYear=new Intl.DateTimeFormat(t,{year:"numeric",month:"short",day:"numeric"})),W[t].dayMonthYear.format(K(e))}}):Q.push({formatAs:"day"},{formatAs:"week"},{formatAs:"month"},{formatAs:"year"});var X={steps:Q,labels:["mini","short-time","narrow","short"]};function ee(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function et(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ee(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var er=et(et({},X),{},{steps:[{formatAs:"now"}].concat(X.steps)});function en(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?en(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ea=eo(eo({},X),{},{steps:X.steps.filter(function(e){return"second"!==e.formatAs})});function ei(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ei(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ei(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ec=eu(eu({},ea),{},{steps:[{formatAs:"now"}].concat(ea.steps)});function es(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function el(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?es(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):es(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ef=el(el({},X),{},{steps:X.steps.filter(function(e){return"second"!==e.formatAs}).map(function(e){return"minute"===e.formatAs?el(el({},e),{},{minTime:60}):e})}),ep={steps:[{formatAs:"second"},{formatAs:"minute"},{formatAs:"hour"},{formatAs:"day"},{formatAs:"month"},{formatAs:"year"}],labels:["mini","short-time","narrow","short"]};function ey(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function em(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ey(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ey(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eh=em(em({},ep),{},{steps:[{formatAs:"now"}].concat(ep.steps)});function eb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ed(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eb(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ev=ed(ed({},ep),{},{steps:ep.steps.filter(function(e){return"second"!==e.formatAs})});function eg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ew(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eg(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eg(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eO=ew(ew({},ev),{},{steps:[{formatAs:"now"}].concat(ev.steps)});function ej(e){return(ej="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eS(e,t){if(e){if("string"==typeof e)return eP(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eP(e,t)}}function eP(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var ek=function(){var e;function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.polyfill;!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,t),"string"==typeof e&&(e=[e]),this.locale=function(e,t){for(var r,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(r)return(r=r.call(e)).next.bind(r);if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return x(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return x(e,void 0)}}(e))){r&&(e=r);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(e);!(r=n()).done;){var o=r.value;if(t(o))return o;for(var a=o.split("-");a.length>1;)if(a.pop(),t(o=a.join("-")))return o}throw Error("No locale data has been registered for any of the locales: ".concat(e.join(", ")))}(e.concat(t.getDefaultLocale()),U),"undefined"!=typeof Intl&&Intl.NumberFormat&&(this.numberFormat=new Intl.NumberFormat(this.locale)),!1===n?(this.IntlRelativeTimeFormat=Intl.RelativeTimeFormat,this.IntlPluralRules=Intl.PluralRules):(this.IntlRelativeTimeFormat=S,this.IntlPluralRules=S.PluralRules),this.relativeTimeFormatCache=new D,this.pluralRulesCache=new D}return e=[{key:"format",value:function(e,t,r){r||(t&&!function(e){return"string"==typeof e||void 0!==E(e)&&null!==e&&e.constructor===I&&(Array.isArray(e.steps)||Array.isArray(e.gradation)||Array.isArray(e.flavour)||"string"==typeof e.flavour||Array.isArray(e.labels)||"string"==typeof e.labels||Array.isArray(e.units)||"function"==typeof e.custom)}(t)?(r=t,t=void 0):r={}),t||(t=Z),"string"==typeof t&&(t=function(e){switch(e){case"default":case"round":return Y;case"round-minute":return Z;case"approximate":default:return G;case"time":case"approximate-time":return H;case"mini":return ep;case"mini-now":return eh;case"mini-minute":return ev;case"mini-minute-now":return eO;case"twitter":return X;case"twitter-now":return er;case"twitter-minute":return ea;case"twitter-minute-now":return ec;case"twitter-first-minute":return ef}}(t));var n,o,a,i,u,c,s,l=function(e){if(e.constructor===Date||"object"===ej(e)&&"function"==typeof e.getTime)return e.getTime();if("number"==typeof e)return e;throw Error("Unsupported relative time formatter input: ".concat(ej(e),", ").concat(e))}(e),f=this.getLabels(t.flavour||t.labels),p=f.labels,y=f.labelsType;void 0!==t.now&&(s=t.now),void 0===s&&void 0!==r.now&&(s=r.now),void 0===s&&(s=Date.now());var m=(s-l)/1e3,h=r.future||m<0,b=(n=U(this.locale).now,o=U(this.locale).long,(a=p.now||n&&n.now)?"string"==typeof a?a:h?a.future:a.past:o&&o.second&&o.second.current?o.second.current:void 0);if(t.custom){var d=t.custom({now:s,date:new Date(l),time:l,elapsed:m,locale:this.locale});if(void 0!==d)return d}var v=(i=t.units,u=Object.keys(p),b&&u.push("now"),i&&(u=i.filter(function(e){return"now"===e||u.indexOf(e)>=0})),u),g=r.round||t.round,w=function(e){if(Array.isArray(e))return e}(c=function(e,t,r){var n=r.now,o=r.future,a=r.round,i=r.units,u=r.getNextStep,c=function(e,t,r){var n=r.now,o=r.future,a=r.round;if(0!==e.length){var i=function e(t,r,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=L(t[o],function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({prevStep:t[o-1],timestamp:n.now-1e3*r},n));return void 0===a||Math.abs(r)<a?o-1:o===t.length-1?o:e(t,r,n,o+1)}(e,t,{now:n,future:o||t<0,round:a});if(-1!==i){var u=e[i];return u.granularity&&0==M(a)(Math.abs(t)/F(u)/u.granularity)*u.granularity&&i>0?e[i-1]:u}}}(e=e.filter(function(e){var t=e.unit,r=e.formatAs;return!(t=t||r)||i.indexOf(t)>=0}),t,{now:n,future:o,round:a});if(u){if(c){var s=e[e.indexOf(c)-1],l=e[e.indexOf(c)+1];return[s,c,l]}return[void 0,void 0,e[0]]}return c}(t.gradation||t.steps||Z.steps,m,{now:s,units:v,round:g,future:h,getNextStep:!0}))||function(e,t){var r,n,o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var a=[],i=!0,u=!1;try{for(o=o.call(e);!(i=(r=o.next()).done)&&(a.push(r.value),3!==a.length);i=!0);}catch(e){u=!0,n=e}finally{try{i||null==o.return||o.return()}finally{if(u)throw n}}return a}}(c,3)||eS(c,3)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),O=w[0],j=w[1],S=w[2],P=this.formatDateForStep(l,j,m,{labels:p,labelsType:y,nowLabel:b,now:s,future:h,round:g})||"";return r.getTimeToNextUpdate?[P,function(e,t,r){var n,o=r.prevStep,a=r.nextStep,i=r.now,u=r.future,c=r.round,s=e.getTime?e.getTime():e,l=function(e){return function(e,t,r){var n=r.now,o=r.round;if(N(e)){var a=1e3*N(e),i=Math.abs(t-n),u=M(o)(i/a)*a;return t>n?u>0?i-u+((1-R(o))*a+1):i-u+1:-(i-u)+R(o)*a}}(e,s,{now:i,round:c})},f=function(e,t,r){var n=r.now,o=r.future,a=r.round,i=r.prevStep;if(e){var u,c,s,l,f=void 0===(l=L(e,{timestamp:t,now:c=(u={now:n,future:o,round:a,prevStep:i}).now,future:s=u.future,round:u.round,prevStep:u.prevStep}))?void 0:s?t-1e3*l+1:0===l&&t===c?31536e9:t+1e3*l;if(void 0===f)return;return f-n}return o?t-n+1:31536e9}(u?t:a,s,{future:u,now:i,round:c,prevStep:u?o:t});if(void 0!==f){if(t&&(t.getTimeToNextUpdate&&(n=t.getTimeToNextUpdate(s,{getTimeToNextUpdateForUnit:l,getRoundFunction:M,now:i,future:u,round:c})),void 0===n)){var p=t.unit||t.formatAs;p&&(n=l(p))}return void 0===n?f:Math.min(n,f)}}(l,j,{nextStep:S,prevStep:O,now:s,future:h,round:g})]:P}},{key:"formatDateForStep",value:function(e,t,r,n){var o=this,a=n.labels,i=n.labelsType,u=n.nowLabel,c=n.now,s=n.future,l=n.round;if(t){if(t.format)return t.format(e,this.locale,{formatAs:function(e,t){return o.formatValue(t,e,{labels:a,future:s})},now:c,future:s});var f=t.unit||t.formatAs;if(!f)throw Error("[javascript-time-ago] Each step must define either `formatAs` or `format()`. Step: ".concat(JSON.stringify(t)));if("now"===f)return u;var p=Math.abs(r)/F(t);t.granularity&&(p=M(l)(p/t.granularity)*t.granularity);var y=-1*Math.sign(r)*M(l)(p);switch(0===y&&(y=s?0:-0),i){case"long":case"short":case"narrow":return this.getFormatter(i).format(y,f);default:return this.formatValue(y,f,{labels:a,future:s})}}}},{key:"formatValue",value:function(e,t,r){var n=r.labels,o=r.future;return this.getFormattingRule(n,t,e,{future:o}).replace("{0}",this.formatNumber(Math.abs(e)))}},{key:"getFormattingRule",value:function(e,t,r,n){var o=n.future;if(this.locale,"string"==typeof(e=e[t]))return e;var a=e[0===r?o?"future":"past":r<0?"past":"future"]||e;return"string"==typeof a?a:a[this.getPluralRules().select(Math.abs(r))]||a.other}},{key:"formatNumber",value:function(e){return this.numberFormat?this.numberFormat.format(e):String(e)}},{key:"getFormatter",value:function(e){return this.relativeTimeFormatCache.get(this.locale,e)||this.relativeTimeFormatCache.put(this.locale,e,new this.IntlRelativeTimeFormat(this.locale,{style:e}))}},{key:"getPluralRules",value:function(){return this.pluralRulesCache.get(this.locale)||this.pluralRulesCache.put(this.locale,new this.IntlPluralRules(this.locale))}},{key:"getLabels",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];"string"==typeof e&&(e=[e]),e=(e=e.map(function(e){switch(e){case"tiny":case"mini-time":return"mini";default:return e}})).concat("long");for(var t,r=U(this.locale),n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(r)return(r=r.call(e)).next.bind(r);if(Array.isArray(e)||(r=eS(e))){r&&(e=r);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(e);!(t=n()).done;){var o=t.value;if(r[o])return{labelsType:o,labels:r[o]}}}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}(),eA="en";ek.getDefaultLocale=function(){return eA},ek.setDefaultLocale=function(e){return eA=e},ek.addDefaultLocale=function(e){if(n)return console.error("[javascript-time-ago] `TimeAgo.addDefaultLocale()` can only be called once. To add other locales, use `TimeAgo.addLocale()`.");n=!0,ek.setDefaultLocale(e.locale),ek.addLocale(e)},ek.addLocale=function(e){$(e),S.addLocale(e)},ek.locale=ek.addLocale,ek.addLabels=function(e,t,r){var n=U(e);n||($({locale:e}),n=U(e)),n[t]=r}}}]);