{"version": 3, "file": "static/chunks/6240.c1006855eaaa9058.js", "mappings": "0gBAcO,IAAMA,EAAU,OAAC,CAACC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAEC,aAAAA,EAAeC,EAAAA,EAAQA,CAACC,KAAK,CAAEC,UAAAA,CAAS,CAAe,CAAAC,EAEjGC,EAAW,GAAAC,EAAAC,GAAA,EAACC,MAAAA,CAAIL,UAAU,mFAC3BN,IAEL,MAAO,GAAAS,EAAAC,GAAA,EAAAD,EAAAG,QAAA,WACH,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIL,UAAWO,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,yDAA0DP,YACzE,GAAAG,EAAAK,IAAA,EAACH,MAAAA,CAAIL,UAAU,iCACVH,IAAiBC,EAAAA,EAAQA,CAACW,IAAI,EAAI,GAAAN,EAAAC,GAAA,EAAAD,EAAAG,QAAA,WAAGJ,IACtC,GAAAC,EAAAK,IAAA,EAACH,MAAAA,CAAIL,UAAU,0BACX,GAAAG,EAAAC,GAAA,EAACM,OAAAA,CAAKV,UAAU,wDAAgDP,IAChE,GAAAU,EAAAC,GAAA,EAACO,KAAAA,CAAGX,UAAU,+BAAuBL,IACpCC,GAAS,GAAAO,EAAAC,GAAA,EAACC,MAAAA,CAAIL,UAAU,uEAA+DJ,OAE3FC,IAAiBC,EAAAA,EAAQA,CAACW,IAAI,EAAI,GAAAN,EAAAC,GAAA,EAAAD,EAAAG,QAAA,WAAGJ,UAItD,2ICNO,IAAMU,EAAmB,IAE5B,IAAMC,EAAeC,CAAAA,EAAAA,EAAAA,MAAAA,EAAyB,MAExCC,EAAUC,EAAMD,OAAO,EAAI,EAAE,CAC7BE,EAAYH,CAAAA,EAAAA,EAAAA,MAAAA,EAAOE,EAAME,MAAM,CACrCD,CAAAA,EAAUE,OAAO,CAAGH,EAAME,MAAM,CAEhC,IAAME,EAAc,QAEhBJ,EADA,IAAME,EAASD,EAAUE,OAAO,CAACE,MAAM,CAAC,CAACC,EAAGC,IAAQA,IAAQC,EAC9C,QAAdR,CAAAA,EAAAA,EAAMS,QAAQ,GAAdT,KAAAA,IAAAA,GAAAA,EAAAA,IAAAA,CAAAA,EAAiBE,EACrB,EAuBA,MAAO,GAAAf,EAAAC,GAAA,EAAAD,EAAAG,QAAA,WAEH,GAAAH,EAAAK,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACV,CAACgB,EAAMU,SAAS,EAAI,GAAAvB,EAAAC,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA4BgB,EAAMvB,KAAK,EAAI,WAE/EsB,CAAAA,EAAQa,MAAM,CAAG,GAAKX,EAAUE,OAAO,CAACS,MAAM,CAAG,IAC/C,GAAAzB,EAAAK,IAAA,EAACH,MAAAA,CAAIL,UAAU,sEACVe,EAAQc,GAAG,CAAC,CAACC,EAAMR,IACT,GAAAnB,EAAAC,GAAA,EAACC,MAAAA,CAAkBL,UAAU,sDAChC,GAAAG,EAAAC,GAAA,EAACC,MAAAA,CAAIL,UAAU,wEACX,GAAAG,EAAAC,GAAA,EAACC,MAAAA,CACGL,UAAU,gDACV+B,MAAO,CAACC,MAAO,GAAiBC,MAAA,CAAdH,EAAKI,QAAQ,CAAC,IAAE,OAJ7BJ,EAAKK,EAAE,GAQ3BlB,EAAUE,OAAO,CAACU,GAAG,CAAC,CAACO,EAAOd,IACpB,GAAAnB,EAAAK,IAAA,EAACH,MAAAA,CAAqBL,UAAU,2BACnC,GAAAG,EAAAC,GAAA,EAACC,MAAAA,CAAIL,UAAU,kCACX,GAAAG,EAAAC,GAAA,EAACiC,MAAAA,CAAIrC,UAAU,+BAA+BsC,IAAKF,EAAOG,IAAI,OAElE,GAAApC,EAAAC,GAAA,EAACoC,SAAAA,CACGC,SAAUzB,EAAMyB,QAAQ,CACxBzC,UAAU,4HACV0C,QAAS,IAAMtB,EAAYE,YAC3B,GAAAnB,EAAAC,GAAA,EAACuC,EAAAA,CAASA,CAAAA,CAACX,MAAO,GAAIY,OAAQ,SARrB,OAASX,MAAA,CAAFX,QAcpC,GAAAnB,EAAAC,GAAA,EAACC,MAAAA,CAAIL,UAAU,kBACX,GAAAG,EAAAC,GAAA,EAACyC,QAAAA,CACGC,KAAK,OACLC,IAAKlC,EACL4B,SAAUzB,EAAMyB,QAAQ,CACxBO,SAAQ,GACRC,OAAO,UAAUxB,SA1DZ,IACjB,GAAI,CAACT,EAAMkC,WAAW,CAAE,OACxB,IAAMC,EAAQC,EAAEC,MAAM,CAACF,KAAK,CAC5B,GAAI,GAAUA,IAAAA,EAAMvB,MAAM,CAG1B,IAAK,IAAM0B,KAAeH,EACtBnC,EAAMkC,WAAW,CAACI,EAAaC,QAG3BvC,EAFA,IAAMwC,EAASD,EAAIE,IAAI,CAACA,IAAI,CAACD,MAAM,CAC7BtC,EAAS,IAAID,EAAUE,OAAO,CAAEqC,EAAOE,QAAQ,CAAC,QACtD1C,CAAAA,EAAAA,EAAMS,QAAQ,GAAdT,KAAAA,IAAAA,GAAAA,EAAAA,IAAAA,CAAAA,EAAiBE,EACrB,EAER,MA+CQ,GAAAf,EAAAC,GAAA,EAACC,MAAAA,UACG,GAAAF,EAAAK,IAAA,EAACmD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UACRnB,SAAUzB,EAAMyB,QAAQ,CACxBC,QAhDA,KACZ7B,GAAgBA,EAAaM,OAAO,EACpCN,EAAaM,OAAO,CAAC0C,KAAK,EAElC,EA6CoB7D,UAAU,oFACd,GAAAG,EAAAC,GAAA,EAAC0D,EAAAA,GAAcA,CAAAA,CAAC9D,UAAU,WAAU,sBAQxD,gICnGO,IAAM+D,EAAiB,IAC1B,GAAM,CAACC,kBAAAA,CAAiB,CAAEC,gBAAAA,CAAe,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACvC,CAACC,WAAAA,CAAU,CAAC,CAAGnD,EAEfoD,EAAUC,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KACpB,IAAMC,EAA8B,EAAE,CAEhCC,EAAc,IAAIN,EAAgB,CAGxC,IAAK,IAAM9B,KAFNoC,EAAYC,QAAQ,CAACL,IAAaI,EAAYE,IAAI,CAACN,GAEvCI,GAAa,CAC1B,IAAMG,EAAKV,CAAiB,CAAC7B,EAAG,CAChC,GAAI,CAACuC,EAAI,SACT,GAAM,CAACC,KAAAA,CAAI,CAAC,CAAGD,EACTE,EAAQD,EAAKjF,IAAI,EAAIiF,EAAKjF,IAAI,CAACoD,IAAI,GAAK+B,EAAAA,UAAUA,CAACC,KAAK,CAAGH,EAAKjF,IAAI,CAACkF,KAAK,CAAG,eAE7E9C,EAA2B,CAC7BiD,MAAOC,KAAAA,EAAWvB,KAAMuB,KAAAA,EAAW7C,GAAAA,EAAI1C,MAAO,GAAYiF,MAAAA,CAATE,EAAM,KAAgB3C,MAAA,CAAbyC,EAAGC,IAAI,CAACM,IAAI,EAAItF,MAAOwC,CACrF,EACAmC,EAAMG,IAAI,CAAC3C,EACf,CACA,OAAOwC,CACX,EAAG,CAACN,EAAmBC,EAAiBE,EAAW,EAEnD,MAAO,GAAAhE,EAAAC,GAAA,EAAAD,EAAAG,QAAA,WACH,GAAAH,EAAAC,GAAA,EAAC8E,EAAAA,CAAYA,CAAAA,CACTzD,SAAU0D,IACNnE,EAAMS,QAAQ,CAAC0D,CAAC,CAAC,EAAE,CACvB,EACAC,YAAajB,EAAa,CAACA,EAAW,CAAG,EAAE,CAC3CkB,YAAY,oBACZC,QAASlB,EACT3B,SAAUzB,EAAMyB,QAAQ,CACxBzC,UAAWgB,EAAMhB,SAAS,IAGtC,8FC/CYuF,4HAAAA,EAAAA,GAAAA,CAAAA,EAAAA,CAAAA,CAAAA,iDAYL,IAAMC,EAAc,GAEnB,GAAAC,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,gBACX,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CACGL,UAAU,4CACV0F,KAAK,kBAEL,GAAAD,EAAArF,GAAA,EAACoC,SAAAA,CACGM,KAAK,SACL9C,UAAW,0CAEViC,MAAA,CADGjB,SAAAA,EAAMrB,KAAK,CAAkB,cAAgB,WAChD,qEACD+C,QAAS,SACL1B,CAAc,QAAdA,CAAAA,EAAAA,EAAMS,QAAQ,GAAdT,KAAAA,IAAAA,GAAAA,EAAAA,IAAAA,CAAAA,EAAAA,OACJ,WAEA,GAAAyE,EAAArF,GAAA,EAACuF,EAAAA,CAAmBA,CAAAA,CAAC3F,UAAU,aAElCgB,EAAM4E,YAAY,EACf,GAAAH,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACI,GAAAmF,EAAArF,GAAA,EAACoC,SAAAA,CACGM,KAAK,SACL9C,UAAW,0CAEViC,MAAA,CADGjB,WAAAA,EAAMrB,KAAK,CAAoB,cAAgB,WAClD,wDACD+C,QAAS,SACL1B,CAAc,QAAdA,CAAAA,EAAAA,EAAMS,QAAQ,GAAdT,KAAAA,IAAAA,GAAAA,EAAAA,IAAAA,CAAAA,EAAAA,SACJ,WAEA,GAAAyE,EAAArF,GAAA,EAACyF,EAAAA,CAASA,CAAAA,CAAC7F,UAAU,eAKjC,GAAAyF,EAAArF,GAAA,EAACoC,SAAAA,CACGM,KAAK,SACL9C,UAAW,yCAEViC,MAAA,CADGjB,UAAAA,EAAMrB,KAAK,CAAmB,cAAgB,WACjD,0DACD+C,QAAS,SACL1B,CAAc,QAAdA,CAAAA,EAAAA,EAAMS,QAAQ,GAAdT,KAAAA,IAAAA,GAAAA,EAAAA,IAAAA,CAAAA,EAAAA,QACJ,WAEA,GAAAyE,EAAArF,GAAA,EAAC0F,EAAAA,CAAoBA,CAAAA,CAAC9F,UAAU,uDC5C7C,IAAM+F,EAAmB,IAC5B,IAAMC,EAASlF,CAAAA,EAAAA,EAAAA,MAAAA,EAAuB,MAChCmF,EAAcnF,CAAAA,EAAAA,EAAAA,MAAAA,EAAO,IAsE3B,MApEAoF,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACN,IAAMC,EAAMH,EAAO7E,OAAO,CAC1B,GAAI,CAACgF,GACDF,EAAY9E,OAAO,CADb,OAEV,GAAM,CAACiF,IAAAA,CAAG,CAAEC,KAAAA,CAAI,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAE3D,OAAAA,CAAM,CAAC,CAAGuD,EAAIK,qBAAqB,GAC9DC,EAAiBC,OAAOC,WAAW,CACnCC,EAAgBF,OAAOG,UAAU,CAEvCC,QAAQC,GAAG,CAAC,UAAW,CACnBZ,IAAKA,EAAIK,qBAAqB,GAC9BC,eAAAA,EAAgBG,cAAAA,EAAeI,OAAQhG,EAAMgG,MAAM,GAIvD,IAAMA,EACF,iBAAOhG,EAAMgG,MAAM,CACb,CACEZ,IAAKpF,EAAMgG,MAAM,CACjBX,KAAMrF,EAAMgG,MAAM,CAClBT,MAAOvF,EAAMgG,MAAM,CACnBV,OAAQtF,EAAMgG,MAAM,EAEtB,iBAAOhG,EAAMgG,MAAM,CACfhG,EAAMgG,MAAM,CAVJ,CAACZ,IAAK,EAAGC,KAAM,EAAGE,MAAO,EAAGD,OAAQ,CAAC,CAa3DU,CAAAA,EAAOZ,GAAG,CAAGY,EAAOZ,GAAG,EAAI,EAC3BY,EAAOV,MAAM,CAAGU,EAAOV,MAAM,EAAI,EACjCU,EAAOT,KAAK,CAAGS,EAAOT,KAAK,EAAI,EAC/BS,EAAOV,MAAM,CAAGU,EAAOV,MAAM,EAAI,EAEjC,IAAIW,EACA,CAACD,EAAOZ,GAAG,EAAI,GACfc,KAAKC,GAAG,CAACf,EAAKY,EAAOZ,GAAG,EAAIA,EAAME,EAAUU,CAAAA,EAAOV,MAAM,EAAI,IAC7Dc,EACA,CAACJ,EAAOX,IAAI,EAAI,GAChBa,KAAKC,GAAG,CAACd,EAAMW,EAAOX,IAAI,EAAIA,EAAOE,EAASS,CAAAA,EAAOT,KAAK,EAAI,IAG9DD,EAASG,EAAiBO,EAAOV,MAAM,EACvCW,CAAAA,EAAa,CAAEX,CAAAA,EAAUG,CAAAA,EAAiBO,EAAOV,MAAM,IAIvDC,EAAQK,EAAgBI,EAAOT,KAAK,EACpCa,CAAAA,EAAa,CAAEb,CAAAA,EAASK,CAAAA,EAAgBI,EAAOT,KAAK,IAkBxDJ,EAAIpE,KAAK,CAACsF,SAAS,CAAG,aAA8BJ,MAAAA,CAAjBG,EAAW,QAAiBnF,MAAA,CAAXgF,EAAW,OAE/DhB,EAAY9E,OAAO,CAAG,EAC1B,EAAG,CAACH,EAAMgG,MAAM,CAAC,EAGb,GAAAvB,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACI,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CAAI0C,IAAKiD,EAAQhG,UAAU,8CACvBgB,EAAMsG,QAAQ,IAI/B,0BC/EA,IAAMC,EAAYC,OAAOC,IAAI,CAACC,GAExBC,EAASC,CAAAA,EAAAA,EAAAA,OAAAA,EACb,IACSC,QAAAC,GAAA,EAAAC,EAAA3E,CAAA,OAAA2E,EAAA3E,CAAA,SAAA4E,IAAA,CAAAD,EAAAE,IAAA,CAAAF,EAAA,QACT,yCACEG,IAAK,KAiBIC,EAAa,OAAC,CACzBC,MAAAA,CAAK,CACLC,OAAAA,CAAM,CACNC,cAAAA,CAAa,CACbC,OAAAA,CAAM,CACNC,OAAAA,CAAM,CACNC,aAAAA,CAAY,CACZC,aAAAA,CAAY,CACI,CAAAzI,EACV,CAAC0I,EAAKC,EAAO,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EACpBH,EAAe,QAAU,QAErBI,EAAahI,CAAAA,EAAAA,EAAAA,MAAAA,EAAY,MAEzB,CAACiI,EAAQC,EAAU,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,UAErC3C,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAM+C,EAAqB,IACrBH,EAAW3H,OAAO,EAAI,CAAC2H,EAAW3H,OAAO,CAAC+H,QAAQ,CAACC,EAAM9F,MAAM,GACjE+E,GAEJ,EAEA,OADAgB,SAASC,gBAAgB,CAAC,QAASJ,GAC5B,KACLG,SAASE,mBAAmB,CAAC,QAASL,EACxC,CACF,GAOE,GAAAxD,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACE,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,qBACb,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,sBACb,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CACCL,UAAW,0BAGTuI,MAAAA,CAFAC,MAAAA,EAAiB,WAAa,QAC/B,kBACuCvG,MAAA,CAAtCsG,MAAAA,EAAiB,UAAY,UAC/BxF,IAAK+F,WAEL,GAAArD,EAAArF,GAAA,EAAC2F,EAAgBA,CAACiB,OAAQ,YACxB,GAAAvB,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gHACb,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,sEACZ0I,GACG,GAAAjD,EAAArF,GAAA,EAACoC,SAAAA,CACGxC,UAAW,uBAIViC,MAAA,CAHG0G,UAAAA,EACM,oBACA,qCAEVjG,QAAS,IAAMkG,EAAO,kBACzB,UAIL,GAAAnD,EAAArF,GAAA,EAACoC,SAAAA,CACGxC,UAAW,wBAIViC,MAAA,CAHG0G,SAAAA,EACM,oBACA,qCAEVjG,QAAS,IAAMkG,EAAO,iBACzB,SAGD,GAAAnD,EAAArF,GAAA,EAACM,OAAAA,CAAKV,UAAU,sBACfyI,GACG,GAAAhD,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACE,GAAAmF,EAAArF,GAAA,EAACoC,SAAAA,CACGxC,UAAU,+DACV0C,QAAS,KACP4F,MAAAA,GAAAA,IACAF,GACF,WACH,gBAMT,GAAA3C,EAAAjF,IAAA,EAACH,MAAAA,WACC,GAAAoF,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACE,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CACGL,UAAW,GAEViC,MAAA,CADGyG,GAAgBC,UAAAA,EAAkB,GAAK,SAC1C,uBAEH,GAAAlD,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,wBACb,GAAAyF,EAAArF,GAAA,EAACuH,EAAAA,CACG4B,aAAc,IACZlB,EAAO,CACLvF,KAAM+B,EAAAA,UAAUA,CAACC,KAAK,CACtBF,MAAOxB,EAAEwB,KAAK,GAEhBwD,GACF,EACApG,MAAM,OACNY,OAAQ,IACR4G,cAAe,CAAEC,YAAa,EAAM,EACpCC,eAAgB,WAMzBf,SAAAA,GACG,GAAAlD,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACE,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAW,GAAkCiC,MAAA,CAA/B0G,SAAAA,EAAiB,GAAK,oBACvC,GAAAlD,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,kCACb,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,2EACb,GAAAyF,EAAArF,GAAA,EAACuJ,EAAAA,CAAmBA,CAAAA,CAAC3J,UAAU,cAEjC,GAAAyF,EAAArF,GAAA,EAACwJ,EAAAA,CAAKA,CAAAA,CACF9G,KAAK,SACLX,GAAG,iBACHV,SA1Fb,IACfuH,EAAU5F,EAAEC,MAAM,CAAC1D,KAAK,CAC1B,EAyF8BA,MAAOoJ,EACP/I,UAAU,oCACVqF,YAAY,SACZwE,SAAQ,QAGd,GAAApE,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,iDACb,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,iBACZuH,EACIlG,MAAM,CAAC,GACJ3B,EAAKoK,WAAW,GAAGtF,QAAQ,CAACuE,EAAOe,WAAW,KAEjDjI,GAAG,CAAC,CAACnC,EAAM8B,IAEN,GAAAiE,EAAArF,GAAA,EAACoC,SAAAA,CAEGxC,UAAU,+BACV0C,QAAS,KACP2F,EAAO,CACLvF,KAAM+B,EAAAA,UAAUA,CAACkF,IAAI,CACrBrK,KAAMA,CACR,GACA0I,GACF,WAEF,GAAA3C,EAAArF,GAAA,EAAC4J,EAAAA,CAAetK,KAAMA,KAVfA,4BA2BjD,EAMasK,EAAiB,OAAC,CAAEtK,KAAAA,CAAI,CAAE,GAAGuK,EAA2B,CAAAhK,EAC7DiK,EAAgBC,CAAkB,CAACzK,EAAK,CAC9C,OAAOwK,GAAiB,GAAAzE,EAAArF,GAAA,EAAC8J,EAAAA,CAAclI,MAAO,GAAIY,OAAQ,GAAK,GAAGqH,CAAI,EACxE,EAMaG,EAAmB,OAAC,CAAE1K,KAAAA,CAAI,CAAE,GAAGuK,EAA6B,CAAAhK,SACvE,EAAS6C,IAAI,GAAK+B,EAAAA,UAAUA,CAACkF,IAAI,CACxB,GAAAtE,EAAArF,GAAA,EAAC4J,EAAAA,CAAetK,KAAMA,EAAKA,IAAI,CAAG,GAAGuK,CAAI,GAE9CvK,EAAKoD,IAAI,GAAK+B,EAAAA,UAAUA,CAACC,KAAK,CACzB,GAAAW,EAAArF,GAAA,EAACC,MAAAA,CAAK,GAAI4J,CAAI,UAAWvK,EAAKkF,KAAK,GAErC,IACT,mFCzMO,IAAMyF,EAAoB,IAC7B,IAAI/E,EAAU,IAAIgF,EAAAA,EAAsBA,IAAKC,EAAAA,EAAwBA,CAAC,CAClEvJ,EAAMwJ,SAAS,GACXxJ,EAAMwJ,SAAS,GAAKC,EAAAA,qBAAqBA,CAACC,QAAQ,CAAEpF,EAAU,IAAIqF,EAAAA,EAA2BA,IAAKC,EAAAA,EAA6BA,CAAC,CAC3H5J,EAAMwJ,SAAS,GAAKC,EAAAA,qBAAqBA,CAACI,MAAM,EAAEvF,EAAQwF,OAAO,IAAIC,EAAAA,EAAuBA,GAGzG,IAAMC,EAAmC1F,EAAQzD,GAAG,CAACoJ,GACjB,EAACxH,KAAMuB,KAAAA,EAAWrF,MAAOsL,EAAEtL,KAAK,CAAEwC,GAAI8I,EAAEtL,KAAK,CAAEF,MAAOwL,EAAEC,KAAK,IAIjG,MAAO,GAAAzF,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAArF,GAAA,EAAC8E,EAAAA,CAAYA,CAAAA,CACTzD,SAAU0D,GAAKnE,EAAMS,QAAQ,CAAC0D,CAAC,CAAC,EAAE,EAClCC,YAAapE,EAAMmD,UAAU,CAAG,CAACnD,EAAMmD,UAAU,CAAC,CAAG,EAAE,CACvDkB,YAAarE,EAAMqE,WAAW,EAAI,eAClCC,QAAS0F,KAGrB,2GCNO,IAAMG,EAAgB,IACzB,GAAM,CAACC,cAAAA,CAAa,CAAEC,QAAAA,CAAO,CAAEC,UAAAA,CAAS,CAAEC,mBAAAA,CAAkB,CAAC,CAAGrH,CAAAA,EAAAA,EAAAA,EAAAA,IAC1D,CAACsH,gBAAAA,CAAe,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAGpBC,EAAU1K,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CAE9DuJ,EAAQG,YAAY,CAAGH,EAAQG,YAAY,EAAI,CAC3CC,WAAY,GACZC,SAAU,GACVC,YAAaC,EAAAA,sBAAsBA,CAACC,QAAQ,CAC5C7K,OAAQ,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,CAC7C,EACAZ,EAAQG,YAAY,CAACxK,MAAM,CAAGqK,EAAQG,YAAY,CAACxK,MAAM,EAAI,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EAE9F,GAAM,CAACjL,OAAAA,CAAM,CAAE2K,YAAAA,CAAW,CAAED,SAAAA,CAAQ,CAAC,CAAGL,EAAQG,YAAY,CAEtDU,EAAWnB,CAAa,CAACM,EAAQG,YAAY,CAACC,UAAU,CAAC,CACzDU,EAAajB,CAAkB,CAACG,EAAQG,YAAY,CAACC,UAAU,CAAC,CAElEW,EAAY,GACZC,EAAkB,GAClB9M,EAAQ,EACP8L,CAAAA,EAAQG,YAAY,CAACC,UAAU,CAEzB,GAAcU,EAGd,CAACD,GAAYC,GACpBC,EAAY,CAAC,CAACD,EAAWG,OAAO,CAChC/M,EAAQ4M,EAAW5M,KAAK,EAAI,IACrB2M,GAAY,CAACA,EAASK,aAAa,GAC1CF,EAAkB,GAClBD,EAAY,KAPZC,EAAkB,GAClBD,EAAY,IAHZ7M,EAAQ,uBAmCZ,IAAMD,EAAQkN,CAvBO,KACjB,GAAI,CAACN,EAAU,MAAO,IAEtB,GAAI,CAACO,KAAAA,CAAI,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,oBAAAA,EACTR,EACAlB,EACAD,EACA/J,EACA,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EACjC,EAAE,CACFhB,EAAU0B,eAAe,CAACC,MAAM,EAG9BC,EAASJ,EAAKjL,GAAG,CAACsL,GAAKA,EAAEC,MAAM,CAACC,YAAY,CAACtB,EAAS,EACtDpM,EAAQ2N,SAASC,OAAOC,CAAAA,EAAAA,EAAAA,8BAAAA,EAA+BN,EAAQlB,YAErE,MAAUrM,GAAe,IAErB8N,EAAAA,EAAkBA,CAACjJ,QAAQ,CAACwH,GACrB,GAAS/J,MAAA,CAANtC,EAAM,KAEbA,CACX,KAGMD,EAAO,GAAA+F,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,oEACvB0L,EAAQhM,IAAI,CAAG,GAAA+F,EAAArF,GAAA,EAACgK,EAAgBA,CAC7B1K,KAAMgM,EAAQhM,IAAI,CAClBM,UAAU,cACT,GAAAyF,EAAArF,GAAA,EAACsN,EAAAA,CAAaA,CAAAA,CAAC1N,UAAU,gBASlC,MANAkG,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACFwG,GACAlB,EAAgBE,EAAQG,YAAY,CAACC,UAAU,EAAE9D,IAAI,EAE7D,EAAG,CAAC0D,EAAQG,YAAY,CAACC,UAAU,CAAC,EAE7B,GAAArG,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,kEACX,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,iCACV0L,EAAQ7L,YAAY,GAAKC,EAAAA,EAAQA,CAACW,IAAI,EAAI,GAAAgF,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WAAGZ,IAC9C,GAAA+F,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,0BACX,GAAAyF,EAAArF,GAAA,EAACM,OAAAA,CAAKV,UAAU,wDAAgD0L,EAAQjM,KAAK,EAAI,YACjF,GAAAgG,EAAArF,GAAA,EAACO,KAAAA,CAAGX,UAAU,+BACTyM,EAAY,GAAAhH,EAAArF,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CAAC3N,UAAU,gBAC9BJ,EAAQ,GAAA6F,EAAArF,GAAA,EAACwN,EAAAA,GAAqBA,CAAAA,CAAC5N,UAAU,cAAcP,MAAOG,IAC9DD,IAEJC,GAAS,GAAA6F,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,uEAA+DJ,OAE3F8L,EAAQ7L,YAAY,GAAKC,EAAAA,EAAQA,CAACW,IAAI,EAAI,GAAAgF,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WAAGZ,UAI9D,EAGamO,EAAe,QAWpBtB,EADAuB,EATJ,GAAM,CAAC1C,cAAAA,CAAa,CAAC,CAAGlH,CAAAA,EAAAA,EAAAA,EAAAA,IAClB,CAAC6J,cAAAA,CAAa,CAAC,CAAG/M,EAElB0K,EAAU1K,EAAM0K,OAAO,CAEvBG,EAAeH,EAAQG,YAAY,CAAGH,EAAQG,YAAY,EAAI,CAACC,WAAY,GAAIC,SAAU,GAAIC,YAAaC,EAAAA,sBAAsBA,CAACC,QAAQ,CAAE7K,OAAQ,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,CAAC,EACrL,CAAC0B,EAAQC,EAAU,CAAGpF,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,GAKjCgD,CAAAA,EAAaC,UAAU,GACnBV,CAAa,CAACS,EAAaC,UAAU,CAAC,EACtCS,CAAAA,EAAWnB,CAAa,CAACS,EAAaC,UAAU,CAAC,CAACS,QAAQ,EAE1DnB,CAAa,CAACS,EAAaC,UAAU,CAAC,EAAIV,CAAa,CAACS,EAAaC,UAAU,CAAC,CAACS,QAAQ,CAAC2B,UAAU,CAACC,UAAU,CAACtC,EAAaE,QAAQ,CAAC,EACtI+B,CAAAA,EAAQ1C,CAAa,CAACS,EAAaC,UAAU,CAAC,CAACS,QAAQ,CAAC2B,UAAU,CAACC,UAAU,CAACtC,EAAaE,QAAQ,CAAC,CAACjJ,IAAI,GAIjH,GAAM,CAAC6B,KAAAA,CAAI,CAAC,CAAGyJ,CAAAA,EAAAA,EAAAA,EAAAA,IAQf,MAPAlI,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACN,GAAI,CAAC2F,EAAaC,UAAU,EAAInH,EAAKmH,UAAU,CAAE,CAC7C,GAAM,CAACA,WAAAA,CAAU,CAAC,CAAGnH,EACrBoJ,EAAc,CAAClC,aAAc,CAAC,GAAGA,CAAY,CAAEC,WAAAA,CAAU,CAAC,EAC9D,CACJ,EAAG,EAAE,EAEE,GAAArG,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YAEH,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,aAC5C,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,iBACX,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,oBACX,GAAAyF,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CACHC,QAAQ,UACR5D,UAAU,sCACV0C,QAAS,IAAMuL,EAAU,CAACD,YACzBtC,EAAQhM,IAAI,CACT,GAAA+F,EAAArF,GAAA,EAACgK,EAAgBA,CACb1K,KAAMgM,EAAQhM,IAAI,CAClBM,UAAU,cAGb,GAAAyF,EAAArF,GAAA,EAACsN,EAAAA,CAAaA,CAAAA,CAAC1N,UAAU,kBAItC,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,2BACVgO,GACG,GAAAvI,EAAArF,GAAA,EAAC+H,EAAUA,CACPE,OAAQ,SAAUvG,CAAI,EAClBiM,EAAc,CAACrO,KAAMoC,CAAI,EAC7B,EACAwG,cAAe,WACXyF,EAAc,CAACrO,KAAMsF,KAAAA,CAAS,EAClC,EACAoD,MAAO,WACH6F,EAAU,GACd,EACA1F,OAAO,IACPC,OAAO,cAQ3B,GAAA/C,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,eAC5C,GAAAyF,EAAArF,GAAA,EAACoF,EAAWA,CAAC7F,MAAO+L,EAAQ7L,YAAY,GAAKC,EAAAA,EAAQA,CAACW,IAAI,CAAG8E,EAAM9E,IAAI,CAAG8E,EAAMxF,KAAK,CACxE0B,SAAU0D,IACN4I,EAAc,CAAClO,aAAcsF,IAAMI,EAAM9E,IAAI,CAAGX,EAAAA,EAAQA,CAACW,IAAI,CAAGX,EAAAA,EAAQA,CAACC,KAAK,EAClF,OAGjB,GAAA0F,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,oBAC5C,GAAAyF,EAAArF,GAAA,EAAC2D,EAAAA,CAAcA,CAAAA,CACXtB,SAAU,CAAC,CAACkC,EAAKmH,UAAU,CAC3BrK,SAAUqK,GAAciC,EAAc,CAClClC,aAAc,CACV,GAAGA,CAAY,CACfC,WAAAA,EACAC,SAAU,GACVC,YAAaC,EAAAA,sBAAsBA,CAACC,QAAQ,CAC5C7K,OAAQ,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,CAC7C,CACJ,GACAnI,WAAY0H,EAAaC,UAAU,MAI1CD,EAAaC,UAAU,EAAIS,GAAY,GAAA9G,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YACpC,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,WAC5C,GAAAyF,EAAArF,GAAA,EAACiO,EAAAA,CAAUA,CAAAA,CACP9B,SAAUA,EACV+B,QACI,GAAA7I,EAAAjF,IAAA,EAACmD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UACR5D,UAAU,oFACd,GAAAyF,EAAArF,GAAA,EAACmO,EAAAA,GAAcA,CAAAA,CAACvO,UAAU,WACzB6L,EAAaxK,MAAM,CAACiL,UAAU,CAAC1K,MAAM,CAAG,EACxC,GAAyCK,MAAA,CAAtC4J,EAAaxK,MAAM,CAACiL,UAAU,CAAC1K,MAAM,CAAC,YACzC,oBAGTP,OAAQwK,EAAaxK,MAAM,CAC3BI,SAAUJ,IACN0M,EAAc,CAAClC,aAAc,CAAC,GAAGA,CAAY,CAAExK,OAAAA,CAAM,CAAC,EAC1D,OAIR,GAAAoE,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,kBAC5C,GAAAyF,EAAArF,GAAA,EAACoO,EAAAA,CAAoBA,CAAAA,CACjB/M,SAAU0D,GAAK4I,EAAc,CAAClC,aAAc,CAAC,GAAGA,CAAY,CAAEE,SAAU5G,CAAC,CAAC,EAAE,CAAC,GAC7EsJ,SAAU5C,EAAaE,QAAQ,CAAG,CAACF,EAAaE,QAAQ,CAAC,CAAG,EAAE,CAC9DD,WAAYD,EAAaC,UAAU,MAG3C,GAAArG,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,iBAC5C,GAAAyF,EAAArF,GAAA,EAACiK,EAAiBA,CACdlG,WAAY0H,EAAaG,WAAW,CACpCxB,UAAWsD,EACXrM,SAAUuK,GAAe+B,EAAc,CAAClC,aAAc,CAAC,GAAGA,CAAY,CAAEG,YAAAA,CAAW,CAAC,aAQxG,4BCjQO,IAAM0C,EAAkB,GACpB,GAAAjJ,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,wDACX,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,iDACVgB,EAAMvB,KAAK,EAAI,aAEpB,GAAAgG,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,eACVgB,EAAMsG,QAAQ,4HCD/B,IAAMqH,GAAS,CAAEC,MAAO,GAAIC,KAAM,OAAQ,EAgBpCC,GAAeC,EAAAA,aAAmB,CAA2B,MAEnE,SAASC,KACP,IAAMC,EAAUF,EAAAA,UAAgB,CAACD,IAEjC,GAAI,CAACG,EACH,MAAM,MAAU,qDAGlB,OAAOA,CACT,CAEA,IAAMC,GAAiBH,EAAAA,UAAgB,CAQrC,CAAA9O,EAAgD8C,QAA/C,CAAEZ,GAAAA,CAAE,CAAEnC,UAAAA,CAAS,CAAEsH,SAAAA,CAAQ,CAAE6H,OAAAA,CAAM,CAAE,GAAGnO,EAAO,CAAAf,EACxCmP,EAAWL,EAAAA,KAAW,GACtBM,EAAU,SAA0CpN,MAAA,CAAjCE,GAAMiN,EAASE,OAAO,CAAC,KAAM,KAEtD,MACE,GAAA7J,EAAArF,GAAA,EAAC0O,GAAaS,QAAQ,EAAC5P,MAAO,CAAEwP,OAAAA,CAAO,WACrC,GAAA1J,EAAAjF,IAAA,EAACH,MAAAA,CACCmP,aAAYH,EACZtM,IAAKA,EACL/C,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EACT,wpBACAP,GAED,GAAGgB,CAAK,WAET,GAAAyE,EAAArF,GAAA,EAACqP,GAAAA,CAAWtN,GAAIkN,EAASF,OAAQA,IACjC,GAAA1J,EAAArF,GAAA,EAACsP,GAAAA,CAAqC,WACnCpI,QAKX,EACA4H,CAAAA,GAAeS,WAAW,CAAG,QAE7B,IAAMF,GAAa,OAAC,CAAEtN,GAAAA,CAAE,CAAEgN,OAAAA,CAAM,CAAuC,CAAAlP,EAC/D2P,EAAcpI,OAAOqI,OAAO,CAACV,GAAQ9N,MAAM,CAC/C,OAAC,CAACyO,EAAGX,EAAO,CAAAlP,SAAKkP,EAAOY,KAAK,EAAIZ,EAAOpK,KAAK,UAG/C,EAAiBnD,MAAM,CAKrB,GAAA6D,EAAArF,GAAA,EAAC2B,QAAAA,CACCiO,wBAAyB,CACvBC,OAAQzI,OAAOqI,OAAO,CAAClB,IACpB9M,GAAG,CACF,OAAC,CAACkO,EAAOG,EAAO,CAAAjQ,QAAK,KACTkC,MAAAA,CAAtB+N,EAAO,iBACPN,MAAAA,CADsBzN,EAAG,SAQbF,MAAA,CAPZ2N,EACC/N,GAAG,CAAC,QAEDsO,KAFE,CAACC,EAAKD,EAAW,CAAAlQ,EACf8E,EACJoL,CAAAA,OAAAA,CAAAA,EAAAA,EAAWJ,KAAK,GAAhBI,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,CAAkB,CAACJ,EAAuC,GAC1DI,EAAWpL,KAAK,CAClB,OAAOA,EAAQ,aAAqBA,MAAAA,CAARqL,EAAI,MAAUnO,MAAA,CAAN8C,EAAM,KAAK,IACjD,GACCsL,IAAI,CAAC,MAAM,WAIHA,IAAI,CAAC,KACV,IAtBK,IAyBX,EAEMC,GAAeZ,GAAAA,CAAyB,CAExCa,GAAsBxB,EAAAA,UAAgB,CAW1C,CAAA9O,EAgBE8C,QAfA,CACEyN,OAAAA,CAAM,CACNC,QAAAA,CAAO,CACPzQ,UAAAA,CAAS,CACT0Q,UAAAA,EAAY,KAAK,CACjBC,UAAAA,EAAY,EAAK,CACjBC,cAAAA,EAAgB,EAAK,CACrB1F,MAAAA,CAAK,CACL2F,eAAAA,CAAc,CACdC,eAAAA,CAAc,CACdC,UAAAA,CAAS,CACThM,MAAAA,CAAK,CACLiM,QAAAA,CAAO,CACPC,SAAAA,CAAQ,CACT,CAAAhR,EAGK,CAAEkP,OAAAA,CAAM,CAAE,CAAGH,KAEbkC,EAAenC,EAAAA,OAAa,CAAC,SAU3BI,EATN,GAAIwB,GAAa,CAACF,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAS7O,MAAM,EAC/B,OAAO,KAGT,GAAM,CAACE,EAAK,CAAG2O,EACTL,EAAM,GAAoDnO,MAAA,CAAjDgP,GAAYnP,EAAKqP,OAAO,EAAIrP,EAAKmD,IAAI,EAAI,SAClDkL,EAAaiB,GAA4BjC,EAAQrN,EAAMsO,GACvDzQ,EACJ,GAAa,iBAAOuL,EAEhBiF,MAAAA,EAAAA,KAAAA,EAAAA,EAAYjF,KAAK,CADjBiE,CAAAA,OAAAA,CAAAA,EAAAA,CAAM,CAACjE,EAA6B,GAApCiE,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAsCjE,KAAK,GAAIA,SAGrD,EAEI,GAAAzF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EAAG,cAAeuQ,YAC/BD,EAAelR,EAAO8Q,KAKxB9Q,EAIE,GAAA8F,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EAAG,cAAeuQ,YAAkBnR,IAHlD,IAIX,EAAG,CACDuL,EACA2F,EACAJ,EACAE,EACAG,EACA3B,EACA8B,EACD,EAED,GAAI,CAACT,GAAU,CAACC,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAS7O,MAAM,EAC7B,OAAO,KAGT,IAAMyP,EAAYZ,IAAAA,EAAQ7O,MAAM,EAAU8O,QAAAA,EAE1C,MACE,GAAAjL,EAAAjF,IAAA,EAACH,MAAAA,CACC0C,IAAKA,EACL/C,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EACT,yHACAP,aAGD,EAA4B,KAAfkR,EACd,GAAAzL,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,wBACZyQ,EAAQ5O,GAAG,CAAC,CAACC,EAAMN,KAClB,IAAM4O,EAAM,GAAmDnO,MAAA,CAAhD+O,GAAWlP,EAAKmD,IAAI,EAAInD,EAAKqP,OAAO,EAAI,SACjDhB,EAAaiB,GAA4BjC,EAAQrN,EAAMsO,GACvDkB,EAAiBvM,GAASjD,EAAK2O,OAAO,CAACc,IAAI,EAAIzP,EAAKiD,KAAK,CAE/D,MACE,GAAAU,EAAArF,GAAA,EAACC,MAAAA,CAECL,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EACT,sGACAmQ,QAAAA,GAAuB,yBAGxBK,GAAajP,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAMnC,KAAK,IAAKqF,KAAAA,GAAalD,EAAKmD,IAAI,CAClD8L,EAAUjP,EAAKnC,KAAK,CAAEmC,EAAKmD,IAAI,CAAEnD,EAAMN,EAAOM,EAAK2O,OAAO,EAE1D,GAAAhL,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YACG6P,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAYzQ,IAAI,EACf,GAAA+F,EAAArF,GAAA,EAAC+P,EAAWzQ,IAAI,KAEhB,CAACkR,GACC,GAAAnL,EAAArF,GAAA,EAACC,MAAAA,CACCL,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EACT,iEACA,CACE,cAAemQ,QAAAA,EACf,MAAOA,SAAAA,EACP,kDACEA,WAAAA,EACF,SAAUW,GAAaX,WAAAA,CACzB,GAEF3O,MACE,CACE,aAAcuP,EACd,iBAAkBA,CACpB,IAKR,GAAA7L,EAAAjF,IAAA,EAACH,MAAAA,CACCL,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EACT,2CACA8Q,EAAY,YAAc,0BAG5B,GAAA5L,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,yBACZqR,EAAYH,EAAe,KAC5B,GAAAzL,EAAArF,GAAA,EAACM,OAAAA,CAAKV,UAAU,iCACbmQ,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAYjF,KAAK,GAAIpJ,EAAKmD,IAAI,MAGlCnD,EAAKnC,KAAK,EACT,GAAA8F,EAAArF,GAAA,EAACM,OAAAA,CAAKV,UAAU,8DACb8B,EAAKnC,KAAK,CAAC6R,cAAc,YAhD/B1P,EAAKqP,OAAO,CAwDvB,OAIR,EAEFZ,CAAAA,GAAoBZ,WAAW,CAAG,eAElC,IAAM8B,GAAc/B,GAAAA,CAAwB,CAEtCgC,GAAqB3C,EAAAA,UAAgB,CAQzC,CAAA9O,EAEE8C,QADA,CAAE/C,UAAAA,CAAS,CAAE2R,SAAAA,EAAW,EAAK,CAAElB,QAAAA,CAAO,CAAEmB,cAAAA,EAAgB,QAAQ,CAAEZ,QAAAA,CAAO,CAAE,CAAA/Q,EAGrE,CAAEkP,OAAAA,CAAM,CAAE,CAAGH,WAEnB,CAAKyB,MAAAA,EAAAA,KAAAA,EAAAA,EAAS7O,MAAM,EAKlB,GAAA6D,EAAArF,GAAA,EAACC,MAAAA,CACC0C,IAAKA,EACL/C,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EACT,yCACAqR,QAAAA,EAA0B,OAAS,OACnC5R,YAGDyQ,EAAQ5O,GAAG,CAAC,IACX,IAAMuO,EAAM,GAAsCnO,MAAA,CAAnC+O,GAAWlP,EAAKqP,OAAO,EAAI,SACpChB,EAAaiB,GAA4BjC,EAAQrN,EAAMsO,GAE7D,MACE,GAAA3K,EAAAjF,IAAA,EAACH,MAAAA,CAECL,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EACT,6FAGD4P,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAYzQ,IAAI,GAAI,CAACiS,EACpB,GAAAlM,EAAArF,GAAA,EAAC+P,EAAWzQ,IAAI,KAEhB,GAAA+F,EAAArF,GAAA,EAACC,MAAAA,CACCL,UAAU,iCACV+B,MAAO,CACL8P,gBAAiB/P,EAAKiD,KAAK,IAIhCoL,MAAAA,EAAAA,KAAAA,EAAAA,EAAYjF,KAAK,GAfbpJ,EAAKnC,KAAK,CAkBrB,KApCK,IAuCX,GAKF,SAASyR,GACPjC,CAAmB,CACnBsB,CAAgB,CAChBL,CAAW,EAEX,GAAI,iBAAOK,GAAwBA,OAAAA,EACjC,OAGF,IAAMqB,EACJ,YAAarB,GACb,iBAAOA,EAAQA,OAAO,EACtBA,OAAAA,EAAQA,OAAO,CACXA,EAAQA,OAAO,CACfzL,KAAAA,EAEF+M,EAAyB3B,EAiB7B,OAdEA,KAAOK,GACP,iBAAOA,CAAO,CAACL,EAA4B,CAE3C2B,EAAiBtB,CAAO,CAACL,EAA4B,CAErD0B,GACA1B,KAAO0B,GACP,iBAAOA,CAAc,CAAC1B,EAAmC,EAEzD2B,CAAAA,EAAiBD,CAAc,CAC7B1B,EACD,EAGI2B,KAAkB5C,EACrBA,CAAM,CAAC4C,EAAe,CACtB5C,CAAM,CAACiB,EAA2B,CAtCxCsB,GAAmB/B,WAAW,CAAG,kECnSjCqC,EAAAA,EAAOA,CAACC,QAAQ,CACZC,EAAAA,EAAaA,CACbC,EAAAA,EAAWA,CACXC,EAAAA,EAAYA,CACZC,EAAAA,EAAWA,CACXC,EAAAA,EAAKA,CACLC,EAAAA,CAAOA,CACPC,EAAAA,EAAMA,EAsBH,IAAMC,GAAkB,IAC3B,GAAM,CAACrH,cAAAA,CAAa,CAAEC,QAAAA,CAAO,CAAEC,UAAAA,CAAS,CAAEC,mBAAAA,CAAkB,CAAC,CAAGrH,CAAAA,EAAAA,EAAAA,EAAAA,IAC1D,CAACsH,gBAAAA,CAAe,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAEpB,CAACiH,EAAgBC,EAAkB,CAAG9J,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,GAC/C,CAAC+J,EAASC,EAAW,CAAGhK,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAGjC6C,EAAU1K,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CAExD2Q,EAAoC,CACtChH,WAAY,GACZzK,OAAQ,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EACzCyG,MAAO,EAAE,EAEPC,EAAiBtH,EAAQsH,cAAc,CAAGtH,EAAQsH,cAAc,EAAIF,EAEpEG,EAAiBvH,EAAQuH,cAAc,EAAI,CAAC,CAClDD,CAAAA,EAAeD,KAAK,CAAGC,EAAeD,KAAK,EAAI,EAAE,CAEjD,IAAMxG,EAAWnB,CAAa,CAACM,EAAQsH,cAAc,CAAClH,UAAU,CAAC,CAC3DU,EAAajB,CAAkB,CAACG,EAAQsH,cAAc,CAAClH,UAAU,CAAC,CAEpEW,EAAY,GACZC,EAAkB,GAClB9M,EAAQ,EACP8L,CAAAA,EAAQsH,cAAc,CAAClH,UAAU,CAE3B,GAAcU,EAGd,CAACD,GAAYC,GACpBC,EAAY,CAAC,CAACD,EAAWG,OAAO,CAChC/M,EAAQ4M,EAAW5M,KAAK,EAAI,IACrB2M,GAAY,CAACA,EAASK,aAAa,GAC1CF,EAAkB,GAClBD,EAAY,KAPZC,EAAkB,GAClBD,EAAY,IAHZ7M,EAAQ,uBAYZ,IAAIsT,EAAY,EAEVC,EAAmB,KACrB,GAAI,CAAC5G,EAAU,MAAO,EAAE,CAExB,IAAMwG,EAAQ,IAAIC,EAAeD,KAAK,CAAC,CAClB,IAAjBA,EAAMnR,MAAM,EAAQmR,EAAMtO,IAAI,CAAC,CAACsH,SAAUqH,EAAAA,WAAWA,CAACC,SAAS,CAAEC,MAAOC,EAAAA,IAAIA,CAACC,GAAG,GAEpF,GAAM,CAAC1G,KAAAA,CAAI,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,oBAAAA,EACXR,EACAlB,EACAD,EACA4H,EAAe3R,MAAM,CACrB,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EACjCyG,EACAzH,EAAU0B,eAAe,CAACC,MAAM,EAIpC,OAFAiG,EAAYhM,KAAKuM,IAAI,CAAC3G,EAAKlL,MAAM,CAAGgR,GAE7B9F,CACX,EASA5G,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACFwG,GACAlB,EAAgBE,EAAQsH,cAAc,CAAClH,UAAU,EAAE9D,IAAI,EAE/D,EAAG,CAAC0D,EAAQsH,cAAc,CAAClH,UAAU,CAAC,EAqGtC,IAAM4H,EAAQC,CAlGO,KAUjB,IAAMlQ,EAAyB,CAC3BmQ,SAAU,EAAE,CAAEC,OAFO,EAAE,EAI3B,GAAItH,EAAU,CACV,IAAIuH,EAAaC,CAAAA,EAAAA,EAAAA,EAAAA,EAAoBxH,EAASA,QAAQ,EAAEuH,UAAU,CAE5DE,EAAezH,EAASA,QAAQ,CAAC2B,UAAU,CAC3C+F,EAAYD,EAAaC,SAAS,CAAC5S,MAAM,CAAC6S,GAAK,CAAC,CAACjB,CAAc,CAACiB,EAAE,EAAI,CAAC,CAACF,EAAa7F,UAAU,CAAC+F,EAAE,EAAI,CAACjB,CAAc,CAACiB,EAAE,CAACC,QAAQ,EACvI,IAAK,IAAI7S,EAAI,EAAGA,EAAI2S,EAAUrS,MAAM,CAAEN,IAAK,KAU5B0S,EATX,IAAM7R,EAAK8R,CAAS,CAAC3S,EAAE,CACjByD,EAAQqP,EAAAA,EAAY,CAAC9S,EAAI8S,EAAAA,EAAYA,CAACxS,MAAM,CAAC,CAC7CyS,EAAKtP,EAAMuP,IAAI,CAACD,EAAE,CAClBE,EAAKxP,EAAMuP,IAAI,CAACC,EAAE,CAExB9Q,EAAKmQ,QAAQ,CAACnP,IAAI,CAAC,CACfoN,gBAAiB0C,EACjBC,YAAaH,EACb5Q,KAAM,EAAE,CACRyH,MAAO8I,CAAAA,OAAAA,CAAAA,EAAAA,EAAa7F,UAAU,CAAChM,EAAG,GAA3B6R,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA6BvU,KAAK,GAAI,gBACjD,EACJ,CACA,IAAMqN,EAAOqG,IACPnM,EAAS,CAAC0L,EAAiB,GAAKE,EAEtC,IAAK,IAAM6B,KADO3H,EAAK4H,KAAK,CAAC1N,EAAQA,EAAS4L,GACjB,CACzB,GAAM,CAACxF,OAAAA,CAAM,CAAEuH,gBAAAA,CAAe,CAAC,CAAGF,EAE5BhV,EAAQmV,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBD,EAAgBE,qBAAqB,CAACf,EAAW,EACjFrQ,EAAKoQ,MAAM,CAACpP,IAAI,CAAChF,GAAS8N,OAAO9N,GAAOqV,IAAI,GAAKvH,OAAO9N,GAAOqV,IAAI,GAAK,YAExE,IAAK,IAAIxT,EAAI,EAAGA,EAAI2S,EAAUrS,MAAM,CAAEN,IAAK,CACvC,IAAMa,EAAK8R,CAAS,CAAC3S,EAAE,CACnByT,EAAkBlK,OAAO+J,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBD,EAAgBE,qBAAqB,CAAC1S,EAAG,GACpF6S,MAAMD,IAAkBA,CAAAA,EAAkB,GAC9CtR,EAAKmQ,QAAQ,CAACtS,EAAE,CAACmC,IAAI,CAACgB,IAAI,CAACsQ,EAC/B,CACJ,CACJ,CACA,MAAO,CAACzP,QA/CQ,CACZ2P,WAAY,GACZC,QAAS,CACLC,OAAQ,CACJC,SAAU,KACd,CACJ,CACJ,EAwCiB3R,KAAAA,CAAI,CACzB,KAmDMA,EAAO4R,CAjDS,KAElB,IAAM5R,EAAmB,EAAE,CACrB0L,EAAsB,CAAC,EAC7B,GAAI5C,EAAU,CACV,IAAIuH,EAAaC,CAAAA,EAAAA,EAAAA,EAAAA,EAAoBxH,EAASA,QAAQ,EAAEuH,UAAU,CAE5DE,EAAezH,EAASA,QAAQ,CAAC2B,UAAU,CAC3C+F,EAAYD,EAAaC,SAAS,CAAC5S,MAAM,CAAC6S,GAAK,CAAC,CAACjB,CAAc,CAACiB,EAAE,EAAI,CAAC,CAACF,EAAa7F,UAAU,CAAC+F,EAAE,EAAI,CAACjB,CAAc,CAACiB,EAAE,CAACC,QAAQ,EAEjImB,EAAUC,CAAAA,EAAAA,EAAAA,EAAAA,EAAsBtB,EAAUrS,MAAM,EAEtD,IAAK,IAAIN,EAAI,EAAGA,EAAI2S,EAAUrS,MAAM,CAAEN,IAAK,KAI5B0S,EAHX,IAAM7R,EAAK8R,CAAS,CAAC3S,EAAE,CAEvB6N,CAAM,CAAChN,EAAG,CAAG,CACT+I,MAAO8I,CAAAA,OAAAA,CAAAA,EAAAA,EAAa7F,UAAU,CAAChM,EAAG,GAA3B6R,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA6BvU,KAAK,GAAI,iBAC7CsF,MAAOuQ,CAAO,CAAChU,EAAE,CAEzB,CACA,IAAMwL,EAAOqG,IACPnM,EAAS,CAAC0L,EAAiB,GAAKE,EAEtC,IAAK,IAAM6B,KADO3H,EAAK4H,KAAK,CAAC1N,EAAQA,EAAS4L,GACjB,CACzB,GAAM,CAACxF,OAAAA,CAAM,CAAEuH,gBAAAA,CAAe,CAAC,CAAGF,EAE5Be,EAAWZ,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBD,EAAgBE,qBAAqB,CAACf,EAAW,EAM9E2B,EAAkB,CACpBvK,MANUsK,GAAYjI,OAAOiI,GAAUV,IAAI,GAAKvH,OAAOiI,GAAUV,IAAI,GAAK,UAO9E,EAEA,IAAK,IAAIxT,EAAI,EAAGA,EAAI2S,EAAUrS,MAAM,CAAEN,IAAK,CACvC,IAAMa,EAAK8R,CAAS,CAAC3S,EAAE,CACnByT,EAAkBlK,OAAO+J,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBD,EAAgBE,qBAAqB,CAAC1S,EAAG,GACpF6S,MAAMD,IAAkBA,CAAAA,EAAkB,GAC9CU,CAAK,CAACtT,EAAG,CAAG4S,CAChB,CACAtR,EAAKgB,IAAI,CAACgR,EACd,CACJ,CACA,MAAO,CAACtG,OAAAA,EAAQ1L,KAAAA,CAAI,CACxB,KAKA,MAAO,GAAAgC,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAArF,GAAA,EAACsO,EAAeA,CAACjP,MAAOiM,EAAQjM,KAAK,EAAI,sBACpCgN,GAAc7M,EAAS,GAAA6F,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,gBACnC,GAAAyF,EAAArF,GAAA,EAACsV,EAAAA,UAAUA,CAAAA,CACPC,KAAM,OACN/V,MAAOA,MAEN,GAAA6F,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YACJ,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,kBACX,GAAAyF,EAAArF,GAAA,EAACwV,EAAAA,EAAIA,CAAAA,CAACtQ,QAASoO,EAAMpO,OAAO,CAAE7B,KAAMiQ,EAAMjQ,IAAI,CAAEzD,UAAU,aAG9D,GAAAyF,EAAArF,GAAA,EAAC8O,GAAcA,CAACC,OAAQ1L,EAAK0L,MAAM,UAC/B,GAAA1J,EAAAjF,IAAA,EAACqV,GAAAA,CAASA,CAAAA,CACNC,mBAAkB,GAClBrS,KAAMA,EAAKA,IAAI,CACfsS,OAAQ,CACJ1P,KAAM,GACNE,MAAO,EACX,YAEA,GAAAd,EAAArF,GAAA,EAAC4V,GAAAA,CAAaA,CAAAA,CAACC,SAAU,KACzB,GAAAxQ,EAAArF,GAAA,EAAC8V,GAAAA,CAAKA,CAAAA,CACF/E,QAAQ,QACRgF,SAAU,GACVC,SAAU,GACVC,WAAY,IAGhB,GAAA5Q,EAAArF,GAAA,EAACkQ,GAAYA,CAACgG,OAAQ,GAAOC,QAAS,GAAA9Q,EAAArF,GAAA,EAACmQ,GAAmBA,CAACG,UAAU,WACrE,GAAAjL,EAAArF,GAAA,EAACqR,GAAWA,CAAC8E,QAAS,GAAA9Q,EAAArF,GAAA,EAACsR,GAAkBA,CAAAA,KACxClK,OAAOC,IAAI,CAAChE,EAAK0L,MAAM,EAAEtN,GAAG,CAACM,GACnB,GAAAsD,EAAArF,GAAA,EAACoW,GAAAA,CAAYA,CAAAA,CAEhBrF,QAAShP,EACTW,KAAK,SAEL2T,OAAQhT,EAAK0L,MAAM,CAAChN,EAAG,CAAC4C,KAAK,CAC7B2R,YAAa,EAEbC,IAAK,CACDpF,KAAM9N,EAAK0L,MAAM,CAAChN,EAAG,CAAC4C,KAAK,EAE/B6R,UAAW,CACPzJ,EAAG,CACP,GAZKhL,SAmBrB,GAAAsD,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,yCAAyC6W,aAAW,6BAC/D,GAAApR,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,8EACX,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,6BAAoB,qBACnC,GAAAyF,EAAAjF,IAAA,EAACsW,EAAAA,EAAMA,CAAAA,CACHC,cAAe5R,IACX,IAAM6R,EAAM1J,SAASnI,GACjB6R,IAAQpE,IACZC,EAAWmE,GACXrE,EAAkB,GACtB,YACA,GAAAlN,EAAArF,GAAA,EAAC6W,EAAAA,EAAaA,CAAAA,CAACjX,UAAU,oCACrB,GAAAyF,EAAArF,GAAA,EAAC8W,EAAAA,EAAWA,CAAAA,CAAClX,UAAU,WAAWqF,YAAauN,MAEnD,GAAAnN,EAAAjF,IAAA,EAAC2W,EAAAA,EAAaA,CAAAA,CAACnX,UAAU,iCACrB,GAAAyF,EAAArF,GAAA,EAACgX,EAAAA,EAAUA,CAAAA,CAACzX,MAAM,KAAKK,UAAU,gCAAuB,OACxD,GAAAyF,EAAArF,GAAA,EAACgX,EAAAA,EAAUA,CAAAA,CAACzX,MAAM,KAAKK,UAAU,gCAAuB,OACxD,GAAAyF,EAAArF,GAAA,EAACgX,EAAAA,EAAUA,CAAAA,CAACzX,MAAM,KAAKK,UAAU,gCAAuB,OACxD,GAAAyF,EAAArF,GAAA,EAACgX,EAAAA,EAAUA,CAAAA,CAACzX,MAAM,MAAMK,UAAU,gCAAuB,iBAIrE,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,mEACX,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,sDAA4C,QACjD0S,EAAe,OAAKQ,KAE9B,GAAAzN,EAAAjF,IAAA,EAAC6W,KAAAA,CAAGrX,UAAU,wBACV,GAAAyF,EAAArF,GAAA,EAACkX,KAAAA,UACG,GAAA7R,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CAACC,QAAS,UACT5D,UAAU,0EACVyC,SAAUiQ,GAAkB,EAC5BhQ,QAnMpB,KACTgQ,EAAiB,GAAGC,EAAkBD,EAAiB,EAC/D,WAkMiC,GAAAjN,EAAArF,GAAA,EAACmX,EAAAA,GAAeA,CAAAA,CAACvX,UAAU,eAGnC,GAAAyF,EAAArF,GAAA,EAACkX,KAAAA,UACG,GAAA7R,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CAACC,QAAS,UACT5D,UAAU,0EACVyC,SAAUiQ,GAAkBQ,EAC5BxQ,QA9MpB,KACTgQ,EAAiBQ,GAAWP,EAAkBD,EAAiB,EACvE,WA6MiC,GAAAjN,EAAArF,GAAA,EAACoX,EAAAA,GAAgBA,CAAAA,CAACxX,UAAU,8BASjE,EAEayX,GAAoB,CAAClL,EAAoBmL,EAAwBzE,KAM1E,GAHAyE,EAAeA,GAAgB,EAAE,CACjCzE,EAAiBA,GAAkB,CAAC,EAEhC1G,EAAU,CACV,IAAK,IAAM6D,KAAO7D,EAAS2B,UAAU,CAAC+F,SAAS,CACtCyD,EAAalT,QAAQ,CAAC4L,IAAMsH,EAAajT,IAAI,CAAC2L,GAC9C6C,CAAc,CAAC7C,EAAI,EAAE6C,CAAAA,CAAc,CAAC7C,EAAI,CAAG,CAAC,GAErDsH,EAAeC,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBD,EACpC,CAEA,MAAO,CAACA,aAAAA,EAAczE,eAAAA,CAAc,CACxC,EAEa2E,GAAiB,QAetBrL,EAdJ,GAAM,CAACnB,cAAAA,CAAa,CAAC,CAAGlH,CAAAA,EAAAA,EAAAA,EAAAA,IAClB,CAAC6J,cAAAA,CAAa,CAAC,CAAG/M,EAElB0K,EAAU1K,EAAM0K,OAAO,CAEvBoH,EAAoC,CACtChH,WAAY,GACZzK,OAAQ,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EACzCyG,MAAO,EAAE,EAEPC,EAAiBtH,EAAQsH,cAAc,CAAGtH,EAAQsH,cAAc,EAAIF,EAW1E,GATAE,EAAeD,KAAK,CAAGC,EAAeD,KAAK,EAAI,EAAE,CAG7CC,EAAelH,UAAU,EACrBV,CAAa,CAAC4H,EAAelH,UAAU,CAAC,EACxCS,CAAAA,EAAWnB,CAAa,CAAC4H,EAAelH,UAAU,CAAC,CAACS,QAAQ,EAIhEA,EAAU,CACV,GAAM,CAACmL,aAAAA,CAAY,CAAEzE,eAAAA,CAAc,CAAC,CAAGwE,GAAkBlL,EAAUb,EAAQgM,YAAY,CAAEhM,EAAQuH,cAAc,CAE/GvH,CAAAA,EAAQgM,YAAY,CAAGA,EACvBhM,EAAQuH,cAAc,CAAGA,CAC7B,CAEA,GAAM,CAACtO,KAAAA,CAAI,CAAC,CAAGyJ,CAAAA,EAAAA,EAAAA,EAAAA,IAQf,MAPAlI,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACN,GAAI,CAAC8M,EAAelH,UAAU,EAAInH,EAAKmH,UAAU,CAAE,CAC/C,GAAM,CAACA,WAAAA,CAAU,CAAC,CAAGnH,EACrBoJ,EAAc,CAACiF,eAAgB,CAAC,GAAGA,CAAc,CAAElH,WAAAA,CAAU,CAAC,EAClE,CACJ,EAAG,EAAE,EAEE,GAAArG,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YAEH,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,oBAC5C,GAAAyF,EAAArF,GAAA,EAAC2D,EAAAA,CAAcA,CAAAA,CACXtB,SAAU,CAAC,CAACkC,EAAKmH,UAAU,CAC3BrK,SAAUqK,GAAciC,EAAc,CAACiF,eAAgB,CAAC,GAAGA,CAAc,CAAElH,WAAAA,CAAU,CAAC,GACtF3H,WAAY6O,EAAelH,UAAU,MAG5CkH,EAAelH,UAAU,EAAIS,GAAY,GAAA9G,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YACtC,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,kBAC5C,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,UACImH,OAAO0F,MAAM,CAACX,EAAS2B,UAAU,CAACC,UAAU,EAAEtM,GAAG,CAAC,CAACgW,EAAGvW,KACnD,IAAM6S,EAAWzI,EAAQuH,cAAc,CAAC4E,EAAE1V,EAAE,CAAC,CAACgS,QAAQ,CACtD,MACI,GAAA1O,EAAAjF,IAAA,EAACH,MAAAA,CAEGqF,KAAK,SACL1F,UAAU,qLACV,GAAAyF,EAAArF,GAAA,EAAC0X,EAAAA,CAAqBA,CAAAA,CAAChV,KAAM+U,EAAE/U,IAAI,CAAE9C,UAAU,WAC/C,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,qDACV6X,EAAEpY,KAAK,GAEZ,GAAAgG,EAAArF,GAAA,EAAC2X,EAAAA,CAAMA,CAAAA,CACH/X,UAAU,UACVgY,eAAe,UACfC,QAAS,CAAC9D,EACV+D,gBAAiBD,IACb,IAAME,EAAkB,CAAC,GAAGzM,EAAQuH,cAAc,CAClDkF,CAAAA,CAAe,CAACN,EAAE1V,EAAE,CAAC,CAACgS,QAAQ,CAAG,CAAC8D,EAClClK,EAAc,CAACkF,eAAgBkF,CAAe,EAClD,MAfCN,EAAE1V,EAAE,CAkBrB,QAIR,GAAAsD,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,WAC5C,GAAAyF,EAAArF,GAAA,EAACiO,EAAAA,CAAUA,CAAAA,CACP9B,SAAUA,EACV+B,QACI,GAAA7I,EAAAjF,IAAA,EAACmD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UACR5D,UAAU,oFACd,GAAAyF,EAAArF,GAAA,EAACmO,EAAAA,GAAcA,CAAAA,CAACvO,UAAU,WACzBgT,EAAe3R,MAAM,CAACiL,UAAU,CAAC1K,MAAM,CAAG,EAC1C,GAA2CK,MAAA,CAAxC+Q,EAAe3R,MAAM,CAACiL,UAAU,CAAC1K,MAAM,CAAC,YAC3C,oBAGTP,OAAQ2R,EAAe3R,MAAM,CAC7BI,SAAUJ,IACN0M,EAAc,CAACiF,eAAgB,CAAC,GAAGA,CAAc,CAAE3R,OAAAA,CAAM,CAAC,EAC9D,OAIR,GAAAoE,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,SAC5C,GAAAyF,EAAArF,GAAA,EAACgY,EAAAA,CAAQA,CAAAA,CACL7L,SAAUA,EACVwG,MAAOC,EAAeD,KAAK,CAC3BtR,SAAUsR,GAAShF,EAAc,CAACiF,eAAgB,CAAC,GAAGA,CAAc,CAAED,MAAAA,CAAK,CAAC,GAC5EzE,QACI,GAAA7I,EAAAjF,IAAA,EAACmD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UACR5D,UAAU,oFACd,GAAAyF,EAAArF,GAAA,EAACiY,EAAAA,GAAoBA,CAAAA,CAACrY,UAAU,WAC/BgT,EAAeD,KAAK,CAACnR,MAAM,CAAG,EAC9B,GAA+BK,MAAA,CAA5B+Q,EAAeD,KAAK,CAACnR,MAAM,CAAC,UAC/B,oBAU7B,wFC7bAoQ,EAAAA,EAAOA,CAACC,QAAQ,CAACqG,EAAAA,EAAUA,CAAE/F,EAAAA,CAAOA,CAAEC,EAAAA,EAAMA,EAcrC,IAAM+F,GAAiB,IAC1B,GAAM,CAACnN,cAAAA,CAAa,CAAEC,QAAAA,CAAO,CAAEC,UAAAA,CAAS,CAAEC,mBAAAA,CAAkB,CAAC,CAAGrH,CAAAA,EAAAA,EAAAA,EAAAA,IAC1D,CAACsH,gBAAAA,CAAe,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAEpBC,EAAU1K,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CACxD6Q,EAAiBtH,EAAQsH,cAAc,CAAGtH,EAAQsH,cAAc,EAAI,CACtElH,WAAY,GACZzK,OAAQ,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EACzCkM,WAAY,EAAE,CACd1E,WAAY,EAChB,CACAd,CAAAA,EAAe3R,MAAM,CAAG2R,EAAe3R,MAAM,EAAI,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EAClF0G,EAAewF,UAAU,CAAGxF,EAAewF,UAAU,EAAI,EAAE,CAE3D,IAAMjM,EAAWnB,CAAa,CAACM,EAAQsH,cAAc,CAAClH,UAAU,CAAC,CAC3DU,EAAajB,CAAkB,CAACG,EAAQsH,cAAc,CAAClH,UAAU,CAAC,CAEpEW,EAAY,GACZC,EAAkB,GAClB9M,EAAQ,EACP8L,CAAAA,EAAQsH,cAAc,CAAClH,UAAU,CAE3B,EAASkH,cAAc,CAACwF,UAAU,EAAKC,MAAMC,OAAO,CAAChN,EAAQsH,cAAc,CAACwF,UAAU,IAAK9M,CAAAA,EAAQsH,cAAc,CAACwF,UAAU,CAAC5W,MAAM,CAAG,GAEtI,GAAc4K,EAGd,CAACD,GAAYC,GACpBC,EAAY,CAAC,CAACD,EAAWG,OAAO,CAChC/M,EAAQ4M,EAAW5M,KAAK,EAAI,IACrB2M,GAAY,CAACA,EAASK,aAAa,GAC1CF,EAAkB,GAClBD,EAAY,KAPZC,EAAkB,GAClBD,EAAY,IAHZ7M,EAAQ,+BAFRA,EAAQ,uBAcZ,IAAMuT,EAAmB,KAQrB,GAAI,CAAC5G,GAAY,CAACyG,EAAewF,UAAU,CAAE,MAAO,CAACG,OAAQ,CAAC,EAAGC,YAAa,CAAC,CAAC,EAEhF,IAAMC,EAA8B,EAAE,CACtCA,EAAYpU,IAAI,CAAC,CAACsH,SAAUqH,EAAAA,WAAWA,CAACC,SAAS,CAAEC,MAAOC,EAAAA,IAAIA,CAACC,GAAG,GAElE,GAAI,CAAC1G,KAAAA,CAAI,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,oBAAAA,EACTR,EACAlB,EACAD,EACA4H,EAAe3R,MAAM,CACrB,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EACjCuM,EACAvN,EAAU0B,eAAe,CAACC,MAAM,EAE9B2L,EAEF,CAAC,EACCD,EAEF,CAAC,EAEDG,EAAU9F,EAAewF,UAAU,CAEvC,IAAK,IAAM/D,KAAO3H,EAAM,CACpB,IAAMiM,EAA4B,CAAC,EAC7BlE,EAEF,CAAC,EAECmE,EAAuB,EAAE,CAC/B,IAAK,IAAM7W,KAAM2W,EAAS,KAIFvM,CAHpBwM,CAAAA,CAAW,CAAC5W,EAAG,CAAG,iBAAOsS,EAAIrH,MAAM,CAACC,YAAY,CAAClL,EAAG,CAAgBsS,EAAIrH,MAAM,CAACC,YAAY,CAAClL,EAAG,CAAC2S,IAAI,GAAKL,EAAIrH,MAAM,CAACC,YAAY,CAAClL,EAAG,CACpI0S,CAAqB,CAAC1S,EAAG,CAAG,iBAAOsS,EAAIE,eAAe,CAACE,qBAAqB,CAAC1S,EAAG,CAAgBsS,EAAIE,eAAe,CAACE,qBAAqB,CAAC1S,EAAG,CAAC2S,IAAI,GAAKL,EAAIE,eAAe,CAACE,qBAAqB,CAAC1S,EAAG,CAEpM,IAAM8W,EAAc1M,CAAAA,OAAAA,CAAAA,EAAAA,EAASA,QAAQ,CAAC2B,UAAU,CAACC,UAAU,CAAChM,EAAG,GAA3CoK,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA6C9M,KAAK,GAAI,iBACpEyZ,EAAoBtE,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBH,EAAIE,eAAe,CAACE,qBAAqB,CAAC1S,EAAG,EAEzF6W,EAAWvU,IAAI,CAAC,GAAmByU,MAAAA,CAAhBD,EAAY,MAAsBhX,MAAA,CAAlBiX,GACvC,CAEA,IAAMC,EAAaC,KAAKC,SAAS,CAACN,GAC5BO,EAAOC,KAAAA,UAAiB,CAAC,QAAQC,MAAM,CAACL,GAAYM,MAAM,CAAC,OAEjE,GAAIb,CAAW,CAACU,EAAK,CAAE,CACnB,IAAMI,EAAgBxS,KAAKyS,GAAG,CAAC,IAAIC,KAAKhB,CAAW,CAACU,EAAK,CAACO,SAAS,EAAEC,OAAO,GAAI,IAAIF,KAAKnF,EAAIoF,SAAS,EAAEC,OAAO,GAC/GlB,CAAAA,CAAW,CAACU,EAAK,CAACO,SAAS,CAAG,IAAID,KAAKF,GAAeK,WAAW,GACjEnB,CAAW,CAACU,EAAK,CAACU,SAAS,CAACvV,IAAI,CAACgQ,EAAItS,EAAE,CAC3C,MACIyW,CAAW,CAACU,EAAK,CAAG,CAChBU,UAAW,CAACvF,EAAItS,EAAE,CAAC,CACnB0X,UAAWpF,EAAIoF,SAAS,CACxB1X,GAAImX,EACJX,OAAQ,CAAC,EACTsB,WAAYjB,EAAW3I,IAAI,CAAC,IAChC,CAEJuI,CAAAA,CAAW,CAACU,EAAK,CAACX,MAAM,CAAClE,EAAItS,EAAE,CAAC,CAAGsS,EACnCkE,CAAM,CAAClE,EAAItS,EAAE,CAAC,CAAGsS,CACrB,CACA,MAAO,CACHmE,YAAAA,EAAaD,OAAAA,CACjB,CACJ,EA+JMuB,EAAMC,CA7JO,KAGf,IAAMvB,EAAcwB,IAAUxB,WAAW,CAKnCnV,EAAqB,CACvBoQ,OAJqB,EAAE,CAKvBD,SAAU,CACN,CACI1I,MAAO,eACPzH,KAPa,EAAE,CAQfoO,gBAAiB,EAOhB,CACD2C,YAAa,EAOZ,CACD6F,YAAa,CACjB,EACH,EAGL,IAAK,IAAI/Y,EAAI,EAAGA,EAAIkG,OAAO0F,MAAM,CAAC0L,GAAahX,MAAM,CAAEN,IAAK,KAWxDmC,EACAA,EAXA,IAAM6W,EAAK9S,OAAO0F,MAAM,CAAC0L,EAAY,CAACtX,EAAE,CAGxCmC,EAAKoQ,MAAM,CAACpP,IAAI,CAAC6V,EAAGL,UAAU,EAAI,IAClCxW,EAAKmQ,QAAQ,CAAC,EAAE,CAACnQ,IAAI,CAACgB,IAAI,CAAC+C,OAAOC,IAAI,CAAC6S,EAAG3B,MAAM,EAAE/W,MAAM,EAExD,IAAMmD,EAAQqP,EAAAA,EAAY,CAAC9S,EAAI8S,EAAAA,EAAYA,CAACxS,MAAM,CAAC,CAC7CyS,EAAKtP,EAAMuP,IAAI,CAACD,EAAE,CAClBE,EAAKxP,EAAMuP,IAAI,CAACC,EAAE,QAExB9Q,CAAAA,EAAAA,EAAKmQ,QAAQ,CAAC,EAAE,CAAC/B,eAAe,GAAhCpO,KAAAA,IAAAA,GAAAA,EAAkCgB,IAAI,CAAC8P,GACX,OAA5B9Q,CAAAA,EAAAA,EAAKmQ,QAAQ,CAAC,EAAE,CAACY,WAAW,GAA5B/Q,KAAAA,IAAAA,GAAAA,EAA8BgB,IAAI,CAAC4P,EAGvC,CAGA,OAAO5Q,CACX,KAwGM8W,EAAUC,CAtGI,KAGhB,IAAM5B,EAAcwB,IAAUxB,WAAW,CAKnCnV,EAAmB,EAOxB,CACK0L,EAAsB,CACxBsL,QAAS,CACLvP,MAAO,SACX,CASJ,EA6BMwP,EAASlT,OAAO0F,MAAM,CAAC0L,GAAahX,MAAM,CAC1C0T,EAAUC,CAAAA,EAAAA,EAAAA,EAAAA,EAAsBmF,EAAQ,GAAI,IAE9CC,EAAa,EAEjB,IAAK,IAAIrZ,EAAI,EAAGA,EAAIoZ,EAAQpZ,IAAK,CAC7B,IAAMgZ,EAAK9S,OAAO0F,MAAM,CAAC0L,EAAY,CAACtX,EAAE,CAGlC4J,EAAQoP,EAAGL,UAAU,EAAI,EAG/B9K,CAAAA,CAAM,CAACjE,EAAM,CAAG,CACZA,MAAAA,EACAnG,MAAOuQ,CAAO,CAAChU,EAAE,EAGrB,IAAMmU,EAAkB,CACpBvK,MAAAA,EACA0P,MAAOpT,OAAOC,IAAI,CAAC6S,EAAG3B,MAAM,EAAE/W,MAAM,CACpC2P,KAAM+D,CAAO,CAAChU,EAAE,EAEpBmC,EAAKgB,IAAI,CAACgR,GAEVkF,GAAcnT,OAAOC,IAAI,CAAC6S,EAAG3B,MAAM,EAAE/W,MAAM,CAiB/C,MAAO,CAACuN,OAAAA,EAAQ1L,KAAAA,EAAMkX,WAAAA,CAAU,CACpC,KAaA,MARAzU,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACFwG,GACAlB,EAAgBE,EAAQsH,cAAc,CAAClH,UAAU,EAAE9D,IAAI,EAE/D,EAAG,CAAC0D,EAAQsH,cAAc,CAAClH,UAAU,CAAC,EAEtChF,QAAQC,GAAG,CAAC,CAACwT,QAAAA,CAAO,GAEb,GAAA9U,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAArF,GAAA,EAACsO,EAAeA,CAACjP,MAAOiM,EAAQjM,KAAK,EAAI,qBACpCgN,GAAc7M,EAAS,GAAA6F,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,gBACnC,GAAAyF,EAAArF,GAAA,EAACsV,EAAAA,UAAUA,CAAAA,CACPC,KAAM,OACN/V,MAAOA,MAEN,GAAA6F,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YACJ,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,kBAEX,GAAAyF,EAAArF,GAAA,EAACya,EAAAA,EAAGA,CAAAA,CAACpX,KAAMyW,EAAKhF,QAAS,CAAC4F,GAAAA,CAAeA,CAAC,CAAE9a,UAAU,aAG1D,GAAAyF,EAAArF,GAAA,EAAC8O,GAAcA,CACXC,OAAQoL,EAAQpL,MAAM,CACtBnP,UAAU,+CAEV,GAAAyF,EAAAjF,IAAA,EAACua,GAAAA,CAAQA,CAAAA,WACL,GAAAtV,EAAArF,GAAA,EAACkQ,GAAYA,CACTgG,OAAQ,GACRC,QAAS,GAAA9Q,EAAArF,GAAA,EAACmQ,GAAmBA,CAACI,UAAS,OAE3C,GAAAlL,EAAArF,GAAA,EAACqR,GAAWA,CAAC8E,QAAS,GAAA9Q,EAAArF,GAAA,EAACsR,GAAkBA,CAAAA,KAEzC,GAAAjM,EAAAjF,IAAA,EAACwa,GAAAA,CAAWA,CAAAA,CACRvX,KAAM8W,EAAQ9W,IAAI,CAClB0N,QAAQ,QACRH,QAAQ,QACRiK,YAAa,GACbvE,YAAa,YACb,GAAAjR,EAAArF,GAAA,EAAC8a,GAAAA,CAASA,CAAAA,CACN/J,QAAQ,QACRnR,UAAU,kBACVyW,OAAO,OACP0E,SAAU,GACVpK,UAAW,QAAwCwJ,SAAqB,OAArBA,CAAAA,EAAAA,EAAQpL,MAAM,CAACxP,EAAM,GAArB4a,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAuBrP,KAAK,IAEnF,GAAAzF,EAAArF,GAAA,EAACgb,GAAAA,CAAaA,CAAAA,CACV7E,QAAS,OAAC,CAAC8E,QAAAA,CAAO,CAAC,CAAApb,EACf,GAAIob,GAAW,OAAQA,GAAW,OAAQA,EACtC,MACI,GAAA5V,EAAAjF,IAAA,EAAC8a,OAAAA,CACGC,EAAGF,EAAQG,EAAE,CACbC,EAAGJ,EAAQK,EAAE,CACbC,WAAW,SACXC,iBAAiB,mBAEjB,GAAAnW,EAAArF,GAAA,EAACyb,QAAAA,CACGN,EAAGF,EAAQG,EAAE,CACbC,EAAGJ,EAAQK,EAAE,CACb1b,UAAU,8CAETua,EAAQI,UAAU,CAACmB,QAAQ,KAEhC,GAAArW,EAAArF,GAAA,EAACyb,QAAAA,CACGN,EAAGF,EAAQG,EAAE,CACbC,EAAG,CAACJ,EAAQK,EAAE,EAAI,GAAK,GACvB1b,UAAU,iCACb,cAMjB,kBAUjC,EAEa+b,GAAgB,QAgBrBxP,EAfJ,GAAM,CAACnB,cAAAA,CAAa,CAAC,CAAGlH,CAAAA,EAAAA,EAAAA,EAAAA,IAClB,CAAC6J,cAAAA,CAAa,CAAC,CAAG/M,EAElB0K,EAAU1K,EAAM0K,OAAO,CAEvBsH,EAAiBtH,EAAQsH,cAAc,CAAGtH,EAAQsH,cAAc,EAAI,CACtElH,WAAY,GACZzK,OAAQ,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EACzCkM,WAAY,EAAE,CACd1E,WAAY,EAChB,CACAd,CAAAA,EAAe3R,MAAM,CAAG2R,EAAe3R,MAAM,EAAI,CAAC8K,MAAOC,EAAAA,KAAKA,CAACC,GAAG,CAAEC,WAAY,EAAE,EAClF0G,EAAewF,UAAU,CAAGxF,EAAewF,UAAU,EAAI,EAAE,CAC3DxF,EAAec,UAAU,CAAGd,EAAec,UAAU,EAAI,GAGrDd,EAAelH,UAAU,EACrBV,CAAa,CAAC4H,EAAelH,UAAU,CAAC,EACxCS,CAAAA,EAAWnB,CAAa,CAAC4H,EAAelH,UAAU,CAAC,CAACS,QAAQ,EAIpE,GAAM,CAAC5H,KAAAA,CAAI,CAAC,CAAGyJ,CAAAA,EAAAA,EAAAA,EAAAA,IAQf,MAPAlI,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACN,GAAI,CAAC8M,EAAelH,UAAU,EAAInH,EAAKmH,UAAU,CAAE,CAC/C,GAAM,CAACA,WAAAA,CAAU,CAAC,CAAGnH,EACrBoJ,EAAc,CAACiF,eAAgB,CAAC,GAAGA,CAAc,CAAElH,WAAAA,CAAU,CAAC,EAClE,CACJ,EAAG,EAAE,EAEE,GAAArG,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YAEH,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,oBAC5C,GAAAyF,EAAArF,GAAA,EAAC2D,EAAAA,CAAcA,CAAAA,CACXtB,SAAU,CAAC,CAACkC,EAAKmH,UAAU,CAC3BrK,SAAUqK,GAAciC,EAAc,CAACiF,eAAgB,CAAC,GAAGA,CAAc,CAAElH,WAAAA,CAAU,CAAC,GACtF3H,WAAY6O,EAAelH,UAAU,MAG5CkH,EAAelH,UAAU,EAAIS,GAAY,GAAA9G,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YACtC,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,WAC5C,GAAAyF,EAAArF,GAAA,EAACiO,EAAAA,CAAUA,CAAAA,CACP9B,SAAUA,EACV+B,QACI,GAAA7I,EAAAjF,IAAA,EAACmD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UACR5D,UAAU,oFACd,GAAAyF,EAAArF,GAAA,EAACmO,EAAAA,GAAcA,CAAAA,CAACvO,UAAU,WACzBgT,EAAe3R,MAAM,CAACiL,UAAU,CAAC1K,MAAM,CAAG,EAC1C,GAA2CK,MAAA,CAAxC+Q,EAAe3R,MAAM,CAACiL,UAAU,CAAC1K,MAAM,CAAC,YAC3C,oBAGTP,OAAQ2R,EAAe3R,MAAM,CAC7BI,SAAUJ,IACN0M,EAAc,CAACiF,eAAgB,CAAC,GAAGA,CAAc,CAAE3R,OAAAA,CAAM,CAAC,EAC9D,OAIR,GAAAoE,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,oCAA2B,qBAC5C,GAAAyF,EAAArF,GAAA,EAACoO,EAAAA,CAAoBA,CAAAA,CACjB/M,SAAU+W,GAAczK,EAAc,CAACiF,eAAgB,CAAC,GAAGA,CAAc,CAAEwF,WAAAA,CAAU,CAAC,GACtF/J,SAAUuE,EAAewF,UAAU,CACnC1M,WAAYkH,EAAelH,UAAU,CACrCkQ,WAAU,aAiB9B,kBCjdO,IAAMC,GAAc,IAIvB,IAAMvQ,EAAU1K,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CAExD,CAACX,EAAO0a,EAAS,CAAGrT,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,GAI7B3H,EAASwK,EAAQxK,MAAM,EAAI,EAAE,CAC7Bib,EAAazQ,EAAQyQ,UAAU,CAE/BC,EAAOC,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,KACjBnb,EAAOU,MAAM,CAAG,GACpBsa,EAAS,CAAC1a,EAAQ,GAAKN,EAAOU,MAAM,CACxC,EAAG,CAACsa,EAAU1a,EAAON,EAAO,EAMtBob,EAASpb,CAAM,CAACM,EAAM,CAY5B,MAVA0E,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACN,GAAI,CAACiW,EAAY,OAEjB,IAAMI,EAAWC,YAAYJ,EAAM,KAEnC,MAAO,KACHK,cAAcF,EAClB,CACJ,EAAG,CAACJ,EAAYC,EAAK,EAEd,GAAA3W,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAArF,GAAA,EAACsO,EAAeA,CAACjP,MAAOiM,EAAQjM,KAAK,UAEhC6c,EAAS,GAAA7W,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YACN,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,kCACX,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAY,6DACZ+B,MAAO,CACH2a,gBAAiB,OAAcza,MAAA,CAAPqa,EAAO,IACnC,WACD,GAAA7W,EAAArF,GAAA,EAACiC,MAAAA,CAAIC,IAAKga,EAAQtc,UAAU,eAAeuC,IAAI,SAGtDrB,GAAUA,EAAOU,MAAM,CAAG,GAAK,GAAA6D,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,mDAC3C,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,WACf,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,kEACX,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCAAsB,SAC1BwB,EAAQ,EAAE,OAAKN,EAAOU,MAAM,IAEvC,GAAA6D,EAAAjF,IAAA,EAAC6W,KAAAA,CAAGrX,UAAU,wBACV,GAAAyF,EAAArF,GAAA,EAACkX,KAAAA,UACG,GAAA7R,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CAACC,QAAS,UACT5D,UAAU,0EACV0C,QAtCvB,KAETwZ,EADY1a,EAAQ,EAAI,EAAIN,EAAOU,MAAM,CAAG,EAAIJ,EAAQ,EAE5D,WAoCgC,GAAAiE,EAAArF,GAAA,EAACmX,EAAAA,GAAeA,CAAAA,CAACvX,UAAU,eAGnC,GAAAyF,EAAArF,GAAA,EAACkX,KAAAA,UACG,GAAA7R,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CAACC,QAAS,UACT5D,UAAU,0EACV0C,QAAS0Z,WACb,GAAA3W,EAAArF,GAAA,EAACoX,EAAAA,GAAgBA,CAAAA,CAACxX,UAAU,2BAO9C,GAAAyF,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WAED,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,gBACX,GAAAyF,EAAArF,GAAA,EAACsV,EAAAA,UAAUA,CAAAA,CAAC9V,MAAM,6BAUvC,EAGa+c,GAAa,IAEtB,GAAM,CAACC,oBAAAA,CAAmB,CAAEC,YAAAA,CAAW,CAAC,CAAGpR,CAAAA,EAAAA,EAAAA,EAAAA,IACrC,CAACsC,cAAAA,CAAa,CAAC,CAAG/M,EAElB0K,EAAU1K,EAAM0K,OAAO,CAE7BA,EAAQxK,MAAM,CAAGwK,EAAQxK,MAAM,EAAI,EAAE,CAGrC,IAAMH,EAAU8b,EAAY,SAAY,EACxBA,EAAY,SAAY,CAACnR,EAAQvJ,EAAE,CAAC,EACpC0a,EAAY,SAAY,CAACnR,EAAQvJ,EAAE,CAAC,CAAC,KAAQ,CAC7CqF,OAAO0F,MAAM,CAAC2P,EAAY,SAAY,CAACnR,EAAQvJ,EAAE,CAAC,CAAC,KAAQ,EAAI,EAAE,CAG5DrB,CAAAA,EAAAA,EAAAA,MAAAA,EAAyB,MAE9C,IAAMG,EAAYH,CAAAA,EAAAA,EAAAA,MAAAA,EAAO4K,EAAQxK,MAAM,SACvCD,EAAUE,OAAO,CAAGuK,EAAQxK,MAAM,CA+B3B,GAAAuE,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YAEH,GAAAmF,EAAArF,GAAA,EAACQ,GAAAA,CAAgBA,CAAAA,CACbM,OAAQD,EAAUE,OAAO,CACzBM,SAAUP,GAAU6M,EAAc,CAAC7M,OAAAA,CAAM,GACzC4b,oBAAmB,GACnB5Z,YArBY,CAAC6Z,EAAYC,KAC7BJ,EAAoB,YAAalR,EAAQvJ,EAAE,CAAE,QAAS4a,EAAMC,EAChE,EAoBQjc,QAASA,IAGb,GAAA0E,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,+BACX,GAAAyF,EAAAjF,IAAA,EAACmB,EAAAA,CAAKA,CAAAA,CAAC3B,UAAU,qEACb,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,qDAA4C,gBAG3D,GAAAyF,EAAArF,GAAA,EAAC2X,EAAAA,CAAMA,CAAAA,CACH/X,UAAU,UACVgY,eAAe,UACfC,QAASvM,EAAQyQ,UAAU,CAC3BjE,gBAAiBiE,IACbpO,EAAc,CAACoO,WAAAA,CAAU,EAC7B,WAMpB,8BCxKO,IAAMc,GAAa,IACtB,IAAMvR,EAAU1K,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CAE9D,MAAO,GAAAsD,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAArF,GAAA,EAACsO,EAAeA,CAACjP,MAAOiM,EAAQjM,KAAK,UACjC,GAAAgG,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCAAsB,IAAE0L,EAAQ6K,OAAO,OAIlE,EAEa2G,GAAY,IACrB,GAAM,CAACnP,cAAAA,CAAa,CAAC,CAAG/M,EAElBmc,EAAcC,CAAAA,EAAAA,GAAAA,CAAAA,IAEd1R,EAAU1K,EAAM0K,OAAO,CAE7B,MAAO,GAAAjG,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC0b,QAAQ,QAAQrd,UAAU,oCAA2B,YAC5D,GAAAyF,EAAArF,GAAA,EAACkd,GAAAA,CAAQA,CAAAA,CAACnb,GAAG,QAAQnC,UAAU,uBACrB+C,IAAKoa,EACLI,aAAc7R,EAAQ6K,OAAO,EAAI,GACjCzJ,KAAM,EACN0Q,OAAQpa,GAAK2K,EAAc,CAACwI,QAASnT,EAAEC,MAAM,CAAC1D,KAAK,CAACmV,IAAI,IAAMpJ,EAAQ6K,OAAO,EAAI,EAAE,SAKzG,kBC7BA,IAAMpH,GAAS,CACXsO,aAAc,CAAC,IAAK,SAAU,MAAM,CACpCC,aAAc,CACV,MACA,QACA,SACA,QACA,cACA,QACA,kBACH,EAGQC,GAAc,IACvB,IAAMjS,EAAU1K,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CAExDyb,EAAYlS,EAAQkS,SAAS,CAEnC,MAAO,GAAAnY,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAArF,GAAA,EAACsO,EAAeA,CAACjP,MAAOiM,EAAQjM,KAAK,UACjC,GAAAgG,EAAArF,GAAA,EAACyd,GAAAA,CAAYD,UAAWA,OAIpC,EAGMC,GAAc9O,EAAAA,IAAU,CAAC,OAAC,CAAC6O,UAAAA,CAAS,CAAwB,CAAA3d,EACxD6d,EAAehd,CAAAA,EAAAA,EAAAA,MAAAA,EAAuB,MAEtCid,EAAYC,GAAAA,CAASA,CAACC,QAAQ,CAChCL,EACAzO,IAmBJ,MAjBAjJ,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACN,GAAI4X,EAAa3c,OAAO,CAAE,CAEtB,IAAM+c,EAAaJ,EAAa3c,OAAO,CAACgd,YAAY,CAAC,CAACC,KAAM,MAAM,GAG5DC,EAAkBjV,SAASkV,aAAa,CAAC,MAC/CD,CAAAA,EAAgBE,SAAS,CAAGR,EAG5BM,EAAgBre,SAAS,CAAG,2CAG5Bke,EAAWM,WAAW,CAACH,EAC3B,CACJ,EAAG,CAACN,EAAU,EAEP,GAAAtY,EAAArF,GAAA,EAACC,MAAAA,CAAI0C,IAAK+a,GACrB,EAkBAD,CAAAA,GAAYlO,WAAW,CAAG,cAEnB,IAAM8O,GAAa,IACtB,GAAM,CAAC1Q,cAAAA,CAAa,CAAC,CAAG/M,EAElBmc,EAAcC,CAAAA,EAAAA,GAAAA,CAAAA,IAEd1R,EAAU1K,EAAM0K,OAAO,CAE7B,MAAO,GAAAjG,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC0b,QAAQ,QAAQrd,UAAU,oCAA2B,YAC5D,GAAAyF,EAAArF,GAAA,EAACkd,GAAAA,CAAQA,CAAAA,CAACnb,GAAG,QAAQnC,UAAU,uBACrB+C,IAAKoa,EACLI,aAAc7R,EAAQkS,SAAS,EAAI,GACnC9Q,KAAM,EACN0Q,OAAQpa,IAIJ,IAAM2a,EAAYC,GAAAA,CAASA,CAACC,QAAQ,CAChC7a,EAAEC,MAAM,CAAC1D,KAAK,CAACmV,IAAI,GACnB3F,GAEAzD,CAAAA,EAAQkS,SAAS,GAAKG,GACtBhQ,EAAc,CAAC6P,UAAWG,CAAS,EAE3C,QAKtB,ECtGaW,GAAkH,CAC3Hlf,QAAS,CACLyF,KAAM,UACNvF,KAAM,GAAA+F,EAAArF,GAAA,EAACue,EAAAA,GAAeA,CAAAA,CAAC3e,UAAU,aACjC8C,KAAM8b,EAAAA,EAAoBA,CAACpf,OAAO,CAClCqf,MAAO7d,GAAS,GAAAyE,EAAArF,GAAA,EAACyN,EAAYA,CAAE,GAAG7M,CAAK,EAC3C,EACA6U,UAAW,CACP5Q,KAAM,aACNvF,KAAM,GAAA+F,EAAArF,GAAA,EAAC0e,EAAAA,GAAeA,CAAAA,CAAC9e,UAAU,aACjC8C,KAAM8b,EAAAA,EAAoBA,CAAC/I,SAAS,CACpCgJ,MAAO7d,GAAS,GAAAyE,EAAArF,GAAA,EAACwX,GAAcA,CAAE,GAAG5W,CAAK,EAC7C,EAMA+Z,SAAU,CACN9V,KAAM,YACNvF,KAAM,GAAA+F,EAAArF,GAAA,EAAC2e,EAAAA,GAAYA,CAAAA,CAAC/e,UAAU,aAC9B8C,KAAM8b,EAAAA,EAAoBA,CAAC7D,QAAQ,CACnC8D,MAAO7d,GAAS,GAAAyE,EAAArF,GAAA,EAAC2b,GAAaA,CAAE,GAAG/a,CAAK,EAC5C,EAMAge,MAAO,CACH/Z,KAAM,SACNvF,KAAM,GAAA+F,EAAArF,GAAA,EAAC6e,EAAAA,GAAUA,CAAAA,CAACjf,UAAU,aAC5B8C,KAAM8b,EAAAA,EAAoBA,CAACI,KAAK,CAChCH,MAAO7d,GAAS,GAAAyE,EAAArF,GAAA,EAACuc,GAAUA,CAAE,GAAG3b,CAAK,EACzC,EACAke,KAAM,CACFja,KAAM,OACNvF,KAAM,GAAA+F,EAAArF,GAAA,EAAC+e,EAAAA,GAAYA,CAAAA,CAACnf,UAAU,aAC9B8C,KAAM8b,EAAAA,EAAoBA,CAACM,IAAI,CAC/BL,MAAO7d,GAAS,GAAAyE,EAAArF,GAAA,EAAC8c,GAASA,CAAE,GAAGlc,CAAK,EACxC,EACAoe,MAAO,CACHna,KAAM,QACNvF,KAAM,GAAA+F,EAAArF,GAAA,EAACif,EAAAA,GAAQA,CAAAA,CAACrf,UAAU,aAC1B8C,KAAM8b,EAAAA,EAAoBA,CAACQ,KAAK,CAChCP,MAAO7d,GAAS,GAAAyE,EAAArF,GAAA,EAACqe,GAAUA,CAAE,GAAGzd,CAAK,EACzC,CACJ,kBC7CO,IAAMse,GAAiB,QAyDtBC,EAxDJ,GAAM,CAACC,QAAAA,CAAO,CAAC,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,IAClB,GAAI,CAACze,EAAM0e,SAAS,CAAE,OAAO,KAG7B,IAAMhU,EAAU1K,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAM2e,eAAe,CAAC,CAErE5R,EAAgB,IAClB,IAAM6R,EAAU,CAAC,GAAGlU,CAAO,CAAE,GAAG8N,CAAM,EAMtCxY,EAAM6e,gBAAgB,CAAC,CAJmB,CACtCC,OAAQ,gBACRpU,QAASkU,CACb,EACoC,CACxC,EA4CA,OAAQlU,MAAAA,EAAAA,KAAAA,EAAAA,EAAS5I,IAAI,EACjB,KAAK8b,EAAAA,EAAoBA,CAACQ,KAAK,CAC3BG,EAAiBb,GAA4BU,KAAK,CAClD,KACJ,MAAKR,EAAAA,EAAoBA,CAACpf,OAAO,CAC7B+f,EAAiBb,GAA4Blf,OAAO,CACpD,KACJ,MAAKof,EAAAA,EAAoBA,CAAC/I,SAAS,CAC/B0J,EAAiBb,GAA4B7I,SAAS,CACtD,KACJ,MAAK+I,EAAAA,EAAoBA,CAAC7D,QAAQ,CAC9BwE,EAAiBb,GAA4B3D,QAAQ,CACrD,KACJ,MAAK6D,EAAAA,EAAoBA,CAACI,KAAK,CAC3BO,EAAiBb,GAA4BM,KAAK,CAClD,KACJ,MAAKJ,EAAAA,EAAoBA,CAACM,IAAI,CAC1BK,EAAiBb,GAA4BQ,IAAI,CAGzD,IAAI3I,EAAqB,GAAA9Q,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,KAErByf,EAAa,GAAAta,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YAAE,IAAEif,MAAAA,EAAAA,KAAAA,EAAAA,EAAgBta,IAAI,IAMzC,OALIsa,GACAhJ,CAAAA,EAAUgJ,EAAeV,KAAK,CAAC,CAACnT,QAAAA,EAASqC,cAAAA,CAAa,IAInD,GAAAtI,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,kCACjBgB,EAAM2e,eAAe,EAAIjU,GAAW6T,EAAiB,GAAA9Z,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WAClD,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,wCACX,GAAAyF,EAAArF,GAAA,EAAC4f,SAAAA,CAAOhgB,UAAU,+BACd,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,6DACV+f,MAGT,GAAAta,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,kCACX,GAAAyF,EAAArF,GAAA,EAAC6f,EAAAA,UAAUA,CAAAA,CAACjgB,UAAU,yBAClB,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,wEACX,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,gCACX,GAAAyF,EAAArF,GAAA,EAACuB,EAAAA,CAAKA,CAAAA,CAAC0b,QAAQ,QAAQrd,UAAU,oCAA2B,UAC5D,GAAAyF,EAAArF,GAAA,EAACwJ,EAAAA,CAAKA,CAAAA,CAACzH,GAAG,QAAQnC,UAAU,uBACrBud,aAAc7R,EAAQjM,KAAK,EAAI,GAC/B+d,OAAQpa,GAAK2K,EAAc,CAACtO,MAAO2D,EAAEC,MAAM,CAAC1D,KAAK,CAACmV,IAAI,IAAMpJ,EAAQjM,KAAK,QAGnF8W,EAED,GAAA9Q,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,gBACX,GAAAyF,EAAAjF,IAAA,EAACmD,EAAAA,CAAMA,CAAAA,CACHjB,QA5FV,KAClB8c,EAAQ,kBAAmB,gDAAiD,KACxE,IAAMU,EAAuC,EAAE,CAG3CC,EAAiB,KACjBC,EAAsB,GAE1B,IAAK,GAAM,CAACC,EAAO5L,EAAI,GAAIjN,OAAOqI,OAAO,CAAC7O,EAAM2K,mBAAmB,CAAC2U,OAAO,EACvE,GAAI7L,EAAInN,QAAQ,EAAImN,EAAInN,QAAQ,CAAC9C,QAAQ,CAACkH,EAAQvJ,EAAE,EAAG,CACnDge,EAAY1L,EACZ2L,EAAcC,EACd,KACJ,CAGJ,GAAI,CAACF,EAAW,CACZrZ,QAAQlH,KAAK,CAAC,yCAA0C8L,EAAQvJ,EAAE,EAClE,MACJ,CAEA+d,EAAazb,IAAI,CAAC,CACdqb,OAAQ,gBACRpU,QAASA,EACT6U,SAAUH,CACd,GAGuE,IAAnEI,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmB,IAAIL,EAAU7Y,QAAQ,CAAC,CAAEoE,EAAQvJ,EAAE,EAAEP,MAAM,EAC9Dse,EAAazb,IAAI,CAAC,CACdqb,OAAQ,YACRrL,IAAK0L,CACT,GAGJnf,EAAM6e,gBAAgB,CAACK,GACvBlf,EAAMyf,kBAAkB,CAAC,GAC7B,EACJ,EAuDgC7c,QAAQ,UACR5D,UAAU,2FACV,GAAAyF,EAAArF,GAAA,EAACsgB,EAAAA,GAASA,CAAAA,CAAC1gB,UAAU,WAAU,uBAvBLgB,EAAM2e,eAAe,IAgCrE,GAAAla,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACD,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,8BACX,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,oEACX,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,iEACX,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,YACX,GAAAyF,EAAArF,GAAA,EAACugB,EAAAA,GAAgBA,CAAAA,CAAC3gB,UAAU,aAEhC,GAAAyF,EAAArF,GAAA,EAACM,OAAAA,CAAKV,UAAU,+BAAsB,uEAQ/D,kBCjJO,IAAM4gB,GAA0B,+CCGhC,IAAMC,GAAc,KAEvB,IAAMC,EAAQhgB,CAAAA,EAAAA,EAAAA,MAAAA,EAAOigB,CAAAA,EAAAA,GAAAA,YAAAA,KAkCrB,MAhCA7a,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACN,IAAM8a,EAAS5X,SAAS6X,cAAc,CAAC,MAAoBhf,MAAA,CAAd6e,EAAM3f,OAAO,GAC1D,GAAI,CAAC6f,EAAQ,OACb,IAAME,EAAMF,EAA8BG,UAAU,CAAC,MACrD,GAAKD,EAEL,GAAI,CACc,IAAIE,GAAAA,EAAEA,CAACF,EAAK,CAEtBzd,KAAM,CACFoQ,OAAQ,CAAC,SAAU,SAAU,SAAS,CACtCD,SAAU,CAAC,CACPnQ,KAAM,CAAC,EAAG,IAAM,GAAI,CACpB4d,aAAc,KAClB,EAAE,EAUNnM,QAAS,CAAC4F,GAAAA,CAAeA,CAAC,EAElC,CAAE,MAAOwG,EAAI,CAEb,CAEJ,GAGI,GAAA7b,EAAArF,GAAA,EAACsO,EAAeA,CAACjP,MAAM,wBACnB,GAAAgG,EAAArF,GAAA,EAAC4gB,SAAAA,CAAO7e,GAAI,MAAoBF,MAAA,CAAd6e,EAAM3f,OAAO,EAAInB,UAAU,YAGzD,kBCzCAgS,EAAAA,EAAOA,CAACC,QAAQ,CACZC,EAAAA,EAAaA,CACbC,EAAAA,EAAWA,CACXoP,EAAAA,EAAUA,CACVjP,EAAAA,EAAKA,CACLC,EAAAA,CAAOA,CACPC,EAAAA,EAAMA,EAKH,IAAMlN,GAAU,CACnB4P,QAAS,CACLzV,MAAO,CACH+hB,QAAS,GACTlG,KAAM,8BACV,CACJ,EACArG,WAAY,GACZwM,OAAQ,CACJlG,EAAG,CACCmG,QAAS,EACb,EACAjG,EAAG,CACCiG,QAAS,EACb,CACJ,CACJ,EAEM7N,GAAS,CAAC,UAAW,WAAY,QAAS,QAAS,MAAO,OAAQ,OAAO,CAElEpQ,GAAO,CAChBoQ,OAAAA,GACAD,SAAU,CACN,CACI1I,MAAO,YACPzH,KAAMoQ,GAAOhS,GAAG,CAAC,IAAM8f,GAAAA,EAAKA,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC1a,IAAK,EAAGwS,IAAK,GAAI,IAC/D9H,gBAAiB,mBACrB,EACA,CACI3G,MAAO,YACPzH,KAAMoQ,GAAOhS,GAAG,CAAC,IAAM8f,GAAAA,EAAKA,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC1a,IAAK,EAAGwS,IAAK,GAAI,IAE/D9H,gBAAiB,mBACrB,EACA,CACI3G,MAAO,YACPzH,KAAMoQ,GAAOhS,GAAG,CAAC,IAAM8f,GAAAA,EAAKA,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC1a,IAAK,EAAGwS,IAAK,GAAI,IAC/D9H,gBAAiB,mBACrB,EACH,EAIQiQ,GAAW,IACb,GAAArc,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAArF,GAAA,EAACsO,EAAeA,CAACjP,MAAM,qBACnB,GAAAgG,EAAArF,GAAA,EAAC2hB,EAAAA,EAAGA,CAAAA,CAACte,KAAMA,GAAM6B,QAASA,GAAStF,UAAU,SAASkV,QAAS,CAAC4F,GAAAA,CAAeA,CAAC,uECtDrF,IAAMkH,GAAmB,GAErB,GAAAvc,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAArF,GAAA,EAAC6hB,GAAAA,CACG3T,QAAS,GAAA7I,EAAAjF,IAAA,EAACmD,EAAAA,CAAMA,CAAAA,CAAC3D,UAAU,+EACvB,GAAAyF,EAAArF,GAAA,EAAC8hB,GAAAA,CAAcA,CAAAA,CAACliB,UAAU,WAAU,iBAGxCmiB,MAAM,QACNtC,iBAAkB7e,EAAM6e,gBAAgB,CACxCY,mBAAoBzf,EAAMyf,kBAAkB,KAiB3CwB,GAAqB,IAE9B,IAAMG,EAAa,CAACtf,EAA4BmC,KAC5C,IAAMib,EAAuC,EAAE,CACzCxU,EAAgC,CAClCjM,MAAOwF,EACP9C,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJje,KAAAA,CACJ,EACA,GAAI9B,EAAMqhB,YAAY,CAAE,CACpB,IAAMC,EAAMthB,EAAMuhB,kBAAkB,EAAI,IAClChC,EAAWvf,EAAMwhB,WAAW,EAAI,GAChCH,EAAerhB,EAAMqhB,YAAY,CAEvC,GAAIC,MAAAA,GAAeA,MAAAA,EAAa,CAC5B,IAAM7N,EAAoB,CACtBtS,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJzZ,SAAU,EAAE,EAEVmb,EAAoC,CACtC3C,OAAQ,SACRrL,IAAAA,CACJ,CACI6N,CAAQ,MAARA,EAAaG,EAAYC,QAAQ,CAAGnC,EACnCkC,EAAYE,OAAO,CAAGpC,EAE3BL,EAAazb,IAAI,CAACge,GAClBvC,EAAazb,IAAI,CAAC,CACdqb,OAAQ,aACRpU,QAAAA,EACA6U,SAAU9L,EAAItS,EAAE,EAExB,KAAO,CACH,IAAMsgB,EAAoC,CACtC3C,OAAQ,aACRpU,QAAAA,EACA6U,SAAAA,CACJ,CACI+B,CAAQ,MAARA,EAAaG,EAAYC,QAAQ,CAAGL,EACnCI,EAAYE,OAAO,CAAGN,EAE3BnC,EAAazb,IAAI,CAACge,EACtB,CACJ,KAAO,CACH,IAAMhO,EAAoB,CACtBtS,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJzZ,SAAU,EAAE,CAEhBoE,CAAAA,EAAQ6U,QAAQ,CAAG9L,EAAItS,EAAE,CACzB+d,EAAazb,IAAI,CAAC,CACdqb,OAAQ,SACRrL,IAAAA,CACJ,GACAyL,EAAazb,IAAI,CAAC,CACdqb,OAAQ,aACRpU,QAAAA,EACA6U,SAAU9L,EAAItS,EAAE,EAExB,CACAnB,EAAM6e,gBAAgB,CAACK,GAEvB0C,WAAW,IAAO5hB,EAAMyf,kBAAkB,CAAC/U,EAAQvJ,EAAE,EAAG,IAE5D,EAGA,MAAO,GAAAsD,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAAjF,IAAA,EAACqiB,GAAAA,EAAYA,CAAAA,WACT,GAAApd,EAAArF,GAAA,EAAC0iB,GAAAA,EAAmBA,CAAAA,CAACC,QAAO,YACvB/hB,EAAMsN,OAAO,GAElB,GAAA7I,EAAArF,GAAA,EAAC4iB,GAAAA,EAAmBA,CAAAA,CAAChjB,UAAU,oDAAoDmiB,MAAOnhB,EAAMmhB,KAAK,UACjG,GAAA1c,EAAArF,GAAA,EAAC6iB,GAAAA,EAAiBA,CAAAA,CAACjjB,UAAU,uCACxBwH,OAAOC,IAAI,CAACiX,IAA6B7c,GAAG,CAACqS,IAE1C,IAAM/N,EAAMuY,EAA2B,CAD3BxK,EACgC,CAC5C,MAAO,GAAAzO,EAAArF,GAAA,EAAC8iB,GAAAA,EAAgBA,CAAAA,CAACljB,UAAU,eAC/B,GAAAyF,EAAAjF,IAAA,EAACmD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,QACRlB,QAASU,IACLgf,EAAWjc,EAAIrD,IAAI,CAAEqD,EAAIlB,IAAI,CACjC,EACAjF,UAAU,mFACbmG,EAAIzG,IAAI,CAAC,IAAEyG,EAAIlB,IAAI,KANkBiP,EASlD,WAKpB,kBCzFO,IAAMiP,GAAgB,GAGzB,EAFsBxX,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CAIvD,GAAAsD,EAAArF,GAAA,EAACgjB,GAAAA,CAAY,GAAGpiB,CAAK,GAFP,KAKnBoiB,GAAa,IACf,IAAM1X,EAAU1K,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CAExDkhB,EAAiC,CACnCvgB,KAAM8d,GACN0C,QAAS5X,MAAAA,EAAAA,KAAAA,EAAAA,EAAS5I,IAAI,CACtBX,GAAInB,EAAMmB,EAAE,CACZoe,SAAUvf,EAAMuf,QAAQ,EAKtB,CACFgD,WAAAA,CAAU,CACVC,UAAAA,CAAS,CACTC,WAAYC,CAAU,CACtBrc,UAAAA,CAAS,CACTsc,WAAAA,CAAU,CACb,CAAGC,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CACbzhB,GAAInB,EAAMmB,EAAE,CACZM,SAVe,GAWfgB,KAAM4f,CACV,GAMM,CAEFI,WAAYI,CAAU,CAIzB,CAAGC,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CACb3hB,GAAInB,EAAMmB,EAAE,CACZM,SA1Be,GA2BfgB,KAAM,CACFsgB,QAAS,CAACnD,GAAwB,CAClC,GAAGyC,CAAQ,CAEnB,GAEMthB,EAAQ,CACVsF,UAAW2c,GAAAA,EAAGA,CAACC,SAAS,CAACnI,QAAQ,CAACzU,EACtC,EAEM6c,EAAWljB,EAAM2e,eAAe,GAAKjU,EAAQvJ,EAAE,CAErD,MAAO,GAAAsD,EAAAjF,IAAA,EAACH,MAAAA,CACJL,UAAW,wBACT2jB,MAAAA,CADiC3iB,EAAMmjB,aAAa,EAAI,wBAAwB,eAE/CD,MAAAA,CADjCP,GAAc,sBAAsB,+CACwB1hB,MAAA,CAA3BiiB,GAAY,eAAe,WAC9DE,cAAapjB,EAAMmB,EAAE,CACrBY,IA9Be,IACf2gB,EAAWhY,GACXmY,EAAWnY,EACf,EA4BI3J,MAAOA,EACPW,QAASU,IACLA,EAAEihB,eAAe,GACjBjhB,EAAEkhB,cAAc,GACXJ,GACDljB,EAAMyf,kBAAkB,CAAC/U,EAAQvJ,EAAE,CAE3C,YACCuJ,EAAQ5I,IAAI,GAAK8b,EAAAA,EAAoBA,CAACpf,OAAO,CAAG,GAAAiG,EAAArF,GAAA,EAAC+K,EAAaA,CAAE,GAAGnK,CAAK,GACxE0K,EAAQ5I,IAAI,GAAK8b,EAAAA,EAAoBA,CAACiC,WAAW,CAAG,GAAApb,EAAArF,GAAA,EAACygB,GAAWA,CAAAA,GAChEnV,EAAQ5I,IAAI,GAAK8b,EAAAA,EAAoBA,CAAC/I,SAAS,CAAG,GAAApQ,EAAArF,GAAA,EAACqS,GAAeA,CAAE,GAAGzR,CAAK,GAC5E0K,EAAQ5I,IAAI,GAAK8b,EAAAA,EAAoBA,CAACkD,QAAQ,CAAG,GAAArc,EAAArF,GAAA,EAAC0hB,GAAQA,CAAAA,GAC1DpW,EAAQ5I,IAAI,GAAK8b,EAAAA,EAAoBA,CAAC7D,QAAQ,CAAG,GAAAtV,EAAArF,GAAA,EAACmY,GAAcA,CAAG,GAAGvX,CAAK,GAC3E0K,EAAQ5I,IAAI,GAAK8b,EAAAA,EAAoBA,CAACM,IAAI,CAAG,GAAAzZ,EAAArF,GAAA,EAAC6c,GAAUA,CAAE,GAAGjc,CAAK,GAClE0K,EAAQ5I,IAAI,GAAK8b,EAAAA,EAAoBA,CAACI,KAAK,CAAG,GAAAvZ,EAAArF,GAAA,EAAC6b,GAAWA,CAAE,GAAGjb,CAAK,GACpE0K,EAAQ5I,IAAI,GAAK8b,EAAAA,EAAoBA,CAACQ,KAAK,CAAG,GAAA3Z,EAAArF,GAAA,EAACud,GAAWA,CAAE,GAAG3c,CAAK,GACpE,KAEAkjB,GAAY,GAAAze,EAAArF,GAAA,EAACmkB,GAAAA,CAAuB,GAAGvjB,CAAK,CAAEwiB,UAAAA,EAAWD,WAAAA,MAElE,EASMgB,GAAoB,IACtB,GAAM,CAAC/E,QAAAA,CAAO,CAAC,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,IAEZ+E,EAAY,KAGd,GAAI,CADY/gB,CADH,CAAC,GAAGzC,EAAM2K,mBAAmB,GACrB2U,OAAO,CAACtf,EAAMuf,QAAQ,CAAC,CAC9B,CACVzZ,QAAQC,GAAG,CAAC,iBACZ,MACJ,CACA,IAAMZ,EAAM,CAAC,GAAGnF,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CAAEA,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,GAAc,EAC5Eb,EAAuC,EAAE,CAC/CA,EAAazb,IAAI,CAAC,CACdqb,OAAQ,aACRpU,QAASvF,EACToa,SAAUvf,EAAMuf,QAAQ,CACxBoC,QAAS3hB,EAAMmB,EAAE,GAErBnB,EAAM6e,gBAAgB,CAACK,GACvBlf,EAAMyf,kBAAkB,CAACta,EAAIhE,EAAE,CACnC,EAEMsiB,EAAW,KACb,IAAMte,EAAMnF,EAAM2K,mBAAmB,CAACC,UAAU,CAAC5K,EAAMmB,EAAE,CAAC,CAC1Dqd,EAAQ,kBAAmB,gDAAiD,KACxE,IAAMU,EAAuC,EAAE,CAC/CA,EAAazb,IAAI,CAAC,CACdqb,OAAQ,gBACRpU,QAASvF,EACToa,SAAUvf,EAAMuf,QAAQ,GAE5Bvf,EAAM6e,gBAAgB,CAACK,GACiF,IAApGM,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBxf,EAAM2K,mBAAmB,CAAC2U,OAAO,CAACtf,EAAMuf,QAAQ,CAAC,CAACjZ,QAAQ,CAAEtG,EAAMmB,EAAE,EAAEP,MAAM,EAC/Fse,EAAazb,IAAI,CAAC,CACdqb,OAAQ,YACRrL,IAAKzT,EAAM2K,mBAAmB,CAAC2U,OAAO,CAACtf,EAAMuf,QAAQ,CAAC,GAG9Dvf,EAAM6e,gBAAgB,CAACK,GACvBlf,EAAMyf,kBAAkB,CAAC,GAC7B,EAEJ,EAGA,MACI,GAAAhb,EAAAjF,IAAA,EAAAiF,EAAAnF,QAAA,YACI,GAAAmF,EAAArF,GAAA,EAACC,MAAAA,CACGL,UAAY,yFACZ,GAAAyF,EAAAjF,IAAA,EAACH,MAAAA,CACGL,UAAU,wDACV0F,KAAK,kBACL,GAAAD,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CACHC,QAAQ,QACR5D,UAAU,4EACT,GAAGgB,EAAMwiB,SAAS,CAClB,GAAGxiB,EAAMuiB,UAAU,UAEpB,GAAA9d,EAAArF,GAAA,EAACskB,GAAAA,CAAcA,CAAAA,CAAC1kB,UAAU,uBAE9B,GAAAyF,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CACHC,QAAQ,QACR5D,UAAU,uFACV0C,QAAS,IACLU,EAAEihB,eAAe,GACjBjhB,EAAEkhB,cAAc,GAChBE,GACJ,WAEA,GAAA/e,EAAArF,GAAA,EAACukB,GAAAA,CAAqBA,CAAAA,CAAC3kB,UAAU,uBAErC,GAAAyF,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CACHC,QAAQ,QACRd,KAAK,SACL9C,UAAU,uFACV0C,QAAS,IACLU,EAAEkhB,cAAc,GAChBlhB,EAAEihB,eAAe,GACjBI,GACJ,WAEA,GAAAhf,EAAArF,GAAA,EAACsgB,GAAAA,CAASA,CAAAA,CAAC1gB,UAAU,4BAIjC,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CACGL,UAAY,sFACZ,GAAAyF,EAAArF,GAAA,EAAC6hB,GAAkBA,CACf3T,QAAS,GAAA7I,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CACZC,QAAQ,QACR5D,UAAY,6EACZ,GAAAyF,EAAArF,GAAA,EAAC8hB,GAAAA,CAAcA,CAAAA,CAAClgB,MAAO,GAAIY,OAAQ,OAEvCuf,MAAM,SACNtC,iBAAkB7e,EAAM6e,gBAAgB,CACxCwC,aAAcrhB,EAAMmB,EAAE,CACtBqgB,YAAaxhB,EAAMuf,QAAQ,CAC3BE,mBAAoBzf,EAAMyf,kBAAkB,CAC5C8B,mBAAoB,QAG5B,GAAA9c,EAAArF,GAAA,EAACC,MAAAA,CACGL,UAAY,kGACZ,GAAAyF,EAAArF,GAAA,EAAC6hB,GAAkBA,CACf3T,QAAS,GAAA7I,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CACZC,QAAQ,QACR5D,UAAY,6EACZ,GAAAyF,EAAArF,GAAA,EAAC8hB,GAAAA,CAAcA,CAAAA,CAAClgB,MAAO,GAAIY,OAAQ,OAEvCuf,MAAM,SACNtC,iBAAkB7e,EAAM6e,gBAAgB,CACxCwC,aAAcrhB,EAAMmB,EAAE,CACtBqgB,YAAaxhB,EAAMuf,QAAQ,CAC3BE,mBAAoBzf,EAAMyf,kBAAkB,CAC5C8B,mBAAoB,QAG5B,GAAA9c,EAAArF,GAAA,EAACC,MAAAA,CACGL,UAAY,2GACZ,GAAAyF,EAAArF,GAAA,EAAC6hB,GAAkBA,CACf3T,QAAS,GAAA7I,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CACZC,QAAQ,QACR5D,UAAY,6EACZ,GAAAyF,EAAArF,GAAA,EAAC8hB,GAAAA,CAAcA,CAAAA,CAAClgB,MAAO,GAAIY,OAAQ,OAEvCuf,MAAM,QACNtC,iBAAkB7e,EAAM6e,gBAAgB,CACxCwC,aAAcrhB,EAAMmB,EAAE,CACtBqgB,YAAaxhB,EAAMuf,QAAQ,CAC3BE,mBAAoBzf,EAAMyf,kBAAkB,CAC5C8B,mBAAoB,QAG5B,GAAA9c,EAAArF,GAAA,EAACC,MAAAA,CACGL,UAAY,6GACZ,GAAAyF,EAAArF,GAAA,EAAC6hB,GAAkBA,CACf3T,QAAS,GAAA7I,EAAArF,GAAA,EAACuD,EAAAA,CAAMA,CAAAA,CACZC,QAAQ,QACR5D,UAAY,6EACZ,GAAAyF,EAAArF,GAAA,EAAC8hB,GAAAA,CAAcA,CAAAA,CAAClgB,MAAO,GAAIY,OAAQ,OAEvCuf,MAAM,MACNtC,iBAAkB7e,EAAM6e,gBAAgB,CACxCwC,aAAcrhB,EAAMmB,EAAE,CACtBqgB,YAAaxhB,EAAMuf,QAAQ,CAC3BE,mBAAoBzf,EAAMyf,kBAAkB,CAC5C8B,mBAAoB,UAKxC,8BCzQO,IAAMqC,GAAuB,IAChC,GAAM,CAACjZ,oBAAAA,CAAmB,CAAC,CAAG3K,EAExB6jB,EAASlZ,EAAoBrE,QAAQ,CAAC1F,MAAM,CAAG,EAErD,MAAO,GAAA6D,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,2DACV0C,QAASU,GAAKpC,EAAMyf,kBAAkB,CAAC,aAC9CoE,EAAS,GAAApf,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACN,GAAAmF,EAAAjF,IAAA,EAACskB,GAAAA,CAAS,GAAG9jB,CAAK,WACd,GAAAyE,EAAArF,GAAA,EAAC6f,EAAAA,UAAUA,CAAAA,CAACjgB,UAAU,4DAClB,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAW,GAA8BiC,MAAA,CAA3BjB,EAAM0e,SAAS,EAAI,kBAClC,GAAAja,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAWO,CAAAA,EAAAA,GAAAA,EAAAA,EAAG,4CAA6CS,EAAM0e,SAAS,CAAG,aAAe,aAC5F1e,EAAM2K,mBAAmB,CAACrE,QAAQ,CAACzF,GAAG,CAACM,IACpC,IAAMsS,EAAMzT,EAAM2K,mBAAmB,CAAC2U,OAAO,CAACne,EAAG,CACjD,GAAI,CAACsS,GAAOA,IAAAA,EAAInN,QAAQ,CAAC1F,MAAM,CAAQ,OAAO,KAE9C,IAAMwO,EAAM,GAASqE,MAAAA,CAANtS,EAAG,KAAiBF,MAAA,CAAdwS,EAAIsQ,SAAS,EAClC,MAAO,GAAAtf,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,sBACjByU,EAAInN,QAAQ,CAACzF,GAAG,CAACM,GAAM,GAAA6iB,EAAA1G,aAAA,EAAC6E,GAAaA,CAAE,GAAGniB,CAAK,CAAEuf,SAAU9L,EAAItS,EAAE,CAAEA,GAAIA,EAAIiO,IAAKjO,MAD7CiO,EAG5C,SAIXpP,EAAM0e,SAAS,EAAI,GAAAja,EAAArF,GAAA,EAAC4hB,GAAgBA,CAAE,GAAGhhB,CAAK,GAC9CikB,CAAAA,EAAAA,GAAAA,YAAAA,EACG,GAAAxf,EAAArF,GAAA,EAAC8kB,GAAAA,EAAWA,CAAAA,UACPlkB,EAAMmkB,aAAa,EAChB,GAAA1f,EAAArF,GAAA,EAAC+iB,GAAaA,CAAE,GAAGniB,CAAK,CAAEmB,GAAInB,EAAMmkB,aAAa,CAAEhB,cAAa,GAAC5D,SAAU,OAcnFnX,SAASgc,IAAI,OAKnB,GAAA3f,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WAED,GAAAmF,EAAArF,GAAA,EAACsV,EAAAA,UAAUA,CAAAA,CACPC,KAAK,OACL/V,MAAO,GAAA6F,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,iEAClB,GAAAyF,EAAArF,GAAA,EAACC,MAAAA,CAAIL,UAAU,YACX,GAAAyF,EAAArF,GAAA,EAAC2e,EAAAA,GAAYA,CAAAA,CAAC/e,UAAU,aAE5B,GAAAyF,EAAArF,GAAA,EAACM,OAAAA,CAAKV,UAAU,+BAAsB,kCAErCgB,EAAM0e,SAAS,EAAI,GAAAja,EAAArF,GAAA,EAAC6hB,GAAkBA,CACnCE,MAAM,SACNtC,iBAAkB7e,EAAM6e,gBAAgB,CACxCY,mBAAoBzf,EAAMyf,kBAAkB,CAC5CnS,QAAS,GAAA7I,EAAAjF,IAAA,EAACmD,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU5D,UAAU,wEACzC,GAAAyF,EAAArF,GAAA,EAAC0D,EAAAA,GAAcA,CAAAA,CAAC9D,UAAU,WAAU,2BASjE,EAGM8kB,GAAU,IAEZ,IAAMO,EAAcvkB,CAAAA,EAAAA,EAAAA,MAAAA,EAAoD,MACpDA,CAAAA,EAAAA,EAAAA,MAAAA,EAA2B,MAqC/C,IAAMwkB,EAAc,CAACnf,EAAuCof,EAA6CC,KACrG,IAAMC,EAAUzkB,EAAM2K,mBAAmB,CAACC,UAAU,CAACzF,EAAIhE,EAAE,CAAC,CACtDujB,EAAY1kB,EAAM2K,mBAAmB,CAAC2U,OAAO,CAACna,EAAIoa,QAAQ,CAAC,CAE3DoF,EAAS3kB,EAAM2K,mBAAmB,CAACC,UAAU,CAAC2Z,EAAUpjB,EAAE,CAAC,CAC3DyjB,EAAU5kB,EAAM2K,mBAAmB,CAAC2U,OAAO,CAACiF,EAAUhF,QAAQ,CAAC,CAErE,GAAI,CAACkF,GAAW,CAACC,GAAa,CAACC,GAAU,CAACC,EAAS,OAEnD,IAAM1F,EAAuC,EAAE,CAa/C,GATAwF,EAAUpe,QAAQ,CAAGue,CAAAA,EAAAA,GAAAA,eAAAA,EAAgBH,EAAUpe,QAAQ,CAAEme,EAAQtjB,EAAE,EACnEujB,EAAUX,SAAS,CAAG,IAAInL,OAAOE,OAAO,GAExCoG,EAAazb,IAAI,CAAC,CACdqb,OAAQ,YACRrL,IAAKiR,CACT,GAGIF,MAAAA,GAAqBA,MAAAA,EAAmB,CACxC,IAAM/Q,EAAoB,CACtBtS,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJzZ,SAAU,CAACme,EAAQtjB,EAAE,CAAC,EAK1B+d,EAAazb,IAAI,CAAC,CACdqb,OAAQ,SACRrL,IAAAA,EACAiO,SAAU8C,MAAAA,EAAoBI,EAAQzjB,EAAE,CAAG,GAC3CwgB,QAAS6C,MAAAA,EAAoBI,EAAQzjB,EAAE,CAAG,EAC9C,EACJ,MACIyjB,EAAQte,QAAQ,CAAGwe,CAAAA,EAAAA,GAAAA,uBAAAA,EAAwBF,EAAQte,QAAQ,CAAEqe,EAAOxjB,EAAE,CAAEsjB,EAAQtjB,EAAE,CAAEqjB,MAAAA,EAAoB,SAAW,SACnHI,EAAQb,SAAS,CAAG,IAAInL,OAAOE,OAAO,GAEtCoG,EAAazb,IAAI,CAAC,CACdqb,OAAQ,YACRrL,IAAKmR,CACT,EAE8B,KAA9BF,EAAUpe,QAAQ,CAAC1F,MAAM,EAGzBse,EAAazb,IAAI,CAAC,CACdqb,OAAQ,YACRrL,IAAKiR,CACT,GAIJ1kB,EAAM6e,gBAAgB,CAACK,GACvBlf,EAAMyf,kBAAkB,CAACta,EAAIhE,EAAE,CACnC,EAmBM4jB,EAAgB,KAClB,IAAMC,EAAcX,EAAYlkB,OAAO,CACnC6kB,GACAA,EAAYta,OAAO,CAACua,SAAS,CAACC,MAAM,CAAC,WAAY,IAAK,IAAK,IAAK,KAEpEb,EAAYlkB,OAAO,CAAG,KAEtB,IAAMgF,EAAMiD,SAAS+c,aAAa,CAAC,iCAC/BhgB,GACAA,EAAI8f,SAAS,CAACC,MAAM,CAAC,WAAY,IAAK,IAAK,IAAK,IAExD,EAMME,EAAe,CACjB5V,EACA6V,EACAC,KAEA,IAAMC,EAAWF,MAAAA,EAAAA,KAAAA,EAAAA,EAAMG,IAAI,CAC3B,GAAI,CAACD,EAAU,MAAO,GAEtB,GAAM,CAAClgB,KAAAA,CAAI,CAAED,IAAAA,CAAG,CAAEG,MAAAA,CAAK,CAAED,OAAAA,CAAM,CAAC,CAAGkK,EAAOgW,IAAI,CAACrlB,OAAO,CAACslB,UAAU,CAG3DC,EAAexf,KAAKyf,GAAG,CAACtgB,EAAOkgB,EAASlgB,IAAI,EAC5CugB,EAAgB1f,KAAKyf,GAAG,CAACtgB,EAAOkgB,EAAShgB,KAAK,EAC9CsgB,EAAc3f,KAAKyf,GAAG,CAACvgB,EAAMmgB,EAASngB,GAAG,QAa/C,CAVAU,QAAQC,GAAG,CAAC,aAAc,CACtB2f,aAAAA,EACAG,YAAAA,EACAD,cAAAA,CACJ,GAMIF,EAHsB,IAIf,IACAE,EALe,GAMf,IACAC,EAPe,GAQf,IAEJ,EACX,EAwDMC,EAAUC,CAAAA,EAAAA,GAAAA,EAAAA,EACZC,CAAAA,EAAAA,GAAAA,EAAAA,EAAUC,GAAAA,EAAaA,CAAE,CACrBC,qBAAsB,CAClBC,SAAU,EACd,CACJ,IAGJ,MACI,GAAA1hB,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACI,GAAAmF,EAAArF,GAAA,EAACgnB,GAAAA,EAAUA,CAAAA,CACPC,UArOZ,SAAuBle,CAAmB,MAMWqH,EAAAA,EAArC6V,EAAAA,EAHZ,GAFIrlB,EAAMmkB,aAAa,EAAEnkB,EAAMsmB,gBAAgB,CAAC,IAE5C,CADgBjC,EAAYlkB,OAAO,CACrB,OAElB,GAAM,CAACqP,OAAAA,CAAM,CAAE6V,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAC,CAAGnd,EAC9B,IAAIkd,CAAAA,GAAAA,CAAAA,OAAQA,CAAAA,EAAAA,EAAK5iB,IAAI,GAAT4iB,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAWllB,OAAO,GAAlBklB,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAoBtC,OAAO,CAACvf,QAAQ,QAACgM,CAAAA,EAAAA,EAAO/M,IAAI,GAAX+M,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAarP,OAAO,GAApBqP,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAsB1N,IAAI,KAIvEujB,EAAKlkB,EAAE,GAAKqO,EAAOrO,EAAE,CAFrB,OAGJ,IAAMolB,EAAWlB,EAAK5iB,IAAI,CAACtC,OAAO,CAC5BqmB,EAAahX,EAAO/M,IAAI,CAACtC,OAAO,CAGhCmhB,EAAM8D,EAAa5V,EAAQ6V,EAAcC,GAEzC,CAACnkB,GAAAA,CAAE,CAAEoe,SAAAA,CAAQ,CAAC,CAAG8F,EAAK5iB,IAAI,CAACtC,OAAO,CAExCmkB,EAAY,CAACnjB,GAAIqlB,EAAWrlB,EAAE,CAAEoe,SAAUiH,EAAWjH,QAAQ,EAAI,EAAE,EAAG,CAACpe,GAAIolB,EAASplB,EAAE,CAAEoe,SAAUgH,EAAShH,QAAQ,EAAI,EAAE,EAAG+B,GAW5Hxb,QAAQC,GAAG,CAAC,WAAYoC,GAExB4c,GACJ,EAqMY0B,WAlEK,QAsCOpC,EArCpB,GAAM,CAAC7U,OAAAA,CAAM,CAAE6V,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAC,CAAGljB,EAC9B,GAAI,CAACijB,EAAM,CACPvf,QAAQC,GAAG,CAAC,WACZ,MACJ,CACA,GAAIsf,EAAKlkB,EAAE,GAAKqO,EAAOrO,EAAE,CAAE,CACvB2E,QAAQC,GAAG,CAAC,oBACZ,MACJ,CAGA,GAAIse,EAAYlkB,OAAO,CACfkkB,EAAYlkB,OAAO,CAACgB,EAAE,GAAKkkB,EAAKlkB,EAAE,GAClCkjB,EAAYlkB,OAAO,CAACuK,OAAO,CAACua,SAAS,CAACC,MAAM,CACxC,IACA,IACA,IACA,IACA,YAEJb,EAAYlkB,OAAO,CAAG,UAEvB,CACH,IAAM6kB,EAAc5c,SAAS+c,aAAa,CAAC,wBAAgClkB,MAAA,CAARokB,EAAKlkB,EAAE,CAAC,OAEvE6jB,GACAX,CAAAA,EAAYlkB,OAAO,CAAG,CAClBuK,QAASsa,EACT7jB,GAAIkkB,EAAKlkB,EAAE,CACf,CAER,CAEKkjB,EAAYlkB,OAAO,EACpB2F,QAAQC,GAAG,CAAC,+BAGhB,IAAMif,EAAAA,OAAcX,CAAAA,EAAAA,EAAYlkB,OAAO,GAAnBkkB,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAqB3Z,OAAO,CAChD,GAAI,CAACsa,EAAa,CACdlf,QAAQC,GAAG,CAAC,sCACZ,MACJ,CAEA,IAAMye,EAAYY,EAAa5V,EAAQ6V,EAAcC,GAErDN,EAAYC,SAAS,CAACC,MAAM,CAAC,IAAK,IAAK,IAAK,IAAK,YAC7CV,GACAQ,EAAYC,SAAS,CAACyB,GAAG,CAAClC,EAAW,YAGzC1e,QAAQC,GAAG,CAAC,OAAQsf,EACxB,EAeYsB,WAzIK,IAQjB,EAkIYC,YAhIM,IACd9gB,QAAQC,GAAG,CAAC,aAAcoC,GAErBA,EAAMqH,MAAM,CAACrO,EAAE,EACpBnB,EAAMsmB,gBAAgB,CAACne,EAAMqH,MAAM,CAACrO,EAAE,CAAC2Z,QAAQ,GACnD,EA4HY+L,aA7GO,IACf9B,GACJ,EA4GYe,QAASA,WAER9lB,EAAMsG,QAAQ,IAI/B,EC1UMwgB,GAAiC,CACnClc,WAAY,CAAC,EACb0U,QAAS,CAAC,EACVhZ,SAAU,EAAE,EAGVygB,GAAqB,CACvB5lB,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJzZ,SAAU,EAAE,EAEhB,IAAK,IAAIhG,EAAI,EAAGA,EAAI,EAAGA,IAAK,CACxB,IAAMgT,EAAuB,CACzBzU,aAAcC,EAAAA,EAAQA,CAACW,IAAI,CAAEoL,aAAc,CAACC,WAAY,GAAIzK,OAAQ,CAACiL,WAAY,EAAE,CAAEH,MAAOC,EAAAA,KAAKA,CAACC,GAAG,EAAGL,YAAaC,EAAAA,sBAAsBA,CAACC,QAAQ,CAAEH,SAAU,EAAE,EAClKtM,MAAO,UACP0C,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJje,KAAM8b,EAAAA,EAAoBA,CAACpf,OAAO,CAEtCsoB,CAAAA,GAAUlc,UAAU,CAAC0I,EAAKnS,EAAE,CAAC,CAAGmS,EAChCyT,GAAKzgB,QAAQ,CAAC7C,IAAI,CAAC6P,EAAKnS,EAAE,CAC9B,CAEA,IAAM6lB,GAAqB,CACvB7lB,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJzZ,SAAU,EAAE,EAEhB,IAAK,IAAIhG,EAAI,EAAGA,EAAI,EAAGA,IAAK,CACxB,IAAM4Y,EAAuB,CACzBza,MAAO,WACP0C,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJje,KAAM8b,EAAAA,EAAoBA,CAAC7D,QAAQ,CACnC/H,eAAgB,CAAClH,WAAY,GAAIzK,OAAQ,CAACiL,WAAY,EAAE,CAAEH,MAAOC,EAAAA,KAAKA,CAACC,GAAG,EAAGmM,WAAY,EAAE,CAAE1E,WAAY,EAAE,CAC/G,CACAgU,CAAAA,GAAUlc,UAAU,CAACsO,EAAI/X,EAAE,CAAC,CAAG+X,EAC/B8N,GAAK1gB,QAAQ,CAAC7C,IAAI,CAACyV,EAAI/X,EAAE,CAC7B,CAEA,IAAM8lB,GAAqB,CACvB9lB,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJzZ,SAAU,EAAE,EAEhB,IAAK,IAAIhG,EAAI,EAAGA,EAAI,EAAGA,IAAK,CACxB,IAAM4mB,EAAyB,CAC3BjV,eAAgB,CAAC,EAAGyE,aAAc,EAAE,CACpCjY,MAAO,UACP0C,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJje,KAAM8b,EAAAA,EAAoBA,CAAC/I,SAAS,CACpC7C,eAAgB,CAAClH,WAAY,GAAIzK,OAAQ,CAACiL,WAAY,EAAE,CAAEH,MAAOC,EAAAA,KAAKA,CAACC,GAAG,EAAG0G,MAAO,EAAE,CAC1F,CACA+U,CAAAA,GAAUlc,UAAU,CAACsc,EAAK/lB,EAAE,CAAC,CAAG+lB,EAChCD,GAAK3gB,QAAQ,CAAC7C,IAAI,CAACyjB,EAAK/lB,EAAE,CAC9B,CA4BA,IAAMgmB,GAAqB,CACvBhmB,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJzZ,SAAU,EAAE,EAEhB,IAAK,IAAIhG,EAAI,EAAGA,EAAI,EAAGA,IAAK,CACxB,IAAMga,EAAoB,CACtB7b,MAAO,UACP0C,GAAI4e,CAAAA,EAAAA,GAAAA,YAAAA,IACJje,KAAM8b,EAAAA,EAAoBA,CAACM,IAAI,CAC/B3I,QAAS,EACb,CACAuR,CAAAA,GAAUlc,UAAU,CAAC0P,EAAKnZ,EAAE,CAAC,CAAGmZ,EAChC6M,GAAK7gB,QAAQ,CAAC7C,IAAI,CAAC6W,EAAKnZ,EAAE,CAC9B,CAGA,CAAC4lB,GAAMC,GAAMG,GAAMF,GAElB,CAACG,OAAO,CAACjb,IACN2a,GAAUxH,OAAO,CAACnT,EAAEhL,EAAE,CAAC,CAAGgL,EAC1B2a,GAAUxgB,QAAQ,CAAC7C,IAAI,CAAC0I,EAAEhL,EAAE,CAChC,GAEO,IAAMkmB,GAAgB7gB,OAAO8gB,MAAM,CAACR,6BC7EpC,IAAMS,GAAgB,IACzB,GAAM,CAACpD,EAAemC,EAAiB,CAAGze,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC/C,CAAC8W,EAAiBc,EAAmB,CAAG5X,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAE/C,CAAC2f,qBAAAA,CAAoB,CAAEC,MAAAA,CAAK,CAAEC,0BAAAA,CAAyB,CAAC,CAAGjd,CAAAA,EAAAA,EAAAA,EAAAA,IAE3DiU,EAAY,CAAC,CAAC+I,EAAME,QAAQ,CAACC,GAAAA,EAAgBA,CAAE,IAG/C,CAAC1a,WAAAA,CAAU,CAAC,CAAGlN,EASf,CAAC6nB,EAAUC,EAAY,CAAGjgB,CAAAA,EAAAA,EAAAA,QAAAA,EAA8BqF,EAAWA,UAAU,CAN/EA,CAAAA,EAAWA,UAAU,CAAGA,EAAWA,UAAU,EAAI,CAAC,EAClDA,EAAWA,UAAU,CAAC5G,QAAQ,CAAG4G,EAAWA,UAAU,CAAC5G,QAAQ,EAAI,EAAE,CACrE4G,EAAWA,UAAU,CAACoS,OAAO,CAAGpS,EAAWA,UAAU,CAACoS,OAAO,EAAI,CAAC,EAClEpS,EAAWA,UAAU,CAACtC,UAAU,CAAGsC,EAAWA,UAAU,CAACtC,UAAU,EAAI,CAAC,EAiB5E,GAAM,CAACnI,EAAMslB,EAAQ,CAAGlgB,CAAAA,EAAAA,EAAAA,QAAAA,EAA8BmgB,CAVjC,KACjB,IAAMC,EAAQC,aAAaC,OAAO,CAAC,eACnC,GAAIF,EACA,GAAI,CACA,OAAO7P,KAAKgQ,KAAK,CAACH,EACtB,CAAE,MAAO7lB,EAAG,CACZ,CAEJ,OAAO,IACX,MACwEilB,IAQnE3I,GAAWC,CAAAA,EAAkB,IAiElC,IAAME,EAAmB,MAAOK,IACA,IAAxBA,EAAate,MAAM,EAIvB,MAAM8mB,EAA0B1nB,EAAMqoB,IAAI,CAAClnB,EAAE,CAAEnB,EAAMqoB,IAAI,CAACC,MAAM,CAAEpJ,EACtE,EAMMqJ,EAAoC,CAAC5d,oBAHfuC,EAAWA,UAAU,CAGesb,WAnF7C,IAInB,EA+E4ErE,cAAAA,EAAemC,iBAAAA,EAAkB3H,gBAAAA,EAAiBc,mBAAAA,EAAoBf,UAAAA,EAAWG,iBAAAA,CAAgB,EAC7K,MAAO,GAAApa,EAAArF,GAAA,EAAAqF,EAAAnF,QAAA,WACH,GAAAmF,EAAAjF,IAAA,EAACH,MAAAA,CAAIL,UAAU,+BACX,GAAAyF,EAAArF,GAAA,EAACwkB,GAAoBA,CAA0B,GAAG2E,CAAU,EAAjC7J,EAAY,EAAI,GAC3C,GAAAja,EAAArF,GAAA,EAACkf,GAAcA,CAAE,GAAGiK,CAAU,OAG1C", "sources": ["webpack://_N_E/./src/components/custom-ui/Infobox.tsx", "webpack://_N_E/./src/components/custom-ui/multiImagePicker.tsx", "webpack://_N_E/./src/components/workspace/main/common/databaseSelect.tsx", "webpack://_N_E/./src/components/custom-ui/alignSelect.tsx", "webpack://_N_E/./src/components/custom-ui/adjustToViewport.tsx", "webpack://_N_E/./src/components/custom-ui/iconPicker.tsx", "webpack://_N_E/./src/components/workspace/main/common/aggregateBySelect.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/elements/infoBox.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/common/dataViewWrapper.tsx", "webpack://_N_E/./src/components/ui/chart.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/elements/lineChart.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/elements/pieChart.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/elements/image.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/elements/text.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/elements/embed.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/common/element.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/dashboardPanel.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/typings.ts", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/elements/funnelChart.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/elements/barChart.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/common/addElementRender.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/common/elementRender.tsx", "webpack://_N_E/./src/components/workspace/main/views/dashboard/components/dashboardContentWrap.tsx", "webpack://_N_E/./src/utils/demo/dashboard.ts", "webpack://_N_E/./src/components/workspace/main/views/dashboard/dashboard.tsx", "webpack://_N_E/./src/components/custom-ui/iconPicker.css", "webpack://_N_E/./src/components/workspace/main/views/dashboard/dashboard.css"], "sourcesContent": ["import React, {ReactNode} from \"react\";\r\nimport {Position} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {cn} from \"@/lib/utils\";\r\n\r\n\r\nexport interface InfoboxProps {\r\n    className?: string\r\n    title: string\r\n    value: string | number | ReactNode\r\n    icon: ReactNode\r\n    error?: string\r\n    iconPosition?: Position.Left | Position.Right;\r\n}\r\n\r\nexport const Infobox = ({title, icon, value, error, iconPosition = Position.Right, className}: InfoboxProps) => {\r\n\r\n    const iconWrap = <div className=\"p-2 size-8 flex items-center bg-neutral-100 rounded-full justify-center\">\r\n        {icon}\r\n    </div>\r\n    return <>\r\n        <div className={cn(\"rounded-none cursor-pointer p-4 bg-white flex-1 h-auto\", className)}>\r\n            <div className=\"flex justify-between\">\r\n                {iconPosition === Position.Left && <>{iconWrap}</>}\r\n                <div className=\"flex flex-col\">\r\n                    <span className=\"text-xs text-gray-light font-medium truncate\">{title}</span>\r\n                    <h1 className=\"font-bold text-base\">{value}</h1>\r\n                    {error && <div className='text-xs !text-[10px] font-medium text-red-500 truncate mt-1'>{error}</div>}\r\n                </div>\r\n                {iconPosition !== Position.Left && <>{iconWrap}</>}\r\n            </div>\r\n        </div>\r\n    </>\r\n}", "import {UploadItem} from \"@/components/workspace/main/views/table/renderer/fields/files\";\r\nimport {Label} from \"@/components/ui/label\";\r\nimport {XMarkIcon} from \"@heroicons/react/24/outline\";\r\nimport {Button} from \"@/components/ui/button\";\r\nimport {CirclePlusIcon} from \"@/components/icons/FontAwesomeRegular\";\r\nimport React, {useRef} from \"react\";\r\nimport {BackendAPIResponse} from \"@/api/common\";\r\nimport {WorkspaceUpload} from \"@/typings/workspace\";\r\n\r\n\r\n\r\nexport type MultiImagePickerUploadOnCompleteCallback = (res: BackendAPIResponse<{ upload: WorkspaceUpload }>) => void\r\n\r\ninterface MultiImagePickerProps {\r\n    images: string[],\r\n    onChange?: (images: string[]) => void\r\n    uploadImage?: (file: File, onComplete: MultiImagePickerUploadOnCompleteCallback) => void\r\n    uploads?: UploadItem[]\r\n    maxImages?: number\r\n    title?: string\r\n    hideTitle?: boolean\r\n    addImageButtonLabel?: string\r\n    allowRemovingImages?: boolean\r\n    disabled?: boolean\r\n}\r\n\r\nexport const MultiImagePicker = (props: MultiImagePickerProps) => {\r\n\r\n    const fileInputRef = useRef<HTMLInputElement>(null)\r\n\r\n    const uploads = props.uploads || []\r\n    const imagesRef = useRef(props.images)\r\n    imagesRef.current = props.images\r\n\r\n    const removeImage = (index: number) => {\r\n        const images = imagesRef.current.filter((i, idx) => idx !== index)\r\n        props.onChange?.(images)\r\n    }\r\n\r\n    const onSelectFile = (e: any) => {\r\n        if (!props.uploadImage) return\r\n        const files = e.target.files\r\n        if (!files || files.length === 0) {\r\n            return;\r\n        }\r\n        for (const currentFile of files) {\r\n            props.uploadImage(currentFile, res => {\r\n                const upload = res.data.data.upload\r\n                const images = [...imagesRef.current, upload.finalUrl]\r\n                props.onChange?.(images)\r\n            })\r\n        }\r\n    }\r\n\r\n    const handleClick = () => {\r\n        if (fileInputRef && fileInputRef.current) {\r\n            fileInputRef.current.click()\r\n        }\r\n    }\r\n\r\n    return <>\r\n\r\n        <div className='flex flex-col gap-2'>\r\n            {!props.hideTitle && <Label className=\"text-xs text-neutral-500\">{props.title || 'Images'}</Label>}\r\n\r\n            {(uploads.length > 0 || imagesRef.current.length > 0) &&\r\n                <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4\">\r\n                    {uploads.map((item, i) => {\r\n                        return <div key={item.id} className=\"relative bg-neutral-100 group min-h-[75px]\">\r\n                            <div className=\"w-full bg-gray-200 rounded-xs h-0.5 absolute bottom-0 left-0\">\r\n                                <div\r\n                                    className=\"bg-blue-600 h-0.5 rounded-xs dark:bg-blue-500\"\r\n                                    style={{width: `${item.progress}%`}}></div>\r\n                            </div>\r\n                        </div>\r\n                    })}\r\n                    {imagesRef.current.map((image, i) => {\r\n                        return <div key={`img-${i}`} className=\"relative group\">\r\n                            <div className='bg-neutral-100 p-[1px]'>\r\n                                <img className=\"h-auto max-w-full rounded-xs\" src={image} alt=\"\"/>\r\n                            </div>\r\n                            <button\r\n                                disabled={props.disabled}\r\n                                className=\"absolute -top-2 -right-2 w-4 h-4 p-0.5 rounded-full text-brand-red bg-neutral-100 shadow-lg invisible group-hover:visible\"\r\n                                onClick={() => removeImage(i)}>\r\n                                <XMarkIcon width={12} height={12}/>\r\n                            </button>\r\n                        </div>\r\n                    })}\r\n                </div>}\r\n\r\n            <div className='hidden'>\r\n                <input\r\n                    type=\"file\"\r\n                    ref={fileInputRef}\r\n                    disabled={props.disabled}\r\n                    multiple\r\n                    accept=\"image/*\" onChange={onSelectFile}/>\r\n            </div>\r\n            <div>\r\n                <Button variant=\"outline\"\r\n                        disabled={props.disabled}\r\n                        onClick={handleClick}\r\n                        className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\">\r\n                    <CirclePlusIcon className=\"size-3\"/>\r\n                    Add Images\r\n                </Button>\r\n            </div>\r\n\r\n\r\n        </div>\r\n    </>\r\n}", "import {useWorkspace} from \"@/providers/workspace\";\r\nimport React, {useMemo} from \"react\";\r\nimport {TagItem} from \"@/components/workspace/main/views/table/renderer/common/tag\";\r\nimport {ObjectType} from \"opendb-app-db-utils/lib/typings/common\";\r\nimport {CustomSelect} from \"@/components/custom-ui/customSelect\";\r\n\r\ninterface DbSelectProps {\r\n    selectedId: string\r\n    onChange: (id: string) => void\r\n    className?: string\r\n    disabled?: boolean\r\n}\r\n\r\nexport const DatabaseSelect = (props: DbSelectProps) => {\r\n    const {databasePageStore, databasePagesId} = useWorkspace()\r\n    const {selectedId} = props\r\n\r\n    const dbItems = useMemo(() => {\r\n        const items: TagItem<undefined>[] = []\r\n\r\n        const databaseIds = [...databasePagesId]\r\n        if (!databaseIds.includes(selectedId)) databaseIds.push(selectedId)\r\n\r\n        for (const id of databaseIds) {\r\n            const db = databasePageStore[id]\r\n            if (!db) continue\r\n            const {page} = db\r\n            const emoji = page.icon && page.icon.type === ObjectType.Emoji ? page.icon.emoji : '📕'\r\n\r\n            const item: TagItem<undefined> = {\r\n                color: undefined, data: undefined, id, title: `${emoji} ${db.page.name}`, value: id\r\n            }\r\n            items.push(item)\r\n        }\r\n        return items\r\n    }, [databasePageStore, databasePagesId, selectedId])\r\n\r\n    return <>\r\n        <CustomSelect\r\n            onChange={v => {\r\n                props.onChange(v[0])\r\n            }}\r\n            selectedIds={selectedId ? [selectedId] : []}\r\n            placeholder=\"Choose a database\"\r\n            options={dbItems}\r\n            disabled={props.disabled}\r\n            className={props.className}\r\n        />\r\n    </>\r\n}\r\n\r\n", "import {Bars3BottomLeftIcon, Bars3BottomRightIcon, Bars3Icon,} from \"@heroicons/react/24/outline\";\r\n\r\nexport enum Align {\r\n    Left = \"left\",\r\n    Right = \"right\",\r\n    Center = \"center\",\r\n}\r\n\r\nexport interface AlignSelectProps {\r\n    value?: Align;\r\n    onChange?: (value: Align) => void;\r\n    enableCenter?: boolean;\r\n}\r\n\r\nexport const AlignSelect = (props: AlignSelectProps) => {\r\n    return (\r\n        <div className=\"flex\">\r\n            <div\r\n                className=\"inline-flex rounded-none shadow-sm size-8\"\r\n                role=\"group\"\r\n            >\r\n                <button\r\n                    type=\"button\"\r\n                    className={`p-2 text-sm font-medium text-gray-900  ${\r\n                        props.value === Align.Left ? \"bg-gray-100\" : \"bg-white\"\r\n                    } border border-gray-200 border-r-0 rounded-none hover:bg-gray-100`}\r\n                    onClick={() => {\r\n                        props.onChange?.(Align.Left);\r\n                    }}\r\n                >\r\n                    <Bars3BottomLeftIcon className='size-4'/>\r\n                </button>\r\n                {props.enableCenter && (\r\n                    <>\r\n                        <button\r\n                            type=\"button\"\r\n                            className={`p-2 text-sm font-medium text-gray-900  ${\r\n                                props.value === Align.Center ? \"bg-gray-100\" : \"bg-white\"\r\n                            } border border-gray-200 border-r-0 hover:bg-gray-100`}\r\n                            onClick={() => {\r\n                                props.onChange?.(Align.Center);\r\n                            }}\r\n                        >\r\n                            <Bars3Icon className='size-4'/>\r\n                        </button>\r\n                    </>\r\n                )}\r\n\r\n                <button\r\n                    type=\"button\"\r\n                    className={`p-2 text-sm font-medium text-gray-900 ${\r\n                        props.value === Align.Right ? \"bg-gray-100\" : \"bg-white\"\r\n                    } border border-gray-200 rounded-none hover:bg-gray-100`}\r\n                    onClick={() => {\r\n                        props.onChange?.(Align.Right);\r\n                    }}\r\n                >\r\n                    <Bars3BottomRightIcon className='size-4'/>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n", "import {PropsWithChildren, useEffect, useRef} from \"react\";\r\n\r\nexport interface AdjustToViewPortProps extends PropsWithChildren {\r\n    offset?:\r\n        | {\r\n        top?: number;\r\n        left?: number;\r\n        right?: number;\r\n        bottom?: number;\r\n    }\r\n        | number;\r\n}\r\n\r\nexport const AdjustToViewPort = (props: AdjustToViewPortProps) => {\r\n    const eleRef = useRef<HTMLDivElement>(null);\r\n    const renderedRef = useRef(false);\r\n\r\n    useEffect(() => {\r\n        const ele = eleRef.current;\r\n        if (!ele) return;\r\n        if (renderedRef.current) return;\r\n        const {top, left, bottom, right, height} = ele.getBoundingClientRect();\r\n        const viewportHeight = window.innerHeight;\r\n        const viewportWidth = window.innerWidth;\r\n\r\n        console.log(\"Adjust:\", {\r\n            ele: ele.getBoundingClientRect(),\r\n            viewportHeight, viewportWidth, offset: props.offset\r\n        })\r\n\r\n        const defaultOffset = {top: 0, left: 0, right: 0, bottom: 0};\r\n        const offset =\r\n            typeof props.offset === \"number\"\r\n                ? {\r\n                    top: props.offset,\r\n                    left: props.offset,\r\n                    right: props.offset,\r\n                    bottom: props.offset,\r\n                }\r\n                : typeof props.offset === \"object\"\r\n                    ? props.offset\r\n                    : defaultOffset;\r\n\r\n        offset.top = offset.top || 0;\r\n        offset.bottom = offset.bottom || 0;\r\n        offset.right = offset.right || 0;\r\n        offset.bottom = offset.bottom || 0;\r\n\r\n        let translateY =\r\n            (offset.top || 0) -\r\n            Math.min(top, offset.top || top - bottom + (offset.bottom || 0));\r\n        let translateX =\r\n            (offset.left || 0) -\r\n            Math.min(left, offset.left || left - right + (offset.right || 0));\r\n\r\n        // If the element's height is greater than the remaining space at the bottom, adjust translateY\r\n        if (bottom > viewportHeight - offset.bottom) {\r\n            translateY = -(bottom - (viewportHeight - offset.bottom));\r\n        }\r\n\r\n        // If the element's width is greater than the remaining space at the right, adjust translateX\r\n        if (right > viewportWidth - offset.right) {\r\n            translateX = -(right - (viewportWidth - offset.right));\r\n        }\r\n        // console.log(\r\n        //   \"Rect:\",\r\n        //   renderedRef.current,\r\n        //   ele.getBoundingClientRect(),\r\n        //   {\r\n        //     viewportWidth,\r\n        //     viewportHeight,\r\n        //   },\r\n        //   {\r\n        //     translateX,\r\n        //     translateY,\r\n        //   },\r\n        //   offset,\r\n        //   ele\r\n        // );\r\n\r\n        ele.style.transform = `translate(${translateX}px, ${translateY}px)`;\r\n\r\n        renderedRef.current = true;\r\n    }, [props.offset]);\r\n\r\n    return (\r\n        <>\r\n            <div ref={eleRef} className=\"your-absolutely-positioned-element\">\r\n                {props.children}\r\n            </div>\r\n        </>\r\n    );\r\n};\r\n", "\"use client\";\r\n\r\nimport { MagnifyingGlassIcon } from \"@heroicons/react/24/outline\";\r\nimport * as HeroIcons from \"@heroicons/react/24/outline\";\r\nimport dynamic from \"next/dynamic\";\r\nimport React, { HTMLProps, useEffect, useRef, useState } from \"react\";\r\nimport {AdjustToViewPort} from \"@/components/custom-ui/adjustToViewport\";\r\nimport {Emoji, Icon, ObjectType} from \"opendb-app-db-utils/lib/typings/common\";\r\n\r\nimport \"./iconPicker.css\";\r\nimport {Input} from \"@/components/ui/input\";\r\n\r\n\r\nconst iconsList = Object.keys(HeroIcons);\r\n\r\nconst Picker = dynamic(\r\n  () => {\r\n    return import(\"emoji-picker-react\");\r\n  },\r\n  { ssr: false }\r\n);\r\n\r\ntype IconPickerItem = \"emoji\" | \"icon\";\r\n\r\nexport type PickedIcon = Icon | Emoji;\r\n\r\nexport interface IconPickerProps {\r\n  onPick: (item: PickedIcon) => void;\r\n  requestRemove?: () => void;\r\n  close: () => void;\r\n  xAlign?: \"l\" | \"r\" | \"c\";\r\n  yAlign?: \"t\" | \"b\" | \"m\";\r\n  enableRemove?: boolean;\r\n  enableEmojis?: boolean;\r\n}\r\n\r\nexport const IconPicker = ({\r\n  close,\r\n  onPick,\r\n  requestRemove,\r\n  xAlign,\r\n  yAlign,\r\n  enableRemove,\r\n  enableEmojis,\r\n}: IconPickerProps) => {\r\n  const [tab, setTab] = useState<IconPickerItem>(\r\n    enableEmojis ? \"emoji\" : \"icon\"\r\n  );\r\n  const popoverRef = useRef<any>(null);\r\n\r\n  const [search, setSearch] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: any) => {\r\n      if (popoverRef.current && !popoverRef.current.contains(event.target)) {\r\n        close();\r\n      }\r\n    };\r\n    document.addEventListener(\"click\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"click\", handleClickOutside);\r\n    };\r\n  });\r\n\r\n  const onChange = (e: any) => {\r\n    setSearch(e.target.value);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"relative \">\r\n        <div className=\"fixed z-20\">\r\n          <div\r\n            className={`w-[420px] absolute p-1 ${\r\n              yAlign === \"b\" ? \"bottom-0\" : \"top-0\"\r\n            }\r\n            ${xAlign === \"r\" ? \"right-0\" : \"left-0\"}`}\r\n            ref={popoverRef}\r\n          >\r\n            <AdjustToViewPort offset={20}>\r\n              <div className=\"relative bg-white overflow-hidden rounded-none shadow-lg ring-1 ring-black ring-opacity-5 pt-1 z-20\">\r\n                <div className=\"text-neutral-800 text-xs font-semibold px-1 border-b flex\">\r\n                  {enableEmojis && (\r\n                      <button\r\n                          className={`mr-2 p-2 border-b-2 ${\r\n                              tab === \"emoji\"\r\n                                  ? \"border-brand-blue\"\r\n                                  : \"text-gray-500  border-transparent\"\r\n                          }`}\r\n                          onClick={() => setTab(\"emoji\")}\r\n                      >\r\n                        Emoji\r\n                      </button>\r\n                  )}\r\n                  <button\r\n                      className={`mr-2 p-2 border-b-2  ${\r\n                          tab === \"icon\"\r\n                              ? \"border-brand-blue\"\r\n                              : \"text-gray-500  border-transparent\"\r\n                      }`}\r\n                      onClick={() => setTab(\"icon\")}\r\n                  >\r\n                    Icon\r\n                  </button>\r\n                  <span className=\"inline-block grow\"></span>\r\n                  {enableRemove && (\r\n                      <>\r\n                        <button\r\n                            className=\"mb-2 p-2 border-transparent  text-xs hover:bg-[#ddd] rounded\"\r\n                            onClick={() => {\r\n                              requestRemove?.();\r\n                              close();\r\n                            }}\r\n                        >\r\n                          Remove\r\n                        </button>\r\n                      </>\r\n                  )}\r\n                </div>\r\n                <div>\r\n                  <>\r\n                    <div\r\n                        className={`${\r\n                            enableEmojis && tab === \"emoji\" ? \"\" : \"hidden\"\r\n                        } h-[302px]`}\r\n                    >\r\n                      <div className='emoji-picker'>\r\n                        <Picker\r\n                            onEmojiClick={(e) => {\r\n                              onPick({\r\n                                type: ObjectType.Emoji,\r\n                                emoji: e.emoji,\r\n                              });\r\n                              close();\r\n                            }}\r\n                            width=\"100%\"\r\n                            height={302}\r\n                            previewConfig={{ showPreview: false }}\r\n                            searchDisabled={true}\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </>\r\n\r\n                  {tab === \"icon\" && (\r\n                      <>\r\n                        <div className={`${tab === \"icon\" ? \"\" : \"hidden\"}`}>\r\n                          <div className=\"relative p-2 border-b\">\r\n                            <div className=\"absolute top-0 left-0 w-full h-full pointer-events-none z-5 p-4\">\r\n                              <MagnifyingGlassIcon className=\"w-4 h-4\" />\r\n                            </div>\r\n                            <Input\r\n                                type=\"search\"\r\n                                id=\"sidebar-search\"\r\n                                onChange={onChange}\r\n                                value={search}\r\n                                className=\"text-xs rounded-none p-3 pl-8 h-8\"\r\n                                placeholder=\"Search\"\r\n                                required\r\n                            />\r\n                          </div>\r\n                          <div className=\"h-[250px] overflow-y-auto icon-picker\">\r\n                            <div className=\"icons\">\r\n                              {iconsList\r\n                                  .filter((icon) =>\r\n                                      icon.toLowerCase().includes(search.toLowerCase())\r\n                                  )\r\n                                  .map((icon, index) => {\r\n                                    return (\r\n                                        <button\r\n                                            key={icon}\r\n                                            className=\"icon hover:bg-[#ddd] rounded\"\r\n                                            onClick={() => {\r\n                                              onPick({\r\n                                                type: ObjectType.Icon,\r\n                                                icon: icon,\r\n                                              });\r\n                                              close();\r\n                                            }}\r\n                                        >\r\n                                          <IconPickerIcon icon={icon} />\r\n                                        </button>\r\n                                    );\r\n                                  })}\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </AdjustToViewPort>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport interface IconPickerIconProps extends HTMLProps<HTMLElement> {\r\n  icon: string;\r\n}\r\n\r\nexport const IconPickerIcon = ({ icon, ...rest }: IconPickerIconProps) => {\r\n  const IconComponent = (HeroIcons as any)[icon];\r\n  return IconComponent && <IconComponent width={24} height={24} {...rest} />;\r\n};\r\n\r\nexport interface PickedIconRenderProps extends HTMLProps<HTMLElement> {\r\n  icon: PickedIcon;\r\n}\r\n\r\nexport const PickedIconRender = ({ icon, ...rest }: PickedIconRenderProps) => {\r\n  if (icon.type === ObjectType.Icon)\r\n    return <IconPickerIcon icon={icon.icon} {...rest} />;\r\n\r\n  if (icon.type === ObjectType.Emoji)\r\n    return <div {...(rest as any)}>{icon.emoji}</div>;\r\n\r\n  return null;\r\n};\r\n", "import {DatabaseFieldDataType} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {CustomSelect} from \"@/components/custom-ui/customSelect\";\r\nimport React from \"react\";\r\nimport {checkCountAggregateFunction, checkPercentAggregateFunction, countAggregateFunction, numberAggregateFunction, percentAggregateFunction} from \"@/components/workspace/main/views/summaryTable/renderers/header\";\r\nimport {TagItem} from \"@/components/workspace/main/views/table/renderer/common/tag\";\r\nimport {CountAggregateFunction} from \"opendb-app-db-utils/lib\";\r\nimport {CheckboxAggregateFunction, CheckPercentAggregateFunction, NumberAggregateFunction, PercentAggregateFunction} from \"opendb-app-db-utils/lib/methods/number\";\r\n\r\n\r\ntype AggregateByFunction = CountAggregateFunction | NumberAggregateFunction | CheckboxAggregateFunction | PercentAggregateFunction | CheckPercentAggregateFunction\r\n\r\ninterface AggregateBySelectProps {\r\n    fieldType?: DatabaseFieldDataType\r\n    // resolveToNumber?: number\r\n    selectedId?: AggregateByFunction\r\n    onChange: (aggregateBy: CountAggregateFunction | NumberAggregateFunction | CheckboxAggregateFunction | PercentAggregateFunction | CheckPercentAggregateFunction) => void\r\n    placeholder?: string\r\n}\r\n\r\nexport const AggregateBySelect = (props: AggregateBySelectProps) => {\r\n    let options = [...countAggregateFunction, ...percentAggregateFunction]\r\n    if (props.fieldType) {\r\n        if (props.fieldType === DatabaseFieldDataType.Checkbox) options = [...checkCountAggregateFunction, ...checkPercentAggregateFunction]\r\n        else if (props.fieldType === DatabaseFieldDataType.Number) options.unshift(...numberAggregateFunction)\r\n    }\r\n\r\n    const tagOptions: TagItem<undefined>[] = options.map(o => {\r\n        const tag: TagItem<undefined> = {data: undefined, value: o.value, id: o.value, title: o.label}\r\n        return tag\r\n    })\r\n\r\n    return <>\r\n        <CustomSelect\r\n            onChange={v => props.onChange(v[0] as AggregateByFunction)}\r\n            selectedIds={props.selectedId ? [props.selectedId] : []}\r\n            placeholder={props.placeholder || 'Summarize by'}\r\n            options={tagOptions}\r\n        />\r\n    </>\r\n}", "import {UserGroupIcon} from \"@heroicons/react/24/outline\";\r\nimport {Label} from \"@/components/ui/label\";\r\nimport React, {useEffect, useState} from \"react\";\r\nimport {PanelProps} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {Align, AlignSelect} from \"@/components/custom-ui/alignSelect\";\r\nimport {InfoboxElement, Position} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {IconPicker, PickedIconRender} from \"@/components/custom-ui/iconPicker\";\r\nimport {Icon} from \"opendb-app-db-utils/lib/typings/common\";\r\nimport {Button} from \"@/components/ui/button\";\r\nimport {DatabaseSelect} from \"@/components/workspace/main/common/databaseSelect\";\r\nimport {CountAggregateFunction} from \"opendb-app-db-utils/lib\";\r\nimport {DatabaseColumnSelect} from \"@/components/workspace/main/common/databaseColumnSelect\";\r\nimport {AggregateBySelect} from \"@/components/workspace/main/common/aggregateBySelect\";\r\nimport {DatabaseFieldDataType, Match} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {useWorkspace} from \"@/providers/workspace\";\r\nimport {ElementRenderProps} from \"@/components/workspace/main/views/dashboard/components/common/elementRender\";\r\nimport {filterAndSortRecords} from \"@/components/workspace/main/views/table\";\r\nimport {resolveColumnValuesAggregation} from \"opendb-app-db-utils/lib/utils/db\";\r\nimport {percentAggregateFn} from \"@/components/workspace/main/views/summaryTable/renderers/cellRenderer\";\r\nimport {useViews} from \"@/providers/views\";\r\nimport {Loader} from \"@/components/custom-ui/loader\";\r\nimport {CircleExclamationIcon, FilterListIcon} from \"@/components/icons/FontAwesomeRegular\";\r\nimport {Database} from \"@/typings/database\";\r\nimport {ViewFilter} from \"@/components/workspace/main/views/common/viewFilter\";\r\nimport {Infobox} from \"@/components/custom-ui/Infobox\";\r\nimport {usePage} from \"@/providers/page\";\r\n\r\nexport const InfoBox = () => {\r\n    return <>\r\n        <Infobox value={250} title={'Total record'} icon={<UserGroupIcon width={20} height={20}/>}/>\r\n    </>\r\n}\r\n\r\nexport const InfoboxRender = (props: ElementRenderProps) => {\r\n    const {databaseStore, members, workspace, databaseErrorStore} = useWorkspace()\r\n    const {refreshDatabase} = useViews()\r\n\r\n\r\n    const element = props.dashboardDefinition.elementMap[props.id] as InfoboxElement\r\n\r\n    element.valueResolve = element.valueResolve || {\r\n        databaseId: '',\r\n        columnId: '',\r\n        aggregateBy: CountAggregateFunction.CountAll,\r\n        filter: {match: Match.All, conditions: []},\r\n    }\r\n    element.valueResolve.filter = element.valueResolve.filter || {match: Match.All, conditions: []}\r\n\r\n    const {filter, aggregateBy, columnId} = element.valueResolve\r\n\r\n    const database = databaseStore[element.valueResolve.databaseId]\r\n    const errorState = databaseErrorStore[element.valueResolve.databaseId]\r\n\r\n    let isLoading = false\r\n    let shouldRefreshDb = false\r\n    let error = ''\r\n    if (!element.valueResolve.databaseId) {\r\n        error = 'Database not defined'\r\n    } else if (!database && !errorState) {\r\n        shouldRefreshDb = true\r\n        isLoading = true\r\n    } else if (!database && errorState) {\r\n        isLoading = !!errorState.loading\r\n        error = errorState.error || ''\r\n    } else if (database && !database.recordsLoaded) {\r\n        shouldRefreshDb = true\r\n        isLoading = true\r\n    }\r\n\r\n    const getInfoValue = () => {\r\n        if (!database) return '-'\r\n\r\n        let {rows} = filterAndSortRecords(\r\n            database,\r\n            members,\r\n            databaseStore,\r\n            filter,\r\n            {match: Match.All, conditions: []},\r\n            [],\r\n            workspace.workspaceMember.userId\r\n        )\r\n\r\n        const values = rows.map(r => r.record.recordValues[columnId])\r\n        const value = parseInt(String(resolveColumnValuesAggregation(values, aggregateBy)))\r\n\r\n        if (isNaN(value)) return '-'\r\n\r\n        if (percentAggregateFn.includes(aggregateBy)) {\r\n            return `${value}%`\r\n        }\r\n        return value\r\n    }\r\n    const value = getInfoValue()\r\n\r\n    const icon = <div className=\"p-2 size-8 flex items-center bg-neutral-100 rounded-full\">\r\n        {element.icon ? <PickedIconRender\r\n            icon={element.icon as Icon}\r\n            className=\"size-full\"\r\n        /> : <UserGroupIcon className=\"size-full\"/>}\r\n    </div>\r\n\r\n    useEffect(() => {\r\n        if (shouldRefreshDb) {\r\n            refreshDatabase(element.valueResolve.databaseId).then()\r\n        }\r\n    }, [element.valueResolve.databaseId]);\r\n\r\n    return <>\r\n        <div className=\"rounded-none cursor-pointer p-4 bg-white flex-1 h-auto\">\r\n            <div className=\"flex justify-between\">\r\n                {element.iconPosition === Position.Left && <>{icon}</>}\r\n                <div className=\"flex flex-col\">\r\n                    <span className=\"text-xs text-gray-light font-medium truncate\">{element.title || 'Infobox'}</span>\r\n                    <h1 className=\"font-bold text-base\">\r\n                        {isLoading ? <Loader className='size-4 mt-2'/> :\r\n                         error ? <CircleExclamationIcon className='size-3 mt-2' title={error}/> :\r\n                         value}\r\n                    </h1>\r\n                    {error && <div className='text-xs !text-[10px] font-medium text-red-500 truncate mt-1'>{error}</div>}\r\n                </div>\r\n                {element.iconPosition !== Position.Left && <>{icon}</>}\r\n            </div>\r\n        </div>\r\n    </>\r\n}\r\n\r\n\r\nexport const InfoboxPanel = (props: PanelProps) => {\r\n    const {databaseStore} = useWorkspace()\r\n    const {updateElement} = props\r\n\r\n    const element = props.element as InfoboxElement\r\n\r\n    const valueResolve = element.valueResolve = element.valueResolve || {databaseId: '', columnId: '', aggregateBy: CountAggregateFunction.CountAll, filter: {match: Match.All, conditions: []}}\r\n    const [picker, setPicker] = useState(false);\r\n\r\n\r\n    let field: DatabaseFieldDataType | undefined = undefined\r\n    let database: Database | undefined\r\n    if (valueResolve.databaseId) {\r\n        if (databaseStore[valueResolve.databaseId]) {\r\n            database = databaseStore[valueResolve.databaseId].database\r\n        }\r\n        if (databaseStore[valueResolve.databaseId] && databaseStore[valueResolve.databaseId].database.definition.columnsMap[valueResolve.columnId]) {\r\n            field = databaseStore[valueResolve.databaseId].database.definition.columnsMap[valueResolve.columnId].type\r\n        }\r\n    }\r\n\r\n    const {page} = usePage()\r\n    useEffect(() => {\r\n        if (!valueResolve.databaseId && page.databaseId) {\r\n            const {databaseId} = page\r\n            updateElement({valueResolve: {...valueResolve, databaseId}})\r\n        }\r\n    }, [])\r\n\r\n    return <>\r\n\r\n        <div className='flex flex-col gap-2'>\r\n            <Label className=\"text-xs text-neutral-500\">Box Icon</Label>\r\n            <div className=\"flex\">\r\n                <div className=\"h-8 mr-2\">\r\n                    <Button\r\n                        variant='outline'\r\n                        className=\"rounded-none p-2 size-8 item-center\"\r\n                        onClick={() => setPicker(!picker)}>\r\n                        {element.icon ? (\r\n                            <PickedIconRender\r\n                                icon={element.icon as Icon}\r\n                                className=\"size-full\"\r\n                            />\r\n                        ) : (\r\n                             <UserGroupIcon className=\"size-full\"/>\r\n                         )}\r\n                    </Button>\r\n                </div>\r\n                <div className=\"relative top-10\">\r\n                    {picker && (\r\n                        <IconPicker\r\n                            onPick={function (item): void {\r\n                                updateElement({icon: item});\r\n                            }}\r\n                            requestRemove={function (): void {\r\n                                updateElement({icon: undefined});\r\n                            }}\r\n                            close={function (): void {\r\n                                setPicker(false);\r\n                            }}\r\n                            xAlign=\"r\"\r\n                            yAlign=\"t\"\r\n                            // enableRemove\r\n                        />\r\n                    )}\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div className='flex flex-col gap-2'>\r\n            <Label className=\"text-xs text-neutral-500\">Icon Align</Label>\r\n            <AlignSelect value={element.iconPosition === Position.Left ? Align.Left : Align.Right}\r\n                         onChange={v => {\r\n                             updateElement({iconPosition: v === Align.Left ? Position.Left : Position.Right})\r\n                         }}/>\r\n        </div>\r\n\r\n        <div className='flex flex-col gap-2'>\r\n            <Label className=\"text-xs text-neutral-500\">Choose database</Label>\r\n            <DatabaseSelect\r\n                disabled={!!page.databaseId}\r\n                onChange={databaseId => updateElement({\r\n                    valueResolve: {\r\n                        ...valueResolve,\r\n                        databaseId,\r\n                        columnId: '',\r\n                        aggregateBy: CountAggregateFunction.CountAll,\r\n                        filter: {match: Match.All, conditions: []}\r\n                    }\r\n                })}\r\n                selectedId={valueResolve.databaseId}/>\r\n        </div>\r\n\r\n\r\n        {valueResolve.databaseId && database && <>\r\n            <div className='flex flex-col gap-2'>\r\n                <Label className=\"text-xs text-neutral-500\">Filter</Label>\r\n                <ViewFilter\r\n                    database={database}\r\n                    trigger={\r\n                        <Button variant=\"outline\"\r\n                                className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\">\r\n                            <FilterListIcon className=\"size-3\"/>\r\n                            {valueResolve.filter.conditions.length > 0 ?\r\n                             `${valueResolve.filter.conditions.length} filters` :\r\n                             'Filter records'}\r\n                        </Button>\r\n                    }\r\n                    filter={valueResolve.filter}\r\n                    onChange={filter => {\r\n                        updateElement({valueResolve: {...valueResolve, filter}})\r\n                    }}\r\n                />\r\n            </div>\r\n\r\n            <div className='flex flex-col gap-2'>\r\n                <Label className=\"text-xs text-neutral-500\">Choose column</Label>\r\n                <DatabaseColumnSelect\r\n                    onChange={v => updateElement({valueResolve: {...valueResolve, columnId: v[0]}})}\r\n                    selected={valueResolve.columnId ? [valueResolve.columnId] : []}\r\n                    databaseId={valueResolve.databaseId}\r\n                />\r\n            </div>\r\n            <div className='flex flex-col gap-2'>\r\n                <Label className=\"text-xs text-neutral-500\">Summarize by</Label>\r\n                <AggregateBySelect\r\n                    selectedId={valueResolve.aggregateBy}\r\n                    fieldType={field}\r\n                    onChange={aggregateBy => updateElement({valueResolve: {...valueResolve, aggregateBy}})}\r\n                />\r\n            </div>\r\n\r\n        </>}\r\n\r\n\r\n    </>\r\n}", "import React, {PropsWithChildren} from \"react\";\r\n\r\n\r\nexport interface DataViewWrapperProps {\r\n    title: string\r\n}\r\n\r\nexport const DataViewWrapper = (props: PropsWithChildren<DataViewWrapperProps>) => {\r\n    return <>\r\n        <div className=\"rounded-none cursor-pointer bg-white flex-1\">\r\n            <div className=\"p-4 py-2.5 border-b text-xs font-bold\">\r\n                {props.title || 'Untitled'}\r\n            </div>\r\n            <div className=\"p-4\">\r\n                {props.children}\r\n            </div>\r\n        </div>\r\n    </>\r\n}", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RechartsPrimitive from \"recharts\"\r\nimport {\r\n  NameType,\r\n  Payload,\r\n  ValueType,\r\n} from \"recharts/types/component/DefaultTooltipContent\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\n// Format: { THEME_NAME: CSS_SELECTOR }\r\nconst THEMES = { light: \"\", dark: \".dark\" } as const\r\n\r\nexport type ChartConfig = {\r\n  [k in string]: {\r\n    label?: React.ReactNode\r\n    icon?: React.ComponentType\r\n  } & (\r\n    | { color?: string; theme?: never }\r\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\r\n  )\r\n}\r\n\r\ntype ChartContextProps = {\r\n  config: ChartConfig\r\n}\r\n\r\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\r\n\r\nfunction useChart() {\r\n  const context = React.useContext(ChartContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nconst ChartContainer = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    config: ChartConfig\r\n    children: React.ComponentProps<\r\n      typeof RechartsPrimitive.ResponsiveContainer\r\n    >[\"children\"]\r\n  }\r\n>(({ id, className, children, config, ...props }, ref) => {\r\n  const uniqueId = React.useId()\r\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\r\n\r\n  return (\r\n    <ChartContext.Provider value={{ config }}>\r\n      <div\r\n        data-chart={chartId}\r\n        ref={ref}\r\n        className={cn(\r\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <ChartStyle id={chartId} config={config} />\r\n        <RechartsPrimitive.ResponsiveContainer>\r\n          {children}\r\n        </RechartsPrimitive.ResponsiveContainer>\r\n      </div>\r\n    </ChartContext.Provider>\r\n  )\r\n})\r\nChartContainer.displayName = \"Chart\"\r\n\r\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\r\n  const colorConfig = Object.entries(config).filter(\r\n    ([_, config]) => config.theme || config.color\r\n  )\r\n\r\n  if (!colorConfig.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <style\r\n      dangerouslySetInnerHTML={{\r\n        __html: Object.entries(THEMES)\r\n          .map(\r\n            ([theme, prefix]) => `\r\n${prefix} [data-chart=${id}] {\r\n${colorConfig\r\n  .map(([key, itemConfig]) => {\r\n    const color =\r\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\r\n      itemConfig.color\r\n    return color ? `  --color-${key}: ${color};` : null\r\n  })\r\n  .join(\"\\n\")}\r\n}\r\n`\r\n          )\r\n          .join(\"\\n\"),\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst ChartTooltip = RechartsPrimitive.Tooltip\r\n\r\nconst ChartTooltipContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\r\n    React.ComponentProps<\"div\"> & {\r\n      hideLabel?: boolean\r\n      hideIndicator?: boolean\r\n      indicator?: \"line\" | \"dot\" | \"dashed\"\r\n      nameKey?: string\r\n      labelKey?: string\r\n    }\r\n>(\r\n  (\r\n    {\r\n      active,\r\n      payload,\r\n      className,\r\n      indicator = \"dot\",\r\n      hideLabel = false,\r\n      hideIndicator = false,\r\n      label,\r\n      labelFormatter,\r\n      labelClassName,\r\n      formatter,\r\n      color,\r\n      nameKey,\r\n      labelKey,\r\n    },\r\n    ref\r\n  ) => {\r\n    const { config } = useChart()\r\n\r\n    const tooltipLabel = React.useMemo(() => {\r\n      if (hideLabel || !payload?.length) {\r\n        return null\r\n      }\r\n\r\n      const [item] = payload\r\n      const key = `${labelKey || item.dataKey || item.name || \"value\"}`\r\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n      const value =\r\n        !labelKey && typeof label === \"string\"\r\n          ? config[label as keyof typeof config]?.label || label\r\n          : itemConfig?.label\r\n\r\n      if (labelFormatter) {\r\n        return (\r\n          <div className={cn(\"font-medium\", labelClassName)}>\r\n            {labelFormatter(value, payload)}\r\n          </div>\r\n        )\r\n      }\r\n\r\n      if (!value) {\r\n        return null\r\n      }\r\n\r\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\r\n    }, [\r\n      label,\r\n      labelFormatter,\r\n      payload,\r\n      hideLabel,\r\n      labelClassName,\r\n      config,\r\n      labelKey,\r\n    ])\r\n\r\n    if (!active || !payload?.length) {\r\n      return null\r\n    }\r\n\r\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className={cn(\r\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\r\n          className\r\n        )}\r\n      >\r\n        {!nestLabel ? tooltipLabel : null}\r\n        <div className=\"grid gap-1.5\">\r\n          {payload.map((item, index) => {\r\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\r\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n            const indicatorColor = color || item.payload.fill || item.color\r\n\r\n            return (\r\n              <div\r\n                key={item.dataKey}\r\n                className={cn(\r\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\r\n                  indicator === \"dot\" && \"items-center\"\r\n                )}\r\n              >\r\n                {formatter && item?.value !== undefined && item.name ? (\r\n                  formatter(item.value, item.name, item, index, item.payload)\r\n                ) : (\r\n                  <>\r\n                    {itemConfig?.icon ? (\r\n                      <itemConfig.icon />\r\n                    ) : (\r\n                      !hideIndicator && (\r\n                        <div\r\n                          className={cn(\r\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\r\n                            {\r\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\r\n                              \"w-1\": indicator === \"line\",\r\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\r\n                                indicator === \"dashed\",\r\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\r\n                            }\r\n                          )}\r\n                          style={\r\n                            {\r\n                              \"--color-bg\": indicatorColor,\r\n                              \"--color-border\": indicatorColor,\r\n                            } as React.CSSProperties\r\n                          }\r\n                        />\r\n                      )\r\n                    )}\r\n                    <div\r\n                      className={cn(\r\n                        \"flex flex-1 justify-between leading-none\",\r\n                        nestLabel ? \"items-end\" : \"items-center\"\r\n                      )}\r\n                    >\r\n                      <div className=\"grid gap-1.5\">\r\n                        {nestLabel ? tooltipLabel : null}\r\n                        <span className=\"text-muted-foreground\">\r\n                          {itemConfig?.label || item.name}\r\n                        </span>\r\n                      </div>\r\n                      {item.value && (\r\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\r\n                          {item.value.toLocaleString()}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </div>\r\n            )\r\n          })}\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n)\r\nChartTooltipContent.displayName = \"ChartTooltip\"\r\n\r\nconst ChartLegend = RechartsPrimitive.Legend\r\n\r\nconst ChartLegendContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> &\r\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\r\n      hideIcon?: boolean\r\n      nameKey?: string\r\n    }\r\n>(\r\n  (\r\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\r\n    ref\r\n  ) => {\r\n    const { config } = useChart()\r\n\r\n    if (!payload?.length) {\r\n      return null\r\n    }\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className={cn(\r\n          \"flex items-center justify-center gap-4\",\r\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\r\n          className\r\n        )}\r\n      >\r\n        {payload.map((item) => {\r\n          const key = `${nameKey || item.dataKey || \"value\"}`\r\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n\r\n          return (\r\n            <div\r\n              key={item.value}\r\n              className={cn(\r\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\r\n              )}\r\n            >\r\n              {itemConfig?.icon && !hideIcon ? (\r\n                <itemConfig.icon />\r\n              ) : (\r\n                <div\r\n                  className=\"h-2 w-2 shrink-0 rounded-[2px]\"\r\n                  style={{\r\n                    backgroundColor: item.color,\r\n                  }}\r\n                />\r\n              )}\r\n              {itemConfig?.label}\r\n            </div>\r\n          )\r\n        })}\r\n      </div>\r\n    )\r\n  }\r\n)\r\nChartLegendContent.displayName = \"ChartLegend\"\r\n\r\n// Helper to extract item config from a payload.\r\nfunction getPayloadConfigFromPayload(\r\n  config: ChartConfig,\r\n  payload: unknown,\r\n  key: string\r\n) {\r\n  if (typeof payload !== \"object\" || payload === null) {\r\n    return undefined\r\n  }\r\n\r\n  const payloadPayload =\r\n    \"payload\" in payload &&\r\n    typeof payload.payload === \"object\" &&\r\n    payload.payload !== null\r\n      ? payload.payload\r\n      : undefined\r\n\r\n  let configLabelKey: string = key\r\n\r\n  if (\r\n    key in payload &&\r\n    typeof payload[key as keyof typeof payload] === \"string\"\r\n  ) {\r\n    configLabelKey = payload[key as keyof typeof payload] as string\r\n  } else if (\r\n    payloadPayload &&\r\n    key in payloadPayload &&\r\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\r\n  ) {\r\n    configLabelKey = payloadPayload[\r\n      key as keyof typeof payloadPayload\r\n    ] as string\r\n  }\r\n\r\n  return configLabelKey in config\r\n    ? config[configLabelKey]\r\n    : config[key as keyof typeof config]\r\n}\r\n\r\nexport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  ChartStyle,\r\n}\r\n", "import React, {useEffect, useState} from 'react';\r\nimport {CategoryScale, Chart as ChartJS, Legend, LinearScale, LineElement, PointElement, Title, Tooltip,} from 'chart.js';\r\nimport {Line} from 'react-chartjs-2';\r\nimport {DataViewWrapper} from \"@/components/workspace/main/views/dashboard/components/common/dataViewWrapper\";\r\nimport {PanelProps} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {useWorkspace} from \"@/providers/workspace\";\r\nimport {LineChartElement, ViewColumnCustomization} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {DbRecordsResolver, MagicColumn, Match, Sort} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {Label} from \"@/components/ui/label\";\r\nimport {DatabaseSelect} from \"@/components/workspace/main/common/databaseSelect\";\r\nimport {Database} from \"@/typings/database\";\r\nimport {arrayDeDuplicate} from \"opendb-app-db-utils/lib\";\r\nimport {ViewFilter} from \"@/components/workspace/main/views/common/viewFilter\";\r\nimport {Button} from \"@/components/ui/button\";\r\nimport {ArrowUpWideShortIcon, ChevronLeftIcon, ChevronRightIcon, FilterListIcon} from \"@/components/icons/FontAwesomeRegular\";\r\nimport {ViewSort} from \"@/components/workspace/main/views/common/viewSort\";\r\nimport {DatabaseFieldTypeIcon} from \"@/components/workspace/main/database/databaseFieldTypeIcon\";\r\nimport {Switch} from \"@/components/ui/switch\";\r\nimport {ElementRenderProps} from \"@/components/workspace/main/views/dashboard/components/common/elementRender\";\r\nimport {useViews} from \"@/providers/views\";\r\nimport {DataViewRow, filterAndSortRecords} from \"@/components/workspace/main/views/table\";\r\nimport {PageLoader} from \"@/components/custom-ui/loader\";\r\nimport {ColorEntries, generateVibrantColors} from \"@/utils/color\";\r\nimport {recordValueToText} from \"opendb-app-db-utils/lib/utils/db\";\r\nimport {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from \"@/components/custom-ui/select\";\r\nimport {usePage} from \"@/providers/page\";\r\nimport {getDatabaseTitleCol} from \"@/components/workspace/main/views/form/components/element/linked\";\r\nimport {ChartConfig, ChartContainer, ChartLegend, ChartLegendContent, ChartTooltip, ChartTooltipContent} from \"@/components/ui/chart\";\r\nimport {CartesianGrid, Line as ReChartsLine, LineChart, XAxis} from \"recharts\";\r\n\r\nChartJS.register(\r\n    CategoryScale,\r\n    LinearScale,\r\n    PointElement,\r\n    LineElement,\r\n    Title,\r\n    Tooltip,\r\n    Legend\r\n);\r\n\r\ninterface LineChartDataset {\r\n    label: string\r\n    data: number[]\r\n    borderColor: string\r\n    backgroundColor: string\r\n}\r\n\r\ninterface LineChartOptions {\r\n    labels: string[]\r\n    datasets: LineChartDataset[]\r\n}\r\n\r\n\r\nexport interface ChartRow {\r\n    label: string;\r\n\r\n    [key: string]: string | number\r\n}\r\n\r\nexport const LineChartRender = (props: ElementRenderProps) => {\r\n    const {databaseStore, members, workspace, databaseErrorStore} = useWorkspace()\r\n    const {refreshDatabase} = useViews()\r\n\r\n    const [paginationPage, setPaginationPage] = useState(1)\r\n    const [perPage, setPerPage] = useState(20)\r\n\r\n\r\n    const element = props.dashboardDefinition.elementMap[props.id] as LineChartElement\r\n\r\n    const defaultResolve: DbRecordsResolver = {\r\n        databaseId: '',\r\n        filter: {match: Match.All, conditions: []},\r\n        sorts: [],\r\n    }\r\n    const recordsResolve = element.recordsResolve = element.recordsResolve || defaultResolve\r\n\r\n    const columnPropsMap = element.columnPropsMap || {}\r\n    recordsResolve.sorts = recordsResolve.sorts || []\r\n\r\n    const database = databaseStore[element.recordsResolve.databaseId]\r\n    const errorState = databaseErrorStore[element.recordsResolve.databaseId]\r\n\r\n    let isLoading = false\r\n    let shouldRefreshDb = false\r\n    let error = ''\r\n    if (!element.recordsResolve.databaseId) {\r\n        error = 'Database not defined'\r\n    } else if (!database && !errorState) {\r\n        shouldRefreshDb = true\r\n        isLoading = true\r\n    } else if (!database && errorState) {\r\n        isLoading = !!errorState.loading\r\n        error = errorState.error || ''\r\n    } else if (database && !database.recordsLoaded) {\r\n        shouldRefreshDb = true\r\n        isLoading = true\r\n    }\r\n\r\n    let totalPage = 0\r\n\r\n    const getProcessedRows = (): DataViewRow[] => {\r\n        if (!database) return []\r\n\r\n        const sorts = [...recordsResolve.sorts]\r\n        if (sorts.length === 0) sorts.push({columnId: MagicColumn.CreatedAt, order: Sort.Asc})\r\n\r\n        const {rows} = filterAndSortRecords(\r\n            database,\r\n            members,\r\n            databaseStore,\r\n            recordsResolve.filter,\r\n            {match: Match.All, conditions: []},\r\n            sorts,\r\n            workspace.workspaceMember.userId\r\n        )\r\n        totalPage = Math.ceil(rows.length / perPage)\r\n\r\n        return rows\r\n    }\r\n\r\n    const nextPage = () => {\r\n        if (paginationPage < totalPage) setPaginationPage(paginationPage + 1)\r\n    }\r\n    const prevPage = () => {\r\n        if (paginationPage > 1) setPaginationPage(paginationPage - 1)\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (shouldRefreshDb) {\r\n            refreshDatabase(element.recordsResolve.databaseId).then()\r\n        }\r\n    }, [element.recordsResolve.databaseId]);\r\n\r\n\r\n    const getChartInfo = () => {\r\n        const options = {\r\n            responsive: true,\r\n            plugins: {\r\n                legend: {\r\n                    position: 'top' as const,\r\n                },\r\n            },\r\n        }\r\n        const labels: string[] = []\r\n        const data: LineChartOptions = {\r\n            datasets: [], labels\r\n        }\r\n        if (database) {\r\n            let titleColId = getDatabaseTitleCol(database.database).titleColId\r\n\r\n            const dbDefinition = database.database.definition\r\n            const columnIds = dbDefinition.columnIds.filter(k => !!columnPropsMap[k] && !!dbDefinition.columnsMap[k] && !columnPropsMap[k].isHidden)\r\n            for (let i = 0; i < columnIds.length; i++) {\r\n                const id = columnIds[i];\r\n                const color = ColorEntries[i % ColorEntries.length]\r\n                const fg = color.info.fg;\r\n                const bg = color.info.bg;\r\n\r\n                data.datasets.push({\r\n                    backgroundColor: bg,\r\n                    borderColor: fg,\r\n                    data: [],\r\n                    label: dbDefinition.columnsMap[id]?.title || 'Unknown column'\r\n                })\r\n            }\r\n            const rows = getProcessedRows()\r\n            const offset = (paginationPage - 1) * perPage;\r\n            const pagedRows = rows.slice(offset, offset + perPage)\r\n            for (const row of pagedRows) {\r\n                const {record, processedRecord} = row\r\n\r\n                const title = recordValueToText(processedRecord.processedRecordValues[titleColId])\r\n                data.labels.push(title && String(title).trim() ? String(title).trim() : 'Untitled')\r\n\r\n                for (let i = 0; i < columnIds.length; i++) {\r\n                    const id = columnIds[i];\r\n                    let recordV1lToText = Number(recordValueToText(processedRecord.processedRecordValues[id]))\r\n                    if (isNaN(recordV1lToText)) recordV1lToText = 0 // could be ''\r\n                    data.datasets[i].data.push(recordV1lToText)\r\n                }\r\n            }\r\n        }\r\n        return {options, data}\r\n    }\r\n\r\n    const getChartInfo2 = () => {\r\n\r\n        const data: ChartRow[] = []\r\n        const config: ChartConfig = {}\r\n        if (database) {\r\n            let titleColId = getDatabaseTitleCol(database.database).titleColId\r\n\r\n            const dbDefinition = database.database.definition\r\n            const columnIds = dbDefinition.columnIds.filter(k => !!columnPropsMap[k] && !!dbDefinition.columnsMap[k] && !columnPropsMap[k].isHidden)\r\n\r\n            const colours = generateVibrantColors(columnIds.length)\r\n\r\n            for (let i = 0; i < columnIds.length; i++) {\r\n                const id = columnIds[i];\r\n\r\n                config[id] = {\r\n                    label: dbDefinition.columnsMap[id]?.title || 'Unknown column',\r\n                    color: colours[i]\r\n                }\r\n            }\r\n            const rows = getProcessedRows()\r\n            const offset = (paginationPage - 1) * perPage;\r\n            const pagedRows = rows.slice(offset, offset + perPage)\r\n            for (const row of pagedRows) {\r\n                const {record, processedRecord} = row\r\n\r\n                const rawTitle = recordValueToText(processedRecord.processedRecordValues[titleColId])\r\n                const label = rawTitle && String(rawTitle).trim() ? String(rawTitle).trim() : 'Untitled';\r\n\r\n                // data.labels.push(rawTitle && String(rawTitle).trim() ? String(rawTitle).trim() : 'Untitled')\r\n\r\n\r\n                const datum: ChartRow = {\r\n                    label,\r\n                }\r\n\r\n                for (let i = 0; i < columnIds.length; i++) {\r\n                    const id = columnIds[i];\r\n                    let recordV1lToText = Number(recordValueToText(processedRecord.processedRecordValues[id]))\r\n                    if (isNaN(recordV1lToText)) recordV1lToText = 0 // could be ''\r\n                    datum[id] = recordV1lToText\r\n                }\r\n                data.push(datum)\r\n            }\r\n        }\r\n        return {config, data}\r\n    }\r\n    const chart = getChartInfo()\r\n\r\n    const data = getChartInfo2()\r\n\r\n    return <>\r\n        <DataViewWrapper title={element.title || \"Line Chart\"}>\r\n            {(isLoading || error) ? <div className='h-36'>\r\n                <PageLoader\r\n                    size={'full'}\r\n                    error={error}\r\n                />\r\n            </div> : <>\r\n                 <div className='hidden'>\r\n                     <Line options={chart.options} data={chart.data} className=\"w-full\"/>\r\n                 </div>\r\n\r\n                 <ChartContainer config={data.config}>\r\n                     <LineChart\r\n                         accessibilityLayer\r\n                         data={data.data}\r\n                         margin={{\r\n                             left: 12,\r\n                             right: 12,\r\n                         }}\r\n                     >\r\n                         <CartesianGrid vertical={false}/>\r\n                         <XAxis\r\n                             dataKey=\"label\"\r\n                             tickLine={false}\r\n                             axisLine={false}\r\n                             tickMargin={8}\r\n                             // tickFormatter={(value) => value.slice(0, 3)}\r\n                         />\r\n                         <ChartTooltip cursor={false} content={<ChartTooltipContent indicator=\"line\"/>}/>\r\n                         <ChartLegend content={<ChartLegendContent />} />\r\n                         {Object.keys(data.config).map(id => {\r\n                             return <ReChartsLine\r\n                                 key={id}\r\n                                 dataKey={id}\r\n                                 type=\"linear\"\r\n                                 // stroke=\"var(--color-desktop)\"\r\n                                 stroke={data.config[id].color}\r\n                                 strokeWidth={2}\r\n                                 // dot={true}\r\n                                 dot={{\r\n                                     fill: data.config[id].color,\r\n                                 }}\r\n                                 activeDot={{\r\n                                     r: 6,\r\n                                 }}\r\n                             />\r\n                         })}\r\n\r\n                     </LineChart>\r\n                 </ChartContainer>\r\n\r\n                 <div className=\"flex items-center justify-between mt-4\" aria-label=\"Table navigation\">\r\n                     <div className=\"text-xs font-medium text-muted-foreground flex gap-1 items-center\">\r\n                         <div className='whitespace-nowrap'>Records per page</div>\r\n                         <Select\r\n                             onValueChange={v => {\r\n                                 const val = parseInt(v)\r\n                                 if (val === perPage) return\r\n                                 setPerPage(val)\r\n                                 setPaginationPage(1)\r\n                             }}>\r\n                             <SelectTrigger className=\"h-7 rounded-none text-xs\">\r\n                                 <SelectValue className='!text-xs' placeholder={perPage}/>\r\n                             </SelectTrigger>\r\n                             <SelectContent className='rounded-none text-xs'>\r\n                                 <SelectItem value=\"10\" className='rounded-none text-xs'>10</SelectItem>\r\n                                 <SelectItem value=\"20\" className='rounded-none text-xs'>20</SelectItem>\r\n                                 <SelectItem value=\"50\" className='rounded-none text-xs'>50</SelectItem>\r\n                                 <SelectItem value=\"100\" className='rounded-none text-xs'>100</SelectItem>\r\n                             </SelectContent>\r\n                         </Select>\r\n                     </div>\r\n                     <div className=\"inline-flex -space-x-px gap-2 text-sm h-8 items-center\">\r\n                         <div className=\"text-xs font-medium text-muted-foreground\">\r\n                             Page {paginationPage} of {totalPage}\r\n                         </div>\r\n                         <ul className=\"inline-flex\">\r\n                             <li>\r\n                                 <Button variant={\"outline\"}\r\n                                         className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\"\r\n                                         disabled={paginationPage <= 1}\r\n                                         onClick={prevPage}>\r\n                                     <ChevronLeftIcon className='size-3'/>\r\n                                 </Button>\r\n                             </li>\r\n                             <li>\r\n                                 <Button variant={\"outline\"}\r\n                                         className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\"\r\n                                         disabled={paginationPage >= totalPage}\r\n                                         onClick={nextPage}>\r\n                                     <ChevronRightIcon className='size-3'/>\r\n                                 </Button>\r\n                             </li>\r\n                         </ul>\r\n                     </div>\r\n                 </div>\r\n             </>}\r\n        </DataViewWrapper>\r\n    </>\r\n}\r\n\r\nexport const fixColumnPropsMap = (database: Database, columnsOrder: string[], columnPropsMap: {\r\n    [key: string]: ViewColumnCustomization\r\n}) => {\r\n    columnsOrder = columnsOrder || []\r\n    columnPropsMap = columnPropsMap || {}\r\n\r\n    if (database) {\r\n        for (const key of database.definition.columnIds) {\r\n            if (!columnsOrder.includes(key)) columnsOrder.push(key)\r\n            if (!columnPropsMap[key]) columnPropsMap[key] = {}\r\n        }\r\n        columnsOrder = arrayDeDuplicate(columnsOrder)\r\n    }\r\n\r\n    return {columnsOrder, columnPropsMap}\r\n}\r\n\r\nexport const LineChartPanel = (props: PanelProps) => {\r\n    const {databaseStore} = useWorkspace()\r\n    const {updateElement} = props\r\n\r\n    const element = props.element as LineChartElement\r\n\r\n    const defaultResolve: DbRecordsResolver = {\r\n        databaseId: '',\r\n        filter: {match: Match.All, conditions: []},\r\n        sorts: [],\r\n    }\r\n    const recordsResolve = element.recordsResolve = element.recordsResolve || defaultResolve\r\n\r\n    recordsResolve.sorts = recordsResolve.sorts || []\r\n\r\n    let database: Database | undefined\r\n    if (recordsResolve.databaseId) {\r\n        if (databaseStore[recordsResolve.databaseId]) {\r\n            database = databaseStore[recordsResolve.databaseId].database\r\n        }\r\n    }\r\n\r\n    if (database) {\r\n        const {columnsOrder, columnPropsMap} = fixColumnPropsMap(database, element.columnsOrder, element.columnPropsMap)\r\n\r\n        element.columnsOrder = columnsOrder\r\n        element.columnPropsMap = columnPropsMap\r\n    }\r\n\r\n    const {page} = usePage()\r\n    useEffect(() => {\r\n        if (!recordsResolve.databaseId && page.databaseId) {\r\n            const {databaseId} = page\r\n            updateElement({recordsResolve: {...recordsResolve, databaseId}})\r\n        }\r\n    }, [])\r\n\r\n    return <>\r\n\r\n        <div className='flex flex-col gap-2'>\r\n            <Label className=\"text-xs text-neutral-500\">Choose database</Label>\r\n            <DatabaseSelect\r\n                disabled={!!page.databaseId}\r\n                onChange={databaseId => updateElement({recordsResolve: {...recordsResolve, databaseId}})}\r\n                selectedId={recordsResolve.databaseId}/>\r\n        </div>\r\n\r\n        {recordsResolve.databaseId && database && <>\r\n            <div className='flex flex-col gap-2'>\r\n                <Label className=\"text-xs text-neutral-500\">Choose Fields</Label>\r\n                <div>\r\n                    {Object.values(database.definition.columnsMap).map((c, i) => {\r\n                        const isHidden = element.columnPropsMap[c.id].isHidden;\r\n                        return (\r\n                            <div\r\n                                key={c.id}\r\n                                role='button'\r\n                                className=\"flex select-none font-medium cursor-pointer text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden relative items-center hover:bg-transparent\">\r\n                                <DatabaseFieldTypeIcon type={c.type} className=\"size-3\"/>\r\n                                <div className=\"flex-1 overflow-hidden truncate text-left\">\r\n                                    {c.title}\r\n                                </div>\r\n                                <Switch\r\n                                    className=\"h-4 w-8\"\r\n                                    thumbClassName=\"!size-3\"\r\n                                    checked={!isHidden}\r\n                                    onCheckedChange={checked => {\r\n                                        const updatedPropsMap = {...element.columnPropsMap}\r\n                                        updatedPropsMap[c.id].isHidden = !checked\r\n                                        updateElement({columnPropsMap: updatedPropsMap})\r\n                                    }}/>\r\n                            </div>\r\n                        );\r\n                    })}\r\n                </div>\r\n            </div>\r\n\r\n            <div className='flex flex-col gap-2'>\r\n                <Label className=\"text-xs text-neutral-500\">Filter</Label>\r\n                <ViewFilter\r\n                    database={database}\r\n                    trigger={\r\n                        <Button variant=\"outline\"\r\n                                className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\">\r\n                            <FilterListIcon className=\"size-3\"/>\r\n                            {recordsResolve.filter.conditions.length > 0 ?\r\n                             `${recordsResolve.filter.conditions.length} filters` :\r\n                             'Filter records'}\r\n                        </Button>\r\n                    }\r\n                    filter={recordsResolve.filter}\r\n                    onChange={filter => {\r\n                        updateElement({recordsResolve: {...recordsResolve, filter}})\r\n                    }}\r\n                />\r\n            </div>\r\n\r\n            <div className='flex flex-col gap-2'>\r\n                <Label className=\"text-xs text-neutral-500\">Sort</Label>\r\n                <ViewSort\r\n                    database={database}\r\n                    sorts={recordsResolve.sorts}\r\n                    onChange={sorts => updateElement({recordsResolve: {...recordsResolve, sorts}})}\r\n                    trigger={\r\n                        <Button variant=\"outline\"\r\n                                className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\">\r\n                            <ArrowUpWideShortIcon className=\"size-3\"/>\r\n                            {recordsResolve.sorts.length > 0 ?\r\n                             `${recordsResolve.sorts.length} sorts` :\r\n                             'Sort'}\r\n                        </Button>\r\n                    }\r\n                />\r\n            </div>\r\n\r\n        </>}\r\n\r\n\r\n    </>\r\n}\r\n\r\n\r\n\r\n", "import React, {useEffect} from 'react';\r\nimport {ArcElement, Chart as ChartJS, <PERSON>, Tooltip} from 'chart.js';\r\nimport {Pie} from 'react-chartjs-2';\r\nimport {DataViewWrapper} from \"@/components/workspace/main/views/dashboard/components/common/dataViewWrapper\";\r\nimport ChartDataLabels from \"chartjs-plugin-datalabels\";\r\nimport {PanelProps} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {useWorkspace} from \"@/providers/workspace\";\r\nimport {PieChartElement} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {DatabaseColumnReturnValue, DbRecordSort, MagicColumn, Match, RecordValues, Sort} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {Database} from \"@/typings/database\";\r\nimport {Label} from \"@/components/ui/label\";\r\nimport {DatabaseSelect} from \"@/components/workspace/main/common/databaseSelect\";\r\nimport {ViewFilter} from \"@/components/workspace/main/views/common/viewFilter\";\r\nimport {But<PERSON>} from \"@/components/ui/button\";\r\nimport {FilterListIcon} from \"@/components/icons/FontAwesomeRegular\";\r\nimport {DatabaseColumnSelect} from \"@/components/workspace/main/common/databaseColumnSelect\";\r\nimport {ElementRenderProps} from \"@/components/workspace/main/views/dashboard/components/common/elementRender\";\r\nimport {useViews} from \"@/providers/views\";\r\nimport {PageLoader} from \"@/components/custom-ui/loader\";\r\nimport {DataViewRow, filterAndSortRecords} from \"@/components/workspace/main/views/table\";\r\nimport crypto from \"crypto\";\r\nimport {SummaryViewRow} from \"@/components/workspace/main/views/summaryTable/summaryTableView\";\r\nimport {recordValueToText} from \"opendb-app-db-utils/lib/utils/db\";\r\nimport {ColorEntries, generateVibrantColors} from \"@/utils/color\";\r\nimport {usePage} from \"@/providers/page\";\r\nimport {ChartConfig, ChartContainer, ChartLegend, ChartLegendContent, ChartTooltip, ChartTooltipContent} from \"@/components/ui/chart\";\r\nimport {ChartRow} from \"@/components/workspace/main/views/dashboard/components/elements/lineChart\";\r\nimport {Label as ReChartsLabel, LabelList, Pie as ReChartsPie, PieChart} from \"recharts\";\r\n\r\nChartJS.register(ArcElement, Tooltip, Legend);\r\n\r\n\r\ninterface PieChartData {\r\n    labels: string[]\r\n    datasets: {\r\n        label: string,\r\n        data: number[],\r\n        backgroundColor?: string[],\r\n        borderColor?: string[],\r\n        borderWidth?: number\r\n    }[]\r\n}\r\n\r\nexport const PieChartRender = (props: ElementRenderProps) => {\r\n    const {databaseStore, members, workspace, databaseErrorStore} = useWorkspace()\r\n    const {refreshDatabase} = useViews()\r\n\r\n    const element = props.dashboardDefinition.elementMap[props.id] as PieChartElement\r\n    const recordsResolve = element.recordsResolve = element.recordsResolve || {\r\n        databaseId: '',\r\n        filter: {match: Match.All, conditions: []},\r\n        groupByIds: [],\r\n        titleColId: '',\r\n    }\r\n    recordsResolve.filter = recordsResolve.filter || {match: Match.All, conditions: []}\r\n    recordsResolve.groupByIds = recordsResolve.groupByIds || []\r\n\r\n    const database = databaseStore[element.recordsResolve.databaseId]\r\n    const errorState = databaseErrorStore[element.recordsResolve.databaseId]\r\n\r\n    let isLoading = false\r\n    let shouldRefreshDb = false\r\n    let error = ''\r\n    if (!element.recordsResolve.databaseId) {\r\n        error = 'Database not defined'\r\n    } else if (!element.recordsResolve.groupByIds || !Array.isArray(element.recordsResolve.groupByIds) || element.recordsResolve.groupByIds.length < 1) {\r\n        error = 'Group columns not configured'\r\n    } else if (!database && !errorState) {\r\n        shouldRefreshDb = true\r\n        isLoading = true\r\n    } else if (!database && errorState) {\r\n        isLoading = !!errorState.loading\r\n        error = errorState.error || ''\r\n    } else if (database && !database.recordsLoaded) {\r\n        shouldRefreshDb = true\r\n        isLoading = true\r\n    }\r\n\r\n    const getProcessedRows = (): {\r\n        rowMap: {\r\n            [key: string]: DataViewRow\r\n        },\r\n        groupRowMap: {\r\n            [key: string]: SummaryViewRow\r\n        }\r\n    } => {\r\n        if (!database || !recordsResolve.groupByIds) return {rowMap: {}, groupRowMap: {}}\r\n\r\n        const sortOptions: DbRecordSort[] = []\r\n        sortOptions.push({columnId: MagicColumn.CreatedAt, order: Sort.Asc})\r\n\r\n        let {rows} = filterAndSortRecords(\r\n            database,\r\n            members,\r\n            databaseStore,\r\n            recordsResolve.filter,\r\n            {match: Match.All, conditions: []},\r\n            sortOptions,\r\n            workspace.workspaceMember.userId\r\n        )\r\n        const groupRowMap: {\r\n            [key: string]: SummaryViewRow\r\n        } = {}\r\n        const rowMap: {\r\n            [key: string]: DataViewRow\r\n        } = {}\r\n\r\n        let groupBy = recordsResolve.groupByIds\r\n\r\n        for (const row of rows) {\r\n            const groupValues: RecordValues = {}\r\n            const processedRecordValues: {\r\n                [key: string]: DatabaseColumnReturnValue;\r\n            } = {}\r\n\r\n            const textValues: string[] = []\r\n            for (const id of groupBy) {\r\n                groupValues[id] = typeof row.record.recordValues[id] === 'string' ? row.record.recordValues[id].trim() : row.record.recordValues[id]\r\n                processedRecordValues[id] = typeof row.processedRecord.processedRecordValues[id] === \"string\" ? row.processedRecord.processedRecordValues[id].trim() : row.processedRecord.processedRecordValues[id]\r\n\r\n                const columnTitle = database.database.definition.columnsMap[id]?.title || 'Unknown column'\r\n                const columnValueToText = recordValueToText(row.processedRecord.processedRecordValues[id] as DatabaseColumnReturnValue)\r\n\r\n                textValues.push(`${columnTitle}: ${columnValueToText}`)\r\n            }\r\n\r\n            const valuesJSON = JSON.stringify(groupValues)\r\n            const hash = crypto.createHash('sha1').update(valuesJSON).digest('hex')\r\n\r\n            if (groupRowMap[hash]) {\r\n                const updatedAtTime = Math.max(new Date(groupRowMap[hash].updatedAt).getTime(), new Date(row.updatedAt).getTime())\r\n                groupRowMap[hash].updatedAt = new Date(updatedAtTime).toISOString()\r\n                groupRowMap[hash].recordIds.push(row.id)\r\n            } else {\r\n                groupRowMap[hash] = {\r\n                    recordIds: [row.id],\r\n                    updatedAt: row.updatedAt,\r\n                    id: hash,\r\n                    rowMap: {},\r\n                    valuesText: textValues.join(',')\r\n                }\r\n            }\r\n            groupRowMap[hash].rowMap[row.id] = row\r\n            rowMap[row.id] = row\r\n        }\r\n        return {\r\n            groupRowMap, rowMap\r\n        }\r\n    }\r\n\r\n    const getPieData = () => {\r\n\r\n        const processed = getProcessedRows();\r\n        const groupRowMap = processed.groupRowMap\r\n\r\n        const labels: string[] = []\r\n        const values: number[] = []\r\n\r\n        const data: PieChartData = {\r\n            labels: labels,\r\n            datasets: [\r\n                {\r\n                    label: '# of Records',\r\n                    data: values,\r\n                    backgroundColor: [\r\n                        // 'rgba(255, 99, 132, 0.2)',\r\n                        // 'rgba(54, 162, 235, 0.2)',\r\n                        // 'rgba(255, 206, 86, 0.2)',\r\n                        // 'rgba(75, 192, 192, 0.2)',\r\n                        // 'rgba(153, 102, 255, 0.2)',\r\n                        // 'rgba(255, 159, 64, 0.2)',\r\n                    ],\r\n                    borderColor: [\r\n                        // 'rgba(255, 99, 132, 1)',\r\n                        // 'rgba(54, 162, 235, 1)',\r\n                        // 'rgba(255, 206, 86, 1)',\r\n                        // 'rgba(75, 192, 192, 1)',\r\n                        // 'rgba(153, 102, 255, 1)',\r\n                        // 'rgba(255, 159, 64, 1)',\r\n                    ],\r\n                    borderWidth: 1,\r\n                },\r\n            ],\r\n        };\r\n\r\n        for (let i = 0; i < Object.values(groupRowMap).length; i++) {\r\n            const gR = Object.values(groupRowMap)[i];\r\n            // labels.push(gR.valuesText || '')\r\n            // values.push(Object.keys(gR.rowMap).length)\r\n            data.labels.push(gR.valuesText || '')\r\n            data.datasets[0].data.push(Object.keys(gR.rowMap).length)\r\n\r\n            const color = ColorEntries[i % ColorEntries.length]\r\n            const fg = color.info.fg;\r\n            const bg = color.info.bg;\r\n\r\n            data.datasets[0].backgroundColor?.push(bg)\r\n            data.datasets[0].borderColor?.push(fg)\r\n\r\n\r\n        }\r\n\r\n\r\n        return data;\r\n    }\r\n\r\n    const getPieData2 = () => {\r\n\r\n        const processed = getProcessedRows();\r\n        const groupRowMap = processed.groupRowMap\r\n\r\n        // const labels: string[] = []\r\n        // const values: number[] = []\r\n\r\n        const data: ChartRow[] = [\r\n            // { month: \"January\", desktop: 186, mobile: 80 },\r\n            // { month: \"February\", desktop: 305, mobile: 200 },\r\n            // { month: \"March\", desktop: 237, mobile: 120 },\r\n            // { month: \"April\", desktop: 73, mobile: 190 },\r\n            // { month: \"May\", desktop: 209, mobile: 130 },\r\n            // { month: \"June\", desktop: 214, mobile: 140 },\r\n        ]\r\n        const config: ChartConfig = {\r\n            records: {\r\n                label: \"Records\",\r\n            },\r\n            // desktop: {\r\n            //     label: \"Desktop\",\r\n            //     color: \"hsl(var(--chart-1))\",\r\n            // },\r\n            // mobile: {\r\n            //     label: \"Mobile\",\r\n            //     color: \"hsl(var(--chart-2))\",\r\n            // },\r\n        }\r\n\r\n        // const data: PieChartData = {\r\n        //     labels: labels,\r\n        //     datasets: [\r\n        //         {\r\n        //             label: '# of Records',\r\n        //             data: values,\r\n        //             backgroundColor: [\r\n        //                 // 'rgba(255, 99, 132, 0.2)',\r\n        //                 // 'rgba(54, 162, 235, 0.2)',\r\n        //                 // 'rgba(255, 206, 86, 0.2)',\r\n        //                 // 'rgba(75, 192, 192, 0.2)',\r\n        //                 // 'rgba(153, 102, 255, 0.2)',\r\n        //                 // 'rgba(255, 159, 64, 0.2)',\r\n        //             ],\r\n        //             borderColor: [\r\n        //                 // 'rgba(255, 99, 132, 1)',\r\n        //                 // 'rgba(54, 162, 235, 1)',\r\n        //                 // 'rgba(255, 206, 86, 1)',\r\n        //                 // 'rgba(75, 192, 192, 1)',\r\n        //                 // 'rgba(153, 102, 255, 1)',\r\n        //                 // 'rgba(255, 159, 64, 1)',\r\n        //             ],\r\n        //             borderWidth: 1,\r\n        //         },\r\n        //     ],\r\n        // };\r\n\r\n        const groups = Object.values(groupRowMap).length\r\n        const colours = generateVibrantColors(groups, 80, 35)\r\n\r\n        let totalCount = 0\r\n\r\n        for (let i = 0; i < groups; i++) {\r\n            const gR = Object.values(groupRowMap)[i];\r\n            // labels.push(gR.valuesText || '')\r\n            // values.push(Object.keys(gR.rowMap).length)\r\n            const label = gR.valuesText || ''\r\n\r\n\r\n            config[label] = {\r\n                label,\r\n                color: colours[i]\r\n            }\r\n\r\n            const datum: ChartRow = {\r\n                label,\r\n                count: Object.keys(gR.rowMap).length,\r\n                fill: colours[i]\r\n            }\r\n            data.push(datum)\r\n\r\n            totalCount += Object.keys(gR.rowMap).length\r\n\r\n            // data.labels.push(gR.valuesText || '')\r\n            // data.datasets[0].data.push(Object.keys(gR.rowMap).length)\r\n\r\n\r\n            // const color = ColorEntries[i % ColorEntries.length]\r\n            // const fg = color.info.fg;\r\n            // const bg = color.info.bg;\r\n            //\r\n            // data.datasets[0].backgroundColor?.push(bg)\r\n            // data.datasets[0].borderColor?.push(fg)\r\n\r\n\r\n        }\r\n\r\n\r\n        return {config, data, totalCount};\r\n    }\r\n\r\n    const pie = getPieData()\r\n    const pieData = getPieData2()\r\n\r\n    useEffect(() => {\r\n        if (shouldRefreshDb) {\r\n            refreshDatabase(element.recordsResolve.databaseId).then()\r\n        }\r\n    }, [element.recordsResolve.databaseId]);\r\n\r\n    console.log({pieData})\r\n\r\n    return <>\r\n        <DataViewWrapper title={element.title || 'Pie Chart'}>\r\n            {(isLoading || error) ? <div className='h-36'>\r\n                <PageLoader\r\n                    size={'full'}\r\n                    error={error}\r\n                />\r\n            </div> : <>\r\n                 <div className='hidden'>\r\n                     {/*@ts-ignore*/}\r\n                     <Pie data={pie} plugins={[ChartDataLabels]} className=\"w-full\"/>\r\n                 </div>\r\n\r\n                 <ChartContainer\r\n                     config={pieData.config}\r\n                     className=\"mx-auto aspect-square max-h-[300px]\"\r\n                 >\r\n                     <PieChart>\r\n                         <ChartTooltip\r\n                             cursor={false}\r\n                             content={<ChartTooltipContent hideLabel/>}\r\n                         />\r\n                         <ChartLegend content={<ChartLegendContent/>}/>\r\n\r\n                         <ReChartsPie\r\n                             data={pieData.data}\r\n                             dataKey=\"count\"\r\n                             nameKey=\"label\"\r\n                             innerRadius={60}\r\n                             strokeWidth={5}>\r\n                             <LabelList\r\n                                 dataKey=\"label\"\r\n                                 className=\"fill-background\"\r\n                                 stroke=\"none\"\r\n                                 fontSize={12}\r\n                                 formatter={(value: keyof typeof pieData.config) => pieData.config[value]?.label}\r\n                             />\r\n                             <ReChartsLabel\r\n                                 content={({viewBox}) => {\r\n                                     if (viewBox && \"cx\" in viewBox && \"cy\" in viewBox) {\r\n                                         return (\r\n                                             <text\r\n                                                 x={viewBox.cx}\r\n                                                 y={viewBox.cy}\r\n                                                 textAnchor=\"middle\"\r\n                                                 dominantBaseline=\"middle\"\r\n                                             >\r\n                                                 <tspan\r\n                                                     x={viewBox.cx}\r\n                                                     y={viewBox.cy}\r\n                                                     className=\"fill-foreground text-3xl font-bold\"\r\n                                                 >\r\n                                                     {pieData.totalCount.toString()}\r\n                                                 </tspan>\r\n                                                 <tspan\r\n                                                     x={viewBox.cx}\r\n                                                     y={(viewBox.cy || 0) + 24}\r\n                                                     className=\"fill-muted-foreground\"\r\n                                                 >\r\n                                                     Records\r\n                                                 </tspan>\r\n                                             </text>\r\n                                         )\r\n                                     }\r\n                                 }}\r\n                             />\r\n                         </ReChartsPie>\r\n                     </PieChart>\r\n                 </ChartContainer>\r\n             </>}\r\n\r\n        </DataViewWrapper>\r\n\r\n    </>\r\n}\r\n\r\nexport const PieChartPanel = (props: PanelProps) => {\r\n    const {databaseStore} = useWorkspace()\r\n    const {updateElement} = props\r\n\r\n    const element = props.element as PieChartElement\r\n\r\n    const recordsResolve = element.recordsResolve = element.recordsResolve || {\r\n        databaseId: '',\r\n        filter: {match: Match.All, conditions: []},\r\n        groupByIds: [],\r\n        titleColId: '',\r\n    }\r\n    recordsResolve.filter = recordsResolve.filter || {match: Match.All, conditions: []}\r\n    recordsResolve.groupByIds = recordsResolve.groupByIds || []\r\n    recordsResolve.titleColId = recordsResolve.titleColId || ''\r\n\r\n    let database: Database | undefined\r\n    if (recordsResolve.databaseId) {\r\n        if (databaseStore[recordsResolve.databaseId]) {\r\n            database = databaseStore[recordsResolve.databaseId].database\r\n        }\r\n    }\r\n\r\n    const {page} = usePage()\r\n    useEffect(() => {\r\n        if (!recordsResolve.databaseId && page.databaseId) {\r\n            const {databaseId} = page\r\n            updateElement({recordsResolve: {...recordsResolve, databaseId}})\r\n        }\r\n    }, [])\r\n\r\n    return <>\r\n\r\n        <div className='flex flex-col gap-2'>\r\n            <Label className=\"text-xs text-neutral-500\">Choose database</Label>\r\n            <DatabaseSelect\r\n                disabled={!!page.databaseId}\r\n                onChange={databaseId => updateElement({recordsResolve: {...recordsResolve, databaseId}})}\r\n                selectedId={recordsResolve.databaseId}/>\r\n        </div>\r\n\r\n        {recordsResolve.databaseId && database && <>\r\n            <div className='flex flex-col gap-2'>\r\n                <Label className=\"text-xs text-neutral-500\">Filter</Label>\r\n                <ViewFilter\r\n                    database={database}\r\n                    trigger={\r\n                        <Button variant=\"outline\"\r\n                                className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\">\r\n                            <FilterListIcon className=\"size-3\"/>\r\n                            {recordsResolve.filter.conditions.length > 0 ?\r\n                             `${recordsResolve.filter.conditions.length} filters` :\r\n                             'Filter records'}\r\n                        </Button>\r\n                    }\r\n                    filter={recordsResolve.filter}\r\n                    onChange={filter => {\r\n                        updateElement({recordsResolve: {...recordsResolve, filter}})\r\n                    }}\r\n                />\r\n            </div>\r\n\r\n            <div className='flex flex-col gap-2'>\r\n                <Label className=\"text-xs text-neutral-500\">Group by columns</Label>\r\n                <DatabaseColumnSelect\r\n                    onChange={groupByIds => updateElement({recordsResolve: {...recordsResolve, groupByIds}})}\r\n                    selected={recordsResolve.groupByIds}\r\n                    databaseId={recordsResolve.databaseId}\r\n                    isMultiple\r\n                />\r\n            </div>\r\n\r\n            {/*<div className='flex flex-col gap-2'>*/}\r\n            {/*    <Label className=\"text-xs text-neutral-500\">Chart Title Column</Label>*/}\r\n            {/*    <DatabaseColumnSelect*/}\r\n            {/*        onChange={v => updateElement({recordsResolve: {...recordsResolve, titleColId: v[0]}})}*/}\r\n            {/*        selected={recordsResolve.titleColId ? [recordsResolve.titleColId] : []}*/}\r\n            {/*        databaseId={recordsResolve.databaseId}*/}\r\n            {/*    />*/}\r\n            {/*</div>*/}\r\n\r\n        </>}\r\n\r\n\r\n    </>\r\n}\r\n\r\n", "import React, {useCallback, useEffect, useRef, useState} from 'react';\r\nimport {DataViewWrapper} from \"@/components/workspace/main/views/dashboard/components/common/dataViewWrapper\";\r\nimport {PanelProps} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {ImageElement} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {Label} from \"@/components/ui/label\";\r\nimport {Button} from \"@/components/ui/button\";\r\nimport {ChevronLeftIcon, ChevronRightIcon} from \"@/components/icons/FontAwesomeRegular\";\r\nimport {Switch} from \"@/components/ui/switch\";\r\nimport {useViews} from \"@/providers/views\";\r\nimport {ElementRenderProps} from \"@/components/workspace/main/views/dashboard/components/common/elementRender\";\r\nimport {PageLoader} from \"@/components/custom-ui/loader\";\r\nimport {MultiImagePicker, MultiImagePickerUploadOnCompleteCallback} from \"@/components/custom-ui/multiImagePicker\";\r\n\r\n\r\nexport const ImageRender = (props: ElementRenderProps) => {\r\n    // const element = props.id;\r\n    // let {images, autoRotate} = element\r\n\r\n    const element = props.dashboardDefinition.elementMap[props.id] as ImageElement\r\n\r\n    const [index, setIndex] = useState(0);\r\n\r\n\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    const images = element.images || []\r\n    const autoRotate = element.autoRotate\r\n\r\n    const next = useCallback(() => {\r\n        if (images.length < 1) return;\r\n        setIndex((index + 1) % images.length);\r\n    }, [setIndex, index, images]);\r\n\r\n    const prev = () => {\r\n        const ind = index - 1 < 0 ? images.length - 1 : index - 1;\r\n        setIndex(ind);\r\n    };\r\n    const imgUrl = images[index];\r\n\r\n    useEffect(() => {\r\n        if (!autoRotate) return\r\n\r\n        const interval = setInterval(next, 5000)\r\n\r\n        return () => {\r\n            clearInterval(interval)\r\n        }\r\n    }, [autoRotate, next]);\r\n\r\n    return <>\r\n        <DataViewWrapper title={element.title}>\r\n\r\n            {imgUrl ? <>\r\n                <div className=\"w-full h-full relative\">\r\n                    <div className={`w-full h-full bg-center bg-cover bg-norepeat min-h-[200px]`}\r\n                         style={{\r\n                             backgroundImage: `url(${imgUrl})`,\r\n                         }}>\r\n                        <img src={imgUrl} className=\"w-full block\" alt=''/>\r\n                    </div>\r\n                </div>\r\n                {images && images.length > 0 && <div className=\"flex items-center justify-between pt-4\">\r\n                    <div className='flex-1'></div>\r\n                    <div className=\"inline-flex text-sm h-8 gap-2 items-center text-black\">\r\n                        <div className=\"text-xs font-medium\">\r\n                            Image {index + 1} of {images.length}\r\n                        </div>\r\n                        <ul className=\"inline-flex\">\r\n                            <li>\r\n                                <Button variant={\"outline\"}\r\n                                        className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\"\r\n                                        onClick={prev}>\r\n                                    <ChevronLeftIcon className='size-3'/>\r\n                                </Button>\r\n                            </li>\r\n                            <li>\r\n                                <Button variant={\"outline\"}\r\n                                        className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\"\r\n                                        onClick={next}>\r\n                                    <ChevronRightIcon className='size-3'/>\r\n                                </Button>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                </div>}\r\n\r\n            </> : <>\r\n\r\n                 <div className='h-28'>\r\n                     <PageLoader error='No images to show'/>\r\n                 </div>\r\n\r\n\r\n             </>}\r\n\r\n\r\n        </DataViewWrapper>\r\n\r\n    </>\r\n}\r\n\r\n\r\nexport const ImagePanel = (props: PanelProps) => {\r\n\r\n    const {uploadWorkspaceFile, uploadQueue} = useViews()\r\n    const {updateElement} = props\r\n\r\n    const element = props.element as ImageElement\r\n\r\n    element.images = element.images || [];\r\n\r\n\r\n    const uploads = uploadQueue['dashboard'] &&\r\n                    uploadQueue['dashboard'][element.id] &&\r\n                    uploadQueue['dashboard'][element.id]['panel'] ?\r\n                    Object.values(uploadQueue['dashboard'][element.id]['panel']) : []\r\n\r\n\r\n    const fileInputRef = useRef<HTMLInputElement>(null)\r\n\r\n    const imagesRef = useRef(element.images)\r\n    imagesRef.current = element.images\r\n\r\n    // const onSelectFile = (e: any) => {\r\n    //     const files = e.target.files\r\n    //     if (!files || files.length === 0) {\r\n    //         return;\r\n    //     }\r\n    //     for (const currentFile of files) {\r\n    //         uploadWorkspaceFile('dashboard', element.id, 'panel', currentFile, res => {\r\n    //             const upload = res.data.data.upload\r\n    //             const images = [...imagesRef.current, upload.finalUrl]\r\n    //             updateElement({images})\r\n    //         })\r\n    //     }\r\n    // }\r\n\r\n    const uploadImage = (file: File, cb: MultiImagePickerUploadOnCompleteCallback) => {\r\n        uploadWorkspaceFile('dashboard', element.id, 'panel', file, cb)\r\n    }\r\n\r\n    // const handleClick = () => {\r\n    //     if (fileInputRef && fileInputRef.current) {\r\n    //         fileInputRef.current.click()\r\n    //     }\r\n    // }\r\n    //\r\n    // const removeImage = (index: number) => {\r\n    //     const images = imagesRef.current.filter((i, idx) => idx !== index)\r\n    //     updateElement({images})\r\n    // }\r\n\r\n    return <>\r\n\r\n        <MultiImagePicker\r\n            images={imagesRef.current}\r\n            onChange={images => updateElement({images})}\r\n            allowRemovingImages\r\n            uploadImage={uploadImage}\r\n            uploads={uploads}\r\n        />\r\n\r\n        <div className='flex flex-col gap-2'>\r\n            <Label className=\"flex select-none font-medium text-xs h-auto gap-2 w-full\">\r\n                <div className=\"flex-1 overflow-hidden truncate text-left\">\r\n                    Auto rotate\r\n                </div>\r\n                <Switch\r\n                    className=\"h-4 w-8\"\r\n                    thumbClassName=\"!size-3\"\r\n                    checked={element.autoRotate}\r\n                    onCheckedChange={autoRotate => {\r\n                        updateElement({autoRotate})\r\n                    }}/>\r\n            </Label>\r\n        </div>\r\n\r\n\r\n    </>\r\n}", "import React from 'react';\r\nimport {DataViewWrapper} from \"@/components/workspace/main/views/dashboard/components/common/dataViewWrapper\";\r\nimport {ElementRenderProps} from \"@/components/workspace/main/views/dashboard/components/common/elementRender\";\r\nimport {TextElement} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {Label} from \"@/components/ui/label\";\r\nimport {PanelProps} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {Textarea} from \"@/components/ui/textarea\";\r\nimport useAutoHeightTextarea from \"@/components/custom-ui/autoHeightTextArea\";\r\n\r\n\r\nexport const TextRender = (props: ElementRenderProps) => {\r\n    const element = props.dashboardDefinition.elementMap[props.id] as TextElement\r\n\r\n    return <>\r\n        <DataViewWrapper title={element.title}>\r\n            <div className='text-xs font-medium'> {element.content}</div>\r\n        </DataViewWrapper>\r\n\r\n    </>\r\n}\r\n\r\nexport const TextPanel = (props: PanelProps) => {\r\n    const {updateElement} = props\r\n\r\n    const textareaRef = useAutoHeightTextarea();\r\n\r\n    const element = props.element as TextElement\r\n\r\n    return <>\r\n        <div className='flex flex-col gap-2'>\r\n            <Label htmlFor=\"title\" className=\"text-xs text-neutral-500\">Content</Label>\r\n            <Textarea id=\"title\" className=\"rounded-none text-xs\"\r\n                      ref={textareaRef}\r\n                      defaultValue={element.content || ''}\r\n                      rows={5}\r\n                      onBlur={e => updateElement({content: e.target.value.trim() || element.content || ''})}\r\n            />\r\n        </div>\r\n\r\n    </>\r\n}", "import React, {useEffect, useRef} from 'react';\r\nimport {DataViewWrapper} from \"@/components/workspace/main/views/dashboard/components/common/dataViewWrapper\";\r\nimport {ElementRenderProps} from \"@/components/workspace/main/views/dashboard/components/common/elementRender\";\r\nimport {EmbedElement} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {Label} from \"@/components/ui/label\";\r\nimport {PanelProps} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {Textarea} from \"@/components/ui/textarea\";\r\nimport useAutoHeightTextarea from \"@/components/custom-ui/autoHeightTextArea\";\r\nimport DOMPurify from \"dompurify\";\r\n\r\n\r\nconst config = {\r\n    ALLOWED_TAGS: [\"p\", \"iframe\", \"div\"],\r\n    ALLOWED_ATTR: [\r\n        \"src\",\r\n        \"width\",\r\n        \"height\",\r\n        \"title\",\r\n        \"frameBorder\",\r\n        \"allow\",\r\n        \"allowFullScreen\",\r\n    ],\r\n};\r\n\r\nexport const EmbedRender = (props: ElementRenderProps) => {\r\n    const element = props.dashboardDefinition.elementMap[props.id] as EmbedElement\r\n\r\n    const embedCode = element.embedCode\r\n\r\n    return <>\r\n        <DataViewWrapper title={element.title}>\r\n            <EmbedViewer embedCode={embedCode}/>\r\n        </DataViewWrapper>\r\n\r\n    </>\r\n}\r\n\r\n\r\nconst EmbedViewer = React.memo(({embedCode}: { embedCode: string }) => {\r\n    const containerRef = useRef<HTMLDivElement>(null);\r\n\r\n    const cleanHtml = DOMPurify.sanitize(\r\n        embedCode,\r\n        config\r\n    );\r\n    useEffect(() => {\r\n        if (containerRef.current) {\r\n            // Create shadow root\r\n            const shadowRoot = containerRef.current.attachShadow({mode: 'open'});\r\n\r\n            // Create a container inside the shadow root to hold the sanitized embed code\r\n            const shadowContainer = document.createElement('div');\r\n            shadowContainer.innerHTML = cleanHtml;\r\n\r\n            // Apply necessary styles or classes to the shadow container\r\n            shadowContainer.className = \"h-min-[350px] rounded-lg overflow-hidden\";\r\n\r\n            // Append the shadow container to the shadow root\r\n            shadowRoot.appendChild(shadowContainer);\r\n        }\r\n    }, [cleanHtml]);\r\n\r\n    return <div ref={containerRef}/>;\r\n});\r\n\r\n// const EmbedViewer = React.memo(({embedCode}: { embedCode: string }) => {\r\n//\r\n//     const cleanHtml = DOMPurify.sanitize(\r\n//         embedCode,\r\n//         config\r\n//     );\r\n//\r\n//     return (\r\n//         <>\r\n//             <div\r\n//                 dangerouslySetInnerHTML={{__html: cleanHtml}}\r\n//                 className=\"h-min-[350px] rounded-lg overflow-hidden\"\r\n//             />\r\n//         </>\r\n//     );\r\n// });\r\nEmbedViewer.displayName = \"EmbedViewer\"; // Set the displayName\r\n\r\nexport const EmbedPanel = (props: PanelProps) => {\r\n    const {updateElement} = props\r\n\r\n    const textareaRef = useAutoHeightTextarea();\r\n\r\n    const element = props.element as EmbedElement\r\n\r\n    return <>\r\n        <div className='flex flex-col gap-2'>\r\n            <Label htmlFor=\"title\" className=\"text-xs text-neutral-500\">Content</Label>\r\n            <Textarea id=\"title\" className=\"rounded-none text-xs\"\r\n                      ref={textareaRef}\r\n                      defaultValue={element.embedCode || ''}\r\n                      rows={5}\r\n                      onBlur={e => {\r\n\r\n\r\n                          // Sanitize the HTML content\r\n                          const cleanHtml = DOMPurify.sanitize(\r\n                              e.target.value.trim(),\r\n                              config\r\n                          );\r\n                          if (element.embedCode !== cleanHtml) {\r\n                              updateElement({embedCode: cleanHtml});\r\n                          }\r\n                      }}\r\n            />\r\n        </div>\r\n\r\n    </>\r\n}", "import {DashboardElementType} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {DashboardElementTypeDefinition} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {AddressCardIcon, ChartLineUpIcon, ChartPieIcon, CodeIcon, ImagesIcon, LanguageIcon} from \"@/components/icons/FontAwesomeRegular\";\r\nimport {InfoboxPanel} from \"@/components/workspace/main/views/dashboard/components/elements/infoBox\";\r\nimport {LineChartPanel} from \"@/components/workspace/main/views/dashboard/components/elements/lineChart\";\r\nimport {PieChartPanel} from \"@/components/workspace/main/views/dashboard/components/elements/pieChart\";\r\nimport {ImagePanel} from \"@/components/workspace/main/views/dashboard/components/elements/image\";\r\nimport {TextPanel} from \"@/components/workspace/main/views/dashboard/components/elements/text\";\r\nimport {EmbedPanel} from \"@/components/workspace/main/views/dashboard/components/elements/embed\";\r\n\r\n\r\nexport const DashboardElementDefinitions: Partial<Record<keyof typeof DashboardElementType, DashboardElementTypeDefinition>> = {\r\n    Infobox: {\r\n        name: \"Infobox\",\r\n        icon: <AddressCardIcon className=\"size-3.5\"/>,\r\n        type: DashboardElementType.Infobox,\r\n        panel: props => <InfoboxPanel {...props} />\r\n    },\r\n    LineChart: {\r\n        name: \"Line Chart\",\r\n        icon: <ChartLineUpIcon className=\"size-3.5\"/>,\r\n        type: DashboardElementType.LineChart,\r\n        panel: props => <LineChartPanel {...props} />\r\n    },\r\n    // BarChart: {\r\n    //     name: \"Bar Chart\",\r\n    //     icon: <ChartBarIcon className=\"size-4\"/>,\r\n    //     type: DashboardElementType.BarChart\r\n    // },\r\n    PieChart: {\r\n        name: \"Pie Chart\",\r\n        icon: <ChartPieIcon className=\"size-3.5\"/>,\r\n        type: DashboardElementType.PieChart,\r\n        panel: props => <PieChartPanel {...props} />\r\n    },\r\n    // FunnelChart: {\r\n    //     name: \"Funnel Chart\",\r\n    //     icon: <FunnelIcon className=\"size-4\"/>,\r\n    //     type: DashboardElementType.FunnelChart\r\n    // },\r\n    Image: {\r\n        name: \"Images\",\r\n        icon: <ImagesIcon className=\"size-3.5\"/>,\r\n        type: DashboardElementType.Image,\r\n        panel: props => <ImagePanel {...props} />\r\n    },\r\n    Text: {\r\n        name: \"Text\",\r\n        icon: <LanguageIcon className=\"size-3.5\"/>,\r\n        type: DashboardElementType.Text,\r\n        panel: props => <TextPanel {...props} />\r\n    },\r\n    Embed: {\r\n        name: \"Embed\",\r\n        icon: <CodeIcon className=\"size-3.5\"/>,\r\n        type: DashboardElementType.Embed,\r\n        panel: props => <EmbedPanel {...props} />\r\n    },\r\n}", "import {DashboardContentProps} from \"@/components/workspace/main/views/dashboard/dashboard\";\r\nimport {Label} from \"@/components/ui/label\";\r\nimport {Input} from \"@/components/ui/input\";\r\nimport React, {ReactNode} from \"react\";\r\nimport {ScrollArea} from \"@/components/ui/scroll-area\";\r\nimport {ArrowPointerIcon, TrashIcon} from \"@/components/icons/FontAwesomeRegular\";\r\nimport {DashboardElementDefinitions} from \"@/components/workspace/main/views/dashboard/components/common/element\";\r\nimport {DashboardElement, DashboardElementType, DashboardTransaction} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {DashboardElementTypeDefinition} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {Button} from \"@/components/ui/button\";\r\nimport {removeAllArrayItem} from \"opendb-app-db-utils/lib\";\r\nimport {useAlert} from \"@/providers/alert\";\r\n\r\nexport const DashboardPanel = (props: DashboardContentProps) => {\r\n    const {confirm} = useAlert()\r\n    if (!props.isEditing) return null\r\n\r\n\r\n    const element = props.dashboardDefinition.elementMap[props.activeElementId]\r\n\r\n    const updateElement = (update: Partial<DashboardElement>) => {\r\n        const updated = {...element, ...update}\r\n\r\n        const transaction: DashboardTransaction = {\r\n            action: 'updateElement',\r\n            element: updated\r\n        }\r\n        props.pushTransactions([transaction])\r\n    }\r\n\r\n    const deleteElement = () => {\r\n        confirm(\"Delete element?\", \"Are you sure you want to delete this element?\", () => {\r\n            const transactions: DashboardTransaction[] = []\r\n            \r\n            // Find the parent row by searching which row contains this element\r\n            let parentRow: any = null\r\n            let parentRowId: string = ''\r\n            \r\n            for (const [rowId, row] of Object.entries(props.dashboardDefinition.rowsMap)) {\r\n                if (row.children && row.children.includes(element.id)) {\r\n                    parentRow = row\r\n                    parentRowId = rowId\r\n                    break\r\n                }\r\n            }\r\n            \r\n            if (!parentRow) {\r\n                console.error('Could not find parent row for element:', element.id)\r\n                return\r\n            }\r\n\r\n            transactions.push({\r\n                action: 'deleteElement',\r\n                element: element,\r\n                parentId: parentRowId // Pass the correct parentId in the transaction\r\n            })\r\n\r\n            // Check if this was the last element in the row\r\n            if (removeAllArrayItem([...parentRow.children], element.id).length === 0) {\r\n                transactions.push({\r\n                    action: 'deleteRow',\r\n                    row: parentRow\r\n                })\r\n            }\r\n            \r\n            props.pushTransactions(transactions)\r\n            props.setActiveElementId(\"\")\r\n        });\r\n    }\r\n\r\n    let typeDefinition: DashboardElementTypeDefinition | undefined = undefined;\r\n\r\n    switch (element?.type) {\r\n        case DashboardElementType.Embed:\r\n            typeDefinition = DashboardElementDefinitions.Embed\r\n            break;\r\n        case DashboardElementType.Infobox:\r\n            typeDefinition = DashboardElementDefinitions.Infobox\r\n            break;\r\n        case DashboardElementType.LineChart:\r\n            typeDefinition = DashboardElementDefinitions.LineChart\r\n            break;\r\n        case DashboardElementType.PieChart:\r\n            typeDefinition = DashboardElementDefinitions.PieChart\r\n            break;\r\n        case DashboardElementType.Image:\r\n            typeDefinition = DashboardElementDefinitions.Image\r\n            break;\r\n        case DashboardElementType.Text:\r\n            typeDefinition = DashboardElementDefinitions.Text\r\n            break;\r\n    }\r\n    let content: ReactNode = <></>\r\n    // let panelTitle = <>{typeDefinition?.icon} {typeDefinition?.name}</>\r\n    let panelTitle = <> {typeDefinition?.name}</>\r\n    if (typeDefinition) {\r\n        content = typeDefinition.panel({element, updateElement})\r\n    }\r\n\r\n\r\n    return <div className=\"w-80 min-w-80 border-l\">\r\n        {props.activeElementId && element && typeDefinition ? <>\r\n            <div className=\"flex flex-col h-full w-full\" key={props.activeElementId}>\r\n                <header className=\"flex gap-2 border-b\">\r\n                    <div className=\"font-semibold p-3 flex gap-2 items-center text-xs\">\r\n                        {panelTitle}\r\n                    </div>\r\n                </header>\r\n                <div className=\"flex-1 overflow-hidden\">\r\n                    <ScrollArea className=\"w-full h-full\">\r\n                        <div className=\"h-full w-full overflow-y-auto flex flex-col gap-3 p-3 pb-12\">\r\n                            <div className='flex flex-col gap-2'>\r\n                                <Label htmlFor=\"title\" className=\"text-xs text-neutral-500\">Title</Label>\r\n                                <Input id=\"title\" className=\"rounded-none text-xs\"\r\n                                       defaultValue={element.title || ''}\r\n                                       onBlur={e => updateElement({title: e.target.value.trim() || element.title})}\r\n                                />\r\n                            </div>\r\n                            {content}\r\n\r\n                            <div className='mt-4'>\r\n                                <Button\r\n                                    onClick={deleteElement}\r\n                                    variant=\"outline\"\r\n                                    className=\"text-xs rounded-none p-1.5 !px-3 h-auto w-auto gap-2 justify-start font-medium\">\r\n                                    <TrashIcon className=\"size-3\"/>\r\n                                    Delete\r\n                                </Button>\r\n                            </div>\r\n\r\n                        </div>\r\n                    </ScrollArea>\r\n                </div>\r\n            </div>\r\n        </> : <>\r\n             <div className='size-full relative'>\r\n                 <div className='absolute -translate-y-1/2 top-1/2 w-full p-6 text-center'>\r\n                     <div className='my-2 flex flex-col gap-4 items-center justify-center'>\r\n                         <div className=''>\r\n                             <ArrowPointerIcon className='size-8'/>\r\n                         </div>\r\n                         <span className='text-xs font-medium'>Add an element or click an existing element to customize</span>\r\n                     </div>\r\n\r\n                 </div>\r\n             </div>\r\n         </>}\r\n\r\n    </div>\r\n}\r\n\r\n", "import {ReactNode} from \"react\";\r\nimport {DashboardElement, DashboardElementType} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {InfoboxPanel} from \"@/components/workspace/main/views/dashboard/components/elements/infoBox\";\r\n\r\nexport const DashboardElementTypeKey = \"dashboardElement\"\r\n\r\nexport interface DashboardElementTypeDefinition {\r\n    name: string\r\n    icon: ReactNode\r\n    type: DashboardElementType\r\n    panel: (props: PanelProps) => ReactNode\r\n}\r\n\r\nexport interface PanelProps {\r\n    element: DashboardElement\r\n    updateElement: (update: Partial<DashboardElement>) => void\r\n}\r\n", "import {DataViewWrapper} from \"@/components/workspace/main/views/dashboard/components/common/dataViewWrapper\";\r\nimport React, {useEffect, useRef} from \"react\";\r\nimport ChartDataLabels from 'chartjs-plugin-datalabels';\r\n\r\nimport {FunnelChart as FC} from 'chartjs-chart-funnel';\r\nimport {generateUUID} from \"opendb-app-db-utils/lib/methods/string\";\r\n\r\nexport const FunnelChart = () => {\r\n\r\n    const idRef = useRef(generateUUID())\r\n\r\n    useEffect(() => {\r\n        const canvas = document.getElementById(`fc-${idRef.current}`)\r\n        if (!canvas) return\r\n        const ctx = (canvas as HTMLCanvasElement).getContext(\"2d\");\r\n        if (!ctx) return\r\n        // ctx.d\r\n        try {\r\n            const chart = new FC(ctx, {\r\n                // type: 'funnel',\r\n                data: {\r\n                    labels: ['Step 1', 'Step 2', 'Step 3'],\r\n                    datasets: [{\r\n                        data: [1, 0.75, 0.5],\r\n                        shrinkAnchor: 'top',\r\n                    }]\r\n                },\r\n                // title: \"Funnel Chart\",\r\n                // options: {\r\n                //     indexAxis: 'y',\r\n                //     // title: {\r\n                //     //     display: true,\r\n                //     //     text: 'Chart.js Funnel Chart'\r\n                //     // }\r\n                // },\r\n                plugins: [ChartDataLabels],\r\n            });\r\n        } catch (ex) {\r\n            // console.log(ex)\r\n        }\r\n\r\n    })\r\n\r\n    return (\r\n        <DataViewWrapper title=\"Funnel Chart\">\r\n            <canvas id={`fc-${idRef.current}`} className=\"w-full\"></canvas>\r\n        </DataViewWrapper>\r\n    );\r\n}", "import React from 'react';\r\nimport {BarElement, CategoryScale, Chart as ChartJ<PERSON>, Legend, LinearScale, Title, Tooltip,} from 'chart.js';\r\nimport {Bar} from 'react-chartjs-2';\r\nimport {faker} from '@faker-js/faker';\r\nimport {DataViewWrapper} from \"@/components/workspace/main/views/dashboard/components/common/dataViewWrapper\";\r\nimport ChartDataLabels from \"chartjs-plugin-datalabels\";\r\n\r\nChartJS.register(\r\n    CategoryScale,\r\n    LinearScale,\r\n    BarElement,\r\n    Title,\r\n    Tooltip,\r\n    Legend\r\n);\r\n\r\n// plugins: [ChartDataLabels],\r\n\r\nexport const options = {\r\n    plugins: {\r\n        title: {\r\n            display: true,\r\n            text: 'Chart.js Bar Chart - Stacked',\r\n        },\r\n    },\r\n    responsive: true,\r\n    scales: {\r\n        x: {\r\n            stacked: true,\r\n        },\r\n        y: {\r\n            stacked: true,\r\n        },\r\n    },\r\n};\r\n\r\nconst labels = ['January', 'February', 'March', 'April', 'May', 'June', 'July'];\r\n\r\nexport const data = {\r\n    labels,\r\n    datasets: [\r\n        {\r\n            label: 'Dataset 1',\r\n            data: labels.map(() => faker.datatype.number({min: 0, max: 1000})),\r\n            backgroundColor: 'rgb(255, 99, 132)',\r\n        },\r\n        {\r\n            label: 'Dataset 2',\r\n            data: labels.map(() => faker.datatype.number({min: 0, max: 1000})),\r\n            // data: labels.map(() => faker.datatype.number({min: -1000, max: 1000})),\r\n            backgroundColor: 'rgb(75, 192, 192)',\r\n        },\r\n        {\r\n            label: 'Dataset 3',\r\n            data: labels.map(() => faker.datatype.number({min: 0, max: 1000})),\r\n            backgroundColor: 'rgb(53, 162, 235)',\r\n        },\r\n    ],\r\n};\r\n\r\n\r\nexport const BarChart = () => {\r\n    return <>\r\n        <DataViewWrapper title=\"Bar Chart\">\r\n            <Bar data={data} options={options} className=\"w-full\" plugins={[ChartDataLabels]}/>\r\n        </DataViewWrapper>\r\n    </>\r\n}", "import {But<PERSON>} from \"@/components/ui/button\";\r\nimport {PlusCircleIcon} from \"@heroicons/react/24/outline\";\r\nimport React, {ReactNode} from \"react\";\r\nimport {DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuTrigger} from \"@/components/ui/dropdown-menu\";\r\nimport {DashboardElementDefinitions} from \"@/components/workspace/main/views/dashboard/components/common/element\";\r\nimport {DashboardElementTypeDefinition,} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {DashboardContentProps} from \"@/components/workspace/main/views/dashboard/dashboard\";\r\nimport {generateUUID} from \"opendb-app-db-utils/lib/methods/string\";\r\nimport {DashboardBaseElement, DashboardElementType, DashboardRow, DashboardTransaction, Direction} from \"opendb-app-db-utils/lib/typings/view\";\r\n\r\nexport const AddElementRender = (props: DashboardContentProps) => {\r\n\r\n    return <>\r\n        <AddElementDropdown\r\n            trigger={<Button className=\"gap-2 absolute bottom-4 left-4 w-auto z-20 rounded-none shadow-2xl\">\r\n                <PlusCircleIcon className=\"size-4\"/>\r\n                Add Element\r\n            </Button>}\r\n            align=\"start\"\r\n            pushTransactions={props.pushTransactions}\r\n            setActiveElementId={props.setActiveElementId}\r\n\r\n        />\r\n    </>\r\n}\r\n\r\nexport interface AddElementDropdownProps {\r\n    trigger: ReactNode\r\n    // onClick: (type: DashboardElementType) => void\r\n    pushTransactions: (transactions: DashboardTransaction[]) => void\r\n    setActiveElementId: (id: string) => void\r\n    refElementId?: string\r\n    refParentId?: string\r\n    refCreateDirection?: Direction\r\n    align: \"start\" | \"center\" | \"end\"\r\n}\r\n\r\nexport const AddElementDropdown = (props: AddElementDropdownProps) => {\r\n\r\n    const addElement = (type: DashboardElementType, name: string) => {\r\n        const transactions: DashboardTransaction[] = []\r\n        const element: DashboardBaseElement = {\r\n            title: name,\r\n            id: generateUUID(),\r\n            type,\r\n        }\r\n        if (props.refElementId) {\r\n            const dir = props.refCreateDirection || 'r'\r\n            const parentId = props.refParentId || ''\r\n            const refElementId = props.refElementId\r\n\r\n            if (dir === 't' || dir === \"b\") {\r\n                const row: DashboardRow = {\r\n                    id: generateUUID(),\r\n                    children: [],\r\n                }\r\n                const transaction: DashboardTransaction = {\r\n                    action: \"addRow\",\r\n                    row,\r\n                }\r\n                if (dir === 't') transaction.beforeId = parentId\r\n                else transaction.afterId = parentId\r\n\r\n                transactions.push(transaction)\r\n                transactions.push({\r\n                    action: \"addElement\",\r\n                    element,\r\n                    parentId: row.id\r\n                })\r\n            } else {\r\n                const transaction: DashboardTransaction = {\r\n                    action: \"addElement\",\r\n                    element,\r\n                    parentId\r\n                }\r\n                if (dir === 'l') transaction.beforeId = refElementId\r\n                else transaction.afterId = refElementId\r\n\r\n                transactions.push(transaction)\r\n            }\r\n        } else {\r\n            const row: DashboardRow = {\r\n                id: generateUUID(),\r\n                children: [],\r\n            }\r\n            element.parentId = row.id\r\n            transactions.push({\r\n                action: \"addRow\",\r\n                row\r\n            })\r\n            transactions.push({\r\n                action: \"addElement\",\r\n                element,\r\n                parentId: row.id\r\n            })\r\n        }\r\n        props.pushTransactions(transactions)\r\n\r\n        setTimeout(() =>  props.setActiveElementId(element.id), 200)\r\n\r\n    }\r\n\r\n\r\n    return <>\r\n        <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n                {props.trigger}\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent className=\"w-56  rounded-none text-neutral-800 font-semibold\" align={props.align}>\r\n                <DropdownMenuGroup className=\"p-0.5 flex flex-col gap-0.5\">\r\n                    {Object.keys(DashboardElementDefinitions).map(k => {\r\n                        const key = k as keyof typeof DashboardElementDefinitions\r\n                        const ele = DashboardElementDefinitions[key] as DashboardElementTypeDefinition\r\n                        return <DropdownMenuItem className=\"p-0\" key={k}>\r\n                            <Button variant=\"ghost\"\r\n                                    onClick={e => {\r\n                                        addElement(ele.type, ele.name)\r\n                                    }}\r\n                                    className=\"h-auto p-2 gap-2 text-xs rounded-none w-full justify-start border-none\">\r\n                                {ele.icon} {ele.name}\r\n                            </Button>\r\n                        </DropdownMenuItem>\r\n                    })}\r\n                </DropdownMenuGroup>\r\n            </DropdownMenuContent>\r\n        </DropdownMenu>\r\n    </>\r\n}", "import {DashboardContentProps} from \"@/components/workspace/main/views/dashboard/dashboard\";\r\nimport {DashboardElementTypeKey} from \"@/components/workspace/main/views/dashboard/typings\";\r\nimport {InfoboxRender} from \"@/components/workspace/main/views/dashboard/components/elements/infoBox\";\r\nimport {Funnel<PERSON>hart} from \"@/components/workspace/main/views/dashboard/components/elements/funnelChart\";\r\nimport {LineChartRender} from \"@/components/workspace/main/views/dashboard/components/elements/lineChart\";\r\nimport {BarChart} from \"@/components/workspace/main/views/dashboard/components/elements/barChart\";\r\nimport {PieChartRender} from \"@/components/workspace/main/views/dashboard/components/elements/pieChart\";\r\nimport {TextRender} from \"@/components/workspace/main/views/dashboard/components/elements/text\";\r\nimport {DraggableAttributes, useDraggable, useDroppable} from \"@dnd-kit/core\";\r\nimport {CSS} from \"@dnd-kit/utilities\";\r\nimport React from \"react\";\r\nimport {DocumentDuplicateIcon, PlusCircleIcon, TrashIcon} from \"@heroicons/react/24/outline\";\r\nimport {But<PERSON>} from \"@/components/ui/button\";\r\nimport {AddElementDropdown} from \"@/components/workspace/main/views/dashboard/components/common/addElementRender\";\r\nimport {generateUUID} from \"opendb-app-db-utils/lib/methods/string\";\r\n\r\nimport {DragHandleIcon} from \"@/components/custom-ui/dragHandle\";\r\nimport {SyntheticListenerMap} from \"@dnd-kit/core/dist/hooks/utilities/useSyntheticListeners\";\r\nimport {DashboardElementType, DashboardTransaction, Direction} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {removeAllArrayItem} from \"opendb-app-db-utils/lib\";\r\nimport {useAlert} from \"@/providers/alert\";\r\nimport {ImageRender} from \"@/components/workspace/main/views/dashboard/components/elements/image\";\r\nimport {EmbedRender} from \"@/components/workspace/main/views/dashboard/components/elements/embed\";\r\n\r\n\r\nexport interface ElementDraggableData {\r\n    type: string;\r\n    subType: string;\r\n    id: string;\r\n    parentId?: string;\r\n}\r\n\r\nexport interface ElementRenderProps extends DashboardContentProps {\r\n    id: string\r\n    parentId: string\r\n    isDragOverlay?: boolean\r\n}\r\n\r\nexport const ElementRender = (props: ElementRenderProps) => {\r\n    const element = props.dashboardDefinition.elementMap[props.id]\r\n\r\n    if (!element) return null\r\n\r\n    return <RenderWrap {...props}/>\r\n}\r\n\r\nconst RenderWrap = (props: ElementRenderProps) => {\r\n    const element = props.dashboardDefinition.elementMap[props.id]\r\n\r\n    const dragData: ElementDraggableData = {\r\n        type: DashboardElementTypeKey,\r\n        subType: element?.type,\r\n        id: props.id,\r\n        parentId: props.parentId,\r\n    };\r\n\r\n    const disableDnD = false\r\n\r\n    const {\r\n        attributes,\r\n        listeners,\r\n        setNodeRef: setDragRef,\r\n        transform,\r\n        isDragging,\r\n    } = useDraggable({\r\n        id: props.id,\r\n        disabled: disableDnD,\r\n        data: dragData,\r\n    });\r\n\r\n    const setNodeRef = (element: HTMLElement | null) => {\r\n        setDragRef(element);\r\n        setDropRef(element);\r\n    };\r\n    const {\r\n        // isOver,\r\n        setNodeRef: setDropRef,\r\n        // rect,\r\n        // node,\r\n        // over,\r\n    } = useDroppable({\r\n        id: props.id,\r\n        disabled: disableDnD,\r\n        data: {\r\n            accepts: [DashboardElementTypeKey],\r\n            ...dragData,\r\n        },\r\n    });\r\n\r\n    const style = {\r\n        transform: CSS.Translate.toString(transform),\r\n    };\r\n\r\n    const isActive = props.activeElementId === element.id\r\n\r\n    return <div\r\n        className={`flex-1 flex relative ${props.isDragOverlay && \"opacity-60 shadow-2xl\"} \r\n        ${isDragging && \"invisible opacity-0\"}\r\n         border hover:border-black group ${isActive && \"border-black\"} dashER`}\r\n        data-ele-id={props.id}\r\n        ref={setNodeRef}\r\n        style={style}\r\n        onClick={e => {\r\n            e.stopPropagation()\r\n            e.preventDefault()\r\n            if (!isActive) {\r\n                props.setActiveElementId(element.id)\r\n            }\r\n        }}>\r\n        {element.type === DashboardElementType.Infobox ? <InfoboxRender {...props}/> :\r\n         element.type === DashboardElementType.FunnelChart ? <FunnelChart/> :\r\n         element.type === DashboardElementType.LineChart ? <LineChartRender {...props}/> :\r\n         element.type === DashboardElementType.BarChart ? <BarChart/> :\r\n         element.type === DashboardElementType.PieChart ? <PieChartRender  {...props}/> :\r\n         element.type === DashboardElementType.Text ? <TextRender {...props}/> :\r\n         element.type === DashboardElementType.Image ? <ImageRender {...props}/> :\r\n         element.type === DashboardElementType.Embed ? <EmbedRender {...props}/> :\r\n         null\r\n        }\r\n        {isActive && <ElementRenderKnob {...{...props, listeners, attributes}} />}\r\n    </div>\r\n}\r\n\r\ntype Dir = Direction;\r\n\r\ninterface ElementRenderKnobProp extends ElementRenderProps {\r\n    listeners?: SyntheticListenerMap;\r\n    attributes?: DraggableAttributes;\r\n}\r\n\r\nconst ElementRenderKnob = (props: ElementRenderKnobProp) => {\r\n    const {confirm} = useAlert()\r\n\r\n    const duplicate = () => {\r\n        const data = {...props.dashboardDefinition}\r\n        const currRow = data.rowsMap[props.parentId]\r\n        if (!currRow) {\r\n            console.log(\"Row not found\")\r\n            return\r\n        }\r\n        const ele = {...props.dashboardDefinition.elementMap[props.id], id: generateUUID()}\r\n        const transactions: DashboardTransaction[] = []\r\n        transactions.push({\r\n            action: 'addElement',\r\n            element: ele,\r\n            parentId: props.parentId,\r\n            afterId: props.id\r\n        })\r\n        props.pushTransactions(transactions)\r\n        props.setActiveElementId(ele.id)\r\n    };\r\n\r\n    const doDelete = () => {\r\n        const ele = props.dashboardDefinition.elementMap[props.id]\r\n        confirm(\"Delete element?\", \"Are you sure you want to delete this element?\", () => {\r\n            const transactions: DashboardTransaction[] = []\r\n            transactions.push({\r\n                action: 'deleteElement',\r\n                element: ele,\r\n                parentId: props.parentId\r\n            })\r\n            props.pushTransactions(transactions)\r\n            if (removeAllArrayItem(props.dashboardDefinition.rowsMap[props.parentId].children, props.id).length === 0) {\r\n                transactions.push({\r\n                    action: 'deleteRow',\r\n                    row: props.dashboardDefinition.rowsMap[props.parentId]\r\n                })\r\n            }\r\n            props.pushTransactions(transactions)\r\n            props.setActiveElementId(\"\")\r\n        });\r\n\r\n    };\r\n\r\n\r\n    return (\r\n        <>\r\n            <div\r\n                className={`pge-e-controls absolute left-[-45px] w-[40px] z-20 top-[50%] -translate-y-1/2`}>\r\n                <div\r\n                    className=\"inline-flex rounded-md shadow-xl flex-col text-center\"\r\n                    role=\"group\">\r\n                    <Button\r\n                        variant=\"ghost\"\r\n                        className=\"px-1.5 py-3 h-auto text-sm bg-white border rounded-none hover:bg-gray-100\"\r\n                        {...props.listeners}\r\n                        {...props.attributes}\r\n                    >\r\n                        <DragHandleIcon className=\"mx-auto size-3.5\"/>\r\n                    </Button>\r\n                    <Button\r\n                        variant=\"ghost\"\r\n                        className=\"px-1.5 py-3 h-auto text-sm bg-white border border-t-0 rounded-none hover:bg-gray-100\"\r\n                        onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            e.preventDefault();\r\n                            duplicate();\r\n                        }}\r\n                    >\r\n                        <DocumentDuplicateIcon className=\"mx-auto size-3.5\"/>\r\n                    </Button>\r\n                    <Button\r\n                        variant=\"ghost\"\r\n                        type=\"button\"\r\n                        className=\"px-1.5 py-3 h-auto text-sm bg-white border border-t-0 rounded-none hover:bg-gray-100\"\r\n                        onClick={(e) => {\r\n                            e.preventDefault();\r\n                            e.stopPropagation();\r\n                            doDelete();\r\n                        }}\r\n                    >\r\n                        <TrashIcon className=\"mx-auto size-3.5\"/>\r\n                    </Button>\r\n                </div>\r\n            </div>\r\n            <div\r\n                className={`absolute top-[-11px] left-0 right-0 h-[20px] text-center z-20 pg-knob-wrap`}>\r\n                <AddElementDropdown\r\n                    trigger={<Button\r\n                        variant=\"ghost\"\r\n                        className={`size-[21px] p-0 text-center rounded-full bg-neutral-100 shadow-md`}>\r\n                        <PlusCircleIcon width={21} height={21}/>\r\n                    </Button>}\r\n                    align=\"center\"\r\n                    pushTransactions={props.pushTransactions}\r\n                    refElementId={props.id}\r\n                    refParentId={props.parentId}\r\n                    setActiveElementId={props.setActiveElementId}\r\n                    refCreateDirection={'t'}\r\n                />\r\n            </div>\r\n            <div\r\n                className={`absolute bottom-[-11px] left-0 right-0 h-[20px] text-center z-20 pg-knob-wrap pt-[0px]`}>\r\n                <AddElementDropdown\r\n                    trigger={<Button\r\n                        variant=\"ghost\"\r\n                        className={`size-[21px] p-0 text-center rounded-full bg-neutral-100 shadow-md`}>\r\n                        <PlusCircleIcon width={21} height={21}/>\r\n                    </Button>}\r\n                    align=\"center\"\r\n                    pushTransactions={props.pushTransactions}\r\n                    refElementId={props.id}\r\n                    refParentId={props.parentId}\r\n                    setActiveElementId={props.setActiveElementId}\r\n                    refCreateDirection={'b'}\r\n                />\r\n            </div>\r\n            <div\r\n                className={`absolute left-[-11px] w-[70px] text-left z-20 pg-knob-wrap top-[50%] -translate-y-1/2  h-[21px]`}>\r\n                <AddElementDropdown\r\n                    trigger={<Button\r\n                        variant=\"ghost\"\r\n                        className={`size-[21px] p-0 text-center rounded-full bg-neutral-100 shadow-md`}>\r\n                        <PlusCircleIcon width={21} height={21}/>\r\n                    </Button>}\r\n                    align=\"start\"\r\n                    pushTransactions={props.pushTransactions}\r\n                    refElementId={props.id}\r\n                    refParentId={props.parentId}\r\n                    setActiveElementId={props.setActiveElementId}\r\n                    refCreateDirection={'l'}\r\n                />\r\n            </div>\r\n            <div\r\n                className={`absolute right-[-11px] w-[70px] text-right z-20 pg-knob-wrap top-[50%] -translate-y-1/2  h-[21px]`}>\r\n                <AddElementDropdown\r\n                    trigger={<Button\r\n                        variant=\"ghost\"\r\n                        className={`size-[21px] p-0 text-center rounded-full bg-neutral-100 shadow-md`}>\r\n                        <PlusCircleIcon width={21} height={21}/>\r\n                    </Button>}\r\n                    align=\"end\"\r\n                    pushTransactions={props.pushTransactions}\r\n                    refElementId={props.id}\r\n                    refParentId={props.parentId}\r\n                    setActiveElementId={props.setActiveElementId}\r\n                    refCreateDirection={'r'}\r\n                />\r\n            </div>\r\n        </>\r\n    );\r\n};\r\n", "import React, {PropsWithChildren, useRef} from \"react\";\r\nimport {ScrollArea} from \"@/components/ui/scroll-area\";\r\nimport {Active, DndContext, DragCancelEvent, DragEndEvent, DragMoveEvent, DragOverEvent, DragOverlay, DragStartEvent, Over, PointerSensor, Translate, useSensor, useSensors} from \"@dnd-kit/core\";\r\nimport {DashboardContentProps} from \"@/components/workspace/main/views/dashboard/dashboard\";\r\nimport {ElementDraggableData, ElementRender} from \"@/components/workspace/main/views/dashboard/components/common/elementRender\";\r\nimport {createPortal} from \"react-dom\";\r\nimport {AddElementDropdown, AddElementRender} from \"@/components/workspace/main/views/dashboard/components/common/addElementRender\";\r\nimport {arrayAddElementAdjacent, removeArrayItem} from \"opendb-app-db-utils/lib/methods/array\";\r\nimport {generateUUID} from \"opendb-app-db-utils/lib/methods/string\";\r\nimport {DashboardRow, DashboardTransaction, Direction} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {cn} from \"@/lib/utils\";\r\nimport {PageLoader} from \"@/components/custom-ui/loader\";\r\nimport {ChartPieIcon, CirclePlusIcon} from \"@/components/icons/FontAwesomeRegular\";\r\nimport {Button} from \"@/components/ui/button\";\r\n\r\n\r\nexport const DashboardContentWrap = (props: DashboardContentProps) => {\r\n    const {dashboardDefinition} = props\r\n\r\n    const hasEle = dashboardDefinition.children.length > 0\r\n\r\n    return <div className=\"w-full h-full max-w-full overflow-hidden relative dashCW\"\r\n                onClick={e => props.setActiveElementId(\"\")}>\r\n        {hasEle ? <>\r\n            <DndWrap {...props}>\r\n                <ScrollArea className=\"w-full h-full scrollBlockChild overflow-x-hidden\">\r\n                    <div className={`${props.isEditing && \"pl-10\"}`}>\r\n                        <div className={cn(\"p-4 flex flex-col gap-4 w-full max-w-full\", props.isEditing ? 'pt-8 pb-24' : '')}>\r\n                            {props.dashboardDefinition.children.map(id => {\r\n                                const row = props.dashboardDefinition.rowsMap[id]\r\n                                if (!row || row.children.length === 0) return null\r\n\r\n                                const key = `${id}:${row.updatedTs}`\r\n                                return <div className=\"flex gap-4\" key={key}>\r\n                                    {row.children.map(id => <ElementRender {...props} parentId={row.id} id={id} key={id}/>)}\r\n                                </div>\r\n                            })}\r\n                        </div>\r\n                    </div>\r\n                </ScrollArea>\r\n                {props.isEditing && <AddElementRender {...props}/>}\r\n                {createPortal(\r\n                    <DragOverlay>\r\n                        {props.dragElementId &&\r\n                            <ElementRender {...props} id={props.dragElementId} isDragOverlay parentId={\"\"}/>}\r\n                        {/*{activeColumn && (*/}\r\n                        {/*    <BoardColumn*/}\r\n                        {/*        column={activeColumn}*/}\r\n                        {/*        key={activeColumn.id}*/}\r\n                        {/*        items={items.filter(i => i.columnId === activeColumn.id)}*/}\r\n                        {/*        deleteColumn={deleteColumn}*/}\r\n                        {/*        updateColumn={updateColumn}*/}\r\n                        {/*    />*/}\r\n                        {/*)}*/}\r\n                        {/*{activeItem && (*/}\r\n                        {/*    <ItemCard key={activeItem.id} item={activeItem}/>*/}\r\n                        {/*)}*/}\r\n                    </DragOverlay>,\r\n                    document.body\r\n                )}\r\n            </DndWrap>\r\n\r\n\r\n        </> : <>\r\n\r\n             <PageLoader\r\n                 size='full'\r\n                 error={<div className='my-2 flex flex-col gap-4 items-center justify-center'>\r\n                     <div className=''>\r\n                         <ChartPieIcon className='size-8'/>\r\n                     </div>\r\n                     <span className='text-sm font-medium'>Add an element to get started</span>\r\n\r\n                     {props.isEditing && <AddElementDropdown\r\n                         align='center'\r\n                         pushTransactions={props.pushTransactions}\r\n                         setActiveElementId={props.setActiveElementId}\r\n                         trigger={<Button variant=\"outline\" className=\"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 font-semibold\">\r\n                             <CirclePlusIcon className=\"size-3\"/>\r\n                             Add Element\r\n                         </Button>}\r\n                     />}\r\n                 </div>}\r\n             />\r\n         </>}\r\n\r\n    </div>\r\n}\r\n\r\n\r\nconst DndWrap = (props: PropsWithChildren<DashboardContentProps>) => {\r\n    // const { moveElement } = usePage();\r\n    const dragOverRef = useRef<{ element: HTMLElement; id: string } | null>(null);\r\n    const draggingRef = useRef<HTMLElement | null>(null);\r\n\r\n    function handleDragEnd(event: DragEndEvent) {\r\n        if (props.dragElementId) props.setDragElementId(\"\")\r\n        const dragOverEle = dragOverRef.current;\r\n        if (!dragOverEle) return;\r\n\r\n        const {active, over, delta} = event;\r\n        if (over && over.data?.current?.accepts.includes(active.data?.current?.type)) {\r\n        } else {\r\n            return;\r\n        }\r\n        if (over.id === active.id) return\r\n        const overData = over.data.current as ElementDraggableData\r\n        const activeData = active.data.current as ElementDraggableData\r\n        // do stuff\r\n\r\n        const dir = getDirection(active, over as Over, delta);\r\n\r\n        const {id, parentId} = over.data.current as ElementDraggableData;\r\n\r\n        moveElement({id: activeData.id, parentId: activeData.parentId || \"\"}, {id: overData.id, parentId: overData.parentId || \"\"}, dir)\r\n        //\r\n        // if (dir === \"t\") {\r\n        //     // moveElement(active.id as string, undefined, parentId);\r\n        // } else if (dir === \"b\") {\r\n        //     // moveElement(active.id as string, undefined, undefined, parentId);\r\n        // } else if (dir === \"l\") {\r\n        //     // moveElement(active.id as string, parentId, id);\r\n        // } else if (dir === \"r\") {\r\n        //     // moveElement(active.id as string, parentId, undefined, id);\r\n        // }\r\n        console.log(\"DragEnd:\", event);\r\n\r\n        clearDragOver();\r\n    }\r\n\r\n    const moveElement = (ele: { id: string, parentId: string }, targetRef: { id: string, parentId: string }, direction: Direction) => {\r\n        const moveEle = props.dashboardDefinition.elementMap[ele.id]\r\n        const sourceRow = props.dashboardDefinition.rowsMap[ele.parentId]\r\n\r\n        const refEle = props.dashboardDefinition.elementMap[targetRef.id]\r\n        const destRow = props.dashboardDefinition.rowsMap[targetRef.parentId]\r\n\r\n        if (!moveEle || !sourceRow || !refEle || !destRow) return\r\n\r\n        const transactions: DashboardTransaction[] = []\r\n\r\n        // const data = props.dashboardDefinition\r\n\r\n        sourceRow.children = removeArrayItem(sourceRow.children, moveEle.id)\r\n        sourceRow.updatedTs = new Date().getTime()\r\n\r\n        transactions.push({\r\n            action: 'updateRow',\r\n            row: sourceRow\r\n        })\r\n\r\n\r\n        if (direction === \"t\" || direction === \"b\") {\r\n            const row: DashboardRow = {\r\n                id: generateUUID(),\r\n                children: [moveEle.id],\r\n            }\r\n            // data.rowsMap[row.id] = row\r\n            // data.children = arrayAddElementAdjacent(data.children, destRow.id, row.id, direction === \"t\" ? \"before\" : \"after\")\r\n\r\n            transactions.push({\r\n                action: 'addRow',\r\n                row,\r\n                beforeId: direction === 't' ? destRow.id : '',\r\n                afterId: direction === 'b' ? destRow.id : '',\r\n            })\r\n        } else {\r\n            destRow.children = arrayAddElementAdjacent(destRow.children, refEle.id, moveEle.id, direction === \"l\" ? \"before\" : \"after\")\r\n            destRow.updatedTs = new Date().getTime()\r\n\r\n            transactions.push({\r\n                action: 'updateRow',\r\n                row: destRow\r\n            })\r\n        }\r\n        if (sourceRow.children.length === 0) {\r\n            // delete data.rowsMap[sourceRow.id]\r\n\r\n            transactions.push({\r\n                action: 'deleteRow',\r\n                row: sourceRow\r\n            })\r\n        }\r\n        // props.updateData({elementMap: {...data.elementMap}, rowsMap: {...data.rowsMap}, children: [...data.children]})\r\n        // props.setActiveElementId(ele.id)\r\n        props.pushTransactions(transactions)\r\n        props.setActiveElementId(ele.id)\r\n    }\r\n\r\n    const dragOver = (event: DragOverEvent) => {\r\n        // clearDragOver();\r\n        // if (event.over?.id === event.active.id) return;\r\n        // const overEle = document.querySelector(`[data-pge-id='${event.over?.id}']`);\r\n        // if (overEle) {\r\n        //   dragOverRef.current = overEle as HTMLElement;\r\n        //   overEle.classList.add(\"dragover\");\r\n        // }\r\n    };\r\n\r\n    const dragStart = (event: DragStartEvent) => {\r\n        console.log(\"DragStart:\", event);\r\n\r\n        if (!event.active.id) return;\r\n        props.setDragElementId(event.active.id.toString())\r\n    };\r\n\r\n    const clearDragOver = () => {\r\n        const dragOverEle = dragOverRef.current;\r\n        if (dragOverEle) {\r\n            dragOverEle.element.classList.remove(\"dragover\", \"l\", \"r\", \"t\", \"b\");\r\n        }\r\n        dragOverRef.current = null;\r\n\r\n        const ele = document.querySelector(\".dashER[data-ele-id].dragover\");\r\n        if (ele) {\r\n            ele.classList.remove(\"dragover\", \"l\", \"r\", \"t\", \"b\");\r\n        }\r\n    };\r\n\r\n    const dragCancel = (event: DragCancelEvent) => {\r\n        clearDragOver();\r\n    };\r\n\r\n    const getDirection = (\r\n        active: Active,\r\n        over: Over,\r\n        delta: Translate\r\n    ): Direction => {\r\n        const overRect = over?.rect;\r\n        if (!overRect) return \"\";\r\n\r\n        const {left, top, right, bottom} = active.rect.current.translated!;\r\n\r\n        // calculate the distance from the left to left, left to right, top to top\r\n        const leftDistance = Math.abs(left - overRect.left);\r\n        const rightDistance = Math.abs(left - overRect.right);\r\n        const topDistance = Math.abs(top - overRect.top);\r\n        // const bottomDistance = Math.abs(bottom - overRect.bottom);\r\n        //\r\n        console.log(\"Distance: \", {\r\n            leftDistance,\r\n            topDistance,\r\n            rightDistance,\r\n        });\r\n\r\n        // Define a threshold distance (X distance)\r\n        const thresholdDistance = 10; // Adjust this value as needed\r\n\r\n        // Determine the closest edge and add the appropriate class\r\n        if (leftDistance < thresholdDistance) {\r\n            return \"l\";\r\n        } else if (rightDistance < thresholdDistance) {\r\n            return \"r\";\r\n        } else if (topDistance < thresholdDistance) {\r\n            return \"t\";\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n    const dragMove = (e: DragMoveEvent) => {\r\n        const {active, over, delta} = e;\r\n        if (!over) {\r\n            console.log(\"No over\");\r\n            return;\r\n        }\r\n        if (over.id === active.id) {\r\n            console.log(\"Over over active\");\r\n            return;\r\n        }\r\n        // let currDragOver = dragOverRef.current;\r\n\r\n        if (dragOverRef.current) {\r\n            if (dragOverRef.current.id !== over.id) {\r\n                dragOverRef.current.element.classList.remove(\r\n                    \"l\",\r\n                    \"r\",\r\n                    \"t\",\r\n                    \"b\",\r\n                    \"dragover\"\r\n                );\r\n                dragOverRef.current = null;\r\n            }\r\n        } else {\r\n            const dragOverEle = document.querySelector(`.dashER[data-ele-id='${over.id}']`);\r\n\r\n            if (dragOverEle) {\r\n                dragOverRef.current = {\r\n                    element: dragOverEle as HTMLElement,\r\n                    id: over.id as string,\r\n                };\r\n            }\r\n        }\r\n\r\n        if (!dragOverRef.current) {\r\n            console.log(\"dragOverRef.current is null\");\r\n        }\r\n\r\n        const dragOverEle = dragOverRef.current?.element;\r\n        if (!dragOverEle) {\r\n            console.log(\"Drag Over Ele is null or undefined\")\r\n            return;\r\n        }\r\n\r\n        const direction = getDirection(active, over as Over, delta);\r\n\r\n        dragOverEle.classList.remove(\"l\", \"r\", \"t\", \"b\", \"dragover\");\r\n        if (direction) {\r\n            dragOverEle.classList.add(direction, \"dragover\");\r\n        }\r\n\r\n        console.log(\"Over\", over);\r\n    };\r\n\r\n    const sensors = useSensors(\r\n        useSensor(PointerSensor, {\r\n            activationConstraint: {\r\n                distance: 10,\r\n            },\r\n        })\r\n    );\r\n\r\n    return (\r\n        <>\r\n            <DndContext\r\n                onDragEnd={handleDragEnd}\r\n                onDragMove={dragMove}\r\n                onDragOver={dragOver}\r\n                onDragStart={dragStart}\r\n                onDragCancel={dragCancel}\r\n                sensors={sensors}\r\n            >\r\n                {props.children}\r\n            </DndContext>\r\n        </>\r\n    );\r\n};", "import {DashboardDefinition, DashboardElementType, DashboardRow, InfoboxElement, LineChartElement, PieChartElement, Position, TextElement} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {generateUUID} from \"opendb-app-db-utils/lib/methods/string\";\r\nimport {Match} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport {CountAggregateFunction} from \"opendb-app-db-utils/lib\";\r\n\r\nconst dashboard: DashboardDefinition = {\r\n    elementMap: {},\r\n    rowsMap: {},\r\n    children: []\r\n}\r\n\r\nconst row1: DashboardRow = {\r\n    id: generateUUID(),\r\n    children: [],\r\n}\r\nfor (let i = 0; i < 3; i++) {\r\n    const info: InfoboxElement = {\r\n        iconPosition: Position.Left, valueResolve: {databaseId: '', filter: {conditions: [], match: Match.All}, aggregateBy: CountAggregateFunction.CountAll, columnId: ''},\r\n        title: \"Infobox\",\r\n        id: generateUUID(),\r\n        type: DashboardElementType.Infobox\r\n    }\r\n    dashboard.elementMap[info.id] = info\r\n    row1.children.push(info.id)\r\n}\r\n\r\nconst row2: DashboardRow = {\r\n    id: generateUUID(),\r\n    children: [],\r\n}\r\nfor (let i = 0; i < 3; i++) {\r\n    const pie: PieChartElement = {\r\n        title: \"PieChart\",\r\n        id: generateUUID(),\r\n        type: DashboardElementType.PieChart,\r\n        recordsResolve: {databaseId: '', filter: {conditions: [], match: Match.All}, groupByIds: [], titleColId: ''}\r\n    }\r\n    dashboard.elementMap[pie.id] = pie\r\n    row2.children.push(pie.id)\r\n}\r\n\r\nconst row3: DashboardRow = {\r\n    id: generateUUID(),\r\n    children: [],\r\n}\r\nfor (let i = 0; i < 2; i++) {\r\n    const line: LineChartElement = {\r\n        columnPropsMap: {}, columnsOrder: [],\r\n        title: \"Infobox\",\r\n        id: generateUUID(),\r\n        type: DashboardElementType.LineChart,\r\n        recordsResolve: {databaseId: '', filter: {conditions: [], match: Match.All}, sorts: [],}\r\n    }\r\n    dashboard.elementMap[line.id] = line\r\n    row3.children.push(line.id)\r\n}\r\n\r\n// const row4: DashboardRow = {\r\n//     id: generateUUID(),\r\n//     children: [],\r\n// }\r\n// for (let i = 0; i < 2; i++) {\r\n//     const bar: DashboardElement = {\r\n//         id: generateUUID(),\r\n//         type: DashboardElementType.BarChart\r\n//     }\r\n//     dashboard.elementMap[bar.id] = bar\r\n//     row4.children.push(bar.id)\r\n// }\r\n\r\n// const row5: DashboardRow = {\r\n//     id: generateUUID(),\r\n//     children: [],\r\n// }\r\n// for (let i = 0; i < 2; i++) {\r\n//     const funnel: DashboardElement = {\r\n//         id: generateUUID(),\r\n//         type: DashboardElementType.FunnelChart\r\n//     }\r\n//     dashboard.elementMap[funnel.id] = funnel\r\n//     row5.children.push(funnel.id)\r\n// }\r\n\r\nconst row6: DashboardRow = {\r\n    id: generateUUID(),\r\n    children: [],\r\n}\r\nfor (let i = 0; i < 2; i++) {\r\n    const text: TextElement = {\r\n        title: \"Infobox\",\r\n        id: generateUUID(),\r\n        type: DashboardElementType.Text,\r\n        content: ''\r\n    }\r\n    dashboard.elementMap[text.id] = text\r\n    row6.children.push(text.id)\r\n}\r\n\r\n\r\n[row1, row2, row6, row3\r\n    // , row4, row5\r\n].forEach(r => {\r\n    dashboard.rowsMap[r.id] = r\r\n    dashboard.children.push(r.id)\r\n})\r\n\r\nexport const demoDashboard = Object.freeze(dashboard)\r\n// <div className=\"flex gap-4\">\r\n// <InfoBox/>\r\n// <InfoBox/>\r\n// <InfoBox/>\r\n// </div>\r\n// <div className=\"flex gap-4\">\r\n// <PieChart/>\r\n// <PieChart/>\r\n// <PieChart/>\r\n// </div>\r\n// <div className=\"flex gap-4\">\r\n// <LineChart/>\r\n// <LineChart/>\r\n// </div>\r\n// <div className=\"flex gap-4\">\r\n// <BarChart/>\r\n// </div>\r\n// <div className=\"flex gap-4\">\r\n// <FunnelChart/>\r\n// <FunnelChart/>\r\n// </div>", "import {DashboardPanel} from \"@/components/workspace/main/views/dashboard/components/dashboardPanel\";\r\nimport {DashboardContentWrap} from \"@/components/workspace/main/views/dashboard/components/dashboardContentWrap\";\r\nimport {useState} from \"react\";\r\nimport {demoDashboard} from \"@/utils/demo/dashboard\";\r\nimport \"./dashboard.css\"\r\nimport {DashboardDefinition, DashboardTransaction} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {ViewRenderProps} from \"@/components/workspace/main/views/table\";\r\nimport {DashboardViewDefinition} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {ViewIsEditingKey} from \"@/components/workspace/main/views/common/viewMoreOptions\";\r\nimport {useViews} from \"@/providers/views\";\r\nimport {arrayAddElementAdjacent} from \"opendb-app-db-utils/lib/methods/array\";\r\nimport {removeAllArrayItem} from \"opendb-app-db-utils/lib\";\r\n\r\n\r\nexport interface DashboardContentProps {\r\n    dashboardDefinition: DashboardDefinition\r\n    updateData: (update: Partial<DashboardDefinition>) => void\r\n    pushTransactions: (transactions: DashboardTransaction[]) => void\r\n    dragElementId: string\r\n    setDragElementId: (id: string) => void\r\n    activeElementId: string\r\n    setActiveElementId: (id: string) => void\r\n    isEditing: boolean\r\n}\r\n\r\nexport interface DashboardViewRenderProps extends ViewRenderProps {\r\n    definition: DashboardViewDefinition\r\n}\r\n\r\nexport const DashboardView = (props: DashboardViewRenderProps) => {\r\n    const [dragElementId, setDragElementId] = useState(\"\")\r\n    let [activeElementId, setActiveElementId] = useState(\"\")\r\n\r\n    const {updateViewDefinition, cache, pushDashboardTransactions} = useViews()\r\n\r\n    const isEditing = !!cache.getCache(ViewIsEditingKey, false)\r\n\r\n\r\n    const {definition} = props\r\n\r\n    const fixDefinition = () => {\r\n        definition.definition = definition.definition || {}\r\n        definition.definition.children = definition.definition.children || []\r\n        definition.definition.rowsMap = definition.definition.rowsMap || {}\r\n        definition.definition.elementMap = definition.definition.elementMap || {}\r\n    }\r\n\r\n    const [defState, setDefState] = useState<DashboardDefinition>(definition.definition)\r\n\r\n    fixDefinition()\r\n\r\n    const getLocalData = () => {\r\n        const saved = localStorage.getItem(\"dfDashboard\")\r\n        if (saved) {\r\n            try {\r\n                return JSON.parse(saved)\r\n            } catch (e) {\r\n            }\r\n        }\r\n        return null\r\n    }\r\n    const [data, setData] = useState<DashboardDefinition>(getLocalData() || demoDashboard)\r\n\r\n    const updateData = (update: Partial<DashboardDefinition>) => {\r\n        // const updated: DashboardDefinition = {...data, ...update}\r\n        // localStorage.setItem(\"dfDashboard\", JSON.stringify(updated))\r\n        // setData(updated)\r\n    }\r\n\r\n    if (!isEditing) activeElementId = ''\r\n\r\n\r\n    // const applyTransaction = (currDefinition: DashboardDefinition, transactions: DashboardTransaction[]) => {\r\n    //     const definition: DashboardDefinition = {...currDefinition}\r\n    //\r\n    //     if (transactions.length === 0) return definition\r\n    //\r\n    //     for (const transaction of transactions) {\r\n    //         const {element, row, afterId, parentId, beforeId, action} = transaction\r\n    //\r\n    //         if (action === \"updateRow\") {\r\n    //             if (!row) throw \"Row is undefined\"\r\n    //\r\n    //             const defRow = definition.rowsMap[row.id]\r\n    //             if (!defRow) throw \"Invalid row\"\r\n    //             definition.rowsMap[row.id] = {...defRow, ...row, updatedTs: new Date().getTime()}\r\n    //\r\n    //         } else if (action === \"addRow\") {\r\n    //             if (!row) throw \"Row is undefined\"\r\n    //             definition.rowsMap[row.id] = row\r\n    //\r\n    //             if (afterId) {\r\n    //                 definition.children = arrayAddElementAdjacent(definition.children, afterId, row.id, 'after')\r\n    //             } else if (beforeId) {\r\n    //                 definition.children = arrayAddElementAdjacent(definition.children, beforeId, row.id, 'before')\r\n    //             } else definition.children.push(row.id)\r\n    //\r\n    //         } else if (action === 'deleteRow') {\r\n    //             if (!row) throw \"Row is undefined\"\r\n    //             delete definition.rowsMap[row.id]\r\n    //             definition.children = removeAllArrayItem(definition.children, row.id)\r\n    //\r\n    //         } else if (action === 'addElement') {\r\n    //             if (!element) throw \"Element is undefined\"\r\n    //             if (!parentId) throw \"Element parent is undefined\"\r\n    //\r\n    //             if (!definition.rowsMap[parentId]) throw \"Element parent is not found\"\r\n    //             definition.elementMap[element.id] = element\r\n    //\r\n    //             if (afterId) {\r\n    //                 definition.rowsMap[parentId].children = arrayAddElementAdjacent(definition.rowsMap[parentId].children, afterId, element.id, 'after')\r\n    //             } else if (beforeId) {\r\n    //                 definition.rowsMap[parentId].children = arrayAddElementAdjacent(definition.rowsMap[parentId].children, beforeId, element.id, 'before')\r\n    //             } else definition.rowsMap[parentId].children.push(element.id)\r\n    //\r\n    //             definition.rowsMap[parentId].updatedTs = new Date().getTime()\r\n    //         } else if (action === 'updateElement') {\r\n    //             if (!element || !definition.elementMap[element.id]) throw \"Element is undefined\"\r\n    //             definition.elementMap[element.id] = {...definition.elementMap[element.id], ...element, updatedTs: new Date().getTime()}\r\n    //         } else if (action === 'deleteElement') {\r\n    //             if (!element) throw \"Element is undefined\"\r\n    //             delete definition.elementMap[element.id]\r\n    //\r\n    //             if (!parentId) throw \"Element parent is undefined\"\r\n    //             definition.rowsMap[parentId].children = removeAllArrayItem(definition.rowsMap[parentId].children, element.id)\r\n    //             definition.rowsMap[parentId].updatedTs = new Date().getTime()\r\n    //\r\n    //         } else throw `Invalid action ${action}`\r\n    //\r\n    //     }\r\n    //\r\n    //     return definition\r\n    // }\r\n\r\n    const pushTransactions = async (transactions: DashboardTransaction[]) => {\r\n        if (transactions.length === 0) return\r\n\r\n        // send it to socket\r\n        // setDefState(applyTransaction(defState, transactions))\r\n        await pushDashboardTransactions(props.view.id, props.view.pageId, transactions)\r\n    }\r\n\r\n\r\n    const dashboardDefinition = definition.definition\r\n    // const dashboardDefinition = defState\r\n\r\n    const childProps: DashboardContentProps = {dashboardDefinition, updateData, dragElementId, setDragElementId, activeElementId, setActiveElementId, isEditing, pushTransactions}\r\n    return <>\r\n        <div className=\"w-full h-full flex\">\r\n            <DashboardContentWrap key={isEditing ? 1 : 0} {...childProps}/>\r\n            <DashboardPanel {...childProps}/>\r\n        </div>\r\n    </>\r\n}", "// extracted by mini-css-extract-plugin", "// extracted by mini-css-extract-plugin"], "names": ["Infobox", "title", "icon", "value", "error", "iconPosition", "Position", "Right", "className", "param", "iconWrap", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "div", "Fragment", "cn", "jsxs", "Left", "span", "h1", "MultiImagePicker", "fileInputRef", "useRef", "uploads", "props", "imagesRef", "images", "current", "removeImage", "filter", "i", "idx", "index", "onChange", "hideTitle", "Label", "length", "map", "item", "style", "width", "concat", "progress", "id", "image", "img", "src", "alt", "button", "disabled", "onClick", "XMarkIcon", "height", "input", "type", "ref", "multiple", "accept", "uploadImage", "files", "e", "target", "currentFile", "res", "upload", "data", "finalUrl", "<PERSON><PERSON>", "variant", "click", "CirclePlusIcon", "DatabaseSelect", "databasePageStore", "databasePagesId", "useWorkspace", "selectedId", "dbItems", "useMemo", "items", "databaseIds", "includes", "push", "db", "page", "emoji", "ObjectType", "<PERSON><PERSON><PERSON>", "color", "undefined", "name", "CustomSelect", "v", "selectedIds", "placeholder", "options", "Align", "AlignSelect", "jsx_runtime", "role", "Bars3BottomLeftIcon", "enableCenter", "Bars3Icon", "Bars3BottomRightIcon", "AdjustToViewPort", "eleRef", "renderedRef", "useEffect", "ele", "top", "left", "bottom", "right", "getBoundingClientRect", "viewportHeight", "window", "innerHeight", "viewportWidth", "innerWidth", "console", "log", "offset", "translateY", "Math", "min", "translateX", "transform", "children", "iconsList", "Object", "keys", "HeroIcons", "Picker", "dynamic", "Promise", "all", "__webpack_require__", "then", "bind", "ssr", "IconPicker", "close", "onPick", "requestRemove", "xAlign", "yAlign", "enableRemove", "enableEmojis", "tab", "setTab", "useState", "popoverRef", "search", "setSearch", "handleClickOutside", "contains", "event", "document", "addEventListener", "removeEventListener", "onEmojiClick", "previewConfig", "showPreview", "searchDisabled", "MagnifyingGlassIcon", "Input", "required", "toLowerCase", "Icon", "IconPickerIcon", "rest", "IconComponent", "esm", "PickedIconRender", "AggregateBySelect", "countAggregateFunction", "percentAggregateFunction", "fieldType", "DatabaseFieldDataType", "Checkbox", "checkCountAggregateFunction", "checkPercentAggregateFunction", "Number", "unshift", "numberAggregateFunction", "tagOptions", "o", "label", "InfoboxRender", "databaseStore", "members", "workspace", "databaseErrorStore", "refreshDatabase", "useViews", "element", "dashboardDefinition", "elementMap", "valueResolve", "databaseId", "columnId", "aggregateBy", "CountAggregateFunction", "Count<PERSON>ll", "match", "Match", "All", "conditions", "database", "errorState", "isLoading", "shouldRefreshDb", "loading", "recordsLoaded", "getInfoValue", "rows", "filterAndSortRecords", "workspaceMember", "userId", "values", "r", "record", "recordValues", "parseInt", "String", "resolveColumnValuesAggregation", "percentAggregateFn", "UserGroupIcon", "Loader", "CircleExclamationIcon", "InfoboxPanel", "field", "updateElement", "picker", "setPicker", "definition", "columnsMap", "usePage", "ViewFilter", "trigger", "FilterListIcon", "DatabaseColumnSelect", "selected", "DataViewWrapper", "THEMES", "light", "dark", "ChartContext", "React", "useChart", "context", "ChartContainer", "config", "uniqueId", "chartId", "replace", "Provider", "data-chart", "ChartStyle", "RechartsPrimitive", "displayName", "colorConfig", "entries", "_", "theme", "dangerouslySetInnerHTML", "__html", "prefix", "itemConfig", "key", "join", "ChartTooltip", "ChartTooltipContent", "active", "payload", "indicator", "<PERSON><PERSON><PERSON><PERSON>", "hideIndicator", "labelFormatter", "labelClassName", "formatter", "<PERSON><PERSON><PERSON>", "labelKey", "tooltipLabel", "dataKey", "getPayloadConfigFromPayload", "<PERSON><PERSON><PERSON><PERSON>", "indicatorColor", "fill", "toLocaleString", "ChartLegend", "ChartLegendContent", "hideIcon", "verticalAlign", "backgroundColor", "payloadPayload", "config<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paginationPage", "setPaginationPage", "perPage", "setPerPage", "defaultResolve", "sorts", "recordsResolve", "columnPropsMap", "totalPage", "getProcessedRows", "MagicColumn", "CreatedAt", "order", "Sort", "Asc", "ceil", "chart", "getChartInfo", "datasets", "labels", "titleColId", "getDatabaseTitleCol", "dbDefinition", "columnIds", "k", "isHidden", "ColorEntries", "fg", "info", "bg", "borderColor", "row", "slice", "processedRecord", "recordValueToText", "processedRecordValues", "trim", "recordV1lToText", "isNaN", "responsive", "plugins", "legend", "position", "getChartInfo2", "colours", "generateVibrantColors", "rawTitle", "datum", "<PERSON><PERSON><PERSON><PERSON>", "size", "Line", "Line<PERSON>hart", "accessibilityLayer", "margin", "Cartesian<PERSON><PERSON>", "vertical", "XAxis", "tickLine", "axisLine", "tick<PERSON>argin", "cursor", "content", "ReChartsLine", "stroke", "strokeWidth", "dot", "activeDot", "aria-label", "Select", "onValueChange", "val", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "ul", "li", "ChevronLeftIcon", "ChevronRightIcon", "fixColumnPropsMap", "columnsOrder", "arrayDeDuplicate", "LineChartPanel", "c", "DatabaseFieldTypeIcon", "Switch", "thumbClassName", "checked", "onCheckedChange", "updatedPropsMap", "ViewSort", "ArrowUpWideShortIcon", "ArcElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groupByIds", "Array", "isArray", "rowMap", "groupRowMap", "sortOptions", "groupBy", "groupValues", "textValues", "columnTitle", "columnValueToText", "valuesJSON", "JSON", "stringify", "hash", "crypto", "update", "digest", "updatedAtTime", "max", "Date", "updatedAt", "getTime", "toISOString", "recordIds", "valuesText", "pie", "getPieData", "processed", "borderWidth", "gR", "pieData", "getPieData2", "records", "groups", "totalCount", "count", "Pie", "ChartDataLabels", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "innerRadius", "LabelList", "fontSize", "ReChartsLabel", "viewBox", "text", "x", "cx", "y", "cy", "textAnchor", "dominantBaseline", "tspan", "toString", "PieChartPanel", "isMultiple", "ImageRender", "setIndex", "autoRotate", "next", "useCallback", "imgUrl", "interval", "setInterval", "clearInterval", "backgroundImage", "ImagePanel", "uploadWorkspaceFile", "uploadQueue", "allowRemovingImages", "file", "cb", "TextRender", "TextPanel", "textareaRef", "useAutoHeightTextarea", "htmlFor", "Textarea", "defaultValue", "onBlur", "ALLOWED_TAGS", "ALLOWED_ATTR", "<PERSON><PERSON><PERSON><PERSON>", "embedCode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containerRef", "cleanHtml", "DOMPurify", "sanitize", "shadowRoot", "attachShadow", "mode", "shadow<PERSON><PERSON><PERSON>", "createElement", "innerHTML", "append<PERSON><PERSON><PERSON>", "EmbedPanel", "DashboardElementDefinitions", "AddressCardIcon", "DashboardElementType", "panel", "ChartLineUpIcon", "ChartPieIcon", "Image", "ImagesIcon", "Text", "LanguageIcon", "Embed", "CodeIcon", "DashboardPanel", "typeDefinition", "confirm", "useAlert", "isEditing", "activeElementId", "updated", "pushTransactions", "action", "panelTitle", "header", "ScrollArea", "transactions", "parentRow", "parentRowId", "rowId", "rowsMap", "parentId", "removeAllArrayItem", "setActiveElementId", "TrashIcon", "ArrowPointerIcon", "DashboardElementTypeKey", "FunnelChart", "idRef", "generateUUID", "canvas", "getElementById", "ctx", "getContext", "FC", "shrinkAnchor", "ex", "BarElement", "display", "scales", "stacked", "faker", "datatype", "number", "<PERSON><PERSON><PERSON>", "Bar", "AddElement<PERSON><PERSON>", "AddElementDropdown", "PlusCircleIcon", "align", "addElement", "refElementId", "dir", "refCreateDirection", "refParentId", "transaction", "beforeId", "afterId", "setTimeout", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "DropdownMenuContent", "DropdownMenuGroup", "DropdownMenuItem", "Element<PERSON><PERSON>", "RenderWrap", "dragData", "subType", "attributes", "listeners", "setNodeRef", "setDragRef", "isDragging", "useDraggable", "setDropRef", "useDroppable", "accepts", "CSS", "Translate", "isActive", "isDragOverlay", "data-ele-id", "stopPropagation", "preventDefault", "ElementRenderKnob", "duplicate", "doDelete", "DragHandleIcon", "DocumentDuplicateIcon", "DashboardContentWrap", "<PERSON><PERSON><PERSON>", "DndWrap", "updatedTs", "react", "createPortal", "DragOverlay", "dragElementId", "body", "dragOverRef", "moveElement", "targetRef", "direction", "moveEle", "sourceRow", "refEle", "destRow", "removeArrayItem", "arrayAddElementAdjacent", "clearDragOver", "dragOverEle", "classList", "remove", "querySelector", "getDirection", "over", "delta", "overRect", "rect", "translated", "leftDistance", "abs", "rightDistance", "topDistance", "sensors", "useSensors", "useSensor", "PointerSensor", "activationConstraint", "distance", "DndContext", "onDragEnd", "setDragElementId", "overData", "activeData", "onDragMove", "add", "onDragOver", "onDragStart", "onDragCancel", "dashboard", "row1", "row2", "row3", "line", "row6", "for<PERSON>ach", "demoDashboard", "freeze", "DashboardView", "updateViewDefinition", "cache", "pushDashboardTransactions", "getCache", "ViewIsEditingKey", "defState", "setDefState", "setData", "getLocalData", "saved", "localStorage", "getItem", "parse", "view", "pageId", "childProps", "updateData"], "sourceRoot": ""}