{"version": 3, "file": "static/chunks/2949.927494bbd1aa84a4.js", "mappings": "ubAEA,IAAAA,EAAA,gIAKAC,CAAAA,EAAAC,CAAgB,UAAAC,CAAA,EAEhB,IAAAA,GAGAA,EAAAC,MAAA,MAIA,CADAJ,EAAAK,IAAA,CAAAF,GALA,SAUA,IAAAG,EAAAH,EAAAI,KAAA,cACAD,CAAA,IAAAF,MAAA,KAIAI,CADA,IAAAD,KAAA,MACAE,IAAA,UAAAC,CAAA,EAAsC,OAAAA,EAAAN,MAAA,MAItC,sHCzBA,IAAMO,EAAOC,EAAAA,UAAgB,CAG3B,CAAAC,EAA0BC,QAAzB,CAAEC,UAAAA,CAAS,CAAE,GAAGC,EAAO,CAAAH,QACxB,GAAAI,EAAAC,GAAA,EAACC,MAAAA,CACCL,IAAKA,EACLC,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,wDACAL,GAED,GAAGC,CAAK,IAGbL,CAAAA,EAAKU,WAAW,CAAG,OAYnBC,EAVmBV,UAAgB,CAGjC,CAAAC,EAA0BC,QAAzB,CAAEC,UAAAA,CAAS,CAAE,GAAGC,EAAO,CAAAH,QACxB,GAAAI,EAAAC,GAAA,EAACC,MAAAA,CACCL,IAAKA,EACLC,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,gCAAiCL,GAC9C,GAAGC,CAAK,KAGFK,WAAW,CAAG,aAYzBE,EAVkBX,UAAgB,CAGhC,CAAAC,EAA0BC,QAAzB,CAAEC,UAAAA,CAAS,CAAE,GAAGC,EAAO,CAAAH,QACxB,GAAAI,EAAAC,GAAA,EAACM,KAAAA,CACCV,IAAKA,EACLC,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,4CAA6CL,GAC1D,GAAGC,CAAK,KAGHK,WAAW,CAAG,YAYxBI,EAVwBb,UAAgB,CAGtC,CAAAC,EAA0BC,QAAzB,CAAEC,UAAAA,CAAS,CAAE,GAAGC,EAAO,CAAAH,QACxB,GAAAI,EAAAC,GAAA,EAACQ,IAAAA,CACCZ,IAAKA,EACLC,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,gCAAiCL,GAC9C,GAAGC,CAAK,KAGGK,WAAW,CAAG,kBAE9B,IAAMM,EAAcf,EAAAA,UAAgB,CAGlC,CAAAC,EAA0BC,QAAzB,CAAEC,UAAAA,CAAS,CAAE,GAAGC,EAAO,CAAAH,QACxB,GAAAI,EAAAC,GAAA,EAACC,MAAAA,CAAIL,IAAKA,EAAKC,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,WAAYL,GAAa,GAAGC,CAAK,IAEhEW,CAAAA,EAAYN,WAAW,CAAG,cAY1BO,EAVmBhB,UAAgB,CAGjC,CAAAC,EAA0BC,QAAzB,CAAEC,UAAAA,CAAS,CAAE,GAAGC,EAAO,CAAAH,QACxB,GAAAI,EAAAC,GAAA,EAACC,MAAAA,CACCL,IAAKA,EACLC,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,6BAA8BL,GAC3C,GAAGC,CAAK,KAGFK,WAAW,CAAG,oPC3DlB,SAASQ,EAAShB,CAMT,KANS,CACvBE,UAAAA,CAAS,CACTe,KAAAA,EAAO,QAAQ,CACfC,SAAAA,CAAQ,CACRC,SAAAA,CAAQ,CACR,GAAGhB,EACW,CANSH,EAavB,MACE,GAAAoB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,kDAAmDL,YACpE,GAAAkB,EAAAf,GAAA,EAACgB,IAAUA,CACTH,SAAUA,EACVI,SAVe,CAACC,EAAmBC,KACnCL,GAAYI,GACdJ,EAASI,EAEb,EAOME,OAAM,GACNC,kBAAiB,GACjBC,iBAAgB,GAChBC,aAAa,SACbC,kBAAkB,uBACjB,GAAG1B,CAAK,IAIjB,oPEuBO,IAAM2B,EAAiB,GACrBC,GAAAA,SD9BmCC,CAAQ,CAAEC,CAAS,CAAEC,CAAO,EACtEC,CAAAA,EAAAA,EAAAA,CAAAA,EAAa,EAAGC,WAChB,IAAIC,EAAOC,CAAAA,EAAAA,EAAAA,CAAAA,EAAyBN,EAAUC,GAAaM,EAAAA,EAAoBA,CAC/E,MAAOC,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBN,MAAAA,EAAyC,KAAK,EAAIA,EAAQO,cAAc,EAAEJ,EACrG,EC0B6B,IAAIK,KAAKlB,EAAMmB,GAAG,EAAG,IAAID,KAAKlB,EAAMoB,KAAK,GAGzDC,EAAkB,CAC7BD,EACAE,EACAZ,KAKA,GAAM,CAAEa,SAAAA,EAAWC,OAAOC,UAAU,CAAG,IAAI,CAAEC,YAAAA,EAAc,EAAK,CAAE,CAAGhB,GAAW,CAAC,SAGjF,GAIIY,UAAAA,GAAoBC,EAHfI,CAAAA,EAAAA,EAAAA,OAAAA,EAAOP,EAAO,SAOhBO,CAAAA,EAAAA,EAAAA,OAAAA,EAAOP,EAAO,SACvB,EAEaQ,EAAe,GAC1B,OAAIC,EAAwB,SACrBA,EAAS,GAAK,QAAUA,EAAS,GAAK,SAAW,kCCtF1D,SAASC,EAAgBtB,CAAQ,CAAEC,CAAS,EAC1C,IAAII,EAAOL,EAASuB,WAAW,GAAKtB,EAAUsB,WAAW,IAAMvB,EAASwB,QAAQ,GAAKvB,EAAUuB,QAAQ,IAAMxB,EAASyB,OAAO,GAAKxB,EAAUwB,OAAO,IAAMzB,EAAS0B,QAAQ,GAAKzB,EAAUyB,QAAQ,IAAM1B,EAAS2B,UAAU,GAAK1B,EAAU0B,UAAU,IAAM3B,EAAS4B,UAAU,GAAK3B,EAAU2B,UAAU,IAAM5B,EAAS6B,eAAe,GAAK5B,EAAU4B,eAAe,UACjW,EAAW,EACF,GACExB,EAAO,EACT,EAGAA,CAEX,CAmDe,SAASyB,EAAiBC,CAAa,CAAEC,CAAc,EACpE7B,CAAAA,EAAAA,EAAAA,CAAAA,EAAa,EAAGC,WAChB,IAAIJ,EAAWiC,CAAAA,EAAAA,EAAAA,OAAAA,EAAOF,GAClB9B,EAAYgC,CAAAA,EAAAA,EAAAA,OAAAA,EAAOD,GACnBE,EAAOZ,EAAgBtB,EAAUC,GACjCkC,EAAaC,KAAKC,GAAG,CAACC,CAAAA,EAAAA,EAAAA,OAAAA,EAAyBtC,EAAUC,IAC7DD,EAASuC,OAAO,CAACvC,EAASyB,OAAO,GAAKS,EAAOC,GAI7C,IAAIK,EAAmBC,OAAOnB,EAAgBtB,EAAUC,KAAe,CAACiC,GACpEQ,EAASR,EAAQC,CAAAA,EAAaK,CAAAA,EAElC,OAAOE,IAAAA,EAAe,EAAIA,CAC5B,2BC7DO,IAAMC,EAAkB,IAC7B,IAAM/B,EAAQ,IAAIF,KAAKlB,EAAMoB,KAAK,EAC5BD,EAAM,IAAID,KAAKlB,EAAMmB,GAAG,EAC9B,MAAO,CAACiC,CAAAA,EAAAA,EAAAA,OAAAA,EAAUhC,EAAOD,EAC3B,EAGakC,EAAsB,IACjC,IAAMC,EAAa,IAAIpC,KAAKlB,EAAMoB,KAAK,EACjCmC,EAAW,IAAIrC,KAAKlB,EAAMmB,GAAG,EAInC,GAAI,CAHegC,EAAgBnD,GAIjC,MAAO,CAAC,CACNwD,GAAI,GAAe7B,MAAAA,CAAZ3B,EAAMwD,EAAE,CAAC,KAAoCC,MAAA,CAAjC9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAO2B,EAAY,eACtCI,gBAAiB1D,EAAMwD,EAAE,CACzBzD,KAAM4D,CAAAA,EAAAA,EAAAA,OAAAA,EAAWL,GACjBM,UAAWN,EACXO,QAASN,EACTO,eAAgB,GAChBC,cAAe,GACfC,gBAAiB,GACjBC,aAAc,EACdC,cAAe,EACfC,cAAenE,EACfoE,SAAU,GACVC,WAAY,EACd,EAAE,CAGJ,IAAMC,EAA2B,EAAE,CAC7BC,EAAYjC,EAAiBkC,CAAAA,EAAAA,EAAAA,OAAAA,EAASjB,GAAWI,CAAAA,EAAAA,EAAAA,OAAAA,EAAWL,IAAe,EAE7EmB,EAAcd,CAAAA,EAAAA,EAAAA,OAAAA,EAAWL,GACvBoB,EAAYf,CAAAA,EAAAA,EAAAA,OAAAA,EAAWJ,GACzBU,EAAe,EAEnB,KAAOQ,GAAeC,GAAW,CAC/B,IAAMZ,EAAiBG,IAAAA,EACjBF,EAAgBX,CAAAA,EAAAA,EAAAA,OAAAA,EAAUqB,EAAaC,GACvCV,EAAkB,CAACF,GAAkB,CAACC,EAGtCY,EAAeb,EACjBR,EACAK,CAAAA,EAAAA,EAAAA,OAAAA,EAAWc,GAETG,EAAab,EACfR,EACAiB,CAAAA,EAAAA,EAAAA,OAAAA,EAASC,GAGPI,EAAuB,CAACD,EAAWE,OAAO,GAAKH,EAAaG,OAAO,IAAO,KAC1EV,EAAWS,GAAwB,IAAOb,GAAmBa,GAAwB,GAE3FP,EAASS,IAAI,CAAC,CACZvB,GAAI,GAAe7B,MAAAA,CAAZ3B,EAAMwD,EAAE,CAAC,KAAqCC,MAAA,CAAlC9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAO8C,EAAa,eACvCf,gBAAiB1D,EAAMwD,EAAE,CACzBzD,KAAM0E,EACNb,UAAWe,EACXd,QAASe,EACTd,eAAAA,EACAC,cAAAA,EACAC,gBAAAA,EACAC,aAAAA,EACAC,cAAeK,EACfJ,cAAenE,EACfoE,SAAAA,EACAC,WAAY,EACd,GAEAI,EAAcO,CAAAA,EAAAA,EAAAA,OAAAA,EAAQP,EAAa,GACnCR,GACF,CAEA,OAAOK,CACT,EAGaW,EAAoB,CAACX,EAA0BvE,IACnDuE,EAASY,MAAM,CAACC,GAAW/B,CAAAA,EAAAA,EAAAA,OAAAA,EAAU+B,EAAQpF,IAAI,CAAEA,IAI/CqF,EAAqB,CAChCd,EACAe,EACAC,IAEOhB,EAASY,MAAM,CAACC,GACrBI,CAAAA,EAAAA,EAAAA,OAAAA,EAAiBJ,EAAQpF,IAAI,CAAE,CAAEqB,MAAOiE,EAAWlE,IAAKmE,CAAQ,IAKvDE,EAAmB,GACvBC,EACJC,GAAG,CAAC1F,GAAS2F,GAAoB3F,IACjC4F,OAAO,CAAC5F,GAASqD,EAAoBrD,IAmB7B6F,EAAoB,GACxBvB,EAASY,MAAM,CAACC,GAAWA,EAAQf,QAAQ,EAAIe,EAAQd,UAAU,EAI7DyB,GAAsB,GAC1BxB,EAASY,MAAM,CAACC,GAAW,CAACA,EAAQf,QAAQ,EAIxC2B,GAAmB,IAC9B,IAAMC,EAAYb,EAAQvB,SAAS,CAAC1B,QAAQ,GACtC+D,EAAed,EAAQvB,SAAS,CAACzB,UAAU,GAejD,OAAOS,KAAKsD,GAAG,CAAC,GAHQC,KAFMC,GAAG,CAJFC,GALflB,EAAQtB,OAAO,CAAC3B,QAAQ,GACrBiD,EAAQtB,OAAO,CAAC1B,UAAU,GAOrB,MAJS6D,CAAAA,GAAAA,EAAmBC,CAAAA,EAWtD,EAKaK,GAAsB,IACfnB,EAAQvB,SAAS,CAAC1B,QAAQ,GACvBiD,EAAQvB,SAAS,CAACzB,UAAU,IAOtCoE,GAA2B,IAKtC,IAAMC,EAAc,aAEpB,GAAI,CAACrB,EAAQd,UAAU,CACrB,MAAO,CACLoC,eAAgBD,EAChBE,sBAAuB,GACvBC,QAAS,aACX,EAGF,IAAIF,EAAiB,GACjBC,EAAwB,GAe5B,OAbIvB,EAAQrB,cAAc,EAAIqB,EAAQpB,aAAa,CACjD0C,EAAiBD,EACRrB,EAAQrB,cAAc,EAC/B2C,EAAiB,4BACjBC,EAAwB,QACfvB,EAAQpB,aAAa,EAC9B0C,EAAiB,4BACjBC,EAAwB,SAExBD,EAAiB,aACjBC,EAAwB,aAGnB,CACLD,eAAAA,EACAC,sBAAAA,EACAC,QAASxB,EAAQnB,eAAe,CAAG,aAAe,aACpD,CACF,EAGa4C,GAA0B,CAACzB,EAAuB7D,IAC7D,UAAIA,EAAyB,CAAC6D,EAAQf,QAAQ,EAC1Ce,EAAQf,QAAQ,EAGbe,CAAAA,EAAQrB,cAAc,EAAIqB,EAAQpB,aAAa,EAAI,CAACoB,EAAQd,UAAU,EAIlEwC,GAA6B,GACxC,EAAaxC,UAAU,CAEnBc,EAAQrB,cAAc,CACjB,iBAAsDqB,MAAAA,CAArCA,EAAQjB,aAAa,CAAG,EAAE,aAAgDT,MAAA,CAArC0B,EAAQjB,aAAa,CAAG,EAAI,IAAM,IACtFiB,EAAQpB,aAAa,CACvB,kBAA6CoB,MAAAA,CAA3BA,EAAQlB,YAAY,CAAC,QAA0CR,MAAA,CAApC0B,EAAQlB,YAAY,CAAG,EAAI,IAAM,GAAG,QAEjF,OAAsCkB,MAAAA,CAA/BA,EAAQlB,YAAY,CAAG,EAAE,QAA4BR,MAAA,CAAtB0B,EAAQjB,aAAa,EAPpC,GAwBrByB,GAAsB,IACjC,IAAMvE,EAAQ,IAAIF,KAAKlB,EAAMoB,KAAK,EAIlC,GAAID,IAHYD,KAAKlB,EAAMmB,GAAG,GAGnBC,EAAO,CAChB,IAAM0F,EAAgB,IAAI5F,KAAKE,EAAM0D,OAAO,GAAM,MAClD,MAAO,CACL,GAAG9E,CAAK,CACRmB,IAAK2F,CACP,CACF,CAEA,OAAO9G,CACT,kBC9PO,IAAM+G,GAAwD,OAAC,CACpE5B,QAAAA,CAAO,CACP7D,KAAAA,CAAI,CACJ0F,KAAAA,EAAO,QAAQ,CACftI,UAAAA,EAAY,EAAE,CACduI,aAAAA,CAAY,CACb,CAAAzI,EACC,GAAI,CAAC2G,EAAQd,UAAU,CAAE,OAAO,KAEhC,IAAM6C,EAAcnI,CAAAA,EAAAA,EAAAA,EAAAA,EAClB,8DACA,iCACAL,GAkDIyI,EA5BJ,UAAIH,GAAoB1F,UAAAA,EACf8F,CApBU,KACnB,GAAI9F,SAAAA,EAAiB,CACnB,IAAM+F,EAAiBlC,EAAQrB,cAAc,QAG7C,GAAsB,CAFDmD,EAEuB,GAAArH,EAAAf,GAAA,EAACyI,GAAAA,GAAkBA,CAAAA,CAAC5I,UAAU,YACtE,CAAC2I,GAHgBJ,EAGuB,GAAArH,EAAAf,GAAA,EAAC0I,GAAAA,GAAiBA,CAAAA,CAAC7I,UAAU,YACrE,GAJiBuI,EAKd,KADsC,GAAArH,EAAAf,GAAA,EAAC2I,GAAAA,GAAmBA,CAAAA,CAAC9I,UAAU,WAE9E,QAEA,EAAYoF,cAAc,EAAI,CAACqB,EAAQpB,aAAa,CAAS,GAAAnE,EAAAf,GAAA,EAACyI,GAAAA,GAAkBA,CAAAA,CAAC5I,UAAU,YACvF,CAACyG,EAAQrB,cAAc,EAAIqB,EAAQpB,aAAa,CAAS,GAAAnE,EAAAf,GAAA,EAAC0I,GAAAA,GAAiBA,CAAAA,CAAC7I,UAAU,YACtF,EAASoF,cAAc,EAAKqB,EAAQpB,aAAa,CAE9C,KAFuD,GAAAnE,EAAAf,GAAA,EAAC2I,GAAAA,GAAmBA,CAAAA,CAAC9I,UAAU,WAG/F,KAOMyG,EAAQrB,cAAc,CACjB,KAA2BL,MAAA,CAAtB0B,EAAQjB,aAAa,EACxBiB,EAAQpB,aAAa,CACvB,GAA4BoB,MAAAA,CAAzBA,EAAQjB,aAAa,CAAC,KAAyBT,MAAA,CAAtB0B,EAAQjB,aAAa,EAEjD,GAA+BiB,MAAAA,CAA5BA,EAAQlB,YAAY,CAAG,EAAE,KAAyBR,MAAA,CAAtB0B,EAAQjB,aAAa,SAqB/D,EAsBE,GAAAtE,EAAAf,GAAA,EAAC4I,OAAAA,CACC/I,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACTmI,EACAQ,CA1CiB,KACrB,OAAQV,GACN,IAAK,QACH,MAAO,uCACT,KAAK,QACH,MAAO,+BACT,SACE,MAAO,0CACX,CACF,KAGS,mDAgCH,0BAEFW,MAAOC,CAzBY,KACrB,IAAMC,EAAa1C,EAAQhB,aAAa,CAACwD,KAAK,CACxCG,EAAalF,KAAKmF,IAAI,CAAC5C,EAAQjB,aAAa,CAAG,GAErD,GAAI5C,SAAAA,EAAiB,CACnB,IAAM0G,EAAW,IAAI9G,KAAKiE,EAAQhB,aAAa,CAAC/C,KAAK,EAE/C6G,EAAarF,KAAKsF,KAAK,CAACtF,KAAKC,GAAG,CAACsF,EADZpI,IAAI,CACmB+E,OAAO,GAAKkD,EAASlD,OAAO,IAAO,QAA4B,EAEjH,GAAIgD,EAAa,EACf,MAAO,GAAuBG,MAAAA,CAApBJ,EAAW,WAA0BC,MAAAA,CAAjBG,EAAW,QAAiBxE,MAAA,CAAXqE,EAAW,IAE9D,CAEA,MAAO,OAAsC3C,MAAAA,CAA/BA,EAAQlB,YAAY,CAAG,EAAE,QAAiC4D,MAAAA,CAA3B1C,EAAQjB,aAAa,CAAC,OAAgBT,MAAA,CAAXoE,EAC1E,cAYKV,IA9BI,IAiCX,EAEaiB,GAGR,OAAC,CAAEC,UAAAA,CAAS,CAAE3J,UAAAA,EAAY,EAAE,CAAE,CAAAF,EAcjC,MACE,GAAAoB,EAAAf,GAAA,EAAC4I,OAAAA,CACC/I,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,0CACA,iCACA,kCACAL,YAGD4J,CAtBY,KACf,OAAQD,GACN,IAAK,OACH,MAAO,GAAAzI,EAAAf,GAAA,EAAC0I,GAAAA,GAAiBA,CAAAA,CAAC7I,UAAU,WACtC,KAAK,QACH,MAAO,GAAAkB,EAAAf,GAAA,EAACyI,GAAAA,GAAkBA,CAAAA,CAAC5I,UAAU,WACvC,KAAK,OACH,MAAO,GAAAkB,EAAAf,GAAA,EAAC2I,GAAAA,GAAmBA,CAAAA,CAAC9I,UAAU,WACxC,SACE,MAAO,EACX,CACF,MAcF,kBCtIO,IAAM6J,GAAuB,OAAC,CACnCpD,QAAAA,CAAO,CACPqD,MAAAA,CAAK,CACLC,QAAAA,CAAO,CACPC,cAAAA,CAAa,CACbpH,KAAAA,EAAO,OAAO,CACd2F,aAAAA,CAAY,CACZ0B,WAAAA,CAAU,CASX,CAAAnK,EACOoK,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,EAAuB,MACjC,CAAEC,WAAAA,CAAU,CAAEC,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAEL,WAAYM,CAAa,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CACpF1F,GAAI,WAAsBC,MAAA,CAAX0B,EAAQ3B,EAAE,EACzB2F,KAAM,CACJC,KAAM,UACNC,QAASlE,CACX,EACAmE,SAAUnE,EAAQd,UAAU,EAAIc,EAAQf,QAAQ,GAG5CmF,EAAqBZ,GAAcM,EAGnCO,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAC3B,IAAMC,EAAclB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAO3G,MAAM,EAAG8H,SAASnB,EAAM3G,MAAM,CAAC+H,QAAQ,GAAGC,OAAO,CAAC,KAAM,KAAO,KACpFC,EAAWlD,GAAwBzB,EAAS7D,GAC5CyI,EAAmBlD,GAA2B1B,GAGpD,MAAO,CACL6E,UAAWpI,EAAa8H,GACxBI,SAAAA,EACAC,iBAAAA,EACAE,cAAeH,EAAWzI,EAAgB8D,EAAQvB,SAAS,CAAEtC,EAAM,CAAEI,YAAa,EAAK,GAAK,IAC9F,CACF,EAAG,CAACyD,EAASqD,EAAOlH,EAAK,EAGnB4I,EAAcT,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAC1B,IAAMU,EAAiBC,CAAAA,EAAAA,EAAAA,EAAAA,EAAU,SAC3BC,EAAiB9D,GAAyBpB,GAG1CmF,EAAWH,EAAeI,EAAE,CAACC,KAAK,CAAC,kCACnCC,EAAmBH,EACrB,OAAuBA,MAAAA,CAAhBA,CAAQ,CAAC,EAAE,CAAC,MAAoBA,MAAAA,CAAhBA,CAAQ,CAAC,EAAE,CAAC,MAAgB7G,MAAA,CAAZ6G,CAAQ,CAAC,EAAE,CAAC,KACnDH,EAAeI,EAAE,CAErB,MAAO,CACL/B,MAAO,CACL,GAAGA,CAAK,CACRkC,gBAAiBD,EACjBE,UAAW,OAEXC,UAAW,+DACXjE,QAAS4C,EAAqB,GAAM,CACtC,EACAsB,QAASR,CACX,CACF,EAAG,CAAC7B,EAAOrD,EAASoE,EAAmB,EAGjCuB,EAAerB,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAC3B,IAAMvC,EAAcnI,CAAAA,EAAAA,EAAAA,EAAAA,EAClB,0DACA,CAACwK,GAAsB,iBACvBW,EAAYW,OAAO,CAACpE,cAAc,CAClCyD,EAAYW,OAAO,CAACnE,qBAAqB,CACzCwD,EAAYW,OAAO,CAAClE,OAAO,CAC3B,aAIF,UAAIrF,GAAoBkI,UAAAA,EAAaQ,SAAS,CACrC,CACL9C,YAAAA,EACA6D,iBAAkBhM,CAAAA,EAAAA,EAAAA,EAAAA,EAChB,2CAEFiM,aAAcjM,CAAAA,EAAAA,EAAAA,EAAAA,EACZ,6DACAoG,EAAQd,UAAU,CAAG,cAAgB,eAEvC4G,YAAalM,CAAAA,EAAAA,EAAAA,EAAAA,EACX,mDAEFmM,oBAAqBnM,CAAAA,EAAAA,EAAAA,EAAAA,EACnB,iDAEJ,EAIK,CACLmI,YAAanI,CAAAA,EAAAA,EAAAA,EAAAA,EAAGmI,EAAa,OAC7B6D,iBAAkBhM,CAAAA,EAAAA,EAAAA,EAAAA,EAChB,gBACA,eAEFiM,aAAcjM,CAAAA,EAAAA,EAAAA,EAAAA,EACZ,8DAEFkM,YAAalM,CAAAA,EAAAA,EAAAA,EAAAA,EACX,sBAEFmM,oBAAqBnM,CAAAA,EAAAA,EAAAA,EAAAA,EACnB,qBAEJ,CACF,EAAG,CAACyK,EAAclI,EAAM6D,EAAQd,UAAU,CAAE6F,EAAYW,OAAO,CAAEtB,EAAmB,EAkEpF,MACE,GAAA3J,EAAAf,GAAA,EAACC,MAAAA,CACC0E,GAAI,SAAkCC,MAAA,CAAzB0B,EAAQhB,aAAa,CAACX,EAAE,EACrC/E,IAAK,IACHuK,EAAWmC,GACVvC,EAA0DwC,OAAO,CAAGD,CACvE,EACA3C,MAAO0B,EAAY1B,KAAK,CACxB9J,UAAWoM,EAAa5D,WAAW,CACnCuB,QAASA,EACTC,cAAeA,EACd,GAAIvD,EAAQd,UAAU,EAAIc,EAAQf,QAAQ,CAAG,CAAC,EAAI,CAAE,GAAG2E,CAAS,CAAE,GAAGD,CAAU,CAAE,UAEjFuC,CA5EsB,KACzB,IAAMrL,EAAQmF,EAAQhB,aAAa,OAGnC,UAAI7C,GAAoBkI,UAAAA,EAAaQ,SAAS,CAE1C,GAAApK,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWoM,EAAaC,gBAAgB,WAC3C,GAAAnL,EAAAf,GAAA,EAAC4I,OAAAA,CAAK/I,UAAWoM,EAAaE,YAAY,UAAGhL,EAAM2H,KAAK,GACvD6B,EAAaM,QAAQ,EAAIN,EAAaS,aAAa,EAClD,GAAArK,EAAAf,GAAA,EAAC4I,OAAAA,CAAK/I,UAAWoM,EAAaG,WAAW,UACtCzB,EAAaS,aAAa,GAG9B9E,EAAQd,UAAU,EACjB,GAAAzE,EAAAf,GAAA,EAACkI,GAAkBA,CACjB5B,QAASA,EACT7D,KAAMA,EACN0F,KAAK,QACLtI,UAAWoM,EAAaI,mBAAmB,CAC3CjE,aAAcA,OAStB,GAAArH,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWoM,EAAaC,gBAAgB,WAC3C,GAAAnL,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWoM,EAAaE,YAAY,WACtChL,EAAM2H,KAAK,CACXxC,EAAQd,UAAU,EAAI,CAACmF,EAAaM,QAAQ,EAC3C,GAAAlK,EAAAf,GAAA,EAACuJ,GAAiBA,CAChBC,UAAWlD,EAAQrB,cAAc,CAAG,QAAUqB,EAAQpB,aAAa,CAAG,OAAS,OAC/ErF,UAAU,YAId8K,CAAAA,EAAaM,QAAQ,EAAI3E,EAAQf,QAAQ,GAAKoF,EAAaS,aAAa,EACxE,GAAArK,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWoM,EAAaG,WAAW,WACrCzB,EAAaS,aAAa,CAC1B9E,EAAQd,UAAU,EACjB,GAAAzE,EAAAf,GAAA,EAACuJ,GAAiBA,CAChBC,UAAWlD,EAAQrB,cAAc,CAAG,QAAUqB,EAAQpB,aAAa,CAAG,OAAS,OAC/ErF,UAAU,YAKjByG,EAAQd,UAAU,EACjB,GAAAzE,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWoM,EAAaI,mBAAmB,UAC9C,GAAAtL,EAAAf,GAAA,EAACkI,GAAkBA,CACjB5B,QAASA,EACT7D,KAAMA,EACN0F,KAAMwC,UAAAA,EAAaQ,SAAS,CAAe,SAAW,QACtD/C,aAAcA,QAM1B,MAkBF,kBCnMO,IAAMsE,GAAoC,OAAC,CAAE5D,MAAAA,CAAK,CAAE6D,QAAAA,CAAO,CAAEC,iBAAAA,CAAgB,CAAEC,SAAAA,CAAQ,CAAE,CAAAlN,EAC9F,MACE,GAAAoB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,iEACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,8CACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,mHACb,GAAAkB,EAAAf,GAAA,EAAC8M,MAAAA,CAAIjN,UAAU,qBAAqBkN,KAAK,OAAOC,OAAO,eAAeC,QAAQ,qBAC5E,GAAAlM,EAAAf,GAAA,EAACkN,OAAAA,CAAKC,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,+EAGzE,GAAAvM,EAAAf,GAAA,EAACM,KAAAA,CAAGT,UAAU,iDACXiJ,IAEH,GAAA/H,EAAAf,GAAA,EAACQ,IAAAA,CAAEX,UAAU,mCACV8M,IAEFC,GACC,GAAA7L,EAAA0L,IAAA,EAACc,EAAAA,CAAMA,CAAAA,CACL3D,QAASiD,EACThN,UAAU,gGAEV,GAAAkB,EAAAf,GAAA,EAACwN,GAAAA,CAAQA,CAAAA,CAAC3N,UAAU,YAAY,sBAO5C,ECfM4N,GAAkB,CAACC,EAAwBC,IAE/C,EAAKpJ,CAAAA,EAAAA,EAAAA,OAAAA,EAAUmJ,EAASxM,IAAI,CAAEyM,EAASzM,IAAI,GAGpCwM,EAAS3I,SAAS,CAAG4I,EAAS3I,OAAO,EAAI2I,EAAS5I,SAAS,CAAG2I,EAAS1I,OAAO,CAG1E4I,GAAkB,IAC7B,IAAMC,EAA2B,CAC/BC,eAAgB,EAAE,EAGpB,GAAI,CAACrI,EAASvG,MAAM,CAClB,OAAO2O,EAIT,IAAME,EAAiB,IAAItI,EAAS,CAACuI,IAAI,CAAC,CAACC,EAAGC,KAC5C,IAAMC,EAAYF,EAAElJ,SAAS,CAACkB,OAAO,GAAKiI,EAAEnJ,SAAS,CAACkB,OAAO,UAC7D,IAAIkI,EAAwBA,EAGrBC,EAFapJ,OAAO,CAACiB,OAAO,GAAKiI,EAAEnJ,SAAS,CAACkB,OAAO,GACzCgI,CAAAA,EAAEjJ,OAAO,CAACiB,OAAO,GAAKgI,EAAElJ,SAAS,CAACkB,OAAO,GAE7D,GAEMoI,EAAoB,IAAIC,IAE9B,IAAK,IAAMhI,KAAWyH,EAAgB,CACpC,GAAIM,EAAkBE,GAAG,CAACjI,EAAQ3B,EAAE,EAClC,SAIF,IAAM6J,EAAmBT,EAAe1H,MAAM,CAACoI,GAAKhB,GAAgBnH,EAASmI,IAGvEC,EAA4B,EAAE,CAEpCF,EAAiBG,OAAO,CAACC,IACvB,IAAIC,EAAS,GAEb,IAAK,IAAMC,KAAUJ,EACnB,GAAII,EAAOC,KAAK,CAACN,GAAK,CAAChB,GAAgBmB,EAAcH,IAAK,CACxDK,EAAO5I,IAAI,CAAC0I,GACZC,EAAS,GACT,KACF,CAGGA,GACHH,EAAQxI,IAAI,CAAC,CAAC0I,EAAa,CAE/B,GAEA,IAAMI,EAAaN,EAAQxP,MAAM,CAK7B+P,EAAaC,GADS,CAACF,EAAa,GA7EZ,GA+ExBG,EA/EwB,GAiFxBF,EAhFwB,IAgFgBD,EAAa,GAGvDG,CAAAA,EAAUC,CADaF,GADvBD,CAAAA,EAjF0B,EAiFbI,CAC2BJ,EACZD,CAAAA,EAAa,IAG3CN,EAAQC,OAAO,CAAC,CAACG,EAAQQ,KACvBR,EAAOH,OAAO,CAACY,IACb,GAAIlB,EAAkBE,GAAG,CAACgB,EAAI5K,EAAE,EAAG,OAEnC,IAAM6K,EAAeF,EAAWH,EAEhCtB,EAAYC,cAAc,CAAC5H,IAAI,CAAC,CAC9BI,QAASiJ,EACTE,KAAMD,EACNE,MAAOT,EACPU,OAAQ,GAAKL,EACbM,WAAYZ,EAAa,CAC3B,GAEAX,EAAkBwB,GAAG,CAACN,EAAI5K,EAAE,CAC9B,EACF,GACC6J,EAAiBG,OAAO,CAACF,GAAKJ,EAAkBwB,GAAG,CAACpB,EAAE9J,EAAE,EAC3D,CAEA,OAAOkJ,CACT,EAaaiC,GAAwB,SACnCrK,CAAAA,MACAsK,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAqB,EAErB,GAAItK,EAASvG,MAAM,EAAI6Q,EACrB,MAAO,CAAEC,gBAAiBvK,EAAUwK,UAAW,CAAE,EAUnD,IAAMD,EAAkBE,IANLzK,EAAS,CAACuI,IAAI,CAAC,CAACC,EAAGC,KACpC,IAAMC,EAAYF,EAAElJ,SAAS,CAACkB,OAAO,GAAKiI,EAAEnJ,SAAS,CAACkB,OAAO,UAC7D,IAAIkI,EAAwBA,EACrBD,EAAGlJ,OAAO,CAACiB,OAAO,GAAKiI,EAAEnJ,SAAS,CAACkB,OAAO,GAAOgI,CAAAA,EAAEjJ,OAAO,CAACiB,OAAO,GAAKgI,EAAElJ,SAAS,CAACkB,OAAO,GACnG,GAE+BkK,KAAK,CAAC,EAAGJ,EAAa,GAC/CE,EAAYxK,EAASvG,MAAM,CAAG8Q,EAAgB9Q,MAAM,CAE1D,MAAO,CAAE8Q,gBAAAA,EAAiBC,UAAAA,CAAU,CACtC,kBCpHO,IAAMG,GAAsC,OAAC,CAClDC,aAAAA,CAAY,CACZ5K,SAAAA,CAAQ,CACR6K,cAAAA,CAAa,CACbC,iBAAAA,CAAgB,CAChBC,iBAAAA,CAAgB,CAChBC,YAAAA,CAAW,CACXC,iBAAAA,CAAgB,CAChBjO,KAAAA,CAAI,CACJkO,eAAAA,CAAc,CACf,CAAAhR,EAGOiR,EAAcC,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC/BlM,GAAI,UAA6CC,MAAA,CAAnC9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOuN,EAAc,eACnC/F,KAAM,CACJpJ,KAAMmP,EACN9F,KAAM,YACR,CACF,GAEMuG,EAAWD,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC5BlM,GAAI,UAA2FC,MAAA,CAAjF9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOqD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAAI,eACjF1G,KAAM,CACJpJ,KAAMiF,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAC9DzG,KAAM,aACR,CACF,GAEM0G,EAAWJ,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC5BlM,GAAI,UAA2FC,MAAA,CAAjF9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOqD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAAI,eACjF1G,KAAM,CACJpJ,KAAMiF,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAC9DzG,KAAM,aACR,CACF,GAEM2G,EAAWL,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC5BlM,GAAI,UAA2FC,MAAA,CAAjF9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOqD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAAI,eACjF1G,KAAM,CACJpJ,KAAMiF,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAC9DzG,KAAM,aACR,CACF,GAEM4G,EAAWN,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC5BlM,GAAI,UAA2FC,MAAA,CAAjF9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOqD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAAI,eACjF1G,KAAM,CACJpJ,KAAMiF,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAC9DzG,KAAM,aACR,CACF,GA0BM6G,EAAgB,CAACN,EAAUG,EAAUC,EAAUC,EAxBpCN,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC5BlM,GAAI,UAA2FC,MAAA,CAAjF9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOqD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAAI,eACjF1G,KAAM,CACJpJ,KAAMiF,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAC9DzG,KAAM,aACR,CACF,GAEiBsG,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC5BlM,GAAI,UAA2FC,MAAA,CAAjF9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOqD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAAI,eACjF1G,KAAM,CACJpJ,KAAMiF,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAC9DzG,KAAM,aACR,CACF,GAEiBsG,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC5BlM,GAAI,UAA2FC,MAAA,CAAjF9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOqD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAAI,eACjF1G,KAAM,CACJpJ,KAAMiF,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAI,GAC9DzG,KAAM,aACR,CACF,GAE4F,QAI5F,IAAI9E,EAASvG,MAAM,EAAWyR,EA4MvBlO,QAAAA,EAtML,GAAA1B,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,gDACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,iBACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,sJACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,sBACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,iCAAwB,gBAK3C,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CACCL,IAAKgR,EAAYzG,UAAU,CAC3BtK,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,gCACA0Q,EAAYS,MAAM,EAAI,wBAGvB5L,EAAS0K,KAAK,CAAC,EAAG,GAAGtJ,GAAG,CAAC,QAWV8J,QAVd,GAAA5P,EAAAf,GAAA,EAAC0J,GAAoBA,CAEnBpD,QAASA,EACTqD,MAAO,CAAE3G,OAAQ,OAAQ0M,MAAO,MAAO,EACvC9F,QAAS,IACP0H,EAAEC,eAAe,GACjBhB,EAAiBjK,EAAQzB,eAAe,EACxC2L,EAAiBlK,EAAQhB,aAAa,CACxC,EACA7C,KAAK,MACLqH,WAAY6G,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAgBnG,OAAO,GAAvBmG,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyBhM,EAAE,IAAK2B,EAAQ3B,EAAE,EATjD2B,EAAQ3B,EAAE,IAYlBc,EAASvG,MAAM,CAAG,GACjB,GAAA6B,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,uFAA6E,KACvF4F,EAASvG,MAAM,CAAG,EAAE,mBAsKOsS,CA9JnB,KACrB,IAAMC,EAAWC,MAAMC,IAAI,CAAC,CAAEzS,OAAQ,CAAE,EAAG,CAAC0S,EAAGC,IAAM1L,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ4K,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GAAIa,IAEvGC,EAAiB,CAAC,KACtB,IAAMC,EAAc,IAAIC,IACxBvM,EAASkJ,OAAO,CAACrI,IACf,GAAIA,EAAQd,UAAU,EAAIc,EAAQf,QAAQ,CAAE,CAC1C,IAAM0M,EAAU3L,EAAQzB,eAAe,CAClCkN,EAAYxD,GAAG,CAAC0D,IAAUF,EAAYG,GAAG,CAACD,EAAS,EAAE,EAC1DF,EAAYI,GAAG,CAACF,GAAU/L,IAAI,CAACI,EACjC,CACF,GAEA,IAAM8L,EAA4B,EAAE,CAkBpC,OAjBAL,EAAYpD,OAAO,CAAC,IAClB0D,EAAcrE,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAE/M,IAAI,CAAC+E,OAAO,GAAKiI,EAAEhN,IAAI,CAAC+E,OAAO,IAC9D,IAAMqM,EAAqBD,CAAa,CAAC,EAAE,CACrCE,EAAoBF,CAAa,CAACA,EAAcnT,MAAM,CAAG,EAAE,CAC3DsT,EAAgBf,EAASgB,SAAS,CAACC,GAAOnO,CAAAA,EAAAA,EAAAA,OAAAA,EAAUmO,EAAKJ,EAAmBpR,IAAI,GAChFyR,EAAclB,EAASgB,SAAS,CAACC,GAAOnO,CAAAA,EAAAA,EAAAA,OAAAA,EAAUmO,EAAKH,EAAkBrR,IAAI,GAE/EsR,GAAiB,GAAKG,GAAe,GACvCP,EAASlM,IAAI,CAAC,CACZI,QAASgM,EACTE,cAAAA,EACAG,YAAAA,EACAC,QAASD,EAAcH,EAAgB,EACvCpK,aAAcmK,EAAkBrN,aAAa,EAGnD,GACOkN,CACT,KAEMS,EAAmB,CAAC,KACxB,IAAMC,EAAqD,EAAE,CACvDC,EAA+B,EAAE,CAmBvC,MAhBAC,IAFyBlB,EAAe,CAAC9D,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAEuE,aAAa,CAAGtE,EAAEsE,aAAa,EAAItE,EAAE0E,OAAO,CAAG3E,EAAE2E,OAAO,EAErGjE,OAAO,CAACxN,IACnB,IAAI8R,EAAW,GACf,IAAK,IAAIpB,EAAI,EAAGA,EAAIkB,EAAK7T,MAAM,CAAE2S,IAAK,CACpC,IAAMqB,EAAMH,CAAI,CAAClB,EAAE,CACnB,GAAIqB,EAAInE,KAAK,CAACoE,GAAYhS,EAAMqR,aAAa,CAAGW,EAASR,WAAW,EAAIxR,EAAMwR,WAAW,CAAGQ,EAASX,aAAa,EAAG,CACnHU,EAAIhN,IAAI,CAAC/E,GACT2R,EAAW5M,IAAI,CAAC,CAAE,GAAG/E,CAAK,CAAE+R,IAAKrB,CAAE,GACnCoB,EAAW,GACX,KACF,CACF,CACKA,IACHF,EAAK7M,IAAI,CAAC,CAAC/E,EAAM,EACjB2R,EAAW5M,IAAI,CAAC,CAAE,GAAG/E,CAAK,CAAE+R,IAAKH,EAAK7T,MAAM,CAAG,CAAE,GAErD,GACO4T,CACT,KAEM,CAAE9C,gBAAAA,CAAe,CAAEC,UAAAA,CAAS,CAAE,CAAGH,GACrC+C,EAAiBhM,GAAG,CAACyK,GAAKA,EAAEhL,OAAO,EAAG,GAGlC8M,EAAgBP,EAAiBxM,MAAM,CAAC7F,GAAKwP,EAAgBzQ,IAAI,CAACkP,GAAKA,EAAE9J,EAAE,GAAKnE,EAAE8F,OAAO,CAAC3B,EAAE,GAC5F0O,EAAUpD,EAAY,EAEtBqD,EAAqBT,EAAiB3T,MAAM,CAAG,EAAI6E,KAAKwD,GAAG,IAAIsL,EAAiBhM,GAAG,CAACyK,GAAKA,EAAEkB,aAAa,GAAK,EAEnH,GAAIK,IAAAA,EAAiB3T,MAAM,CAAQ,OAAO,KAE1C,IAAMqU,EAAUV,EAAiB3T,MAAM,CAAG,EAAI6E,KAAKsD,GAAG,IAAIwL,EAAiBhM,GAAG,CAACyK,GAAKA,EAAE4B,GAAG,GAAK,EAAI,EAE5FM,EAAcH,EAAU,IAAMtP,KAAKsD,GAAG,CAAC,EAAGkM,GAG9C,MACF,GAAAxS,EAAAf,GAAA,EAACC,MAAAA,CACCwT,mBAAiB,OACjB5T,UAAU,gDAER,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,iBACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,sJACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,sBACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,iCAAwB,gBAK3C,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,sBAAsB8J,MAAO,CAAE3G,OAAQ,GAAe4B,MAAA,CAfvD4O,GAAAA,EAA0B,GAe6B,KAAI,WACvE,GAAAzS,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,yCACZ4R,EAAS5K,GAAG,CAAC,CAAC6L,EAAKgB,KAClB,IAAMC,EAAOvC,CAAa,CAACsC,EAAS,CAEpC,MACA,GAAA3S,EAAA0L,IAAA,EAACxM,MAAAA,CACCL,IAAK+T,EAAKxJ,UAAU,CAEpBtK,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,2EACAyT,EAAKtC,MAAM,EAAI,cAEjBuC,cAAe,KACb,GAAInD,EAAa,CACf,IAAMoD,EAAU,IAAIxR,KAAKqQ,GACzBmB,EAAQC,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC1BpD,EAAiBmD,EACnB,CACF,YAECT,EACE/M,MAAM,CAAC0N,GAAiBA,EAAcvB,aAAa,GAAKkB,GACxD7M,GAAG,CAAC,QAqBa8J,QApBhB,GAAA5P,EAAAf,GAAA,EAACC,MAAAA,CAECJ,UAAU,gBACV8J,MAAO,CACLqK,IAAK,GAAqCpP,MAAA,CAAlCmP,GAAAA,EAAcb,GAAG,CAAe,EAAE,MAC1CzD,KAAM,MACNC,MAAO,QAA0C9K,MAAA,CAAlCmP,IAAAA,EAAcnB,OAAO,CAAO,QAAsChO,MAAA,CAAhC,CAACmP,EAAcnB,OAAO,CAAG,GAAK,EAAE,OACjF5P,OAAQ,MACV,WAEA,GAAAjC,EAAAf,GAAA,EAAC0J,GAAoBA,CACnBpD,QAASyN,EAAczN,OAAO,CAC9B8B,aAAc2L,EAAc3L,YAAY,CACxCuB,MAAO,CAAE3G,OAAQ,OAAQ0M,MAAO,MAAO,EACvC9F,QAAS,IACP0H,EAAEC,eAAe,GACjBhB,EAAiBwD,EAAczN,OAAO,CAACzB,eAAe,EACtD2L,EAAiBuD,EAAczN,OAAO,CAAChB,aAAa,CACtD,EACA7C,KAAMA,EACNqH,WAAY6G,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAgBnG,OAAO,GAAvBmG,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyBhM,EAAE,IAAKoP,EAAczN,OAAO,CAAC3B,EAAE,IAnBjEoP,EAAczN,OAAO,CAAC3B,EAAE,IAuBlC0O,GAAWK,IAAaJ,GACvB,GAAAvS,EAAA0L,IAAA,EAACxM,MAAAA,CACCJ,UAAU,qEACV8J,MAAO,CACLqK,IAAK,GAAqBpP,MAAA,CAAlB,GAAkB,MAC1B6K,KAAM,MACNwE,MAAO,KACT,EACArK,QAAS,IAAMsK,QAAQC,GAAG,CAAC,0BAC5B,IACGlE,EAAU,aAlDXyD,EAuDT,WAMZ,KAzMS,IA4MX,ECnSMU,GAAW,OAAC,CAChBC,KAAAA,CAAI,CACJnT,KAAAA,CAAI,CACJ0S,cAAAA,CAAa,CACbU,SAAAA,CAAQ,CACRxK,WAAAA,CAAU,CAOX,CAAAnK,EACO,CAAC4U,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvC,CAACC,EAAeC,EAAiB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,EAA0C,MAC9E,CAACG,EAAeC,EAAiB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAiB,GACrDK,EAAepV,EAAAA,MAAY,CAAiB,MAE5CqV,EAAsBR,GAAczK,EAEpCkL,EAA8BC,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,IAC9C,GAAI,CAACH,EAAavI,OAAO,CAAE,OAAO,EAElC,IAAM2I,EAAOJ,EAAavI,OAAO,CAAC4I,qBAAqB,GAGvD,OAAOpR,KAAKsD,GAAG,CAAC,EAAGtD,KAAKwD,GAAG,CAAC,GADbxD,KAAKsF,KAAK,CAAC+L,CADhBC,EAAUH,EAAKlB,GAAG,EACGkB,EAAKlS,MAAM,CAAI,KAEhD,EAAG,EAAE,EAECsS,EAAmBL,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,KACnCT,EAAc,GAChB,EAAG,EAAE,EAECe,EAAmBN,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,KAC9BnL,IACH0K,EAAc,IACdG,EAAiB,MACjBE,EAAiB,GAErB,EAAG,CAAC/K,EAAW,EAET0L,EAAkBP,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,IAClC,IAAMQ,EAAST,EAA4B1D,EAAE+D,OAAO,EACpDV,EAAiB,CAAEe,EAAGpE,EAAEqE,OAAO,CAAEP,EAAG9D,EAAE+D,OAAO,GAC7CR,EAAiBY,EACnB,EAAG,CAACT,EAA4B,EAEhCtV,EAAAA,SAAe,CAAC,KACd,GAAI,CAACoK,EAAY,OAEjB,IAAM8L,EAAwB,IAC5B,GAAId,EAAavI,OAAO,CAAE,CACxB,IAAM2I,EAAOJ,EAAavI,OAAO,CAAC4I,qBAAqB,GAEvD,GAAI7D,EAAEqE,OAAO,EAAIT,EAAKzF,IAAI,EAAI6B,EAAEqE,OAAO,EAAIT,EAAKjB,KAAK,EACjD3C,EAAE+D,OAAO,EAAIH,EAAKlB,GAAG,EAAI1C,EAAE+D,OAAO,EAAIH,EAAKW,MAAM,CAAE,CACrD,IAAMJ,EAAST,EAA4B1D,EAAE+D,OAAO,EACpDV,EAAiB,CAAEe,EAAGpE,EAAEqE,OAAO,CAAEP,EAAG9D,EAAE+D,OAAO,GAC7CR,EAAiBY,GACjBjB,EAAc,GAChB,CACF,CACF,EAGA,OADAsB,SAASC,gBAAgB,CAAC,YAAaH,GAChC,IAAME,SAASE,mBAAmB,CAAC,YAAaJ,EACzD,EAAG,CAAC9L,EAAYkL,EAA4B,EAE5C,IAAMiB,EAAchB,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,IACb,IAAb3D,EAAE4E,MAAM,EACVtC,EAAcgB,EAElB,EAAG,CAACA,EAAehB,EAAc,EAEjC,MACE,GAAA7S,EAAA0L,IAAA,EAACxM,MAAAA,CACCL,IAAKkV,EACLjV,UAAU,8CACV8J,MAAO,CAAE3G,OAAQ,MAAO,EACxBmT,aAAcb,EACdc,aAAcb,EACdc,YAAab,EACb5L,QAASqM,YAGT,GAAAlV,EAAAf,GAAA,EAACsW,GAAAA,CACCpV,KAAMA,EACNmT,KAAMA,EACNO,cAAeA,EACf2B,SAAUxB,IAGXT,IAGP,EAGMgC,GAAe,OAAC,CACpBpV,KAAAA,CAAI,CACJmT,KAAAA,CAAI,CACJO,cAAAA,CAAa,CACb2B,SAAAA,CAAQ,CAMT,CAAA5W,EACO,CAAEwK,WAAAA,CAAU,CAAEkH,OAAAA,CAAM,CAAE,CAAGR,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC1ClM,GAAI,QAAsC0P,MAAAA,CAA9BvR,CAAAA,EAAAA,EAAAA,OAAAA,EAAO5B,EAAM,cAAc,KAAQ0D,MAAA,CAALyP,GAC1C/J,KAAM,CACJpJ,KAAMA,EACNmT,KAAAA,EACAoB,OAAQb,EACRrK,KAAM,iBACR,CACF,GAEA,MACE,GAAAxJ,EAAAf,GAAA,EAACC,MAAAA,CACCL,IAAKuK,EACLtK,UAAU,kCAGhB,EAEa2W,GAAkC,OAAC,CAC9CnG,aAAAA,CAAY,CACZzJ,OAAAA,CAAM,CACN0J,cAAAA,CAAa,CACbC,iBAAAA,CAAgB,CAChBG,iBAAAA,CAAgB,CAChBD,YAAAA,CAAW,CACXgG,eAAAA,CAAc,CACdjG,iBAAAA,CAAgB,CAChBG,eAAAA,CAAc,CACf,CAAAhR,EACO+W,EAAQhF,MAAMC,IAAI,CAAC,CAAEzS,OAAQ,EAAG,EAAG,CAAC0S,EAAGC,IAAMA,GAG7C8E,EAAc/L,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAEnBxE,EADaO,EAAiBC,GACCyJ,GACrC,CAACzJ,EAAQyJ,EAAa,EAGnBuG,EAAiBhM,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAAM5D,EAAkB2P,GAAc,CAACA,EAAY,EAC5EE,EAAmBjM,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAAM3D,GAAoB0P,GAAc,CAACA,EAAY,EAGhF,CAAE7I,eAAAA,CAAc,CAAE,CAAGlD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAC1BgD,GAAgBiJ,GACtB,CAACA,EAAiB,EAGfC,EAAsBlM,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAClCmM,CAAAA,EAAAA,EAAAA,CAAAA,EAAQ1G,GACJ,CACEgE,KAAM,IAAIhS,OAAOgB,QAAQ,GACzB2T,QAAS,IAAI3U,OAAOiB,UAAU,EAChC,EACA,KACJ,CAAC+M,EAAa,EAsHhB,MACE,GAAAtP,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,0CAEb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,+EACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,iDACZiD,CAAAA,EAAAA,EAAAA,OAAAA,EAAOuN,EAAc,UAExB,GAAAtP,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,mFACA6W,CAAAA,EAAAA,EAAAA,CAAAA,EAAQ1G,GACJ,sBACA,4CAEHvN,CAAAA,EAAAA,EAAAA,OAAAA,EAAOuN,EAAc,UAKzBsG,EAAYzX,MAAM,CAAG,GACpB,GAAA6B,EAAAf,GAAA,EAACoQ,GAASA,CACRC,aAAcA,EACd5K,SAAUmR,EACVtG,cAAeA,EACfC,iBAAkBA,EAClBC,iBAAkBA,EAClBC,YAAaA,EACbC,iBAAkBA,EAClBjO,KAAK,MACLkO,eAAgBA,IAKnBgG,IAAAA,EAAYzX,MAAM,CAlJrB,GAAA6B,EAAAf,GAAA,EAAC0M,GAAQA,CACP5D,MAAM,sBACN6D,QAASoK,CAAAA,EAAAA,EAAAA,CAAAA,EAAQ1G,GACb,0DACA,GAAwCzL,MAAA,CAArC9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOuN,EAAc,gBAAgB,wBAC5CzD,iBAAkB6D,EAClB5D,SAAU,KACR,IAAMgH,EAAU,IAAIxR,KAAKgO,GACzBwD,EAAQC,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC1BpD,EAAiBmD,EACnB,IAMF,GAAA9S,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,yCAAyC8E,GAAG,+BACxD+R,EAAM7P,GAAG,CAAC,CAACwN,EAAMxC,IAChB,GAAA9Q,EAAA0L,IAAA,EAACxM,MAAAA,CAECJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,yEACA2R,IAAM6E,EAAMxX,MAAM,CAAG,GAAK,wBAE5ByK,MAAO,CAAE3G,OAAQ,MAAO,YAGxB,GAAAjC,EAAAf,GAAA,EAACC,MAAAA,CACCgX,mBAAiB,OACjBpX,UAAU,sJAEV,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,uBACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,iCACZiD,CAAAA,EAAAA,EAAAA,OAAAA,EAAOgR,CAAAA,EAAAA,EAAAA,OAAAA,EAASzD,EAAcgE,GAAO,OAExC,GAAAtT,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,yCACZiD,CAAAA,EAAAA,EAAAA,OAAAA,EAAOgR,CAAAA,EAAAA,EAAAA,OAAAA,EAASzD,EAAcgE,GAAO,YAM5C,GAAAtT,EAAAf,GAAA,EAACoU,GAAAA,CACCC,KAAMA,EACNnT,KAAMmP,EACNvG,WAAY,CAAC,CAAC6G,EACdiD,cAAe,IACb,GAAInD,EAAa,CACf,IAAMoD,EAAU,IAAIxR,KAAKgO,GACzBwD,EAAQC,QAAQ,CAACO,EAAMoB,EAAQ,EAAG,GAClC/E,EAAiBmD,EACnB,CACF,WAEC/F,EAAejH,GAAG,CAAC,QAoBV8J,EAeQA,EA/BhB,GAAI,EAHwBrK,OAAO,CAACvB,SAAS,CACZ1B,QAAQ,KAAOgR,EAE9B,OAAO,KAEzB,IAAM6C,EAAgBhQ,GAAiBiQ,EAAO7Q,OAAO,EAC/C8Q,EAAY3P,GAAoB0P,EAAO7Q,OAAO,EAEpD,MACE,GAAAvF,EAAAf,GAAA,EAAC0J,GAAoBA,CAEnBpD,QAAS6Q,EAAO7Q,OAAO,CACvBqD,MAAO,CACL3G,OAAQ,GAAiB4B,MAAA,CAAdsS,EAAc,MACzBG,SAAU,WACVrD,IAAK,GAAapP,MAAA,CAAVwS,EAAU,MAClB3H,KAAM,GAAe7K,MAAA,CAAZuS,EAAO1H,IAAI,CAAC,KACrBC,MAAO,GAAgB9K,MAAA,CAAbuS,EAAOzH,KAAK,CAAC,KACvBC,OACEgB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAgBnG,OAAO,GAAvBmG,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyBhM,EAAE,IAAKwS,EAAO7Q,OAAO,CAAC3B,EAAE,CAAG,GACpDwS,EAAOxH,MAAM,CACf2H,aAAc,MACdC,OAAQJ,EAAOvH,UAAU,CAAG,kBAAoB,MAClD,EACAhG,QAAS,IACP0H,EAAEC,eAAe,GACjB,IAAMiG,EAAY1B,SAAS2B,cAAc,CAAC,sBACvCD,GACDf,CAAAA,EAAelK,OAAO,CAAGiL,EAAUE,SAAS,EAE9CnH,EAAiB4G,EAAO7Q,OAAO,CAACzB,eAAe,EAC/C2L,EAAiB2G,EAAO7Q,OAAO,CAAChB,aAAa,CAC/C,EACA7C,KAAK,MACLqH,WAAY6G,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAgBnG,OAAO,GAAvBmG,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyBhM,EAAE,IAAKwS,EAAO7Q,OAAO,CAAC3B,EAAE,EAxBxDwS,EAAO7Q,OAAO,CAAC3B,EAAE,CA2B5B,OAzEGkN,IA+ERiF,GACC,GAAA/V,EAAA0L,IAAA,EAACxM,MAAAA,CACCJ,UAAU,yEACV8J,MAAO,CACLqK,IAAK,GAAsEpP,MAAA,CAAnE,CAACkS,EAAoBzC,IAAI,CAAGyC,EAAoBE,OAAO,CAAG,IAAM,GAAG,MAC3E/C,MAAO,KACT,YAEA,GAAAlT,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,4EACf,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,uDA4CzB,eC9TA,IAAMuU,GAAW,OAAC,CAChB1B,IAAAA,CAAG,CACH2B,KAAAA,CAAI,CACJC,SAAAA,CAAQ,CACRV,cAAAA,CAAa,CACb9J,WAAAA,CAAU,CAOX,CAAAnK,EACO,CAAC4U,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvC,CAACC,EAAeC,EAAiB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,EAA0C,MAC9E,CAACG,EAAeC,EAAiB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAiB,GACrDK,EAAepV,EAAAA,MAAY,CAAiB,MAE5CqV,EAAsBR,GAAczK,EAEpCkL,EAA8BC,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,IAC9C,GAAI,CAACH,EAAavI,OAAO,CAAE,OAAO,EAElC,IAAM2I,EAAOJ,EAAavI,OAAO,CAAC4I,qBAAqB,GAGvD,OAAOpR,KAAKsD,GAAG,CAAC,EAAGtD,KAAKwD,GAAG,CAAC,GADbxD,KAAKsF,KAAK,CAAC+L,CADhBC,EAAUH,EAAKlB,GAAG,EACGkB,EAAKlS,MAAM,CAAI,KAEhD,EAAG,EAAE,EAECsS,EAAmBL,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,KACnCT,EAAc,GAChB,EAAG,EAAE,EAECe,EAAmBN,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,KAC9BnL,IACH0K,EAAc,IACdG,EAAiB,MACjBE,EAAiB,GAErB,EAAG,CAAC/K,EAAW,EAET0L,EAAkBP,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,IAClC,IAAMQ,EAAST,EAA4B1D,EAAE+D,OAAO,EACpDV,EAAiB,CAAEe,EAAGpE,EAAEqE,OAAO,CAAEP,EAAG9D,EAAE+D,OAAO,GAC7CR,EAAiBY,EACnB,EAAG,CAACT,EAA4B,EAEhCtV,EAAAA,SAAe,CAAC,KACd,GAAI,CAACoK,EAAY,OAEjB,IAAM8L,EAAwB,IAC5B,GAAId,EAAavI,OAAO,CAAE,CACxB,IAAM2I,EAAOJ,EAAavI,OAAO,CAAC4I,qBAAqB,GACvD,GAAI7D,EAAEqE,OAAO,EAAIT,EAAKzF,IAAI,EAAI6B,EAAEqE,OAAO,EAAIT,EAAKjB,KAAK,EACnD3C,EAAE+D,OAAO,EAAIH,EAAKlB,GAAG,EAAI1C,EAAE+D,OAAO,EAAIH,EAAKW,MAAM,CAAE,CACnD,IAAMJ,EAAST,EAA4B1D,EAAE+D,OAAO,EACpDV,EAAiB,CAAEe,EAAGpE,EAAEqE,OAAO,CAAEP,EAAG9D,EAAE+D,OAAO,GAC7CR,EAAiBY,GACjBjB,EAAc,GAChB,CACF,CACF,EAGA,OADAsB,SAASC,gBAAgB,CAAC,YAAaH,GAChC,IAAME,SAASE,mBAAmB,CAAC,YAAaJ,EACzD,EAAG,CAAC9L,EAAYkL,EAA4B,EAE5C,IAAMiB,EAAchB,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,IACb,IAAb3D,EAAE4E,MAAM,EACVtC,EAAcgB,EAElB,EAAG,CAACA,EAAehB,EAAc,EAEjC,MACE,GAAA7S,EAAA0L,IAAA,EAACxM,MAAAA,CACCL,IAAKkV,EACLjV,UAAU,0FACVsW,aAAcb,EACdc,aAAcb,EACdc,YAAab,EACb5L,QAASqM,YAET,GAAAlV,EAAAf,GAAA,EAACsW,GAAYA,CACX5D,IAAKA,EACL2B,KAAMA,EACNO,cAAeA,EACf2B,SAAUxB,IAGXT,IAGP,EAEMgC,GAAe,OAAC,CACpB5D,IAAAA,CAAG,CACH2B,KAAAA,CAAI,CACJO,cAAAA,CAAa,CACb2B,SAAAA,CAAQ,CAMT,CAAA5W,EACO,CAAEwK,WAAAA,CAAU,CAAEkH,OAAAA,CAAM,CAAE,CAAGR,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC1ClM,GAAI,QAAqC0P,MAAAA,CAA7BvR,CAAAA,EAAAA,EAAAA,OAAAA,EAAO4P,EAAK,cAAc,KAAQ9N,MAAA,CAALyP,GACzC/J,KAAM,CACJpJ,KAAMwR,EACN2B,KAAAA,EACAoB,OAAQb,EACRrK,KAAM,iBACR,CACF,GAEA,MACE,GAAAxJ,EAAAf,GAAA,EAACC,MAAAA,CACCL,IAAKuK,EACLtK,UAAU,kCAGhB,EAEa8X,GAAoC,OAAC,CAChDtH,aAAAA,CAAY,CACZzJ,OAAAA,CAAM,CACN0J,cAAAA,CAAa,CACbC,iBAAAA,CAAgB,CAChBqH,gBAAAA,CAAe,CACflH,iBAAAA,CAAgB,CAChBD,YAAAA,CAAW,CACXgG,eAAAA,CAAc,CACdjG,iBAAAA,CAAgB,CAChBG,eAAAA,CAAc,CACf,CAAAhR,EACOkY,EAAmBjN,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAC/B,IAAMpE,EAAYuK,CAAAA,EAAAA,GAAAA,OAAAA,EAAYV,EAAc,CAAEW,aAAc,CAAE,GACxDvK,EAAUqR,CAAAA,EAAAA,GAAAA,OAAAA,EAAUzH,EAAc,CAAEW,aAAc,CAAE,GACpD+G,EAAOrG,MAAMC,IAAI,CAAC,CAAEzS,OAAQ,CAAE,EAAG,CAAC0S,EAAGC,IAAM1L,CAAAA,EAAAA,EAAAA,OAAAA,EAAQK,EAAWqL,IAC9DmG,EAAaD,EAAKtF,SAAS,CAACC,GAAOqE,CAAAA,EAAAA,EAAAA,CAAAA,EAAQrE,IAEjD,MAAO,CACLlM,UAAAA,EACAC,QAAAA,EACAsR,KAAAA,EACAC,WAAAA,CACF,CACF,EAAG,CAAC3H,EAAa,EAEX,CAAE0H,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAE,CAAGH,EACvBnB,EAAQhF,MAAMC,IAAI,CAAC,CAAEzS,OAAQ,EAAG,EAAG,CAAC0S,EAAGC,IAAMA,GAE7CoG,EAAerN,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAEpBrE,EADaI,EAAiBC,GACEiR,EAAiBrR,SAAS,CAAEqR,EAAiBpR,OAAO,EAC1F,CAACG,EAAQiR,EAAiBrR,SAAS,CAAEqR,EAAiBpR,OAAO,CAAC,EAE3DmQ,EAAiBhM,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAAM5D,EAAkBiR,GAAe,CAACA,EAAa,EAC9EpB,EAAmBjM,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAAM3D,GAAoBgR,GAAe,CAACA,EAAa,EAElFnB,EAAsBlM,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAClCoN,KAAAA,EACI,CACAtE,SAAUsE,EACV3D,KAAM,IAAIhS,OAAOgB,QAAQ,GACzB2T,QAAS,IAAI3U,OAAOiB,UAAU,EAChC,EACE,KACJ,CAAC0U,EAAW,EA+Hd,MACE,GAAAjX,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,0CACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CACCiY,mBAAiB,OACjBrY,UAAU,kEAEV,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,mCACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,6CACf,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,6DACZkY,EAAKlR,GAAG,CAAC,CAAC6L,EAAKb,IACd,GAAA9Q,EAAA0L,IAAA,EAACxM,MAAAA,CAECJ,UAAU,sDACV+J,QAAS,IAAMgO,EAAgBlF,aAE/B,GAAA3R,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,gCACA,oBAEC4C,CAAAA,EAAAA,EAAAA,OAAAA,EAAO4P,EAAK,SAEf,GAAA3R,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,mFACA6W,CAAAA,EAAAA,EAAAA,CAAAA,EAAQrE,GACJ,sBACA,4CAEH5P,CAAAA,EAAAA,EAAAA,OAAAA,EAAO4P,EAAK,SAhBVb,WAyBf,GAAA9Q,EAAAf,GAAA,EAACoQ,GAASA,CACRC,aAAcA,EACd5K,SAAUmR,EACVtG,cAAeA,EACfC,iBAAkBA,EAClBC,iBAAkBA,EAClBC,YAAaA,EACbC,iBAAkBA,EAClBjO,KAAK,OACLkO,eAAgBA,IAIjBsH,IAAAA,EAAa/Y,MAAM,CAtKtB,GAAA6B,EAAAf,GAAA,EAAC0M,GAAQA,CACP5D,MAAM,sBACN6D,QAAQ,kEACRC,iBAAkB6D,EAClB5D,SAAU,IAAM6D,EAAiBL,KAKnC,GAAAtP,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,wFAAwF8E,GAAG,+BACxG,GAAA5D,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,0DACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,mDACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,oBACZ6W,EAAM7P,GAAG,CAAC,CAACwN,EAAMxC,IAChB,GAAA9Q,EAAA0L,IAAA,EAACxM,MAAAA,CAECJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,yEACA2R,IAAM6E,EAAMxX,MAAM,CAAG,GAAK,wBAE5ByK,MAAO,CAAE3G,OAAQ,MAAO,YAExB,GAAAjC,EAAAf,GAAA,EAACC,MAAAA,CACCgX,mBAAiB,OACjBpX,UAAU,sJAEV,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,sBACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,iCACZiD,CAAAA,EAAAA,EAAAA,OAAAA,EAAOgR,CAAAA,EAAAA,EAAAA,OAAAA,EAAS,IAAIzR,KAAQgS,GAAO,aAKzC0D,EAAKlR,GAAG,CAAC,IAER,GAAM,CAAEiH,eAAAA,CAAc,CAAE,CAAGF,GADPxH,EAAkByQ,EAAkBnE,IAGxD,MACE,GAAA3R,EAAAf,GAAA,EAACoU,GAAQA,CAEP1B,IAAKA,EACL2B,KAAMA,EACNvK,WAAY,CAAC,CAAC6G,EACdiD,cAAe,IACb,GAAInD,EAAa,CACf,IAAMoD,EAAU,IAAIxR,KAAKqQ,GACzBmB,EAAQC,QAAQ,CAACO,EAAMoB,EAAQ,EAAG,GAClC/E,EAAiBmD,EACnB,CACF,WAEC/F,EAAejH,GAAG,CAAC,QAmBJ8J,EAcEA,EA7BhB,GAAI,EAHwBrK,OAAO,CAACvB,SAAS,CACZ1B,QAAQ,KAAOgR,EAE9B,OAAO,KAEzB,IAAM6C,EAAgBhQ,GAAiBiQ,EAAO7Q,OAAO,EAC/C8Q,EAAY3P,GAAoB0P,EAAO7Q,OAAO,EAEpD,MACE,GAAAvF,EAAAf,GAAA,EAAC0J,GAAoBA,CAEnBpD,QAAS6Q,EAAO7Q,OAAO,CACvBqD,MAAO,CACL3G,OAAQ,GAAiB4B,MAAA,CAAdsS,EAAc,MACzBG,SAAU,WACVrD,IAAK,GAAapP,MAAA,CAAVwS,EAAU,MAClB3H,KAAM,GAAe7K,MAAA,CAAZuS,EAAO1H,IAAI,CAAC,KACrBC,MAAO,GAAgB9K,MAAA,CAAbuS,EAAOzH,KAAK,CAAC,KACvBC,OAAQgB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAgBnG,OAAO,GAAvBmG,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyBhM,EAAE,IAAKwS,EAAO7Q,OAAO,CAAC3B,EAAE,CAAG,GAAKwS,EAAOxH,MAAM,CAC9E2H,aAAc,MACdC,OAAQJ,EAAOvH,UAAU,CAAG,kBAAoB,MAClD,EACAhG,QAAS,IACP0H,EAAEC,eAAe,GACjB,IAAMiG,EAAY1B,SAAS2B,cAAc,CAAC,uBACtCD,GACFf,CAAAA,EAAelK,OAAO,CAAGiL,EAAUE,SAAS,EAE9CnH,EAAiB4G,EAAO7Q,OAAO,CAACzB,eAAe,EAC/C2L,EAAiB2G,EAAO7Q,OAAO,CAAChB,aAAa,CAC/C,EACA7C,KAAK,OACLqH,WAAY6G,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAgBnG,OAAO,GAAvBmG,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyBhM,EAAE,IAAKwS,EAAO7Q,OAAO,CAAC3B,EAAE,EAtBxDwS,EAAO7Q,OAAO,CAAC3B,EAAE,CAyB5B,IAhDK,GAAwB0P,MAAAA,CAArB3B,EAAIyF,WAAW,GAAG,KAAQvT,MAAA,CAALyP,GAmDnC,KA3EKA,MAgFVyC,GACC,GAAA/V,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,uFACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,kCACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CACCJ,UAAU,6BACV8J,MAAO,CACLqK,IAAK,GAAsEpP,MAAA,CAAnE,CAACkS,EAAoBzC,IAAI,CAAGyC,EAAoBE,OAAO,CAAG,IAAM,GAAG,MAC3EvH,KAAM,GAA4C7K,MAAA,CAAzCkS,EAAqBpD,QAAQ,CAAG,EAAK,IAAI,KAClDhE,MAAO,GAAiB9K,MAAA,CAAd,EAAK,EAAK,IAAI,IAC1B,YAEA,GAAA7D,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,4EACf,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,+DAgEjC,0CC/WO,IAAMuY,GAAoB,OAAC,CAChCjX,MAAAA,CAAK,CACLwI,MAAAA,CAAK,CACLC,QAAAA,CAAO,CACPC,cAAAA,CAAa,CACbpH,KAAAA,EAAO,OAAO,CACdqH,WAAAA,CAAU,CACVuO,UAAAA,EAAY,EAAI,CAChBC,YAAAA,EAAc,EAAI,CAUnB,CAAA3Y,EACOoK,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,EAAuB,MACjC,CAAEC,WAAAA,CAAU,CAAEC,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAEL,WAAYM,CAAa,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CACpF1F,GAAI,SAAkBC,MAAA,CAATzD,EAAMwD,EAAE,EACrB2F,KAAM,CACJC,KAAM,QACNC,QAASrJ,CACX,EAEAsJ,SAAU,CAAC6N,GAAenX,EAAMqE,UAAU,EAAIrE,EAAMoE,QAAQ,GAIxDmF,EAAqBZ,GAAcM,EAGnCO,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAC3B,IAAMrI,EAAQ,IAAIF,KAAKlB,EAAMoB,KAAK,EAGlC,MAAO,CACLA,MAAAA,EACA4I,UAAWpI,EAJO4G,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAO3G,MAAM,EAAG8H,SAASnB,EAAM3G,MAAM,CAAC+H,QAAQ,GAAGC,OAAO,CAAC,KAAM,KAAO,MAKxFuN,UAAW9W,EAAeN,GAC1BiK,cAAe5I,EAAgBD,EAAOE,EAAM,CAAEI,YAAa,EAAK,EAClE,CACF,EAAG,CAAC1B,EAAOwI,EAAOlH,EAAK,EAGjB4I,EAAcT,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAC1B,IAAMU,EAAiBC,CAAAA,EAAAA,EAAAA,EAAAA,EAAU,SAG3BE,EAAWH,EAAeI,EAAE,CAACC,KAAK,CAAC,kCACnCC,EAAmBH,EACrB,OAAuBA,MAAAA,CAAhBA,CAAQ,CAAC,EAAE,CAAC,MAAoBA,MAAAA,CAAhBA,CAAQ,CAAC,EAAE,CAAC,MAAgB7G,MAAA,CAAZ6G,CAAQ,CAAC,EAAE,CAAC,KACnDH,EAAeI,EAAE,CAErB,MAAO,CACL,GAAG/B,CAAK,CACRkC,gBAAiBD,EACjBE,UAAWrJ,UAAAA,EAAmB,OAAS,OACvC+V,aAAc/V,UAAAA,EAAmB,MAAQ,MAEzCsJ,UAAW,+DACXjE,QAAS4C,EAAqB,GAAM,CACtC,CACF,EAAG,CAACf,EAAOlH,EAAMiI,EAAmB,EAG9BuB,EAAerB,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAC3B,IAAM6N,EAAmBH,GAAe,CAACnX,EAAMqE,UAAU,EAAI,CAACrE,EAAMoE,QAAQ,OAG5E,UAAI9C,GAAoBkI,UAAAA,EAAaQ,SAAS,CACrC,CACL9C,YAAanI,CAAAA,EAAAA,EAAAA,EAAAA,EACX,4DACA,CAACwK,GAAsB+N,GAAoB,iBAC3C,CAAC/N,GAAsB,CAAC+N,GAAoB,iBAC5C,OAEFvM,iBAAkBhM,CAAAA,EAAAA,EAAAA,EAAAA,EAChB,8BACA,eAEFiM,aAAcjM,CAAAA,EAAAA,EAAAA,EAAAA,EACZ,6DACA,eAEFkM,YAAalM,CAAAA,EAAAA,EAAAA,EAAAA,EACX,mCACA,iBAEJ,EAIK,CACLmI,YAAanI,CAAAA,EAAAA,EAAAA,EAAAA,EACX,4DACA,CAACwK,GAAsB+N,GAAoB,iBAC3C,CAAC/N,GAAsB,CAAC+N,GAAoB,iBAC5C,OAEFvM,iBAAkBhM,CAAAA,EAAAA,EAAAA,EAAAA,EAChB,gBACA,eAEFiM,aAAcjM,CAAAA,EAAAA,EAAAA,EAAAA,EACZ,8DAEFkM,YAAalM,CAAAA,EAAAA,EAAAA,EAAAA,EACX,qBAEJ,CACF,EAAG,CAACyK,EAAclI,EAAMiI,EAAoB4N,EAAanX,EAAMqE,UAAU,CAAErE,EAAMoE,QAAQ,CAAC,EAmC1F,MACE,GAAAxE,EAAAf,GAAA,EAACC,MAAAA,CACC0E,GAAI,SAAkBC,MAAA,CAATzD,EAAMwD,EAAE,EACrB/E,IAAK,IACHuK,EAAWmC,GACVvC,EAA0DwC,OAAO,CAAGD,CACvE,EACA3C,MAAO0B,EACPxL,UAAWoM,EAAa5D,WAAW,CACnCuB,QAASA,EACTC,cAAeA,EACd,GAAI,EAAOrE,UAAU,EAAKrE,EAAMoE,QAAQ,GAAI+S,EAAgD,CAAC,EAAnC,CAAE,GAAGpO,CAAS,CAAE,GAAGD,CAAU,CAAO,UAzCjG,UAAIxH,GAAoBkI,UAAAA,EAAaQ,SAAS,CAE1C,GAAApK,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWoM,EAAaC,gBAAgB,WAC1CmM,GACC,GAAAtX,EAAAf,GAAA,EAAC4I,OAAAA,CAAK/I,UAAWoM,EAAaE,YAAY,UAAGhL,EAAM2H,KAAK,GAEzDuP,GAAa1N,EAAaS,aAAa,EACtC,GAAArK,EAAAf,GAAA,EAAC4I,OAAAA,CAAK/I,UAAWoM,EAAaG,WAAW,UACtCzB,EAAaS,aAAa,MASnC,GAAArK,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWoM,EAAaC,gBAAgB,WAC1CmM,GACC,GAAAtX,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWoM,EAAaE,YAAY,UAAGhL,EAAM2H,KAAK,GAExDuP,GAAa1N,EAAaS,aAAa,EACtC,GAAArK,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWoM,EAAaG,WAAW,UACrCzB,EAAaS,aAAa,OAuBvC,kBC5JO,IAAMsN,GAAoD,OAAC,CAChErI,aAAAA,CAAY,CACZzJ,OAAAA,CAAM,CACN0J,cAAAA,CAAa,CACbC,iBAAAA,CAAgB,CAChBC,iBAAAA,CAAgB,CACjB,CAAA7Q,EACO,CAAE+C,SAAAA,CAAQ,CAAE,CAAGiW,CAAAA,EAAAA,EAAAA,CAAAA,IAEfC,EAAYhS,EACfP,MAAM,CAAClF,GAASoD,CAAAA,EAAAA,EAAAA,OAAAA,EAAU,IAAIlC,KAAKlB,EAAMoB,KAAK,EAAG8N,IACjDrC,IAAI,CAAC,CAACC,EAAGC,IAAM,IAAI7L,KAAK4L,EAAE1L,KAAK,EAAE0D,OAAO,GAAK,IAAI5D,KAAK6L,EAAE3L,KAAK,EAAE0D,OAAO,IAEzE,MACE,GAAAlF,EAAA0L,IAAA,EAACxM,MAAAA,CACC4Y,iBAAe,OACfhZ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,4FACAwC,EAAW,kBAAoB,kBAGjC,GAAA3B,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,wEACA,0CAEA,GAAAa,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,2GACf,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,0BACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,6CACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,wCACf,GAAAkB,EAAAf,GAAA,EAAC8Y,KAAAA,CAAGjZ,UAAU,4CACXiD,CAAAA,EAAAA,EAAAA,OAAAA,EAAOuN,EAAc,oBAG1B,GAAAtP,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,wCACb,GAAAkB,EAAAf,GAAA,EAAC8M,MAAAA,CAAIjN,UAAU,2BAA2BkN,KAAK,OAAOC,OAAO,eAAeC,QAAQ,qBAClF,GAAAlM,EAAAf,GAAA,EAACkN,OAAAA,CAAKC,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,kDAEvE,GAAAvM,EAAA0L,IAAA,EAACjM,IAAAA,CAAEX,UAAU,qCACV+Y,EAAU1Z,MAAM,CAAC,IAAE0Z,IAAAA,EAAU1Z,MAAM,CAAS,QAAU,SAAS,yBAMxE,GAAA6B,EAAAf,GAAA,EAAC+Y,GAAAA,UAAUA,CAAAA,CAAClZ,UAAU,sBACnB+Y,IAAAA,EAAU1Z,MAAM,CACf,GAAA6B,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,gFACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,6FACf,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,0BACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,+JACb,GAAAkB,EAAAf,GAAA,EAAC8M,MAAAA,CAAIjN,UAAU,2BAA2BkN,KAAK,OAAOC,OAAO,eAAeC,QAAQ,qBAClF,GAAAlM,EAAAf,GAAA,EAACkN,OAAAA,CAAKC,cAAc,QAAQC,eAAe,QAAQC,YAAa,IAAKC,EAAE,+EAG3E,GAAAvM,EAAAf,GAAA,EAACQ,IAAAA,CAAEX,UAAU,qDAA4C,wBAGzD,GAAAkB,EAAAf,GAAA,EAACQ,IAAAA,CAAEX,UAAU,oCAA2B,sCAM5C,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,qBACZ+Y,EAAU/R,GAAG,CAAC,GACb,GAAA9F,EAAA0L,IAAA,EAAChN,GAAAA,EAAIA,CAAAA,CAEHI,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,8CACAoQ,IAAkBnP,EAAMwD,EAAE,CACtB,2FACA,2GAENiF,QAAS,KACP2G,EAAiBpP,EAAMwD,EAAE,EACzB6L,EAAiBrP,EACnB,YAEA,GAAAJ,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,gHACf,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,0BACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,kDACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,yCACkBiB,EAAMwD,EAAE,CAAG,wBAE5BxD,EAAM2H,KAAK,GAEd,GAAA/H,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,0CACAoQ,IAAkBnP,EAAMwD,EAAE,CAAG,iBAAmB,uBAGpD,GAAA5D,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,sCACAoQ,IAAkBnP,EAAMwD,EAAE,CAAG,mBAAqB,8BAElD,GAAA5D,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,wDACAoQ,IAAkBnP,EAAMwD,EAAE,CAAG,iBAAmB,2BAEhD,GAAA5D,EAAAf,GAAA,EAAC8M,MAAAA,CAAIjN,UAAU,cAAckN,KAAK,OAAOC,OAAO,eAAeC,QAAQ,qBACrE,GAAAlM,EAAAf,GAAA,EAACkN,OAAAA,CAAKC,cAAc,QAAQC,eAAe,QAAQC,YAAa,IAAKC,EAAE,oDAG3E,GAAAvM,EAAA0L,IAAA,EAAC7D,OAAAA,CAAK/I,UAAU,wBACbiD,CAAAA,EAAAA,EAAAA,OAAAA,EAAO,IAAIT,KAAKlB,EAAMoB,KAAK,EAAG,UAAU,MAAIO,CAAAA,EAAAA,EAAAA,OAAAA,EAAO,IAAIT,KAAKlB,EAAMmB,GAAG,EAAG,qBAvC1EnB,EAAMwD,EAAE,SAkD7B,kBCzGA,IAAMqU,GAAU,OAAC,CAAC9X,KAAAA,CAAI,CAACoT,SAAAA,CAAQ,CAAC1K,QAAAA,CAAO,CAACqP,eAAAA,CAAc,CAMrD,CAAAtZ,EACO,CAAEwK,WAAAA,CAAU,CAAEkH,OAAAA,CAAM,CAAE,CAAGR,CAAAA,EAAAA,GAAAA,EAAAA,EAAa,CAC1ClM,GAAI,WAAsCC,MAAA,CAA3B9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAO5B,EAAM,eAC5BoJ,KAAM,CACJpJ,KAAMA,EACNqJ,KAAM,SACR,CACF,GAEA,MACE,GAAAxJ,EAAAf,GAAA,EAACC,MAAAA,CACCL,IAAKuK,EACLP,QAASA,EACT/J,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,+IACA+Y,EACI,+BACA,sCACJ5H,GAAU,uCAGXiD,GAGP,EAEM4E,GAAkB,CAAC/X,EAAsBuR,KAC7C,IAAMjO,EAAa,IAAIpC,KAAKlB,EAAMoB,KAAK,EACjCmC,EAAW,IAAIrC,KAAKlB,EAAMmB,GAAG,EAC7B6W,EAAW,IAAI9W,KAAKqQ,GACpB0G,EAAS,IAAI/W,KAAKqQ,GAGxB,OAFA0G,EAAOtF,QAAQ,CAAC,GAAI,GAAI,GAAI,KAErBrP,GAAc2U,GAAU1U,GAAYyU,CAC7C,EAEM7U,GAAkB,IACtB,IAAMG,EAAa,IAAIpC,KAAKlB,EAAMoB,KAAK,EACjCmC,EAAW,IAAIrC,KAAKlB,EAAMmB,GAAG,EAE7B+W,EAAY,IAAIhX,KAAKoC,EAAWvB,WAAW,GAAIuB,EAAWtB,QAAQ,GAAIsB,EAAWrB,OAAO,IACxFkW,EAAU,IAAIjX,KAAKqC,EAASxB,WAAW,GAAIwB,EAASvB,QAAQ,GAAIuB,EAAStB,OAAO,IAEtF,OAAOiW,EAAUpT,OAAO,KAAOqT,EAAQrT,OAAO,EAChD,EAEMsT,GAAiB,CAACC,EAAiB5S,IAChCgE,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KACb,IAAM6O,EAAyB,IAAIzH,IAC7B0H,EAAiB,IAAI1H,IA0F3B,OAxFAwH,EAAM7K,OAAO,CAAC,CAACgL,EAAMC,KACnB,IAAMpT,EAAYmT,CAAI,CAAC,EAAE,CACnBlT,EAAU,IAAIpE,KAAKsX,CAAI,CAAC,EAAE,EAChClT,EAAQqN,QAAQ,CAAC,GAAI,GAAI,GAAI,KAE7B,IAAM+F,EAAajT,EAAOP,MAAM,CAAClF,IAC/B,IAAMsD,EAAa,IAAIpC,KAAKlB,EAAMoB,KAAK,EACjCmC,EAAW,IAAIrC,KAAKlB,EAAMmB,GAAG,EACnC,OAAOmC,GAAcgC,GAAW/B,GAAY8B,CAC9C,GAEMsL,EAAwB,EAAE,CAChC+H,EAAWlL,OAAO,CAACxN,IACjB,IAAMsD,EAAa,IAAIpC,KAAKlB,EAAMoB,KAAK,EACjCmC,EAAW,IAAIrC,KAAKlB,EAAMmB,GAAG,EAE7BkQ,EAAgBmH,EAAKlH,SAAS,CAACC,GAAOnO,CAAAA,EAAAA,EAAAA,OAAAA,EAAUmO,EAAKjO,IACrDkO,EAAcgH,EAAKlH,SAAS,CAACC,GAAOnO,CAAAA,EAAAA,EAAAA,OAAAA,EAAUmO,EAAKhO,IAEnDoV,EAActH,KAAAA,EAAuBA,EAAgB,EACrDuH,EAAYpH,KAAAA,EAAqBA,EAAc,EAE9BH,CAAAA,KAAAA,GAAwBG,KAAAA,GAAuBlO,EAAa+B,GAAa9B,EAAW+B,CAAAA,GAGzGqL,EAAe5L,IAAI,CAAC,CAClB/E,MAAAA,EACAqR,cAAesH,EACfnH,YAAaoH,EACbnH,QAASmH,EAAYD,EAAc,EACnCtU,WAAYlB,GAAgBnD,EAC9B,EAEJ,GAEA,IAAM6R,EAAelB,EAAe9D,IAAI,CAAC,CAACC,EAAGC,IAC3C,EAAMsE,aAAa,GAAKtE,EAAEsE,aAAa,CAC9BvE,EAAEuE,aAAa,CAAGtE,EAAEsE,aAAa,CAEnCtE,EAAE0E,OAAO,CAAG3E,EAAE2E,OAAO,EAGxBE,EAAoB,EAAE,CACtBC,EAAgB,EAAE,CAExBC,EAAarE,OAAO,CAACqL,IACnB,IAAMC,EAAeN,EAAKxJ,KAAK,CAAC6J,EAAUxH,aAAa,CAAEwH,EAAUrH,WAAW,CAAG,GAOjF,GAAI,CANasH,EAAalL,KAAK,CAAC2D,IAClC,IAAMwH,EAASpX,CAAAA,EAAAA,EAAAA,OAAAA,EAAO4P,EAAK,cAE3B,OAAOyH,EADcT,CAAAA,EAAevH,GAAG,CAAC+H,IAAW,EAErD,GAGE,OAGF,IAAIjH,EAAW,GACf,IAAK,IAAIpB,EAAI,EAAGA,EAAIkB,EAAK7T,MAAM,CAAE2S,IAAK,CACpC,IAAMqB,EAAMH,CAAI,CAAClB,EAAE,CAMnB,GAAI,CALgBqB,EAAI3T,IAAI,CAAC6a,GAC3BJ,EAAUxH,aAAa,EAAI4H,EAAczH,WAAW,EACpDqH,EAAUrH,WAAW,EAAIyH,EAAc5H,aAAa,EAGpC,CAChBU,EAAIhN,IAAI,CAAC8T,GACTlH,EAAW5M,IAAI,CAAC,CAAE,GAAG8T,CAAS,CAAE9G,IAAKrB,CAAE,GACvCoB,EAAW,GACX,KACF,CACF,CAEKA,IACHF,EAAK7M,IAAI,CAAC,CAAC8T,EAAU,EACrBlH,EAAW5M,IAAI,CAAC,CAAE,GAAG8T,CAAS,CAAE9G,IAAKH,EAAK7T,MAAM,CAAG,CAAE,IAGvD+a,EAAatL,OAAO,CAAC+D,IACnB,IAAMwH,EAASpX,CAAAA,EAAAA,EAAAA,OAAAA,EAAO4P,EAAK,cACrByH,EAAeT,EAAevH,GAAG,CAAC+H,IAAW,EACnDR,EAAexH,GAAG,CAACgI,EAAQC,EAAe,EAC5C,EACF,GAEAV,EAAuBvH,GAAG,CAAC0H,EAAW9G,EACxC,GAEO,CAAE2G,uBAAAA,EAAwBC,eAAAA,CAAe,CAClD,EAAG,CAACF,EAAO5S,EAAO,EAGPyT,GAAsC,OAAC,CAClDhK,aAAAA,CAAY,CACZzJ,OAAAA,CAAM,CACN0J,cAAAA,CAAa,CACbC,iBAAAA,CAAgB,CAChBqH,gBAAAA,CAAe,CACflH,iBAAAA,CAAgB,CAChBD,YAAAA,CAAW,CACXD,iBAAAA,CAAgB,CAChBG,eAAAA,CAAc,CACf,CAAAhR,EACqB2a,CAAAA,EAAAA,EAAAA,EAAAA,IAIpB,IAAMC,EAAoB3P,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAChC,IAAM4P,EAAaC,CAAAA,EAAAA,GAAAA,OAAAA,EAAapK,GAC1BqK,EAAWC,CAAAA,EAAAA,GAAAA,OAAAA,EAAWtK,GACtBlH,EAAW4H,CAAAA,EAAAA,GAAAA,OAAAA,EAAYyJ,EAAY,CAAExJ,aAAc,CAAE,GACrD4J,EAAS9C,CAAAA,EAAAA,GAAAA,OAAAA,EAAU4C,EAAU,CAAE1J,aAAc,CAAE,GAE/C+G,EAAO,EAAE,CACXrF,EAAMvJ,EACV,KAAOuJ,GAAOkI,GACZ7C,EAAK7R,IAAI,CAACwM,GACVA,EAAMvM,CAAAA,EAAAA,EAAAA,OAAAA,EAAQuM,EAAK,GAGrB,IAAM8G,EAAQ,EAAE,CAChB,IAAK,IAAI3H,EAAI,EAAGA,EAAIkG,EAAK7Y,MAAM,CAAE2S,GAAK,EACpC2H,EAAMtT,IAAI,CAAC6R,EAAK5H,KAAK,CAAC0B,EAAGA,EAAI,IAG/B,MAAO,CAAE2I,WAAAA,EAAYE,SAAAA,EAAUvR,SAAAA,EAAUyR,OAAAA,EAAQ7C,KAAAA,EAAMyB,MAAAA,CAAM,CAC/D,EAAG,CAACnJ,EAAa,EAEXwK,EAAcjQ,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAC1BhE,EAAOP,MAAM,CAAClF,IACZ,IAAMsD,EAAa,IAAIpC,KAAKlB,EAAMoB,KAAK,EACjCmC,EAAW,IAAIrC,KAAKlB,EAAMmB,GAAG,EACnC,OAAOmC,GAAc8V,EAAkBK,MAAM,EACtClW,GAAY6V,EAAkBpR,QAAQ,GAE/C,CAACvC,EAAQ2T,EAAkBpR,QAAQ,CAAEoR,EAAkBK,MAAM,CAAC,EAG1D,CAAEnB,uBAAAA,CAAsB,CAAE,CAAGF,GAAegB,EAAkBf,KAAK,CAAEqB,GACrE,CAAEC,QAAAA,CAAO,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,EAAAA,IAEpB,GAAIF,IAAAA,EAAY3b,MAAM,CACpB,MACE,GAAA6B,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,2DACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,yCACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,iEACZ,CAAC,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,WAAW,CAACgH,GAAG,CAACmU,GAClF,GAAAja,EAAAf,GAAA,EAACC,MAAAA,CAAkBJ,UAAU,mEAC1Bmb,GADOA,MAMd,GAAAja,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,mDACb,GAAAkB,EAAAf,GAAA,EAAC0M,GAAQA,CACP5D,MAAM,uBACN6D,QAAS,GAAqC/H,MAAA,CAAlC9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOuN,EAAc,aAAa,mDAC9CzD,iBAAkB6D,EAClB5D,SAAU,IAAM6D,EAAiBL,UAKtCyK,eAAAA,GACC,GAAA/Z,EAAAf,GAAA,EAAC0Y,GAAgBA,CACfrI,aAAcA,EACdzJ,OAAQA,EACR0J,cAAeA,EACfC,iBAAkBA,EAClBC,iBAAkBA,OAO5B,IAAMyK,EAAuB,CAACvI,EAAWkG,KACvC,IAAMK,EAAiBvG,EAAIvP,QAAQ,KAAOkN,EAAalN,QAAQ,GACzD+X,EAAenE,CAAAA,EAAAA,EAAAA,CAAAA,EAAQrE,GAKvByI,EAAoBC,EADO/U,MAAM,CAAClF,GAAS+X,GAAgB/X,EAAOuR,IACjCxT,MAAM,CAGvCkU,EAAgBJ,EADShF,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAEiF,GAAG,CAAGhF,EAAEgF,GAAG,EACxB/C,KAAK,CAAC,EAPd,GAQrBkD,EAAU8H,EARW,EAWrBE,EAASjI,EAAckI,MAAM,CAAC,CAACjU,EAAKlG,IAAU4C,KAAKsD,GAAG,CAACA,EAAKlG,EAAM+R,GAAG,EAAG,IACxEqI,EAAkBlI,EACpBmI,IACA,CAACH,EAAS,GAbK,GAenB,MACE,GAAAta,EAAA0L,IAAA,EAAA1L,EAAA0a,QAAA,YACE,GAAA1a,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,mDACb,GAAAkB,EAAAf,GAAA,EAAC4I,OAAAA,CAAK/I,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACf,qFACAgb,EAAe,sBAAwBjC,EAAiB,kCAAoC,6BAE3FnW,CAAAA,EAAAA,EAAAA,OAAAA,EAAO4P,EAAK,OAEdjC,GAAewI,GACd,GAAAlY,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR7b,UAAU,2HACV+J,QAAS,IACP0H,EAAEC,eAAe,GACjBoK,WAAW,IAAMjL,EAAiBgC,GAAM,IAC1C,WAEA,GAAA3R,EAAAf,GAAA,EAACwN,GAAAA,CAAQA,CAAAA,CAAC3N,UAAU,4BAK1B,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,WAAW8J,MAAO,CAAE3G,OAAQ,GAAmB4B,MAAA,CAAhB2W,EAAgB,KAAI,YAC/DnI,EAAcvM,GAAG,CAAC+U,QAqBDjL,QApBhB,GAAA5P,EAAAf,GAAA,EAACC,MAAAA,CAECJ,UAAU,WACV8J,MAAO,CACLqK,IAAK,GAAuBpP,MAAA,CAApBgX,GAAAA,EAAG1I,GAAG,CAAc,MAC5BzD,KAAM,MACNC,MAAOkM,EAAGhJ,OAAO,CAAG,EAChB,QAA+BhO,MAAA,CAAvBgX,IAAAA,EAAGhJ,OAAO,CAAO,QAA8BhO,MAAA,CAAxB,CAACgX,EAAGhJ,OAAO,CAAG,GAAK,KAAK,OACvD,mBACJjD,OAAQ,GAAKiM,EAAG1I,GAAG,WAGrB,GAAAnS,EAAAf,GAAA,EAACoY,GAAiBA,CAChBjX,MAAOya,EAAGza,KAAK,CACfsB,KAAK,QACLmH,QAAS,IACP0H,EAAEC,eAAe,GACjBhB,EAAiBqL,EAAGza,KAAK,CAACwD,EAAE,EAC5B6L,EAAiBoL,EAAGza,KAAK,CAC3B,EACA2I,WAAY6G,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAgBnG,OAAO,GAAvBmG,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAyBhM,EAAE,IAAKiX,EAAGza,KAAK,CAACwD,EAAE,CACvD2T,YAAa,CAACsD,EAAGpW,UAAU,IApBxBoW,EAAGza,KAAK,CAACwD,EAAE,IAyBnB0O,GACC,GAAAtS,EAAA0L,IAAA,EAACxM,MAAAA,CACCJ,UAAU,iEACV8J,MAAO,CACL0N,SAAU,WACVrD,IAAK,GAAmCpP,MAAA,CAAhC4W,IAAgC,MACxC/L,KAAM,KACR,EACA7F,QAAS,IACP0H,EAAEC,eAAe,GACjBqG,EAAgBlF,EAClB,YACD,KAtEiB3O,KAAKsD,GAAG,CAAC,EAAG8T,EATX,GAgFI,gBAMjC,EAEA,MACE,GAAApa,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,2DACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,yCACb,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CACCiY,mBAAiB,OACjBrY,UAAU,mFAET,CAAC,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,WAAW,CAACgH,GAAG,CAACmU,GAClF,GAAAja,EAAAf,GAAA,EAACC,MAAAA,CAAkBJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EAC5B,uCACA,yBAEC8a,EAAQa,SAAS,CAAC,EAAG,IAJdb,MASd,GAAAja,EAAAf,GAAA,EAAC+Y,GAAAA,UAAUA,CAAAA,CAAClZ,UAAU,kBACpB,GAAAkB,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,wDACZ0a,EAAkBf,KAAK,CAAC3S,GAAG,CAAC,CAAC8S,EAAMC,IAClCD,EAAK9S,GAAG,CAAC,CAAC6L,EAAKgB,KAEb,IAAMkF,EAAYiB,CADCJ,EAAuBtH,GAAG,CAACyH,IAAc,EAAE,EACjCvT,MAAM,CAACuV,GAAMA,EAAGpJ,aAAa,GAAKkB,GACzDuF,EAAiBvG,EAAIvP,QAAQ,KAAOkN,EAAalN,QAAQ,GAE/D,MACE,GAAApC,EAAAf,GAAA,EAACgZ,GAAAA,CAEC9X,KAAMwR,EACNuG,eAAgBA,EAChBrP,QAAS,IAAMgO,EAAgBlF,YAE9BuI,EAAqBvI,EAAKkG,IALtB,GAAgBlF,MAAAA,CAAbkG,EAAU,KAAYhV,MAAA,CAAT8O,GAQ3B,WAMPoH,eAAAA,GACC,GAAA/Z,EAAAf,GAAA,EAAC0Y,GAAgBA,CACfrI,aAAcA,EACdzJ,OAAQA,EACR0J,cAAeA,EACfC,iBAAkBA,EAClBC,iBAAkBA,MAK5B,8BCjZO,IAAMsL,GAAwC,OAAC,CAAEC,UAAAA,CAAS,CAAEC,iBAAAA,CAAgB,CAAEC,WAAAA,CAAU,CAAO,CAAAtc,EACpG,GAAI,CAACqc,GAAoB,CAACC,EACxB,OAAOF,EAGT,IAAMG,EAAoBpG,SAASqG,aAAa,CAAC,kCACjD,GAAI,CAACD,EACH,OAAOH,EAGT,IAAMK,EAAgBF,EAAkB/G,qBAAqB,GAEvDkH,EAAWvG,SAASqG,aAAa,CAAC,2BACpCG,EAAOF,EAAcnI,KAAK,CAAG+H,EAAiBtM,KAAK,CAEnD2M,GAEFC,CAAAA,EAAOvY,KAAKwD,GAAG,CAAC+U,EAAMC,EADQpH,qBAAqB,GAChB1F,IAAI,CAAGuM,EAAiBtM,KAAK,GAGlE,IAAM8M,EAAa1G,SAASqG,aAAa,CAAC,6BACpCM,EAAa3G,SAASqG,aAAa,CAAC,6BACpCO,EAAY5G,SAASqG,aAAa,CAAC,6BAErCQ,EAAOP,EAAc3M,IAAI,CACzBmN,EAAOR,EAAcpI,GAAG,CACtB6I,EAAOT,EAAcvG,MAAM,CAAGmG,EAAiBhZ,MAAM,CAEvDwZ,GAEFG,CAAAA,EAAO5Y,KAAKsD,GAAG,CAACsV,EAAMG,EADY3H,qBAAqB,GAClBlB,KAAK,GAGxCwI,GAEFG,CAAAA,EAAO7Y,KAAKsD,GAAG,CAACuV,EAAMG,EADY5H,qBAAqB,GAClBU,MAAM,GAIzC6G,GAEFE,CAAAA,EAAO7Y,KAAKsD,GAAG,CAACuV,EAAMI,EADU7H,qBAAqB,GACjBU,MAAM,GAI5C,IAAMoH,EAAWlB,EAAUrG,CAAC,CAAGsG,EAAiBvM,IAAI,CAC9CyN,EAAWnB,EAAU3G,CAAC,CAAG4G,EAAiBhI,GAAG,CAG7CmJ,EAAepZ,KAAKwD,GAAG,CAACxD,KAAKsD,GAAG,CAAC4V,EAAUN,GAAOL,GAClDc,EAAerZ,KAAKwD,GAAG,CAACxD,KAAKsD,GAAG,CAAC6V,EAAUN,GAAOC,GAExD,MAAO,CACL,GAAGd,CAAS,CACZrG,EAAGyH,EAAenB,EAAiBvM,IAAI,CACvC2F,EAAGgI,EAAepB,EAAiBhI,GAAG,CAE1C,8EChBA,IAAMqJ,GAAc,IAClB,IAAMzd,EAAMoK,CAAAA,EAAAA,EAAAA,MAAAA,IAIZ,MAHAsT,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR1d,EAAI2M,OAAO,CAAGgR,CAChB,GACO3d,EAAI2M,OAAO,EAGPiR,GAAe,QAklBMC,EAAAA,EACQA,EAAAA,EAllBxC,GAAM,CAAEC,cAAAA,CAAa,CAAEC,QAAAA,CAAO,CAAEC,UAAAA,CAAS,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACxC,CAAEC,WAAAA,CAAU,CAAE,CAAGhe,EACjB,CAAEie,YAAAA,CAAW,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAClB,CAAEtb,SAAAA,CAAQ,CAAE,CAAGiW,CAAAA,EAAAA,EAAAA,CAAAA,IACf8E,EAAcnD,CAAAA,EAAAA,EAAAA,EAAAA,IAEd2D,EAAcC,CAAAA,EAAAA,EAAAA,EAAAA,IACdC,EAAgBC,CAAAA,EAAAA,EAAAA,EAAAA,IAEhB,CAAC/N,EAAcuH,EAAgB,CAAGnD,CAAAA,EAAAA,EAAAA,QAAAA,EAAe,IAAIpS,MACrD,CAACgc,EAAUC,EAAY,CAAG7J,CAAAA,EAAAA,EAAAA,QAAAA,EAA2B,QACrD,CAACnE,EAAeC,EAAiB,CAAGkE,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,MAC5D,CAAC8J,EAAYC,EAAc,CAAG/J,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvC,CAACgK,EAAkBC,EAAoB,CAAGjK,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,CAAC/R,GACpD,CAACiO,EAAgBgO,GAAkB,CAAGlK,CAAAA,EAAAA,EAAAA,QAAAA,EAAqB,MAC3DgC,GAAiBzM,CAAAA,EAAAA,EAAAA,MAAAA,EAAO,GACxB4U,GAAqB5U,CAAAA,EAAAA,EAAAA,MAAAA,EAAO,CAAE0L,EAAG,EAAGN,EAAG,CAAE,GAEzCyJ,GAAgB,CAAC,CAACpB,CAExBK,CAAAA,EAAWzX,MAAM,CAAGyX,EAAWzX,MAAM,EAAI,CAAEyY,WAAY,EAAE,CAAEnT,MAAOoT,EAAAA,KAAKA,CAACC,GAAG,EAC3ElB,EAAWmB,KAAK,CAAGnB,EAAWmB,KAAK,EAAI,EAAE,CAEtBnB,EAAWoB,UAAU,CACxC,IAAMC,GAAWzB,CAAa,CAACI,EAAWoB,UAAU,CAAC,CAE/CE,GAAkB,CAAC,CAACnB,EACpBoB,GAAW,CAACvB,EAAWwB,WAAW,EAAI,CAACF,IAAmB,CAAC,CAACrB,CAExC,CAACI,GAAkBF,GAAgBmB,IAAoBtB,EAAWwB,WAAW,CACvG,IAAI7O,GAAc,CAAC,CAAE,EAAC0N,GAAiB,CAACF,GAAe,CAACmB,IAAmB,CAACtB,EAAWwB,WAAW,EAAIvB,GAAesB,EAAAA,EAE/G,CAAEE,cAAAA,EAAa,CAAEC,mBAAAA,EAAkB,CAAEC,gBAAAA,EAAe,CAAEC,aAAAA,EAAY,CAAEC,gBAAAA,EAAe,CAAEC,cAAAA,EAAa,CAAEC,0BAAAA,EAAyB,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAClI,CAAEb,MAAAA,EAAK,CAAE5Y,OAAAA,EAAM,CAAE0Z,OAAAA,EAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAC5BC,GAAmB5C,GAAYqC,IAC/B,CAAEQ,WAAAA,EAAU,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IAEjBC,GAAUC,CAAAA,EAAAA,GAAAA,EAAAA,EACdC,CAAAA,EAAAA,GAAAA,EAAAA,EAAUC,GAAAA,EAAaA,CAAE,CACvBC,qBAAsB,CACpBC,SAAU,CACZ,CACF,GACAH,CAAAA,EAAAA,GAAAA,EAAAA,EAAUI,GAAAA,EAAWA,CAAE,CACrBF,qBAAsB,CACpBG,MAAO,IACPC,UAAW,CACb,CACF,IAyBF,GAtBAtD,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAMuD,EAAcxC,QAAAA,EAAqB,qBAAuB,sBAC1D7G,EAAY1B,SAAS2B,cAAc,CAACoJ,GAEtCrJ,GACFsJ,sBAAsB,KACpBtJ,EAAUE,SAAS,CAAGjB,GAAelK,OAAO,EAGlD,EAAG,CAAC+D,EAAe+N,EAAS,EAE5Bf,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACRoB,EAAoB,CAAChc,EACvB,EAAG,CAACA,EAAS,EAGb4a,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACJ2C,IAAoB,CAACP,IACvBC,GAAgB7B,EAAWoB,UAAU,CAEzC,EAAG,CAACQ,GAAcO,GAAkBnC,EAAWoB,UAAU,CAAES,GAAgB,EAEvE,CAACR,GAAU,MAAO,GAAApe,EAAAf,GAAA,EAAC+gB,EAAAA,UAAUA,CAAAA,CAAC5Y,KAAK,SAEvC,IAAM6Y,GAAY,SAUdpD,EACAuB,EAVF,GAAI,CAACA,GAAU,MAAO,EAAE,CAExB,GAAM,CAAEpM,KAAAA,CAAI,CAAE,CAAGkO,CAAAA,EAAAA,EAAAA,oBAAAA,EACf9B,GACAxB,EACAD,EACAI,EAAWzX,MAAM,EAAI,CAAEsF,MAAOoT,EAAAA,KAAKA,CAACC,GAAG,CAAEF,WAAY,EAAE,EACvDzY,GACA4Y,GAAM/f,MAAM,CAAG+f,GAASnB,EAAWmB,KAAK,EAAI,EAAE,CAC9CrB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAWsD,eAAe,GAA1BtD,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA4BuD,MAAM,GAAI,GACtChC,CAAAA,MAAAA,GAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,GAAUA,QAAQ,GAAlBA,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAoBxa,EAAE,GAAI,IAGtByc,EAAeC,CAAAA,EAAAA,EAAAA,qBAAAA,EAAsBtB,IAAU,GAAIhN,GACnDuO,EAAeC,CAAAA,EAAAA,GAAAA,EAAAA,EAAoBpC,GAASA,QAAQ,EAE1D,OAAOiC,EAAava,GAAG,CAACqM,QAElBmG,EAQAC,EATJ,IAAMkI,EAAatO,EAAIuO,eAAe,CAACC,qBAAqB,CAAC5D,EAAW6D,kBAAkB,CAAC,CAU3F,GANEtI,EADEmI,GAAc,iBAAOA,EACX,IAAInf,KAAKmf,GAET,IAAInf,KAIdyb,EAAW8D,gBAAgB,CAAE,CAC/B,IAAMC,EAAW3O,EAAIuO,eAAe,CAACC,qBAAqB,CAAC5D,EAAW8D,gBAAgB,CAAC,CAErFtI,MAAcjX,KADZwf,GAAY,iBAAOA,EACFA,EAEAxI,EAAUpT,OAAO,GAAK,IAAC6X,CAAAA,EAAWgE,eAAe,EAAI,IAE5E,MACExI,EAAU,IAAIjX,KAAKgX,EAAUpT,OAAO,GAAK,IAAC6X,CAAAA,EAAWgE,eAAe,EAAI,KAG1E,IAAMhZ,EAAQiZ,CAAAA,EAAAA,GAAAA,EAAAA,EACZ7O,EAAI8O,MAAM,CACVV,EAAaW,UAAU,CACvBX,EAAaY,YAAY,CACzBZ,EAAaa,UAAU,EAInB3c,EAAa,CAACjB,CAAAA,EAAAA,EAAAA,OAAAA,EAAU8U,EAAWC,GACnC8I,EAAgB,CAAC9I,EAAQrT,OAAO,GAAKoT,EAAUpT,OAAO,IAAO,KAGnE,MAAO,CACLtB,GAAIuO,EAAIvO,EAAE,CACVmE,MAAAA,EACAvG,MAAO8W,EACP/W,IAAKgX,EACL0I,OAAQ9O,EAAI8O,MAAM,CAClBP,gBAAiBvO,EAAIuO,eAAe,CACpCjc,WAAAA,EACAD,SAVe6c,GAAiB,IAAO5c,GAAc4c,GAAiB,EAWxE,CACF,EACF,EA8CMC,GAAY,MAAA1iB,QAsBZ2iB,EAtBmB,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAyC,CAAA7iB,EAK9E,GAJAmW,SAASE,mBAAmB,CAAC,YAAaR,IAC1CM,SAASE,mBAAmB,CAAC,YAAayM,IAC1C9D,GAAkB,MAEd,CAAC6D,GAAQ,CAACD,GAAU,CAAC9R,IAAeqN,EAAWwB,WAAW,EAAIiD,EAAO5d,EAAE,GAAK6d,EAAK7d,EAAE,CACrF,OAGF,IAAM+d,EAAaH,EAAOjY,IAAI,CAACiC,OAAO,CAChCoW,EAAWH,EAAKlY,IAAI,CAACiC,OAAO,CAElC,GAAI,CAACmW,GAAc,CAACC,EAClB,OAGF,GAAM,CAAEnY,QAAAA,CAAO,CAAED,KAAAA,CAAI,CAAE,CAAGmY,EACpBE,EAA+BrY,YAAAA,EAAqBC,EAAQlF,aAAa,CAAGkF,EAC5EqY,EAAgB,IAAIxgB,KAAKugB,EAAcrgB,KAAK,EAC5CugB,EAAc,IAAIzgB,KAAKugB,EAActgB,GAAG,EACxCygB,EAAW9gB,CAAAA,EAAAA,EAAAA,CAAAA,EAAyB6gB,EAAaD,GAIvD,GAAIF,EAASpY,IAAI,CAACyY,UAAU,CAAC,UAAW,CACtC,IAAMC,EAAgBxf,EAAiBkf,EAASzhB,IAAI,CAAE4D,CAAAA,EAAAA,EAAAA,OAAAA,EAAW+d,IAEjEP,CADAA,EAAWnc,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ0c,EAAeI,EAAAA,EACzBnP,QAAQ,CAAC,EAAG,EAAG,EAAG,EAC7B,MAAO,GAAI6O,oBAAAA,EAASpY,IAAI,CAGtB+X,CADAA,EAAW,IAAIjgB,KAAKsgB,EAASzhB,IAAI,GACxB4S,QAAQ,CAAC6O,EAAStO,IAAI,CAAEsO,EAASlN,MAAM,CAAE,EAAG,OACf,CAAjC,GAAIkN,YAAAA,EAASpY,IAAI,CAItB,OAHA,IAAM0Y,EAAgBxf,EAAiBkf,EAASzhB,IAAI,CAAE4D,CAAAA,EAAAA,EAAAA,OAAAA,EAAW+d,IACjEP,EAAWnc,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ0c,EAAeI,EACpC,CAIA,IAAMC,EAAS,IAAI7gB,KAAKigB,EAASrc,OAAO,GAAK8c,GAEvCI,EAAWP,EAAcZ,MAAM,CAACrd,EAAE,CAClCye,EAAY,CAChB,CAACtF,EAAW6D,kBAAkB,CAAC,CAAEW,EAASnK,WAAW,GACrD,GAAI2F,EAAW8D,gBAAgB,EAAI,CAAE,CAAC9D,EAAW8D,gBAAgB,CAAC,CAAEsB,EAAO/K,WAAW,EAAG,CAAC,EAG5F,GAAI,CACF,MAAMqH,GAAmB1B,EAAWoB,UAAU,CAAE,CAACiE,EAAS,CAAEC,GAC5DC,EAAAA,EAAKA,CAACC,OAAO,CAAC,UAA8B1e,MAAA,CAApBge,EAAc9Z,KAAK,CAAC,cAC9C,CAAE,MAAOya,EAAO,CACdF,EAAAA,EAAKA,CAACE,KAAK,CAAC,0BACd,CACF,EAEM/N,GAAkB,IACtBoJ,GAAmBrS,OAAO,CAAG,CAAEmJ,EAAGvU,EAAMwU,OAAO,CAAEP,EAAGjU,EAAMkU,OAAO,CACnE,EACMoN,GAAkB,IACpB,IAAMe,EAAQriB,EAAMsiB,OAAO,CAAC,EAAE,CAC9B7E,GAAmBrS,OAAO,CAAG,CAAEmJ,EAAG8N,EAAM7N,OAAO,CAAEP,EAAGoO,EAAMnO,OAAO,CACrE,EAGMzO,GAAS8c,CA5GW,KACxB,IAAMC,EAAa3C,YAEnB,EAAgB4C,IAAI,GAIbD,EAAWtd,MAAM,CAAClF,IACvB,IAAM0iB,EAActF,EAAWuF,WAAW,GAC1C,OAAO3iB,EAAM2H,KAAK,CAACgb,WAAW,GAAGC,QAAQ,CAACF,EAC5C,GANSF,CAOX,KAmGMK,GAAY,IAAMpM,EAAgB,IAAIvV,MAEtC4hB,GAAe,KACnB,OAAQ5F,GACN,IAAK,MACHzG,EAAgBsM,GAAY/d,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ+d,EAAU,KAC9C,KACF,KAAK,OACHtM,EAAgBsM,GAAYC,CAAAA,EAAAA,EAAAA,OAAAA,EAASD,EAAU,IAC/C,KACF,KAAK,QACHtM,EAAgBsM,GAAYE,CAAAA,EAAAA,EAAAA,OAAAA,EAAUF,EAAU,GAEpD,CACF,EAEMG,GAAW,KACf,OAAQhG,GACN,IAAK,MACHzG,EAAgBsM,GAAY/d,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ+d,EAAU,IAC9C,KACF,KAAK,OACHtM,EAAgBsM,GAAYI,CAAAA,EAAAA,EAAAA,OAAAA,EAASJ,EAAU,IAC/C,KACF,KAAK,QACHtM,EAAgBsM,GAAYK,CAAAA,EAAAA,EAAAA,OAAAA,EAAUL,EAAU,GAEpD,CACF,EAEMM,GAA2B,SAACtjB,CAAAA,MAAYujB,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,EAAAA,SAAAA,CAAAA,EAAAA,CAC5C,GAAIA,EAAe,CACjB,IAAMC,EAAM,IAAIriB,KACVwR,EAAU,IAAIxR,KAAKnB,GACzB2S,EAAQC,QAAQ,CAAC4Q,EAAIrhB,QAAQ,GAAIqhB,EAAIphB,UAAU,GAAIohB,EAAInhB,UAAU,GAAImhB,EAAIlhB,eAAe,IACxFmhB,GAAkB9Q,EACpB,MACE8Q,GAAkBzjB,EAEtB,EAEMyjB,GAAoB,qBAAOzjB,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAa,IAAImB,KAEhD,GAAI,CAACoO,IAAeqN,EAAWwB,WAAW,CAAE,OAE5C,IAAMva,EAAY,IAAI1C,KAAKnB,GACrB8D,EAAU,IAAI3C,KAAK0C,EAAUkB,OAAO,GAAK,IAAC6X,CAAAA,EAAWgE,eAAe,EAAI,KACxER,EAAeC,CAAAA,EAAAA,GAAAA,EAAAA,EAAoBpC,GAASA,QAAQ,EAE1D,GAAI,CACF,IAAMyF,EAAoB,CACxB,CAAC9G,EAAW6D,kBAAkB,CAAC,CAAE5c,EAAUoT,WAAW,GACtD,GAAI2F,EAAW8D,gBAAgB,EAAI,CAAE,CAAC9D,EAAW8D,gBAAgB,CAAC,CAAE5c,EAAQmT,WAAW,EAAG,CAAC,CAGzFmJ,CAAAA,EAAaW,UAAU,EACzB2C,CAAAA,CAAY,CAACtD,EAAaW,UAAU,CAAC,CAAG,aAG1C,IAAM5d,EAAS,MAAMkb,GAAczB,EAAWoB,UAAU,CAAE,CAAC0F,EAAa,EAExE,GAAIvgB,GAAUA,EAAOwgB,OAAO,EAAIxgB,EAAOwgB,OAAO,CAAC3lB,MAAM,CAAG,EAAG,CACzD,IAAM4lB,EAAczgB,EAAOwgB,OAAO,CAAC,EAAE,CAAClgB,EAAE,CAEpCmgB,GACF,MAAMnF,GAAgB7B,EAAWoB,UAAU,EAC3CO,GAAgBqF,GAChBzB,EAAAA,EAAKA,CAACC,OAAO,CAAC,sBAEdD,EAAAA,EAAKA,CAACE,KAAK,CAAC,gCAEhB,MACEF,EAAAA,EAAKA,CAACE,KAAK,CAAC,kCAEhB,CAAE,MAAOA,EAAO,CACdF,EAAAA,EAAKA,CAACE,KAAK,CAAC,yBACd,CACF,EAEM/S,GAAmB,IAEvB,IAAIsN,EAAWwB,WAAW,EAItBne,GAASA,EAAMwD,EAAE,CAAE,CACrB,IAAMkc,EAAcxC,QAAAA,EAAqB,qBAAuB,sBAC1D7G,EAAY1B,SAAS2B,cAAc,CAACoJ,GACtCrJ,GACFf,CAAAA,GAAelK,OAAO,CAAGiL,EAAUE,SAAS,EAG9CwI,GAAW/e,EAAMwD,EAAE,CAAExD,EAAM6gB,MAAM,CAAC9C,UAAU,EAC5C3O,EAAiBpP,EAAMwD,EAAE,CAC3B,CACF,EAEMogB,GAAuB,KAC3B,OAAQ1G,GACN,IAAK,MAML,QALE,MAAOvb,CAAAA,EAAAA,EAAAA,OAAAA,EAAOuN,EAAc,eAC9B,KAAK,OACH,MAAO,GAAuEvN,MAAAA,CAApEA,CAAAA,EAAAA,EAAAA,OAAAA,EAAOqD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQkK,EAAc,CAACA,EAAa2U,MAAM,IAAK,SAAS,OAA2EpgB,MAAA,CAAtE9B,CAAAA,EAAAA,EAAAA,OAAAA,EAAOqD,CAAAA,EAAAA,EAAAA,OAAAA,EAAQkK,EAAc,EAAEA,EAAa2U,MAAM,IAAK,eACvI,KAAK,QACH,MAAOliB,CAAAA,EAAAA,EAAAA,OAAAA,EAAOuN,EAAc,YAGhC,CACF,EAYM4U,GAAqC,CACzC,CAAEtgB,GAAI,MAAO4Y,MAAO,MAAOzU,MAAO,MAAOwB,KAAM,KAAM,EACrD,CAAE3F,GAAI,OAAQ4Y,MAAO,OAAQzU,MAAO,OAAQwB,KAAM,MAAO,EACzD,CAAE3F,GAAI,QAAS4Y,MAAO,QAASzU,MAAO,QAASwB,KAAM,OAAQ,EAC9D,CAEK4a,GAAqB7G,QAAAA,EAAqB,CAAC,MAAM,CAAGA,SAAAA,EAAsB,CAAC,OAAO,CAAG,CAAC,QAAQ,CAEpG,MACE,GAAAtd,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,0CACd,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,uCACA2e,IAAiB,kBAEhBnc,EAEC,GAAA3B,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,MAAO2e,IAAiB,kBACzC,GAAA9d,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,mDACb,GAAAkB,EAAAf,GAAA,EAACmlB,KAAAA,CAAGtlB,UAAU,iEACXklB,OAEH,GAAAhkB,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR9R,QAAS,IAAM8U,EAAoB,CAACD,GACpC5e,UAAU,qEACX,gBAKH,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,8CACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,wCACb,GAAAkB,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR9R,QAASoa,GACTnkB,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,wDACA2e,GAAgB,MAAQ,gBAE3B,UAGD,GAAA9d,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,8BACb,GAAAkB,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR9R,QAASqa,GACTpkB,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,+CACA2e,GAAgB,UAAY,oBAG9B,GAAA9d,EAAAf,GAAA,EAAColB,GAAAA,GAAaA,CAAAA,CAACvlB,UAAU,cAE3B,GAAAkB,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR9R,QAASya,GACTxkB,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,+CACA2e,GAAgB,UAAY,oBAG9B,GAAA9d,EAAAf,GAAA,EAACqlB,GAAAA,EAAcA,CAAAA,CAACxlB,UAAU,oBAKhC,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,wCAEb,GAAAkB,EAAAf,GAAA,EAACslB,GAAAA,CAAYA,CAAAA,CACXzjB,QAASojB,GACTM,YAAaL,GACbjkB,SAAU,IACJJ,EAAS3B,MAAM,CAAG,GACpBof,EAAYzd,CAAQ,CAAC,EAAE,CAE3B,EACAhB,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,yFACA2e,GAAgB,MAAQ,OAE1B2G,YAAY,OACZC,WAAY,KAGbhV,IAAe,CAACqN,EAAWwB,WAAW,EACrC,GAAAve,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACL3D,QAAS,IAAM4a,GAAyBnU,EAAc,IACtDxQ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,2FACA2e,GAAgB,MAAQ,gBAG1B,GAAA9d,EAAAf,GAAA,EAACwN,GAAAA,GAAQA,CAAAA,CAAC3N,UAAU,uBAOhC,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,yCACA2e,GAAgB,OAAS,kBAEzB,GAAA9d,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,wCACb,GAAAkB,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR9R,QAASoa,GACTnkB,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,wDACA2e,GAAgB,MAAQ,gBAE3B,UAGD,GAAA9d,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,8BACb,GAAAkB,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR9R,QAASqa,GACTpkB,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,+CACA2e,GAAgB,UAAY,oBAG9B,GAAA9d,EAAAf,GAAA,EAAColB,GAAAA,GAAaA,CAAAA,CAACvlB,UAAU,cAE3B,GAAAkB,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR9R,QAASya,GACTxkB,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,+CACA2e,GAAgB,UAAY,oBAG9B,GAAA9d,EAAAf,GAAA,EAACqlB,GAAAA,EAAcA,CAAAA,CAACxlB,UAAU,iBAI9B,GAAAkB,EAAAf,GAAA,EAACmlB,KAAAA,CAAGtlB,UAAU,4CACXklB,UAIL,GAAAhkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,wCACZ,CAACgf,IACA,GAAA9d,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,qBACb,GAAAkB,EAAAf,GAAA,EAAC0lB,EAAAA,CAAKA,CAAAA,CACJF,YAAY,mBACZjI,MAAOgB,EACPtd,SAAU,GAAOud,EAAclN,EAAEqU,MAAM,CAACpI,KAAK,EAC7C1d,UAAU,wEAEZ,GAAAkB,EAAAf,GAAA,EAAC4lB,GAAAA,GAAmBA,CAAAA,CAAC/lB,UAAU,oFAInC,GAAAkB,EAAAf,GAAA,EAACslB,GAAAA,CAAYA,CAAAA,CACXzjB,QAASojB,GACTM,YAAaL,GACbjkB,SAAU,IACJJ,EAAS3B,MAAM,CAAG,GACpBof,EAAYzd,CAAQ,CAAC,EAAE,CAE3B,EACAhB,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,oFACA2e,GAAgB,WAAa,YAE/B2G,YAAY,OACZC,WAAY,KAGbhV,IAAe,CAACqN,EAAWwB,WAAW,EACrC,GAAAve,EAAA0L,IAAA,EAACc,EAAAA,CAAMA,CAAAA,CACL3D,QAAS,IAAM4a,GAAyBnU,EAAc,IACtDxQ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,iGACA2e,GAAgB,MAAQ,iBAG1B,GAAA9d,EAAAf,GAAA,EAACwN,GAAAA,GAAQA,CAAAA,CAAC3N,UAAU,YACnB,CAACgf,IAAiB,GAAA9d,EAAAf,GAAA,EAAC4I,OAAAA,UAAK,iBAK7B,GAAA7H,EAAA0L,IAAA,EAACoZ,GAAAA,EAAYA,CAAAA,WACX,GAAA9kB,EAAAf,GAAA,EAAC8lB,GAAAA,EAAmBA,CAAAA,CAACC,QAAO,YAC1B,GAAAhlB,EAAAf,GAAA,EAACuN,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR7b,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACT,mDACA2e,GAAgB,UAAY,oBAG9B,GAAA9d,EAAAf,GAAA,EAACgmB,GAAAA,CAAsBA,CAAAA,CAACnmB,UAAU,gBAGtC,GAAAkB,EAAA0L,IAAA,EAACwZ,GAAAA,EAAmBA,CAAAA,CAACpmB,UAAU,6CAA6CqmB,MAAM,gBAChF,GAAAnlB,EAAA0L,IAAA,EAAC0Z,GAAAA,CAAKA,CAAAA,CAACtmB,UAAU,gHACf,GAAAkB,EAAAf,GAAA,EAAComB,GAAAA,GAAQA,CAAAA,CAACvmB,UAAU,WACpB,GAAAkB,EAAAf,GAAA,EAAC4I,OAAAA,CAAK/I,UAAU,6BAAoB,iBACpC,GAAAkB,EAAAf,GAAA,EAACqmB,GAAAA,CAAMA,CAAAA,CACLxmB,UAAU,UACVymB,QAAS,CAAC,CAACxI,EAAWwB,WAAW,CACjCiH,gBAAiBjH,IAEfO,GAA0B/f,EAAM2C,IAAI,CAACkC,EAAE,CAAE7E,EAAM2C,IAAI,CAAC+jB,MAAM,CAAE,CAAElH,YAAAA,CAAY,EAC5E,EACAmH,eAAe,eAInB,GAAA1lB,EAAAf,GAAA,EAAC0mB,GAAAA,CAAUA,CAAAA,CACTvH,SAAUA,GAASA,QAAQ,CAC3BwH,QACE,GAAA5lB,EAAA0L,IAAA,EAACc,EAAAA,CAAMA,CAAAA,CAACmO,QAAQ,QACR7b,UAAU,oFAChB,GAAAkB,EAAAf,GAAA,EAAC4mB,GAAAA,GAAcA,CAAAA,CAAC/mB,UAAU,WAAU,kBAEnCie,EAAWzX,MAAM,CAACyY,UAAU,CAAC5f,MAAM,CAAG,GAAK,IAAwC0F,MAAA,CAApCkZ,EAAWzX,MAAM,CAACyY,UAAU,CAAC5f,MAAM,CAAC,QAGxFmH,OAAQyX,EAAWzX,MAAM,CACzBpF,SAAUoF,GAAUwZ,GAA0B/f,EAAM2C,IAAI,CAACkC,EAAE,CAAE7E,EAAM2C,IAAI,CAAC+jB,MAAM,CAAE,CAAEngB,OAAAA,CAAO,GACzFwgB,gBAAiBpJ,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAaqJ,UAAU,GAAvBrJ,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAyBuE,MAAM,GAA/BvE,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiC9Y,EAAE,CACpDoiB,wBAAyBtJ,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAaqJ,UAAU,GAAvBrJ,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAyBuE,MAAM,GAA/BvE,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiCyB,UAAU,GAGtE,GAAAne,EAAAf,GAAA,EAACgnB,GAAAA,CAAQA,CAAAA,CACP7H,SAAUA,GAASA,QAAQ,CAC3BF,MAAOnB,EAAWmB,KAAK,CACvBhe,SAAUge,GAASY,GAA0B/f,EAAM2C,IAAI,CAACkC,EAAE,CAAE7E,EAAM2C,IAAI,CAAC+jB,MAAM,CAAE,CAAEvH,MAAAA,CAAM,GACvF0H,QACE,GAAA5lB,EAAA0L,IAAA,EAACc,EAAAA,CAAMA,CAAAA,CAACmO,QAAQ,QACR7b,UAAU,oFAChB,GAAAkB,EAAAf,GAAA,EAACinB,GAAAA,GAAoBA,CAAAA,CAACpnB,UAAU,WAAU,gBAEzCie,EAAWmB,KAAK,CAAC/f,MAAM,CAAG,GAAK,IAA4B0F,MAAA,CAAxBkZ,EAAWmB,KAAK,CAAC/f,MAAM,CAAC,sBAU3EwD,GAAY,CAACmc,IACZ,GAAA9d,EAAAf,GAAA,EAACC,MAAAA,CAAIJ,UAAU,qBACb,GAAAkB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,qBACb,GAAAkB,EAAAf,GAAA,EAAC0lB,EAAAA,CAAKA,CAAAA,CACJF,YAAY,mBACZjI,MAAOgB,EACPtd,SAAU,GAAOud,EAAclN,EAAEqU,MAAM,CAACpI,KAAK,EAC7C1d,UAAU,0EAEZ,GAAAkB,EAAAf,GAAA,EAAC4lB,GAAAA,GAAmBA,CAAAA,CAAC/lB,UAAU,yFAOtC,CAACuf,IAAmBtB,EAAWwB,WAAW,EACzC,GAAAve,EAAAf,GAAA,EAACknB,EAAAA,EAAaA,CAAAA,CAAAA,GAIhB,GAAAnmB,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,gCACZ4e,GACD,GAAA1d,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,EACd,qBACAwC,EAAW,yDAA2D,+CAEtE,GAAA3B,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,8EACb,GAAAkB,EAAAf,GAAA,EAACM,KAAAA,CAAGT,UAAU,4CAAmC,aAChD6C,GACC,GAAA3B,EAAA0L,IAAA,EAACc,EAAAA,CAAMA,CAAAA,CACLmO,QAAQ,QACR9R,QAAS,IAAM8U,EAAoB,IACnC7e,UAAU,qEAEV,GAAAkB,EAAAf,GAAA,EAAC4I,OAAAA,CAAK/I,UAAU,mBAAU,UAAY,aAK5C,GAAAkB,EAAAf,GAAA,EAACW,EAAQA,CACPC,KAAK,SACLC,SAAUwP,EACVvP,SAAU,IACJI,IACF0W,EAAgB1W,GACZwB,GACFgc,EAAoB,IAG1B,EACA7e,UAAU,2BAKd,GAAAkB,EAAA0L,IAAA,EAAC0a,GAAAA,EAAUA,CAAAA,CACT/G,QAASA,GACTgH,YA1gBe,IAElB,GAAI,KAAgBtJ,EAAWwB,WAAW,EAItCne,EAAMohB,MAAM,CAACjY,IAAI,CAACiC,OAAO,CAAE,KAELpL,EAAAA,EAQoCA,EACEA,EATtCA,EADxB,IAAMkmB,EAAkBzI,GAAmBrS,OAAO,CAAC6I,CAAC,CAC9CkS,EAAkBnmB,OAAAA,CAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAMohB,MAAM,CAACrN,IAAI,CAAC3I,OAAO,GAAzBpL,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAA2BomB,UAAU,GAArCpmB,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAuC6S,GAAG,GAA1C7S,KAAAA,IAAAA,EAAAA,EAA8C,EAKhE,CAAEqJ,QAAAA,CAAO,CAAED,KAAAA,CAAI,CAAE,CAAGpJ,EAAMohB,MAAM,CAACjY,IAAI,CAACiC,OAAO,CAC7C0F,EAAU1H,YAAAA,EAAqBC,EAAQlF,aAAa,CAACX,EAAE,CAAG6F,EAAQ7F,EAAE,CACpE6iB,EAAiB1R,SAAS2B,cAAc,CAAC,SAAiB7S,MAAA,CAARqN,IAClDvC,EAAQ8X,EAAiBA,EAAeC,WAAW,QAAGtmB,CAAAA,EAAAA,EAAMohB,MAAM,CAACrN,IAAI,CAAC3I,OAAO,CAACgb,UAAU,GAApCpmB,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAsCuO,KAAK,CACjG1M,EAASwkB,EAAiBA,EAAeE,YAAY,QAAGvmB,CAAAA,EAAAA,EAAMohB,MAAM,CAACrN,IAAI,CAAC3I,OAAO,CAACgb,UAAU,GAApCpmB,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAsC6B,MAAM,CAE1G2b,GAAkB,CAChB,GAAGxd,EAAMohB,MAAM,CAACjY,IAAI,CAACiC,OAAO,CAC5Bob,YAZkBN,EAAkBC,EAapC5X,MAAAA,EACA1M,OAAAA,CACF,GAEA8S,SAASC,gBAAgB,CAAC,YAAaP,IACvCM,SAASC,gBAAgB,CAAC,YAAa0M,GACzC,CACF,EA8eKJ,UAAWA,GACXuF,UAAW,CAAC9L,GAA4B,WAE1C,GAAA/a,EAAA0L,IAAA,EAACxM,MAAAA,CAAIJ,UAAU,iBAAiBgoB,wBAAsB,iBACnDxJ,QAAAA,GACF,GAAAtd,EAAAf,GAAA,EAACwW,GAAOA,CACNnG,aAAcA,EACdzJ,OAAQA,GACR0J,cAAeA,EACfC,iBAAkBA,EAClBG,iBAAkB8T,GAClB/T,YAAaA,IAAe,CAACqN,EAAWwB,WAAW,CACnD7I,eAAgBA,GAChBjG,iBAAkBA,GAClBG,eAAgBA,IAGnB0N,SAAAA,GACC,GAAAtd,EAAAf,GAAA,EAAC2X,GAAQA,CACPtH,aAAcA,EACdzJ,OAAQA,GACR0J,cAAeA,EACfC,iBAAkBA,EAClBqH,gBAAiBA,EACjBlH,iBAAkB8T,GAClB/T,YAAaA,IAAe,CAACqN,EAAWwB,WAAW,CACnD7I,eAAgBA,GAChBjG,iBAAkBA,GAClBG,eAAgBA,IAGnB0N,UAAAA,GACC,GAAAtd,EAAAf,GAAA,EAACqa,GAASA,CACRhK,aAAcA,EACdzJ,OAAQA,GACR0J,cAAeA,EACfC,iBAAkBA,EAClBqH,gBAAiBA,EACjBlH,iBAAkB,GAAU8T,GAAyBtjB,EAAM,IAC3DuP,YAAaA,IAAe,CAACqN,EAAWwB,WAAW,CACnD9O,iBAAkBA,GAClBG,eAAgBA,OAKnB,GAAA5P,EAAAf,GAAA,EAAC8nB,GAAAA,EAAWA,CAAAA,CAACC,cAAe,cAC1BpX,GAAkBA,YAAAA,EAAepG,IAAI,CAClC,GAAAxJ,EAAAf,GAAA,EAAC0J,GAAoBA,CACnBpD,QAASqK,EAAenG,OAAO,CAC/B/H,KAAM4b,QAAAA,EAAqB,MAAQ,OACnCzU,QAAS,KAAO,EAChBD,MAAO,CACL+F,MAAOiB,EAAejB,KAAK,CAC3B1M,OAAQ2N,EAAe3N,MAAM,IAG/B2N,GAAkBA,UAAAA,EAAepG,IAAI,CACvC,GAAAxJ,EAAAf,GAAA,EAACoY,GAAiBA,CAChBjX,MAAOwP,EAAenG,OAAO,CAC7B/H,KAAM4b,EACNzU,QAAS,KAAO,EAChBD,MAAO,CACL+F,MAAOiB,EAAejB,KAAK,CAC3B1M,OAAQ2N,EAAe3N,MAAM,IAG/B,eAMZ,uCCxwBA,IAAMglB,EAA2BtoB,EAAAA,UAAgB,CAvBjD,SAA8BC,CAI7B,CAAEsoB,CAAM,KAJqB,CAC5Bnf,MAAAA,CAAK,CACLof,QAAAA,CAAO,CACP,GAAGpoB,EACJ,CAJ6BH,EAK5B,OAAoBD,EAAAA,aAAmB,CAAC,MAAOyoB,OAAOC,MAAM,CAAC,CAC3DC,MAAO,6BACPtb,KAAM,OACNE,QAAS,YACTI,YAAa,IACbL,OAAQ,eACR,cAAe,OACf,YAAa,OACbpN,IAAKqoB,EACL,kBAAmBC,CACrB,EAAGpoB,GAAQgJ,EAAqBpJ,EAAAA,aAAmB,CAAC,QAAS,CAC3DiF,GAAIujB,CACN,EAAGpf,GAAS,KAAmBpJ,EAAAA,aAAmB,CAAC,OAAQ,CACzDyN,cAAe,QACfC,eAAgB,QAChBE,EAAG,8IACL,GACF,EAEAgb,CAAAA,EAAAC,CAAA,CAAeP,uCCDf,IAAMA,EAA2BtoB,EAAAA,UAAgB,CAvBjD,SAAmCC,CAIlC,CAAEsoB,CAAM,KAJ0B,CACjCnf,MAAAA,CAAK,CACLof,QAAAA,CAAO,CACP,GAAGpoB,EACJ,CAJkCH,EAKjC,OAAoBD,EAAAA,aAAmB,CAAC,MAAOyoB,OAAOC,MAAM,CAAC,CAC3DC,MAAO,6BACPtb,KAAM,OACNE,QAAS,YACTI,YAAa,IACbL,OAAQ,eACR,cAAe,OACf,YAAa,OACbpN,IAAKqoB,EACL,kBAAmBC,CACrB,EAAGpoB,GAAQgJ,EAAqBpJ,EAAAA,aAAmB,CAAC,QAAS,CAC3DiF,GAAIujB,CACN,EAAGpf,GAAS,KAAmBpJ,EAAAA,aAAmB,CAAC,OAAQ,CACzDyN,cAAe,QACfC,eAAgB,QAChBE,EAAG,gIACL,GACF,EAEAgb,CAAAA,EAAAC,CAAA,CAAeP", "sources": ["webpack://_N_E/./node_modules/email-validator/index.js", "webpack://_N_E/./src/components/ui/card.tsx", "webpack://_N_E/./src/components/ui/calendar.tsx", "webpack://_N_E/./node_modules/date-fns/esm/differenceInMinutes/index.js", "webpack://_N_E/./src/utils/dateUtils.ts", "webpack://_N_E/./node_modules/date-fns/esm/differenceInDays/index.js", "webpack://_N_E/./src/utils/multiDay.ts", "webpack://_N_E/./src/components/workspace/main/views/calendar/components/multidayevents.tsx", "webpack://_N_E/./src/components/workspace/main/views/calendar/components/eventsegment.tsx", "webpack://_N_E/./src/components/workspace/main/views/calendar/components/noevents.tsx", "webpack://_N_E/./src/utils/eventCollision.ts", "webpack://_N_E/./src/components/workspace/main/views/calendar/components/allday.tsx", "webpack://_N_E/./src/components/workspace/main/views/calendar/views/day.tsx", "webpack://_N_E/./src/components/workspace/main/views/calendar/views/week.tsx", "webpack://_N_E/./src/components/workspace/main/views/calendar/components/eventitem.tsx", "webpack://_N_E/./src/components/workspace/main/views/calendar/components/card.tsx", "webpack://_N_E/./src/components/workspace/main/views/calendar/views/month.tsx", "webpack://_N_E/./src/utils/dragconstraints.ts", "webpack://_N_E/./src/components/workspace/main/views/calendar/index.tsx", "webpack://_N_E/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js", "webpack://_N_E/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassCircleIcon.js"], "sourcesContent": ["\"use strict\";\r\n\r\nvar tester = /^[-!#$%&'*+\\/0-9=?A-Z^_a-z{|}~](\\.?[-!#$%&'*+\\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\\.?[a-zA-Z0-9])*\\.[a-zA-Z](-?[a-zA-Z0-9])+$/;\r\n// Thanks to:\r\n// http://fightingforalostcause.net/misc/2006/compare-email-regex.php\r\n// http://thedailywtf.com/Articles/Validating_Email_Addresses.aspx\r\n// http://stackoverflow.com/questions/201323/what-is-the-best-regular-expression-for-validating-email-addresses/201378#201378\r\nexports.validate = function(email)\r\n{\r\n\tif (!email)\r\n\t\treturn false;\r\n\t\t\r\n\tif(email.length>254)\r\n\t\treturn false;\r\n\r\n\tvar valid = tester.test(email);\r\n\tif(!valid)\r\n\t\treturn false;\r\n\r\n\t// Further checking of some things regex can't handle\r\n\tvar parts = email.split(\"@\");\r\n\tif(parts[0].length>64)\r\n\t\treturn false;\r\n\r\n\tvar domainParts = parts[1].split(\".\");\r\n\tif(domainParts.some(function(part) { return part.length>63; }))\r\n\t\treturn false;\r\n\r\n\treturn true;\r\n}", "import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n", "import * as React from \"react\";\r\nimport DatePicker from \"react-datepicker\";\r\nimport \"react-datepicker/dist/react-datepicker.css\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport type CalendarProps = {\r\n  className?: string;\r\n  mode?: \"single\" | \"range\" | \"multiple\";\r\n  selected?: Date | null;\r\n  onSelect?: (date: Date) => void;\r\n  components?: any;\r\n  showOutsideDays?: boolean;\r\n};\r\n\r\nexport function Calendar({\r\n  className,\r\n  mode = \"single\",\r\n  selected,\r\n  onSelect,\r\n  ...props\r\n}: CalendarProps) {\r\n  const handleChange = (date: Date | null, event: React.SyntheticEvent<any, Event> | undefined) => {\r\n    if (onSelect && date) {\r\n      onSelect(date);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={cn(\"r-date-edit-pop p-0 border-b border-neutral-200\", className)}>\r\n      <DatePicker\r\n        selected={selected}\r\n        onChange={handleChange}\r\n        inline\r\n        showMonthDropdown\r\n        showYearDropdown\r\n        dropdownMode=\"select\"\r\n        calendarClassName=\"shadow-none border-0\"\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n", "import { millisecondsInMinute } from \"../constants/index.js\";\nimport differenceInMilliseconds from \"../differenceInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInMinutes\n * @category Minute Helpers\n * @summary Get the number of minutes between the given dates.\n *\n * @description\n * Get the signed number of full (rounded towards 0) minutes between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of minutes\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many minutes are between 2 July 2014 12:07:59 and 2 July 2014 12:20:00?\n * const result = differenceInMinutes(\n *   new Date(2014, 6, 2, 12, 20, 0),\n *   new Date(2014, 6, 2, 12, 7, 59)\n * )\n * //=> 12\n *\n * @example\n * // How many minutes are between 10:01:59 and 10:00:00\n * const result = differenceInMinutes(\n *   new Date(2000, 0, 1, 10, 0, 0),\n *   new Date(2000, 0, 1, 10, 1, 59)\n * )\n * //=> -1\n */\nexport default function differenceInMinutes(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInMilliseconds(dateLeft, dateRight) / millisecondsInMinute;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}", "import { format, addDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isSameDay, isWithinInterval, startOfDay, endOfDay, differenceInMinutes } from 'date-fns';\r\nimport { CalendarEvent } from '@/typings/page';\r\n\r\n\r\nexport const getEventsForRange = (events: CalendarEvent[], start: Date, end: Date): CalendarEvent[] => {\r\n  return events.filter(event => {\r\n    const eventStart = new Date(event.start);\r\n    const eventEnd = new Date(event.end);\r\n    return eventStart <= end && eventEnd >= start;\r\n  });\r\n};\r\n\r\nexport const getEventDurationInMinutes = (event: CalendarEvent): number => {\r\n  const start = new Date(event.start);\r\n  const end = new Date(event.end);\r\n  return Math.max(30, (end.getTime() - start.getTime()) / (1000 * 60));\r\n};\r\n\r\nexport const formatDateForHeader = (viewType: string, date: Date): string => {\r\n  switch (viewType) {\r\n    case 'day':\r\n      return format(date, 'MMMM d, yyyy');\r\n    case 'week':\r\n      return `${format(startOfWeek(date, { weekStartsOn: 0 }), 'MMM d')} - ${format(endOfWeek(date, { weekStartsOn: 0 }), 'MMM d, yyyy')}`;\r\n    case 'month':\r\n      return format(date, 'MMMM yyyy');\r\n    default:\r\n      return format(date, 'MMMM d, yyyy');\r\n  }\r\n};\r\n\r\nexport const getMonthViewDays = (date: Date): Date[] => {\r\n  const monthStart = startOfMonth(date);\r\n  const monthEnd = endOfMonth(date);\r\n  const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });\r\n  const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });\r\n  \r\n  const days = [];\r\n  let day = startDay;\r\n  while (day <= endDay) {\r\n    days.push(day);\r\n    day = addDays(day, 1);\r\n  }\r\n  \r\n  return days;\r\n};\r\n\r\nexport const getMonthViewWeeks = (date: Date): Date[][] => {\r\n  const days = getMonthViewDays(date);\r\n  const weeks = [];\r\n  \r\n  for (let i = 0; i < days.length; i += 7) {\r\n    weeks.push(days.slice(i, i + 7));\r\n  }\r\n  \r\n  return weeks;\r\n};\r\n\r\nexport const isInCurrentMonth = (date: Date, currentMonth: Date): boolean => {\r\n  return date.getMonth() === currentMonth.getMonth() && \r\n         date.getFullYear() === currentMonth.getFullYear();\r\n};\r\n\r\n// New utilities for event time formatting\r\nexport const isInstantEvent = (event: CalendarEvent): boolean => {\r\n  return differenceInMinutes(new Date(event.end), new Date(event.start)) <= 0;\r\n};\r\n\r\nexport const formatEventTime = (\r\n  start: Date, \r\n  view: 'day' | 'week' | 'month', \r\n  options?: { \r\n    isMobile?: boolean, \r\n    shortFormat?: boolean \r\n  }\r\n): string => {\r\n  const { isMobile = window.innerWidth < 1024, shortFormat = false } = options || {};\r\n\r\n  // Determine format based on view and screen size\r\n  if (shortFormat) {\r\n    return format(start, 'h:mma'); // e.g., 9:30am\r\n  }\r\n\r\n  if (view === 'month' && isMobile) {\r\n    return format(start, 'h:mma'); // e.g., 9:30am\r\n  }\r\n  \r\n  return format(start, 'h:mm a'); // e.g., 9:30 am\r\n};\r\n\r\nexport const getEventSize = (height: number | null): 'small' | 'medium' | 'large' => {\r\n  if (height === null) return 'medium';\r\n  return height < 40 ? 'small' : height < 80 ? 'medium' : 'large';\r\n};\r\n", "import toDate from \"../toDate/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\"; // Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(dateLeft, dateRight) {\n  var diff = dateLeft.getFullYear() - dateRight.getFullYear() || dateLeft.getMonth() - dateRight.getMonth() || dateLeft.getDate() - dateRight.getDate() || dateLeft.getHours() - dateRight.getHours() || dateLeft.getMinutes() - dateRight.getMinutes() || dateLeft.getSeconds() - dateRight.getSeconds() || dateLeft.getMilliseconds() - dateRight.getMilliseconds();\n  if (diff < 0) {\n    return -1;\n  } else if (diff > 0) {\n    return 1;\n    // Return 0 if diff is 0; return NaN if diff is NaN\n  } else {\n    return diff;\n  }\n}\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.floor(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full days according to the local timezone\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n//=> 92\n */\nexport default function differenceInDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareLocalAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarDays(dateLeft, dateRight));\n  dateLeft.setDate(dateLeft.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  var isLastDayNotFull = Number(compareLocalAsc(dateLeft, dateRight) === -sign);\n  var result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}", "import {  addDays, startOfDay, endOfDay, isSameDay, format, differenceInDays, isWithinInterval } from 'date-fns';\r\nimport { CalendarEvent } from '@/typings/page';\r\n\r\nexport interface EventSegment {\r\n  id: string;\r\n  originalEventId: string;\r\n  date: Date;\r\n  startTime: Date;\r\n  endTime: Date;\r\n  isFirstSegment: boolean;\r\n  isLastSegment: boolean;\r\n  isMiddleSegment: boolean;\r\n  segmentIndex: number;\r\n  totalSegments: number;\r\n  originalEvent: CalendarEvent;\r\n  isAllDay: boolean;\r\n  isMultiDay: boolean;\r\n}\r\n\r\n\r\nexport const isMultiDayEvent = (event: CalendarEvent): boolean => {\r\n  const start = new Date(event.start);\r\n  const end = new Date(event.end);\r\n  return !isSameDay(start, end);\r\n};\r\n\r\n\r\nexport const createEventSegments = (event: CalendarEvent): EventSegment[] => {\r\n  const eventStart = new Date(event.start);\r\n  const eventEnd = new Date(event.end);\r\n  const isMultiDay = isMultiDayEvent(event);\r\n  \r\n  // For single day events, return a single segment.\r\n  if (!isMultiDay) {\r\n    return [{\r\n      id: `${event.id}-${format(eventStart, 'yyyy-MM-dd')}`,\r\n      originalEventId: event.id,\r\n      date: startOfDay(eventStart),\r\n      startTime: eventStart,\r\n      endTime: eventEnd,\r\n      isFirstSegment: true,\r\n      isLastSegment: true,\r\n      isMiddleSegment: false,\r\n      segmentIndex: 0,\r\n      totalSegments: 1,\r\n      originalEvent: event,\r\n      isAllDay: false,\r\n      isMultiDay: false\r\n    }];\r\n  }\r\n\r\n  const segments: EventSegment[] = [];\r\n  const totalDays = differenceInDays(endOfDay(eventEnd), startOfDay(eventStart)) + 1;\r\n  \r\n  let currentDate = startOfDay(eventStart);\r\n  const finalDate = startOfDay(eventEnd);\r\n  let segmentIndex = 0;\r\n\r\n  while (currentDate <= finalDate) {\r\n    const isFirstSegment = segmentIndex === 0;\r\n    const isLastSegment = isSameDay(currentDate, finalDate);\r\n    const isMiddleSegment = !isFirstSegment && !isLastSegment;\r\n\r\n    // Calculate segment start and end times.\r\n    const segmentStart = isFirstSegment \r\n      ? eventStart \r\n      : startOfDay(currentDate);\r\n      \r\n    const segmentEnd = isLastSegment \r\n      ? eventEnd \r\n      : endOfDay(currentDate);\r\n\r\n    // Determine if this segment should be treated as all-day\r\n    const segmentDurationHours = (segmentEnd.getTime() - segmentStart.getTime()) / (1000 * 60 * 60);\r\n    const isAllDay = segmentDurationHours >= 23 || (isMiddleSegment && segmentDurationHours >= 22);\r\n\r\n    segments.push({\r\n      id: `${event.id}-${format(currentDate, 'yyyy-MM-dd')}`,\r\n      originalEventId: event.id,\r\n      date: currentDate,\r\n      startTime: segmentStart,\r\n      endTime: segmentEnd,\r\n      isFirstSegment,\r\n      isLastSegment,\r\n      isMiddleSegment,\r\n      segmentIndex,\r\n      totalSegments: totalDays,\r\n      originalEvent: event,\r\n      isAllDay,\r\n      isMultiDay: true\r\n    });\r\n\r\n    currentDate = addDays(currentDate, 1);\r\n    segmentIndex++;\r\n  }\r\n\r\n  return segments;\r\n};\r\n\r\n\r\nexport const getSegmentsForDay = (segments: EventSegment[], date: Date): EventSegment[] => {\r\n  return segments.filter(segment => isSameDay(segment.date, date));\r\n};\r\n\r\n\r\nexport const getSegmentsForWeek = (\r\n  segments: EventSegment[], \r\n  weekStart: Date, \r\n  weekEnd: Date\r\n): EventSegment[] => {\r\n  return segments.filter(segment => \r\n    isWithinInterval(segment.date, { start: weekStart, end: weekEnd })\r\n  );\r\n};\r\n\r\n\r\nexport const eventsToSegments = (events: CalendarEvent[]): EventSegment[] => {\r\n  return events\r\n    .map(event => normalizeEventTimes(event)) \r\n    .flatMap(event => createEventSegments(event));\r\n};\r\n\r\n\r\nexport const groupSegmentsByDate = (segments: EventSegment[]): Map<string, EventSegment[]> => {\r\n  const grouped = new Map<string, EventSegment[]>();\r\n  \r\n  segments.forEach(segment => {\r\n    const dateKey = format(segment.date, 'yyyy-MM-dd');\r\n    if (!grouped.has(dateKey)) {\r\n      grouped.set(dateKey, []);\r\n    }\r\n    grouped.get(dateKey)!.push(segment);\r\n  });\r\n  \r\n  return grouped;\r\n};\r\n\r\n\r\nexport const getAllDaySegments = (segments: EventSegment[]): EventSegment[] => {\r\n  return segments.filter(segment => segment.isAllDay || segment.isMultiDay);\r\n};\r\n\r\n\r\nexport const getTimeSlotSegments = (segments: EventSegment[]): EventSegment[] => {\r\n  return segments.filter(segment => !segment.isAllDay);\r\n};\r\n\r\n\r\nexport const getSegmentHeight = (segment: EventSegment): number => {\r\n  const startHour = segment.startTime.getHours();\r\n  const startMinutes = segment.startTime.getMinutes();\r\n  const endHour = segment.endTime.getHours();\r\n  const endMinutes = segment.endTime.getMinutes();\r\n  \r\n  // Convert to minutes from start of day.\r\n  const startMinutesFromDayStart = (startHour * 60) + startMinutes;\r\n  const endMinutesFromDayStart = (endHour * 60) + endMinutes;\r\n  \r\n  // Calculate duration in minutes, but cap at end of day (23:59)\r\n  const maxMinutesInDay = 23 * 60 + 59;\r\n  const cappedEndMinutes = Math.min(endMinutesFromDayStart, maxMinutesInDay);\r\n  \r\n  const durationMinutes = cappedEndMinutes - startMinutesFromDayStart;\r\n  \r\n  // Return height in pixels (1 minute = 1 pixel, minimum 30px)\r\n  return Math.max(30, durationMinutes);\r\n};\r\n\r\n/**\r\n * Gets the top offset for positioning a segment in the time grid\r\n */\r\nexport const getSegmentTopOffset = (segment: EventSegment): number => {\r\n  const startHour = segment.startTime.getHours();\r\n  const startMinutes = segment.startTime.getMinutes();\r\n  \r\n  // Each hour is 60px, calculate offset within the hour\r\n  return startMinutes; // This will be added to the hour row's top position\r\n};\r\n\r\n\r\nexport const getSegmentStylingClasses = (segment: EventSegment): {\r\n  roundedCorners: string;\r\n  continuationIndicator: string;\r\n  opacity: string;\r\n} => {\r\n  const baseRounded = \"rounded-md\";\r\n  \r\n  if (!segment.isMultiDay) {\r\n    return {\r\n      roundedCorners: baseRounded,\r\n      continuationIndicator: \"\",\r\n      opacity: \"opacity-100\"\r\n    };\r\n  }\r\n\r\n  let roundedCorners = \"\";\r\n  let continuationIndicator = \"\";\r\n  \r\n  if (segment.isFirstSegment && segment.isLastSegment) {\r\n    roundedCorners = baseRounded;\r\n  } else if (segment.isFirstSegment) {\r\n    roundedCorners = \"rounded-l-md rounded-r-sm\";\r\n    continuationIndicator = \"pr-4\";\r\n  } else if (segment.isLastSegment) {\r\n    roundedCorners = \"rounded-r-md rounded-l-sm\";\r\n    continuationIndicator = \"pl-4\";\r\n  } else {\r\n    roundedCorners = \"rounded-sm\";\r\n    continuationIndicator = \"pl-4 pr-4\";\r\n  }\r\n\r\n  return {\r\n    roundedCorners,\r\n    continuationIndicator,\r\n    opacity: segment.isMiddleSegment ? \"opacity-90\" : \"opacity-100\"\r\n  };\r\n};\r\n\r\n\r\nexport const shouldShowTimeInSegment = (segment: EventSegment, view: 'day' | 'week' | 'month'): boolean => {\r\n  if (view === 'month') return !segment.isAllDay;\r\n  if (segment.isAllDay) return false;\r\n  \r\n  // Show time for first and last segments, but not middle all-day segments\r\n  return segment.isFirstSegment || segment.isLastSegment || !segment.isMultiDay;\r\n};\r\n\r\n\r\nexport const getSegmentContinuationText = (segment: EventSegment): string => {\r\n  if (!segment.isMultiDay) return \"\";\r\n  \r\n  if (segment.isFirstSegment) {\r\n    return `Continues for ${segment.totalSegments - 1} more day${segment.totalSegments > 2 ? 's' : ''}`;\r\n  } else if (segment.isLastSegment) {\r\n    return `Continued from ${segment.segmentIndex} day${segment.segmentIndex > 1 ? 's' : ''} ago`;\r\n  } else {\r\n    return `Day ${segment.segmentIndex + 1} of ${segment.totalSegments}`;\r\n  }\r\n};\r\n\r\n\r\nexport const isOvernightEvent = (event: CalendarEvent): boolean => {\r\n  const start = new Date(event.start);\r\n  const end = new Date(event.end);\r\n  \r\n  // Check if event crosses midnight on the same calendar day\r\n  const startDay = startOfDay(start);\r\n  const endDay = startOfDay(end);\r\n  \r\n  return !isSameDay(startDay, endDay) && differenceInDays(endDay, startDay) === 1;\r\n};\r\n\r\n\r\nexport const normalizeEventTimes = (event: CalendarEvent): CalendarEvent => {\r\n  const start = new Date(event.start);\r\n  const end = new Date(event.end);\r\n  \r\n  // Ensure end time is after start time\r\n  if (end <= start) {\r\n    const normalizedEnd = new Date(start.getTime() + (30 * 60 * 1000)); // Add 30 minutes\r\n    return {\r\n      ...event,\r\n      end: normalizedEnd\r\n    };\r\n  }\r\n  \r\n  return event;\r\n};\r\n\r\n\r\nexport const getEventTotalDuration = (event: CalendarEvent): {\r\n  days: number;\r\n  hours: number;\r\n  minutes: number;\r\n  totalMinutes: number;\r\n} => {\r\n  const start = new Date(event.start);\r\n  const end = new Date(event.end);\r\n  \r\n  const totalMinutes = Math.max(0, (end.getTime() - start.getTime()) / (1000 * 60));\r\n  const days = Math.floor(totalMinutes / (24 * 60));\r\n  const remainingMinutes = totalMinutes % (24 * 60);\r\n  const hours = Math.floor(remainingMinutes / 60);\r\n  const minutes = remainingMinutes % 60;\r\n  \r\n  return { days, hours, minutes, totalMinutes };\r\n};\r\n\r\n\r\nexport const shouldTreatAsFullDay = (event: CalendarEvent): boolean => {\r\n  const duration = getEventTotalDuration(event);\r\n  \r\n  \r\n  if (duration.days === 0 && duration.hours >= 22) {\r\n    return true;\r\n  }\r\n  \r\n  if (duration.days > 0) {\r\n    const start = new Date(event.start);\r\n    const end = new Date(event.end);\r\n    \r\n    const startHour = start.getHours();\r\n    const endHour = end.getHours();\r\n    \r\n    return startHour <= 6 && endHour >= 22;\r\n  }\r\n  \r\n  return false;\r\n};", "import React from 'react';\r\nimport { cn } from '@/lib/utils';\r\nimport { EventSegment } from '@/utils/multiDay';\r\nimport { ArrowTurnLeftIcon, ArrowTurnRightIcon, ArrowsLeftRightIcon } from '@/components/icons/FontAwesomeRegular';\r\n\r\ninterface MultiDayEventBadgeProps {\r\n  segment: EventSegment;\r\n  view: 'day' | 'week' | 'month';\r\n  size?: 'small' | 'medium' | 'large';\r\n  className?: string;\r\n  isEndOfEvent?: boolean;\r\n}\r\n\r\nexport const MultiDayEventBadge: React.FC<MultiDayEventBadgeProps> = ({\r\n  segment,\r\n  view,\r\n  size = 'medium',\r\n  className = '',\r\n  isEndOfEvent\r\n}) => {\r\n  if (!segment.isMultiDay) return null;\r\n\r\n  const baseClasses = cn(\r\n    \"inline-flex items-center justify-center font-medium text-xs\",\r\n    \"transition-colors duration-200\",\r\n    className\r\n  );\r\n\r\n  const getArrowIcon = () => {\r\n    if (view === 'week') {\r\n      const startsThisWeek = segment.isFirstSegment;\r\n      const endsThisWeek = isEndOfEvent;\r\n\r\n      if (startsThisWeek && !endsThisWeek) return <ArrowTurnRightIcon className=\"h-2 w-2\" />;\r\n      if (!startsThisWeek && endsThisWeek) return <ArrowTurnLeftIcon className=\"h-2 w-2\" />;\r\n      if (!startsThisWeek && !endsThisWeek) return <ArrowsLeftRightIcon className=\"h-2 w-2\" />;\r\n      return null;\r\n    }\r\n\r\n    if (segment.isFirstSegment && !segment.isLastSegment) return <ArrowTurnRightIcon className=\"h-2 w-2\" />;\r\n    if (!segment.isFirstSegment && segment.isLastSegment) return <ArrowTurnLeftIcon className=\"h-2 w-2\" />;\r\n    if (!segment.isFirstSegment && !segment.isLastSegment) return <ArrowsLeftRightIcon className=\"h-2 w-2\" />;\r\n    \r\n    return null;\r\n  };\r\n\r\n  const getDayIndicator = () => {\r\n    if (size === 'small' || view === 'month') {\r\n      return getArrowIcon();\r\n    }\r\n    \r\n    if (segment.isFirstSegment) {\r\n      return `1/${segment.totalSegments}`;\r\n    } else if (segment.isLastSegment) {\r\n      return `${segment.totalSegments}/${segment.totalSegments}`;\r\n    } else {\r\n      return `${segment.segmentIndex + 1}/${segment.totalSegments}`;\r\n    }\r\n  };\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'small':\r\n        return 'text-[0.6rem] px-1 py-0.5 min-w-4 h-4';\r\n      case 'large':\r\n        return 'text-xs px-2 py-1 min-w-6 h-6';\r\n      default:\r\n        return 'text-[0.65rem] px-1.5 py-0.5 min-w-5 h-5';\r\n    }\r\n  };\r\n\r\n  const getColorClasses = () => {\r\n    return 'bg-gray-100 text-gray-600 border border-gray-200';\r\n  };\r\n\r\n  const dayIndicator = getDayIndicator();\r\n\r\n  if (!dayIndicator) {\r\n    return null;\r\n  }\r\n\r\n  const getTooltipText = () => {\r\n    const eventTitle = segment.originalEvent.title;\r\n    const totalWeeks = Math.ceil(segment.totalSegments / 7);\r\n\r\n    if (view === 'week') {\r\n      const startDay = new Date(segment.originalEvent.start);\r\n      const currentDay = segment.date;\r\n      const weekNumber = Math.floor(Math.abs(currentDay.getTime() - startDay.getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;\r\n\r\n      if (totalWeeks > 1) {\r\n        return `${eventTitle} (Week ${weekNumber} of ${totalWeeks})`;\r\n      }\r\n    }\r\n    \r\n    return `Day ${segment.segmentIndex + 1} of ${segment.totalSegments} - ${eventTitle}`;\r\n  };\r\n\r\n  return (\r\n    <span \r\n      className={cn(\r\n        baseClasses,\r\n        getSizeClasses(),\r\n        getColorClasses(),\r\n        'rounded-full shadow-sm'\r\n      )}\r\n      title={getTooltipText()}\r\n    >\r\n      {dayIndicator}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport const ContinuationArrow: React.FC<{\r\n  direction: 'left' | 'right' | 'both';\r\n  className?: string;\r\n}> = ({ direction, className = '' }) => {\r\n  const getArrow = () => {\r\n    switch (direction) {\r\n      case 'left':\r\n        return <ArrowTurnLeftIcon className=\"h-2 w-2\" />;\r\n      case 'right':\r\n        return <ArrowTurnRightIcon className=\"h-2 w-2\" />;\r\n      case 'both':\r\n        return <ArrowsLeftRightIcon className=\"h-2 w-2\" />;\r\n      default:\r\n        return '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <span \r\n      className={cn(\r\n        \"inline-flex items-center justify-center\",\r\n        \"text-xs opacity-70 font-medium\",\r\n        \"transition-opacity duration-200\",\r\n        className\r\n      )}\r\n    >\r\n      {getArrow()}\r\n    </span>\r\n  );\r\n}; ", "import React, { useMemo, useRef } from 'react';\r\nimport { cn } from '@/lib/utils';\r\nimport { ColorInfo } from '@/utils/color';\r\nimport { formatEventTime, getEventSize } from '@/utils/dateUtils';\r\nimport { EventSegment, getSegmentStylingClasses, shouldShowTimeInSegment, getSegmentContinuationText } from '@/utils/multiDay';\r\nimport { MultiDayEventBadge, ContinuationArrow } from '@/components/workspace/main/views/calendar/components/multidayevents';\r\nimport { useDraggable } from '@dnd-kit/core';\r\n\r\nexport const CalendarEventSegment = ({\r\n  segment,\r\n  style,\r\n  onClick,\r\n  onContextMenu,\r\n  view = 'month',\r\n  isEndOfEvent,\r\n  isDragging\r\n}: {\r\n  segment: EventSegment;\r\n  style?: React.CSSProperties;\r\n  onClick: (e: React.MouseEvent) => void;\r\n  onContextMenu?: (e: React.MouseEvent) => void;\r\n  view?: 'day' | 'week' | 'month';\r\n  isEndOfEvent?: boolean;\r\n  isDragging?: boolean;\r\n}) => {\r\n  const dragRef = useRef<HTMLDivElement>(null);\r\n  const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = useDraggable({\r\n    id: `segment-${segment.id}`,\r\n    data: {\r\n      type: 'segment',\r\n      payload: segment,\r\n    },\r\n    disabled: segment.isMultiDay || segment.isAllDay, // Disable dragging for multi-day and all-day events\r\n  });\r\n\r\n  const combinedIsDragging = isDragging || dndIsDragging;\r\n\r\n  // Memoize event calculations\r\n  const eventDetails = useMemo(() => {\r\n    const eventHeight = style?.height ? parseInt(style.height.toString().replace('px', '')) : null;\r\n    const showTime = shouldShowTimeInSegment(segment, view);\r\n    const continuationText = getSegmentContinuationText(segment);\r\n   \r\n\r\n    return { \r\n      eventSize: getEventSize(eventHeight),\r\n      showTime,\r\n      continuationText,\r\n      formattedTime: showTime ? formatEventTime(segment.startTime, view, { shortFormat: true }) : null\r\n    };\r\n  }, [segment, style, view]);\r\n\r\n  // Memoize styling\r\n  const eventStyles = useMemo(() => {\r\n    const denimColorInfo = ColorInfo('Denim');\r\n    const stylingClasses = getSegmentStylingClasses(segment);\r\n    \r\n    // Extract RGB values from the rgba string and make it fully opaque\r\n    const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\r\n    const opaqueBackground = rgbMatch \r\n      ? `rgb(${rgbMatch[1]}, ${rgbMatch[2]}, ${rgbMatch[3]})`\r\n      : denimColorInfo.bg;\r\n    \r\n    return {\r\n      style: {\r\n        ...style,\r\n        backgroundColor: opaqueBackground,\r\n        minHeight: '24px', // Adjusted for consistency\r\n        // Add subtle shadow for better visual depth\r\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',\r\n        opacity: combinedIsDragging ? 0.5 : 1,\r\n      },\r\n      classes: stylingClasses\r\n    };\r\n  }, [style, segment, combinedIsDragging]);\r\n\r\n  // Memoize classes\r\n  const eventClasses = useMemo(() => {\r\n    const baseClasses = cn(\r\n      \"select-none text-black text-xs overflow-hidden relative\",\r\n      !combinedIsDragging && \"cursor-pointer\",\r\n      eventStyles.classes.roundedCorners,\r\n      eventStyles.classes.continuationIndicator,\r\n      eventStyles.classes.opacity,\r\n      \"p-1\",\r\n    );\r\n\r\n    // Month view or small events\r\n    if (view === 'month' || eventDetails.eventSize === 'small') {\r\n      return {\r\n        baseClasses,\r\n        containerClasses: cn(\r\n          \"flex items-center space-x-1 flex-nowrap\"\r\n        ),\r\n        titleClasses: cn(\r\n          \"font-medium truncate leading-tight text-xs overflow-hidden\",\r\n          segment.isMultiDay ? \"max-w-[60%]\" : \"max-w-[70%]\"\r\n        ),\r\n        timeClasses: cn(\r\n          \"opacity-75 text-xs flex-shrink-0 text-[0.65rem]\"\r\n        ),\r\n        continuationClasses: cn(\r\n          \"text-xs opacity-60 flex-shrink-0 text-[0.6rem]\"\r\n        )\r\n      };\r\n    }\r\n\r\n    // Day and Week views for medium/large events\r\n    return {\r\n      baseClasses: cn(baseClasses, \"p-2\"),\r\n      containerClasses: cn(\r\n        \"flex flex-col\",\r\n        \"space-y-0.5\"\r\n      ),\r\n      titleClasses: cn(\r\n        \"font-medium truncate leading-tight text-xs overflow-hidden\"\r\n      ),\r\n      timeClasses: cn(\r\n        \"opacity-75 text-xs\"\r\n      ),\r\n      continuationClasses: cn(\r\n        \"text-xs opacity-60\"\r\n      )\r\n    };\r\n  }, [eventDetails, view, segment.isMultiDay, eventStyles.classes, combinedIsDragging]);\r\n\r\n  // Render event content based on view and size\r\n  const renderEventContent = () => {\r\n    const event = segment.originalEvent;\r\n    \r\n    // Month view or small events - horizontal layout\r\n    if (view === 'month' || eventDetails.eventSize === 'small') {\r\n      return (\r\n        <div className={eventClasses.containerClasses}>\r\n          <span className={eventClasses.titleClasses}>{event.title}</span>\r\n          {eventDetails.showTime && eventDetails.formattedTime && (\r\n            <span className={eventClasses.timeClasses}>\r\n              {eventDetails.formattedTime}\r\n            </span>\r\n          )}\r\n          {segment.isMultiDay && (\r\n            <MultiDayEventBadge\r\n              segment={segment}\r\n              view={view}\r\n              size=\"small\"\r\n              className={eventClasses.continuationClasses}\r\n              isEndOfEvent={isEndOfEvent}\r\n            />\r\n          )}\r\n        </div>\r\n      );\r\n    }\r\n\r\n    // Day and Week views - vertical layout\r\n    return (\r\n      <div className={eventClasses.containerClasses}>\r\n        <div className={eventClasses.titleClasses}>\r\n          {event.title}\r\n          {segment.isMultiDay && !eventDetails.showTime && (\r\n            <ContinuationArrow\r\n              direction={segment.isFirstSegment ? 'right' : segment.isLastSegment ? 'left' : 'both'}\r\n              className=\"ml-1\"\r\n            />\r\n          )}\r\n        </div>\r\n        {(eventDetails.showTime || segment.isAllDay) && eventDetails.formattedTime && (\r\n          <div className={eventClasses.timeClasses}>\r\n            {eventDetails.formattedTime}\r\n            {segment.isMultiDay && (\r\n              <ContinuationArrow\r\n                direction={segment.isFirstSegment ? 'right' : segment.isLastSegment ? 'left' : 'both'}\r\n                className=\"ml-1\"\r\n              />\r\n            )}\r\n          </div>\r\n        )}\r\n        {segment.isMultiDay && (\r\n          <div className={eventClasses.continuationClasses}>\r\n            <MultiDayEventBadge\r\n              segment={segment}\r\n              view={view}\r\n              size={eventDetails.eventSize === 'large' ? 'medium' : 'small'}\r\n              isEndOfEvent={isEndOfEvent}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div\r\n      id={`event-${segment.originalEvent.id}`}\r\n      ref={(node) => {\r\n        setNodeRef(node);\r\n        (dragRef as React.MutableRefObject<HTMLDivElement | null>).current = node;\r\n      }}\r\n      style={eventStyles.style}\r\n      className={eventClasses.baseClasses}\r\n      onClick={onClick}\r\n      onContextMenu={onContextMenu}\r\n      {...(segment.isMultiDay || segment.isAllDay ? {} : { ...listeners, ...attributes })} // Only add drag listeners if not multi-day or all-day\r\n    >\r\n      {renderEventContent()}\r\n    </div>\r\n  );\r\n}; ", "import React from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { PlusIcon } from '@heroicons/react/24/outline';\r\n\r\ninterface NoEventsProps {\r\n  title: string;\r\n  message: string;\r\n  showCreateButton: boolean;\r\n  onCreate: () => void;\r\n}\r\n\r\nexport const NoEvents: React.FC<NoEventsProps> = ({ title, message, showCreateButton, onCreate }) => {\r\n  return (\r\n    <div className=\"flex-1 flex items-center justify-center bg-neutral-50\">\r\n      <div className=\"text-center max-w-md mx-auto px-6\">\r\n        <div className=\"w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center border border-neutral-300\">\r\n          <svg className=\"w-8 h-8 text-black\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\" />\r\n          </svg>\r\n        </div>\r\n        <h3 className=\"text-xs font-semibold text-black mb-2\">\r\n          {title}\r\n        </h3>\r\n        <p className=\"text-xs text-black mb-6\">\r\n          {message}\r\n        </p>\r\n        {showCreateButton && (\r\n          <Button\r\n            onClick={onCreate}\r\n            className=\"rounded-full h-8 px-3 text-xs border bg-white hover:bg-neutral-100 text-black gap-1\"\r\n          >\r\n            <PlusIcon className=\"w-3 h-3\" />\r\n            Create Event\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}; ", "import { CalendarEvent } from '@/typings/page';\r\nimport { EventSegment } from './multiDay';\r\nimport { isSameDay } from 'date-fns';\r\n\r\nconst GUTTER_WIDTH_PERCENT = 10; // Space on the right for creating new events\r\nconst CASCADE_STAGGER_PERCENT = 15; // How much to offset each cascading event\r\nconst MIN_EVENT_WIDTH_PERCENT = 50; // Minimum width for events to remain readable\r\n\r\nexport interface SegmentLayout {\r\n  segment: EventSegment;\r\n  left: number;\r\n  width: number;\r\n  zIndex: number;\r\n  hasOverlap: boolean; // New property to indicate if this event overlaps with others\r\n}\r\n\r\nexport interface FinalLayout {\r\n  segmentLayouts: SegmentLayout[];\r\n}\r\n\r\n/**\r\n * Checks if two event segments overlap in time on the same day.\r\n */\r\nconst segmentsOverlap = (segment1: EventSegment, segment2: EventSegment): boolean => {\r\n  // Must be on the same day\r\n  if (!isSameDay(segment1.date, segment2.date)) {\r\n    return false;\r\n  }\r\n  return segment1.startTime < segment2.endTime && segment2.startTime < segment1.endTime;\r\n};\r\n\r\nexport const calculateLayout = (segments: EventSegment[]): FinalLayout => {\r\n  const finalLayout: FinalLayout = {\r\n    segmentLayouts: [],\r\n  };\r\n\r\n  if (!segments.length) {\r\n    return finalLayout;\r\n  }\r\n\r\n  // Sort all segments by start time, then by duration (longer events first to establish columns)\r\n  const sortedSegments = [...segments].sort((a, b) => {\r\n    const startDiff = a.startTime.getTime() - b.startTime.getTime();\r\n    if (startDiff !== 0) return startDiff;\r\n    const durationB = b.endTime.getTime() - b.startTime.getTime();\r\n    const durationA = a.endTime.getTime() - a.startTime.getTime();\r\n    return durationB - durationA;\r\n  });\r\n\r\n  const processedSegments = new Set<string>();\r\n\r\n  for (const segment of sortedSegments) {\r\n    if (processedSegments.has(segment.id)) {\r\n      continue;\r\n    }\r\n\r\n    // Find all overlapping segments for the current segment\r\n    const overlappingGroup = sortedSegments.filter(s => segmentsOverlap(segment, s));\r\n    \r\n    // This will hold the columns of segments for the current overlapping group\r\n    const columns: EventSegment[][] = [];\r\n    \r\n    overlappingGroup.forEach(groupSegment => {\r\n      let placed = false;\r\n      // Find the first column where this segment can fit\r\n      for (const column of columns) {\r\n        if (column.every(s => !segmentsOverlap(groupSegment, s))) {\r\n          column.push(groupSegment);\r\n          placed = true;\r\n          break;\r\n        }\r\n      }\r\n      // If it doesn't fit in any existing column, create a new one\r\n      if (!placed) {\r\n        columns.push([groupSegment]);\r\n      }\r\n    });\r\n\r\n    const maxColumns = columns.length;\r\n    const availableWidth = 100 - GUTTER_WIDTH_PERCENT;\r\n\r\n    // --- SMART CASCADING LOGIC ---\r\n    const totalCascadeSpace = (maxColumns - 1) * CASCADE_STAGGER_PERCENT;\r\n    let eventWidth = availableWidth - totalCascadeSpace;\r\n    let stagger = CASCADE_STAGGER_PERCENT;\r\n\r\n    if (eventWidth < MIN_EVENT_WIDTH_PERCENT && maxColumns > 1) {\r\n      eventWidth = MIN_EVENT_WIDTH_PERCENT;\r\n      const remainingSpace = availableWidth - eventWidth;\r\n      stagger = remainingSpace / (maxColumns - 1);\r\n    }\r\n\r\n    columns.forEach((column, colIndex) => {\r\n      column.forEach(seg => {\r\n        if (processedSegments.has(seg.id)) return;\r\n\r\n        const leftPosition = colIndex * stagger;\r\n        \r\n        finalLayout.segmentLayouts.push({\r\n          segment: seg,\r\n          left: leftPosition,\r\n          width: eventWidth,\r\n          zIndex: 10 + colIndex,\r\n          hasOverlap: maxColumns > 1,\r\n        });\r\n\r\n        processedSegments.add(seg.id);\r\n      });\r\n    });\r\n     overlappingGroup.forEach(s => processedSegments.add(s.id));\r\n  }\r\n  \r\n  return finalLayout;\r\n};\r\n\r\n\r\nexport const eventsOverlap = (event1: CalendarEvent, event2: CalendarEvent): boolean => {\r\n  const start1 = new Date(event1.start);\r\n  const end1 = new Date(event1.end);\r\n  const start2 = new Date(event2.start);\r\n  const end2 = new Date(event2.end);\r\n  \r\n  return start1 < end2 && start2 < end1;\r\n};\r\n\r\n\r\nexport const calculateAllDayLayout = (\r\n  segments: EventSegment[],\r\n  maxVisible: number = 3,\r\n): { visibleSegments: EventSegment[]; moreCount: number } => {\r\n  if (segments.length <= maxVisible) {\r\n    return { visibleSegments: segments, moreCount: 0 };\r\n  }\r\n\r\n  // Sort by start time, then duration.\r\n  const sorted = [...segments].sort((a, b) => {\r\n    const startDiff = a.startTime.getTime() - b.startTime.getTime();\r\n    if (startDiff !== 0) return startDiff;\r\n    return (b.endTime.getTime() - b.startTime.getTime()) - (a.endTime.getTime() - a.startTime.getTime());\r\n  });\r\n\r\n  const visibleSegments = sorted.slice(0, maxVisible - 1);\r\n  const moreCount = segments.length - visibleSegments.length;\r\n\r\n  return { visibleSegments, moreCount };\r\n};\r\n", "import React, { useMemo } from 'react';\r\nimport { format, addDays, startOfWeek, endOfWeek, isSameDay } from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\nimport { CalendarEvent } from '@/typings/page';\r\nimport { EventSegment } from '@/utils/multiDay';\r\nimport { CalendarEventSegment } from '@/components/workspace/main/views/calendar/components/eventsegment';\r\nimport { calculateAllDayLayout } from '@/utils/eventCollision';\r\nimport { useDroppable } from '@dnd-kit/core';\r\n\r\ninterface AllDayRowProps {\r\n  selectedDate: Date;\r\n  segments: EventSegment[];\r\n  selectedEvent: string | null;\r\n  setSelectedEvent: (id: string) => void;\r\n  handleEventClick: (event: CalendarEvent) => void;\r\n  canEditData: boolean;\r\n  openAddEventForm: (date: Date) => void;\r\n  view?: 'day' | 'week';\r\n  activeDragData?: any;\r\n}\r\n\r\ninterface SpanningEvent {\r\n  segment: EventSegment;\r\n  startDayIndex: number;\r\n  endDayIndex: number;\r\n  colSpan: number;\r\n  isEndOfEvent: boolean;\r\n}\r\n\r\nexport const AllDayRow: React.FC<AllDayRowProps> = ({\r\n  selectedDate,\r\n  segments,\r\n  selectedEvent,\r\n  setSelectedEvent,\r\n  handleEventClick,\r\n  canEditData,\r\n  openAddEventForm,\r\n  view,\r\n  activeDragData\r\n}) => {\r\n  // Create individual droppable hooks for each possible day\r\n  // IMPORTANT: All hooks must be called before any conditional returns\r\n  const dayViewHook = useDroppable({\r\n    id: `allday-${format(selectedDate, 'yyyy-MM-dd')}`,\r\n    data: {\r\n      date: selectedDate,\r\n      type: 'allday-day'\r\n    }\r\n  });\r\n\r\n  const weekDay1 = useDroppable({\r\n    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 0), 'yyyy-MM-dd')}`,\r\n    data: {\r\n      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 0),\r\n      type: 'allday-week'\r\n    }\r\n  });\r\n\r\n  const weekDay2 = useDroppable({\r\n    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 1), 'yyyy-MM-dd')}`,\r\n    data: {\r\n      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 1),\r\n      type: 'allday-week'\r\n    }\r\n  });\r\n\r\n  const weekDay3 = useDroppable({\r\n    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 2), 'yyyy-MM-dd')}`,\r\n    data: {\r\n      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 2),\r\n      type: 'allday-week'\r\n    }\r\n  });\r\n\r\n  const weekDay4 = useDroppable({\r\n    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 3), 'yyyy-MM-dd')}`,\r\n    data: {\r\n      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 3),\r\n      type: 'allday-week'\r\n    }\r\n  });\r\n\r\n  const weekDay5 = useDroppable({\r\n    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 4), 'yyyy-MM-dd')}`,\r\n    data: {\r\n      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 4),\r\n      type: 'allday-week'\r\n    }\r\n  });\r\n\r\n  const weekDay6 = useDroppable({\r\n    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 5), 'yyyy-MM-dd')}`,\r\n    data: {\r\n      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 5),\r\n      type: 'allday-week'\r\n    }\r\n  });\r\n\r\n  const weekDay7 = useDroppable({\r\n    id: `allday-${format(addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 6), 'yyyy-MM-dd')}`,\r\n    data: {\r\n      date: addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), 6),\r\n      type: 'allday-week'\r\n    }\r\n  });\r\n\r\n  const weekViewHooks = [weekDay1, weekDay2, weekDay3, weekDay4, weekDay5, weekDay6, weekDay7];\r\n\r\n  // If no all-day or multi-day events, don't render the row\r\n  // MOVED AFTER HOOKS: This conditional return is now after all hooks are called\r\n  if (segments.length === 0 && !activeDragData) { // also show if dragging\r\n    return null;\r\n  }\r\n\r\n\r\n  const renderDayView = () => (\r\n    <div className=\"border-b border-neutral-300 bg-white\">\r\n      <div className=\"flex\">\r\n        <div className=\"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black\">\r\n          <div className=\"text-right\">\r\n            <div className=\"text-xs font-semibold\">\r\n              All-day\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div \r\n          ref={dayViewHook.setNodeRef}\r\n          className={cn(\r\n            \"flex-1 relative p-2 space-y-1\",\r\n            dayViewHook.isOver && \"bg-blue-50\"\r\n          )}\r\n        >\r\n          {segments.slice(0, 3).map((segment) => (\r\n            <CalendarEventSegment\r\n              key={segment.id}\r\n              segment={segment}\r\n              style={{ height: '24px', width: '100%' }}\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                setSelectedEvent(segment.originalEventId);\r\n                handleEventClick(segment.originalEvent);\r\n              }}\r\n              view=\"day\"\r\n              isDragging={activeDragData?.payload?.id === segment.id}\r\n            />\r\n          ))}\r\n          {segments.length > 3 && (\r\n            <div className=\"text-xs text-neutral-600 font-medium cursor-pointer hover:text-neutral-800\">\r\n              + {segments.length - 3} more\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderWeekView = () => {\r\n    const weekDays = Array.from({ length: 7 }, (_, i) => addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), i));\r\n\r\n    const spanningEvents = (() => {\r\n      const eventGroups = new Map<string, EventSegment[]>();\r\n      segments.forEach(segment => {\r\n        if (segment.isMultiDay || segment.isAllDay) {\r\n          const eventId = segment.originalEventId;\r\n          if (!eventGroups.has(eventId)) eventGroups.set(eventId, []);\r\n          eventGroups.get(eventId)!.push(segment);\r\n        }\r\n      });\r\n\r\n      const spanning: SpanningEvent[] = [];\r\n      eventGroups.forEach((eventSegments) => {\r\n        eventSegments.sort((a, b) => a.date.getTime() - b.date.getTime());\r\n        const firstSegmentInWeek = eventSegments[0];\r\n        const lastSegmentInWeek = eventSegments[eventSegments.length - 1];\r\n        const startDayIndex = weekDays.findIndex(day => isSameDay(day, firstSegmentInWeek.date));\r\n        const endDayIndex = weekDays.findIndex(day => isSameDay(day, lastSegmentInWeek.date));\r\n\r\n        if (startDayIndex >= 0 && endDayIndex >= 0) {\r\n          spanning.push({\r\n            segment: firstSegmentInWeek,\r\n            startDayIndex,\r\n            endDayIndex,\r\n            colSpan: endDayIndex - startDayIndex + 1,\r\n            isEndOfEvent: lastSegmentInWeek.isLastSegment,\r\n          });\r\n        }\r\n      });\r\n      return spanning;\r\n    })();\r\n\r\n    const positionedEvents = (() => {\r\n      const positioned: Array<SpanningEvent & { row: number }> = [];\r\n      const rows: Array<SpanningEvent[]> = [];\r\n      const sortedEvents = [...spanningEvents].sort((a, b) => a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\r\n\r\n      sortedEvents.forEach(event => {\r\n        let assigned = false;\r\n        for (let i = 0; i < rows.length; i++) {\r\n          const row = rows[i];\r\n          if (row.every(rowEvent => event.startDayIndex > rowEvent.endDayIndex || event.endDayIndex < rowEvent.startDayIndex)) {\r\n            row.push(event);\r\n            positioned.push({ ...event, row: i });\r\n            assigned = true;\r\n            break;\r\n          }\r\n        }\r\n        if (!assigned) {\r\n          rows.push([event]);\r\n          positioned.push({ ...event, row: rows.length - 1 });\r\n        }\r\n      });\r\n      return positioned;\r\n    })();\r\n\r\n    const { visibleSegments, moreCount } = calculateAllDayLayout(\r\n      positionedEvents.map(e => e.segment), 3\r\n    );\r\n\r\n    const visibleEvents = positionedEvents.filter(p => visibleSegments.some(s => s.id === p.segment.id));\r\n    const hasMore = moreCount > 0;\r\n\r\n    const firstEventDayIndex = positionedEvents.length > 0 ? Math.min(...positionedEvents.map(e => e.startDayIndex)) : 0;\r\n    \r\n    if (positionedEvents.length === 0) return null;\r\n\r\n    const maxRows = positionedEvents.length > 0 ? Math.max(...positionedEvents.map(e => e.row)) + 1 : 0;\r\n    const rowHeight = 28;\r\n    const displayRows = hasMore ? 3.5 : Math.max(1, maxRows); \r\n    const totalHeight = displayRows * rowHeight + 16;\r\n\r\n      return (\r\n    <div \r\n      data-all-day-row=\"true\"\r\n      className=\"border-b border-neutral-300 bg-white\"\r\n    >\r\n        <div className=\"flex\">\r\n          <div className=\"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black\">\r\n            <div className=\"text-right\">\r\n              <div className=\"text-xs font-semibold\">\r\n                All-day\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex-1 relative p-2\" style={{ height: `${totalHeight}px` }}>\r\n            <div className=\"grid grid-cols-7 gap-1 h-full\">\r\n              {weekDays.map((day, dayIndex) => {\r\n                const hook = weekViewHooks[dayIndex];\r\n\r\n                return (\r\n                <div \r\n                  ref={hook.setNodeRef}\r\n                  key={dayIndex} \r\n                  className={cn(\r\n                    \"relative cursor-pointer hover:bg-neutral-50 rounded-sm transition-colors\",\r\n                    hook.isOver && \"bg-blue-50\"\r\n                  )}\r\n                  onDoubleClick={() => {\r\n                    if (canEditData) {\r\n                      const newDate = new Date(day);\r\n                      newDate.setHours(9, 0, 0, 0);\r\n                      openAddEventForm(newDate);\r\n                    }\r\n                  }}\r\n                >\r\n                  {visibleEvents\r\n                    .filter(spanningEvent => spanningEvent.startDayIndex === dayIndex)\r\n                    .map((spanningEvent) => (\r\n                      <div\r\n                        key={spanningEvent.segment.id}\r\n                        className=\"absolute z-10\"\r\n                        style={{\r\n                          top: `${spanningEvent.row * rowHeight + 2}px`,\r\n                          left: '0px',\r\n                          width: `calc(${spanningEvent.colSpan * 100}% + ${(spanningEvent.colSpan - 1) * 4}px)`,\r\n                          height: '24px'\r\n                        }}\r\n                      >\r\n                        <CalendarEventSegment\r\n                          segment={spanningEvent.segment}\r\n                          isEndOfEvent={spanningEvent.isEndOfEvent}\r\n                          style={{ height: '24px', width: '100%' }}\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            setSelectedEvent(spanningEvent.segment.originalEventId);\r\n                            handleEventClick(spanningEvent.segment.originalEvent);\r\n                          }}\r\n                          view={view}\r\n                          isDragging={activeDragData?.payload?.id === spanningEvent.segment.id}\r\n                        />\r\n                      </div>\r\n                    ))}\r\n                  {hasMore && dayIndex === firstEventDayIndex && (\r\n                    <div\r\n                      className=\"absolute z-10 cursor-pointer text-xs text-gray-600 hover:underline\"\r\n                      style={{\r\n                        top: `${3 * rowHeight + 2}px`,\r\n                        left: '4px',\r\n                        right: '4px',\r\n                      }}\r\n                      onClick={() => console.log('More clicked')}\r\n                    >\r\n                      +{moreCount} more\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                )\r\n              })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return view === 'day' ? renderDayView() : renderWeekView();\r\n}; \r\n", "import React, { useMemo, useState, useCallback } from 'react';\r\nimport { format, isToday, setHours } from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\nimport { CalendarEvent } from '@/typings/page';\r\nimport { CalendarEventSegment } from '@/components/workspace/main/views/calendar/components/eventsegment';\r\nimport { NoEvents } from '@/components/workspace/main/views/calendar/components/noevents';\r\nimport {eventsToSegments, getSegmentsForDay, getSegmentHeight, getSegmentTopOffset, getAllDaySegments, getTimeSlotSegments} from '@/utils/multiDay';\r\nimport { calculateLayout } from '@/utils/eventCollision';\r\nimport { AllDayRow } from '@/components/workspace/main/views/calendar/components/allday';\r\nimport { useDroppable } from '@dnd-kit/core';\r\n\r\n\r\ninterface DayViewProps {\r\n  selectedDate: Date;\r\n  events: CalendarEvent[];\r\n  selectedEvent: string | null;\r\n  setSelectedEvent: (id: string) => void;\r\n  openAddEventForm: (date: Date) => void;\r\n  canEditData: boolean;\r\n  savedScrollTop: React.MutableRefObject<number>;\r\n  handleEventClick: (event: CalendarEvent) => void;\r\n  activeDragData: any;\r\n}\r\n\r\nconst TimeSlot = ({ \r\n  hour, \r\n  date, \r\n  onDoubleClick, \r\n  children,\r\n  isDragging\r\n}: {\r\n  hour: number;\r\n  date: Date;\r\n  onDoubleClick: (minute: number) => void;\r\n  children: React.ReactNode;\r\n  isDragging: boolean;\r\n}) => {\r\n  const [isHovering, setIsHovering] = useState(false);\r\n  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);\r\n  const [currentMinute, setCurrentMinute] = useState<number>(0);\r\n  const containerRef = React.useRef<HTMLDivElement>(null);\r\n\r\n  const shouldShowPrecision = isHovering || isDragging;\r\n\r\n  const calculateMinuteFromPosition = useCallback((clientY: number) => {\r\n    if (!containerRef.current) return 0;\r\n    \r\n    const rect = containerRef.current.getBoundingClientRect();\r\n    const y = clientY - rect.top;\r\n    const minute = Math.floor((y / rect.height) * 60);\r\n    return Math.max(0, Math.min(59, minute));\r\n  }, []);\r\n\r\n  const handleMouseEnter = useCallback(() => {\r\n    setIsHovering(true);\r\n  }, []);\r\n\r\n  const handleMouseLeave = useCallback(() => {\r\n    if (!isDragging) {\r\n      setIsHovering(false);\r\n      setMousePosition(null);\r\n      setCurrentMinute(0);\r\n    }\r\n  }, [isDragging]);\r\n\r\n  const handleMouseMove = useCallback((e: React.MouseEvent) => {\r\n    const minute = calculateMinuteFromPosition(e.clientY);\r\n    setMousePosition({ x: e.clientX, y: e.clientY });\r\n    setCurrentMinute(minute);\r\n  }, [calculateMinuteFromPosition]);\r\n\r\n  React.useEffect(() => {\r\n    if (!isDragging) return;\r\n\r\n    const handleGlobalMouseMove = (e: MouseEvent) => {\r\n      if (containerRef.current) {\r\n        const rect = containerRef.current.getBoundingClientRect();\r\n        // Check if mouse is over this time slot\r\n        if (e.clientX >= rect.left && e.clientX <= rect.right && \r\n            e.clientY >= rect.top && e.clientY <= rect.bottom) {\r\n          const minute = calculateMinuteFromPosition(e.clientY);\r\n          setMousePosition({ x: e.clientX, y: e.clientY });\r\n          setCurrentMinute(minute);\r\n          setIsHovering(true);\r\n        }\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousemove', handleGlobalMouseMove);\r\n    return () => document.removeEventListener('mousemove', handleGlobalMouseMove);\r\n  }, [isDragging, calculateMinuteFromPosition]);\r\n\r\n  const handleClick = useCallback((e: React.MouseEvent) => {\r\n    if (e.detail === 2) { // Double click\r\n      onDoubleClick(currentMinute);\r\n    }\r\n  }, [currentMinute, onDoubleClick]);\r\n\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className=\"flex-1 relative min-h-[60px] cursor-pointer\"\r\n      style={{ height: '60px' }}\r\n      onMouseEnter={handleMouseEnter}\r\n      onMouseLeave={handleMouseLeave}\r\n      onMouseMove={handleMouseMove}\r\n      onClick={handleClick}\r\n    >\r\n      {/* Single droppable zone for the entire hour */}\r\n      <HourDropZone \r\n        date={date} \r\n        hour={hour} \r\n        currentMinute={currentMinute}\r\n        isActive={shouldShowPrecision}\r\n      />\r\n      \r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\n// Optimized single drop zone per hour with minute precision\r\nconst HourDropZone = ({\r\n  date,\r\n  hour,\r\n  currentMinute,\r\n  isActive\r\n}: {\r\n  date: Date;\r\n  hour: number;\r\n  currentMinute: number;\r\n  isActive: boolean;\r\n}) => {\r\n  const { setNodeRef, isOver } = useDroppable({\r\n    id: `hour-${format(date, 'yyyy-MM-dd')}-${hour}`,\r\n    data: {\r\n      date: date,\r\n      hour,\r\n      minute: currentMinute, // Use the calculated minute from mouse position\r\n      type: 'timeslot-minute'\r\n    }\r\n  });\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      className=\"absolute inset-0 w-full h-full\"\r\n    />\r\n  );\r\n};\r\n\r\nexport const DayView: React.FC<DayViewProps> = ({\r\n  selectedDate,\r\n  events,\r\n  selectedEvent,\r\n  setSelectedEvent,\r\n  openAddEventForm,\r\n  canEditData,\r\n  savedScrollTop,\r\n  handleEventClick,\r\n  activeDragData,\r\n}) => {\r\n  const hours = Array.from({ length: 24 }, (_, i) => i);\r\n  \r\n  // Memoize event segments to prevent unnecessary recalculations\r\n  const daySegments = useMemo(() => {\r\n    const allSegments = eventsToSegments(events);\r\n    return getSegmentsForDay(allSegments, selectedDate);\r\n  }, [events, selectedDate]);\r\n\r\n  // Separate all-day and time-slot segments\r\n  const allDaySegments = useMemo(() => getAllDaySegments(daySegments), [daySegments]);\r\n  const timeSlotSegments = useMemo(() => getTimeSlotSegments(daySegments), [daySegments]);\r\n\r\n  // Calculate layout for overlapping segments\r\n  const { segmentLayouts } = useMemo(() => {\r\n    return calculateLayout(timeSlotSegments);\r\n  }, [timeSlotSegments]);\r\n\r\n  // Memoize current time position\r\n  const currentTimePosition = useMemo(() => \r\n    isToday(selectedDate) \r\n      ? {\r\n          hour: new Date().getHours(),\r\n          minutes: new Date().getMinutes()\r\n        } \r\n      : null, \r\n    [selectedDate]\r\n  );\r\n\r\n  // Render empty state when no events\r\n  const renderEmptyState = () => (\r\n    <NoEvents\r\n      title=\"No events scheduled\"\r\n      message={isToday(selectedDate)\r\n        ? \"You have a free day ahead! Add an event to get started.\"\r\n        : `${format(selectedDate, 'EEEE, MMMM d')} is completely free.`}\r\n      showCreateButton={canEditData}\r\n      onCreate={() => {\r\n        const newDate = new Date(selectedDate);\r\n        newDate.setHours(9, 0, 0, 0);\r\n        openAddEventForm(newDate);\r\n      }}\r\n    />\r\n  );\r\n\r\n  // Render time slots with events\r\n  const renderTimeSlots = () => (\r\n    <div className=\"flex-1 overflow-auto relative bg-white\" id=\"day-view-container\">\r\n      {hours.map((hour, i) => (\r\n        <div \r\n          key={i} \r\n          className={cn(\r\n            \"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\",\r\n            i === hours.length - 1 && \"border-b-neutral-300\"\r\n          )} \r\n          style={{ height: '60px' }}\r\n        >\r\n          {/* Time Label */}\r\n          <div \r\n            data-time-labels=\"true\"\r\n            className=\"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20\"\r\n          >\r\n            <div className=\"text-right\">\r\n              <div className=\"text-xs font-semibold\">\r\n                {format(setHours(selectedDate, hour), 'h')}\r\n              </div>\r\n              <div className=\"text-xs text-black opacity-60\">\r\n                {format(setHours(selectedDate, hour), 'a')}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Time Slot */}\r\n          <TimeSlot\r\n            hour={hour}\r\n            date={selectedDate}\r\n            isDragging={!!activeDragData}\r\n            onDoubleClick={(minute) => {\r\n              if (canEditData) {\r\n                const newDate = new Date(selectedDate);\r\n                newDate.setHours(hour, minute, 0, 0);\r\n                openAddEventForm(newDate);\r\n              }\r\n            }}\r\n          >\r\n            {segmentLayouts.map((layout) => {\r\n              const segmentStart = layout.segment.startTime;\r\n              const isFirstHour = segmentStart.getHours() === hour;\r\n\r\n              if (!isFirstHour) return null;\r\n\r\n              const segmentHeight = getSegmentHeight(layout.segment);\r\n              const topOffset = getSegmentTopOffset(layout.segment);\r\n\r\n              return (\r\n                <CalendarEventSegment\r\n                  key={layout.segment.id}\r\n                  segment={layout.segment}\r\n                  style={{\r\n                    height: `${segmentHeight}px`,\r\n                    position: 'absolute',\r\n                    top: `${topOffset}px`,\r\n                    left: `${layout.left}%`,\r\n                    width: `${layout.width}%`,\r\n                    zIndex:\r\n                      activeDragData?.payload?.id === layout.segment.id ? 50 : \r\n                      layout.zIndex,\r\n                    paddingRight: '2px', // Add small gap between columns\r\n                    border: layout.hasOverlap ? '1px solid white' : 'none', // White border for overlapping events\r\n                  }}\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    const container = document.getElementById('day-view-container');\r\n                    if(container) {\r\n                      savedScrollTop.current = container.scrollTop;\r\n                    }\r\n                    setSelectedEvent(layout.segment.originalEventId);\r\n                    handleEventClick(layout.segment.originalEvent);\r\n                  }}\r\n                  view=\"day\"\r\n                  isDragging={activeDragData?.payload?.id === layout.segment.id}\r\n                />\r\n              );\r\n            })}\r\n          </TimeSlot>\r\n        </div>\r\n      ))}\r\n\r\n      {/* Current Time Indicator */}\r\n      {currentTimePosition && (\r\n        <div\r\n          className=\"absolute flex items-center z-30 pointer-events-none left-14 lg:left-20\"\r\n          style={{\r\n            top: `${(currentTimePosition.hour + currentTimePosition.minutes / 60) * 60}px`,\r\n            right: '4px'\r\n          }}\r\n        >\r\n          <div className=\"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\" />\r\n          <div className=\"flex-1 border-t-2 border-red-500 shadow-sm\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-full bg-white\">\r\n      {/* Header */}\r\n      <div className=\"text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0\">\r\n        <div className=\"font-semibold text-black mb-1 text-xs\">\r\n          {format(selectedDate, 'EEEE')}\r\n        </div>\r\n        <div className={cn(\r\n          \"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\",\r\n          isToday(selectedDate)\r\n            ? \"bg-black text-white\"\r\n            : \"text-black hover:bg-neutral-100\"\r\n        )}>\r\n          {format(selectedDate, 'd')}\r\n        </div>\r\n      </div>\r\n\r\n      {/* All-Day Section */}\r\n      {daySegments.length > 0 && (\r\n        <AllDayRow\r\n          selectedDate={selectedDate}\r\n          segments={allDaySegments}\r\n          selectedEvent={selectedEvent}\r\n          setSelectedEvent={setSelectedEvent}\r\n          handleEventClick={handleEventClick}\r\n          canEditData={canEditData}\r\n          openAddEventForm={openAddEventForm}\r\n          view=\"day\"\r\n          activeDragData={activeDragData}\r\n        />\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      {daySegments.length === 0 \r\n        ? renderEmptyState() \r\n        : renderTimeSlots()}\r\n    </div>\r\n  );\r\n};", "import React, { useMemo, useState, useCallback } from 'react';\r\nimport { format, startOfWeek, endOfWeek, isToday, addDays, setHours } from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\nimport { CalendarEvent } from '@/typings/page';\r\nimport { CalendarEventSegment } from '@/components/workspace/main/views/calendar/components/eventsegment';\r\nimport { AllDayRow } from '@/components/workspace/main/views/calendar/components/allday';\r\nimport { NoEvents } from '@/components/workspace/main/views/calendar/components/noevents';\r\nimport { eventsToSegments, getSegmentsForWeek, getSegmentsForDay, getAllDaySegments, getTimeSlotSegments, getSegmentHeight, getSegmentTopOffset } from '@/utils/multiDay';\r\nimport { calculateLayout } from '@/utils/eventCollision';\r\nimport { useDroppable } from '@dnd-kit/core';\r\n\r\n\r\ninterface WeekViewProps {\r\n  selectedDate: Date;\r\n  events: CalendarEvent[];\r\n  selectedEvent: string | null;\r\n  setSelectedEvent: (id: string) => void;\r\n  setSelectedDate: (date: Date) => void;\r\n  openAddEventForm: (date: Date) => void;\r\n  canEditData: boolean;\r\n  savedScrollTop: React.MutableRefObject<number>;\r\n  handleEventClick: (event: CalendarEvent) => void;\r\n  activeDragData: any;\r\n}\r\n\r\nconst TimeSlot = ({\r\n  day,\r\n  hour,\r\n  children,\r\n  onDoubleClick,\r\n  isDragging\r\n}: {\r\n  day: Date;\r\n  hour: number;\r\n  children: React.ReactNode;\r\n  onDoubleClick: (minute: number) => void;\r\n  isDragging: boolean;\r\n}) => {\r\n  const [isHovering, setIsHovering] = useState(false);\r\n  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);\r\n  const [currentMinute, setCurrentMinute] = useState<number>(0);\r\n  const containerRef = React.useRef<HTMLDivElement>(null);\r\n\r\n  const shouldShowPrecision = isHovering || isDragging;\r\n\r\n  const calculateMinuteFromPosition = useCallback((clientY: number) => {\r\n    if (!containerRef.current) return 0;\r\n\r\n    const rect = containerRef.current.getBoundingClientRect();\r\n    const y = clientY - rect.top;\r\n    const minute = Math.floor((y / rect.height) * 60);\r\n    return Math.max(0, Math.min(59, minute));\r\n  }, []);\r\n\r\n  const handleMouseEnter = useCallback(() => {\r\n    setIsHovering(true);\r\n  }, []);\r\n\r\n  const handleMouseLeave = useCallback(() => {\r\n    if (!isDragging) {\r\n      setIsHovering(false);\r\n      setMousePosition(null);\r\n      setCurrentMinute(0);\r\n    }\r\n  }, [isDragging]);\r\n\r\n  const handleMouseMove = useCallback((e: React.MouseEvent) => {\r\n    const minute = calculateMinuteFromPosition(e.clientY);\r\n    setMousePosition({ x: e.clientX, y: e.clientY });\r\n    setCurrentMinute(minute);\r\n  }, [calculateMinuteFromPosition]);\r\n\r\n  React.useEffect(() => {\r\n    if (!isDragging) return;\r\n\r\n    const handleGlobalMouseMove = (e: MouseEvent) => {\r\n      if (containerRef.current) {\r\n        const rect = containerRef.current.getBoundingClientRect();\r\n        if (e.clientX >= rect.left && e.clientX <= rect.right &&\r\n          e.clientY >= rect.top && e.clientY <= rect.bottom) {\r\n          const minute = calculateMinuteFromPosition(e.clientY);\r\n          setMousePosition({ x: e.clientX, y: e.clientY });\r\n          setCurrentMinute(minute);\r\n          setIsHovering(true);\r\n        }\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousemove', handleGlobalMouseMove);\r\n    return () => document.removeEventListener('mousemove', handleGlobalMouseMove);\r\n  }, [isDragging, calculateMinuteFromPosition]);\r\n\r\n  const handleClick = useCallback((e: React.MouseEvent) => {\r\n    if (e.detail === 2) { // Double click\r\n      onDoubleClick(currentMinute);\r\n    }\r\n  }, [currentMinute, onDoubleClick]);\r\n\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className=\"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\"\r\n      onMouseEnter={handleMouseEnter}\r\n      onMouseLeave={handleMouseLeave}\r\n      onMouseMove={handleMouseMove}\r\n      onClick={handleClick}\r\n    >\r\n      <HourDropZone\r\n        day={day}\r\n        hour={hour}\r\n        currentMinute={currentMinute}\r\n        isActive={shouldShowPrecision}\r\n      />\r\n\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst HourDropZone = ({\r\n  day,\r\n  hour,\r\n  currentMinute,\r\n  isActive\r\n}: {\r\n  day: Date;\r\n  hour: number;\r\n  currentMinute: number;\r\n  isActive: boolean;\r\n}) => {\r\n  const { setNodeRef, isOver } = useDroppable({\r\n    id: `hour-${format(day, 'yyyy-MM-dd')}-${hour}`,\r\n    data: {\r\n      date: day,\r\n      hour,\r\n      minute: currentMinute, \r\n      type: 'timeslot-minute'\r\n    }\r\n  });\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      className=\"absolute inset-0 w-full h-full\"\r\n    />\r\n  );\r\n};\r\n\r\nexport const WeekView: React.FC<WeekViewProps> = ({\r\n  selectedDate,\r\n  events,\r\n  selectedEvent,\r\n  setSelectedEvent,\r\n  setSelectedDate,\r\n  openAddEventForm,\r\n  canEditData,\r\n  savedScrollTop,\r\n  handleEventClick,\r\n  activeDragData,\r\n}) => {\r\n  const weekCalculations = useMemo(() => {\r\n    const weekStart = startOfWeek(selectedDate, { weekStartsOn: 0 });\r\n    const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 0 });\r\n    const days = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));\r\n    const todayIndex = days.findIndex(day => isToday(day));\r\n\r\n    return {\r\n      weekStart,\r\n      weekEnd,\r\n      days,\r\n      todayIndex\r\n    };\r\n  }, [selectedDate]);\r\n\r\n  const { days, todayIndex } = weekCalculations;\r\n  const hours = Array.from({ length: 24 }, (_, i) => i);\r\n\r\n  const weekSegments = useMemo(() => {\r\n    const allSegments = eventsToSegments(events);\r\n    return getSegmentsForWeek(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\r\n  }, [events, weekCalculations.weekStart, weekCalculations.weekEnd]);\r\n\r\n  const allDaySegments = useMemo(() => getAllDaySegments(weekSegments), [weekSegments]);\r\n  const timeSlotSegments = useMemo(() => getTimeSlotSegments(weekSegments), [weekSegments]);\r\n\r\n  const currentTimePosition = useMemo(() =>\r\n    todayIndex !== -1\r\n      ? {\r\n        dayIndex: todayIndex,\r\n        hour: new Date().getHours(),\r\n        minutes: new Date().getMinutes()\r\n      }\r\n      : null,\r\n    [todayIndex]\r\n  );\r\n\r\n  const getEventDurationInMinutes = (event: CalendarEvent): number => {\r\n    const start = new Date(event.start);\r\n    const end = new Date(event.end);\r\n    return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\r\n  };\r\n\r\n  const renderEmptyState = () => (\r\n    <NoEvents\r\n      title=\"No events this week\"\r\n      message=\"Your week is completely free. Add some events to get organized!\"\r\n      showCreateButton={canEditData}\r\n      onCreate={() => openAddEventForm(selectedDate)}\r\n    />\r\n  );\r\n\r\n  const renderTimeSlots = () => (\r\n    <div className=\"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\" id=\"week-view-container\">\r\n      <div className=\"relative overflow-x-auto lg:overflow-x-visible\">\r\n        <div className=\"flex flex-col min-w-[700px] lg:min-w-0\">\r\n          <div className=\"relative\">\r\n            {hours.map((hour, i) => (\r\n              <div\r\n                key={hour}\r\n                className={cn(\r\n                  \"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\",\r\n                  i === hours.length - 1 && \"border-b-neutral-300\"\r\n                )}\r\n                style={{ height: '60px' }}\r\n              >\r\n                <div\r\n                  data-time-labels=\"true\"\r\n                  className=\"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-200 bg-white z-20 w-14 lg:w-20\"\r\n                >\r\n                  <div className=\"text-right\">\r\n                    <div className=\"text-xs font-semibold\">\r\n                      {format(setHours(new Date(), hour), 'h a')}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {days.map((day) => {\r\n                  const daySegments = getSegmentsForDay(timeSlotSegments, day);\r\n                  const { segmentLayouts } = calculateLayout(daySegments);\r\n\r\n                  return (\r\n                    <TimeSlot\r\n                      key={`${day.toISOString()}-${hour}`}\r\n                      day={day}\r\n                      hour={hour}\r\n                      isDragging={!!activeDragData}\r\n                      onDoubleClick={(minute) => {\r\n                        if (canEditData) {\r\n                          const newDate = new Date(day);\r\n                          newDate.setHours(hour, minute, 0, 0);\r\n                          openAddEventForm(newDate);\r\n                        }\r\n                      }}\r\n                    >\r\n                      {segmentLayouts.map((layout) => {\r\n                        const segmentStart = layout.segment.startTime;\r\n                        const isFirstHour = segmentStart.getHours() === hour;\r\n\r\n                        if (!isFirstHour) return null;\r\n\r\n                        const segmentHeight = getSegmentHeight(layout.segment);\r\n                        const topOffset = getSegmentTopOffset(layout.segment);\r\n\r\n                        return (\r\n                          <CalendarEventSegment\r\n                            key={layout.segment.id}\r\n                            segment={layout.segment}\r\n                            style={{\r\n                              height: `${segmentHeight}px`,\r\n                              position: 'absolute',\r\n                              top: `${topOffset}px`,\r\n                              left: `${layout.left}%`,\r\n                              width: `${layout.width}%`,\r\n                              zIndex: activeDragData?.payload?.id === layout.segment.id ? 50 : layout.zIndex,\r\n                              paddingRight: '2px',\r\n                              border: layout.hasOverlap ? '1px solid white' : 'none',\r\n                            }}\r\n                            onClick={(e) => {\r\n                              e.stopPropagation();\r\n                              const container = document.getElementById('week-view-container');\r\n                              if (container) {\r\n                                savedScrollTop.current = container.scrollTop;\r\n                              }\r\n                              setSelectedEvent(layout.segment.originalEventId);\r\n                              handleEventClick(layout.segment.originalEvent);\r\n                            }}\r\n                            view=\"week\"\r\n                            isDragging={activeDragData?.payload?.id === layout.segment.id}\r\n                          />\r\n                        );\r\n                      })}\r\n                    </TimeSlot>\r\n                  );\r\n                })}\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {currentTimePosition && (\r\n            <div className=\"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\">\r\n              <div className=\"relative h-full w-full\">\r\n                <div\r\n                  className=\"absolute flex items-center\"\r\n                  style={{\r\n                    top: `${(currentTimePosition.hour + currentTimePosition.minutes / 60) * 60}px`,\r\n                    left: `${(currentTimePosition.dayIndex / 7) * 100}%`,\r\n                    width: `${(1 / 7) * 100}%`,\r\n                  }}\r\n                >\r\n                  <div className=\"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\" />\r\n                  <div className=\"flex-1 border-t-2 border-red-500 shadow-sm\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col bg-white\">\r\n      <div\r\n        data-day-headers=\"true\"\r\n        className=\"border-b border-neutral-300 bg-white sticky top-0 z-20\"\r\n      >\r\n        <div className=\"flex overflow-x-hidden\">\r\n          <div className=\"sticky left-0 bg-white z-10 w-14 lg:w-20\"></div>\r\n          <div className=\"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\">\r\n            {days.map((day, i) => (\r\n              <div\r\n                key={i}\r\n                className=\"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\"\r\n                onClick={() => setSelectedDate(day)}\r\n              >\r\n                <div className={cn(\r\n                  \"font-semibold text-black mb-1\",\r\n                  \"text-xs\"\r\n                )}>\r\n                  {format(day, 'EEE')}\r\n                </div>\r\n                <div className={cn(\r\n                  \"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\",\r\n                  isToday(day)\r\n                    ? \"bg-black text-white\"\r\n                    : \"text-black hover:bg-neutral-100\"\r\n                )}>\r\n                  {format(day, 'd')}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* All-Day Row */}\r\n      <AllDayRow\r\n        selectedDate={selectedDate}\r\n        segments={allDaySegments}\r\n        selectedEvent={selectedEvent}\r\n        setSelectedEvent={setSelectedEvent}\r\n        handleEventClick={handleEventClick}\r\n        canEditData={canEditData}\r\n        openAddEventForm={openAddEventForm}\r\n        view=\"week\"\r\n        activeDragData={activeDragData}\r\n      />\r\n\r\n      {/* Main Content */}\r\n      {weekSegments.length === 0\r\n        ? renderEmptyState()\r\n        : renderTimeSlots()}\r\n    </div>\r\n  );\r\n};", "import React, { useMemo, useRef } from 'react';\r\nimport { cn } from '@/lib/utils';\r\nimport { CalendarEvent } from '@/typings/page';\r\nimport { ColorInfo } from '@/utils/color';\r\nimport {  formatEventTime,  isInstantEvent,  getEventSize } from '@/utils/dateUtils';\r\nimport { useDraggable } from '@dnd-kit/core';\r\n\r\nexport const CalendarEventItem = ({\r\n  event,\r\n  style,\r\n  onClick,\r\n  onContextMenu,\r\n  view = 'month',\r\n  isDragging,\r\n  showTitle = true,\r\n  isDraggable = true\r\n}: {\r\n  event: CalendarEvent;\r\n  style?: React.CSSProperties;\r\n  onClick: (e: React.MouseEvent) => void;\r\n  onContextMenu?: (e: React.MouseEvent) => void;\r\n  view?: 'day' | 'week' | 'month';\r\n  isDragging?: boolean;\r\n  showTitle?: boolean;\r\n  isDraggable?: boolean;\r\n}) => {\r\n  const dragRef = useRef<HTMLDivElement>(null);\r\n  const { attributes, listeners, setNodeRef, isDragging: dndIsDragging } = useDraggable({\r\n    id: `event-${event.id}`,\r\n    data: {\r\n      type: 'event',\r\n      payload: event,\r\n    },\r\n    // Disable dragging for multi-day and all-day events, or if explicitly set as not draggable\r\n    disabled: !isDraggable || event.isMultiDay || event.isAllDay,\r\n  });\r\n\r\n  // Combine external isDragging with internal dndIsDragging\r\n  const combinedIsDragging = isDragging || dndIsDragging;\r\n\r\n  // Memoize event calculations\r\n  const eventDetails = useMemo(() => {\r\n    const start = new Date(event.start);\r\n    const eventHeight = style?.height ? parseInt(style.height.toString().replace('px', '')) : null;\r\n    \r\n    return { \r\n      start, \r\n      eventSize: getEventSize(eventHeight),\r\n      isInstant: isInstantEvent(event),\r\n      formattedTime: formatEventTime(start, view, { shortFormat: true })\r\n    };\r\n  }, [event, style, view]);\r\n\r\n  // Memoize styling\r\n  const eventStyles = useMemo(() => {\r\n    const denimColorInfo = ColorInfo('Denim');\r\n    \r\n    // Extract RGB values from the rgba string and make it fully opaque\r\n    const rgbMatch = denimColorInfo.bg.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\r\n    const opaqueBackground = rgbMatch \r\n      ? `rgb(${rgbMatch[1]}, ${rgbMatch[2]}, ${rgbMatch[3]})`\r\n      : denimColorInfo.bg;\r\n    \r\n    return {\r\n      ...style,\r\n      backgroundColor: opaqueBackground,\r\n      minHeight: view === 'month' ? '24px' : '30px',\r\n      marginBottom: view === 'month' ? '4px' : '0px',\r\n      // Add subtle shadow for better visual depth\r\n      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',\r\n      opacity: combinedIsDragging ? 0.5 : 1, // Only change opacity when dragging\r\n    };\r\n  }, [style, view, combinedIsDragging]);\r\n\r\n  // Memoize classes\r\n  const eventClasses = useMemo(() => {\r\n    const isEventDraggable = isDraggable && !event.isMultiDay && !event.isAllDay;\r\n    \r\n    // Month view or small events\r\n    if (view === 'month' || eventDetails.eventSize === 'small') {\r\n      return {\r\n        baseClasses: cn(\r\n          \"rounded-md select-none text-black text-xs overflow-hidden\",\r\n          !combinedIsDragging && isEventDraggable && \"cursor-pointer\",\r\n          !combinedIsDragging && !isEventDraggable && \"cursor-default\",\r\n          \"p-1\",\r\n        ),\r\n        containerClasses: cn(\r\n          \"flex items-center space-x-1\",\r\n          \"flex-nowrap\"\r\n        ),\r\n        titleClasses: cn(\r\n          \"font-medium truncate leading-tight text-xs overflow-hidden\",\r\n          \"max-w-[70%]\"\r\n        ),\r\n        timeClasses: cn(\r\n          \"opacity-75 text-xs flex-shrink-0\",\r\n          \"text-[0.65rem]\"\r\n        )\r\n      };\r\n    }\r\n\r\n    // Day and Week views for medium/large events\r\n    return {\r\n      baseClasses: cn(\r\n        \"rounded-md select-none text-black text-xs overflow-hidden\",\r\n        !combinedIsDragging && isEventDraggable && \"cursor-pointer\",\r\n        !combinedIsDragging && !isEventDraggable && \"cursor-default\",\r\n        \"p-2\",\r\n      ),\r\n      containerClasses: cn(\r\n        \"flex flex-col\",\r\n        \"space-y-0.5\"\r\n      ),\r\n      titleClasses: cn(\r\n        \"font-medium truncate leading-tight text-xs overflow-hidden\"\r\n      ),\r\n      timeClasses: cn(\r\n        \"opacity-75 text-xs\"\r\n      )\r\n    };\r\n  }, [eventDetails, view, combinedIsDragging, isDraggable, event.isMultiDay, event.isAllDay]);\r\n\r\n  // Render event content based on view and size\r\n  const renderEventContent = () => {\r\n    // Month view or small events\r\n    if (view === 'month' || eventDetails.eventSize === 'small') {\r\n      return (\r\n        <div className={eventClasses.containerClasses}>\r\n          {showTitle && (\r\n            <span className={eventClasses.titleClasses}>{event.title}</span>\r\n          )}\r\n          {showTitle && eventDetails.formattedTime && (\r\n            <span className={eventClasses.timeClasses}>\r\n              {eventDetails.formattedTime}\r\n            </span>\r\n          )}\r\n        </div>\r\n      );\r\n    }\r\n\r\n    // Day and Week views for medium/large events\r\n    return (\r\n      <div className={eventClasses.containerClasses}>\r\n        {showTitle && (\r\n          <div className={eventClasses.titleClasses}>{event.title}</div>\r\n        )}\r\n        {showTitle && eventDetails.formattedTime && (\r\n          <div className={eventClasses.timeClasses}>\r\n            {eventDetails.formattedTime}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div\r\n      id={`event-${event.id}`}\r\n      ref={(node) => {\r\n        setNodeRef(node);\r\n        (dragRef as React.MutableRefObject<HTMLDivElement | null>).current = node;\r\n      }}\r\n      style={eventStyles}\r\n      className={eventClasses.baseClasses}\r\n      onClick={onClick}\r\n      onContextMenu={onContextMenu}\r\n      {...(!event.isMultiDay && !event.isAllDay && isDraggable ? { ...listeners, ...attributes } : {})}\r\n    >\r\n      {renderEventContent()}\r\n    </div>\r\n  );\r\n};", "import React from 'react';\r\nimport { format, isSameDay } from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\nimport { Card } from '@/components/ui/card';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { CalendarEvent } from '@/typings/page';\r\nimport { useScreenSize } from '@/providers/screenSize';\r\n\r\ninterface CalendarSideCardProps {\r\n  selectedDate: Date;\r\n  events: CalendarEvent[];\r\n  selectedEvent: string | null;\r\n  setSelectedEvent: (id: string) => void;\r\n  handleEventClick: (event: CalendarEvent) => void;\r\n}\r\n\r\nexport const CalendarSideCard: React.FC<CalendarSideCardProps> = ({\r\n  selectedDate,\r\n  events,\r\n  selectedEvent,\r\n  setSelectedEvent,\r\n  handleEventClick\r\n}) => {\r\n  const { isMobile } = useScreenSize();\r\n\r\n  const dayEvents = events\r\n    .filter(event => isSameDay(new Date(event.start), selectedDate))\r\n    .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());\r\n\r\n  return (\r\n    <div \r\n      data-side-card=\"true\"\r\n      className={cn(\r\n        \"border-l border-neutral-300 flex flex-col bg-gradient-to-b from-neutral-50 to-neutral-100\",\r\n        isMobile ? \"w-full border-t\" : \"w-96\"\r\n      )}\r\n    >\r\n      <div className={cn(\r\n        \"border-b border-neutral-300 bg-gradient-to-r from-white to-neutral-50\",\r\n        \"p-4 relative overflow-hidden\"\r\n      )}>\r\n        <div className=\"absolute top-0 right-0 w-20 h-20 bg-neutral-100 rounded-full -translate-y-10 translate-x-10 opacity-30\"></div>\r\n        <div className=\"relative z-10\">\r\n          <div className=\"flex items-center space-x-2 mb-2\">\r\n            <div className=\"w-2 h-2 bg-neutral-400 rounded-full\"></div>\r\n            <h2 className=\"font-semibold text-black text-xs\">\r\n              {format(selectedDate, 'MMM d, yyyy')}\r\n            </h2>\r\n          </div>\r\n          <div className=\"flex items-center space-x-1\">\r\n            <svg className=\"w-3 h-3 text-neutral-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n            </svg>\r\n            <p className=\"text-xs text-neutral-600\">\r\n              {dayEvents.length} {dayEvents.length === 1 ? 'event' : 'events'} scheduled\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <ScrollArea className=\"flex-1 p-4\">\r\n        {dayEvents.length === 0 ? (\r\n          <div className=\"flex flex-col items-center justify-center h-40 text-center relative\">\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-neutral-100 to-transparent rounded-lg opacity-50\"></div>\r\n            <div className=\"relative z-10\">\r\n              <div className=\"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-white to-neutral-100 rounded-2xl flex items-center justify-center border border-neutral-200 shadow-sm\">\r\n                <svg className=\"w-7 h-7 text-neutral-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\" />\r\n                </svg>\r\n              </div>\r\n              <p className=\"text-xs text-neutral-500 font-medium mb-1\">\r\n                No events scheduled\r\n              </p>\r\n              <p className=\"text-xs text-neutral-400\">\r\n                Your day is completely free\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-3\">\r\n            {dayEvents.map((event) => (\r\n              <Card\r\n                key={event.id}\r\n                className={cn(\r\n                  \"cursor-pointer p-4 relative overflow-hidden\",\r\n                  selectedEvent === event.id\r\n                    ? \"bg-gradient-to-r from-neutral-200 to-neutral-300 text-black border-neutral-400 shadow-lg\"\r\n                    : \"bg-gradient-to-r from-white to-neutral-50 hover:from-neutral-50 hover:to-neutral-100 border-neutral-200\"\r\n                )}\r\n                onClick={() => {\r\n                  setSelectedEvent(event.id);\r\n                  handleEventClick(event);\r\n                }}\r\n              >\r\n                <div className=\"absolute top-0 right-0 w-8 h-8 bg-gradient-to-br from-transparent to-neutral-200 opacity-30 rounded-bl-full\"></div>\r\n                <div className=\"relative z-10\">\r\n                  <div className=\"flex items-start justify-between mb-3\">\r\n                    <div className={cn(\r\n                      \"font-semibold text-xs leading-relaxed\",\r\n                      selectedEvent === event.id ? \"text-black\" : \"text-black\"\r\n                    )}>\r\n                      {event.title}\r\n                    </div>\r\n                    <div className={cn(\r\n                      \"w-2 h-2 rounded-full flex-shrink-0 mt-1\",\r\n                      selectedEvent === event.id ? \"bg-neutral-600\" : \"bg-neutral-400\"\r\n                    )}></div>\r\n                  </div>\r\n                  <div className={cn(\r\n                    \"flex items-center space-x-2 text-xs\",\r\n                    selectedEvent === event.id ? \"text-neutral-700\" : \"text-neutral-600\"\r\n                  )}>\r\n                    <div className={cn(\r\n                      \"w-4 h-4 rounded-full flex items-center justify-center\",\r\n                      selectedEvent === event.id ? \"bg-neutral-400\" : \"bg-neutral-200\"\r\n                    )}>\r\n                      <svg className=\"w-2.5 h-2.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2.5} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                      </svg>\r\n                    </div>\r\n                    <span className=\"font-medium\">\r\n                      {format(new Date(event.start), 'h:mm a')} - {format(new Date(event.end), 'h:mm a')}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </ScrollArea>\r\n    </div>\r\n  );\r\n};\r\n", "import React, { useMemo } from 'react';\r\nimport { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { PlusIcon } from '@heroicons/react/24/outline';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { CalendarEvent } from '@/typings/page';\r\nimport { useMaybeRecord } from '@/providers/record';\r\nimport { CalendarEventItem } from '@/components/workspace/main/views/calendar/components/eventitem';\r\nimport { NoEvents } from '@/components/workspace/main/views/calendar/components/noevents';\r\nimport { CalendarSideCard } from '../components/card';\r\nimport { useDroppable } from '@dnd-kit/core';\r\nimport { useViewContext } from \"@/components/workspace/main/views/ViewsRootLayout\";\r\n\r\n\r\ninterface MonthViewProps {\r\n  selectedDate: Date;\r\n  events: CalendarEvent[];\r\n  selectedEvent: string | null;\r\n  setSelectedEvent: (id: string) => void;\r\n  setSelectedDate: (date: Date) => void;\r\n  openAddEventForm: (date: Date) => void;\r\n  canEditData: boolean;\r\n  handleEventClick: (event: CalendarEvent) => void;\r\n  activeDragData: any;\r\n}\r\n\r\nconst DayCell = ({date,children,onClick,isCurrentMonth\r\n}: {\r\n  date: Date;\r\n  children: React.ReactNode;\r\n  onClick: () => void;\r\n  isCurrentMonth: boolean;\r\n}) => {\r\n  const { setNodeRef, isOver } = useDroppable({\r\n    id: `daycell-${format(date, 'yyyy-MM-dd')}`,\r\n    data: {\r\n      date: date,\r\n      type: 'daycell'\r\n    }\r\n  });\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      onClick={onClick}\r\n      className={cn(\r\n        \"border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[170px] lg:p-3 lg:min-h-[190px]\",\r\n        isCurrentMonth\r\n          ? \"bg-white hover:bg-neutral-50\"\r\n          : \"bg-neutral-100 hover:bg-neutral-200\",\r\n        isOver && \"bg-blue-50 border-blue-200\"\r\n      )}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst eventAffectsDay = (event: CalendarEvent, day: Date): boolean => {\r\n  const eventStart = new Date(event.start);\r\n  const eventEnd = new Date(event.end);\r\n  const dayStart = new Date(day);\r\n  const dayEnd = new Date(day);\r\n  dayEnd.setHours(23, 59, 59, 999);\r\n  \r\n  return eventStart <= dayEnd && eventEnd >= dayStart;\r\n};\r\n\r\nconst isMultiDayEvent = (event: CalendarEvent): boolean => {\r\n  const eventStart = new Date(event.start);\r\n  const eventEnd = new Date(event.end);\r\n  \r\n  const startDate = new Date(eventStart.getFullYear(), eventStart.getMonth(), eventStart.getDate());\r\n  const endDate = new Date(eventEnd.getFullYear(), eventEnd.getMonth(), eventEnd.getDate());\r\n  \r\n  return startDate.getTime() !== endDate.getTime();\r\n};\r\n\r\nconst useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {\r\n  return useMemo(() => {\r\n    const positionedEventsByWeek = new Map<number, any[]>();\r\n    const slotUsageByDay = new Map<string, number>(); \r\n\r\n    weeks.forEach((week, weekIndex) => {\r\n      const weekStart = week[0];\r\n      const weekEnd = new Date(week[6]);\r\n      weekEnd.setHours(23, 59, 59, 999);\r\n\r\n      const weekEvents = events.filter(event => {\r\n        const eventStart = new Date(event.start);\r\n        const eventEnd = new Date(event.end);\r\n        return eventStart <= weekEnd && eventEnd >= weekStart;\r\n      });\r\n\r\n      const spanningEvents: any[] = [];\r\n      weekEvents.forEach(event => {\r\n        const eventStart = new Date(event.start);\r\n        const eventEnd = new Date(event.end);\r\n\r\n        const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));\r\n        const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));\r\n\r\n        const actualStart = startDayIndex !== -1 ? startDayIndex : 0;\r\n        const actualEnd = endDayIndex !== -1 ? endDayIndex : 6;\r\n\r\n        const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);\r\n\r\n        if (eventSpansWeek) {\r\n          spanningEvents.push({\r\n            event,\r\n            startDayIndex: actualStart,\r\n            endDayIndex: actualEnd,\r\n            colSpan: actualEnd - actualStart + 1,\r\n            isMultiDay: isMultiDayEvent(event), \r\n          });\r\n        }\r\n      });\r\n      \r\n      const sortedEvents = spanningEvents.sort((a, b) => {\r\n        if (a.startDayIndex !== b.startDayIndex) {\r\n          return a.startDayIndex - b.startDayIndex;\r\n        }\r\n        return b.colSpan - a.colSpan;\r\n      });\r\n\r\n      const positioned: any[] = [];\r\n      const rows: any[][] = [];\r\n\r\n      sortedEvents.forEach(eventData => {\r\n        const affectedDays = week.slice(eventData.startDayIndex, eventData.endDayIndex + 1);\r\n        const canPlace = affectedDays.every(day => {\r\n          const dayKey = format(day, 'yyyy-MM-dd');\r\n          const currentUsage = slotUsageByDay.get(dayKey) || 0;\r\n          return currentUsage < 4; \r\n        });\r\n\r\n        if (!canPlace) {\r\n          return;\r\n        }\r\n\r\n        let assigned = false;\r\n        for (let i = 0; i < rows.length; i++) {\r\n          const row = rows[i];\r\n          const hasConflict = row.some(existingEvent => \r\n            eventData.startDayIndex <= existingEvent.endDayIndex && \r\n            eventData.endDayIndex >= existingEvent.startDayIndex\r\n          );\r\n          \r\n          if (!hasConflict) {\r\n            row.push(eventData);\r\n            positioned.push({ ...eventData, row: i });\r\n            assigned = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (!assigned) {\r\n          rows.push([eventData]);\r\n          positioned.push({ ...eventData, row: rows.length - 1 });\r\n        }\r\n\r\n        affectedDays.forEach(day => {\r\n          const dayKey = format(day, 'yyyy-MM-dd');\r\n          const currentUsage = slotUsageByDay.get(dayKey) || 0;\r\n          slotUsageByDay.set(dayKey, currentUsage + 1);\r\n        });\r\n      });\r\n\r\n      positionedEventsByWeek.set(weekIndex, positioned);\r\n    });\r\n\r\n    return { positionedEventsByWeek, slotUsageByDay };\r\n  }, [weeks, events]);\r\n};\r\n\r\nexport const MonthView: React.FC<MonthViewProps> = ({\r\n  selectedDate,\r\n  events,\r\n  selectedEvent,\r\n  setSelectedEvent,\r\n  setSelectedDate,\r\n  openAddEventForm,\r\n  canEditData,\r\n  handleEventClick,\r\n  activeDragData,\r\n}) => {\r\n  const maybeRecord = useMaybeRecord();\r\n\r\n  const isInRecordTab = !!maybeRecord;\r\n\r\n  const monthCalculations = useMemo(() => {\r\n    const monthStart = startOfMonth(selectedDate);\r\n    const monthEnd = endOfMonth(selectedDate);\r\n    const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });\r\n    const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });\r\n\r\n    const days = [];\r\n    let day = startDay;\r\n    while (day <= endDay) {\r\n      days.push(day);\r\n      day = addDays(day, 1);\r\n    }\r\n\r\n    const weeks = [];\r\n    for (let i = 0; i < days.length; i += 7) {\r\n      weeks.push(days.slice(i, i + 7));\r\n    }\r\n\r\n    return { monthStart, monthEnd, startDay, endDay, days, weeks };\r\n  }, [selectedDate]);\r\n\r\n  const monthEvents = useMemo(() => \r\n    events.filter(event => {\r\n      const eventStart = new Date(event.start);\r\n      const eventEnd = new Date(event.end);\r\n      return eventStart <= monthCalculations.endDay && \r\n             eventEnd >= monthCalculations.startDay;\r\n    }), \r\n    [events, monthCalculations.startDay, monthCalculations.endDay]\r\n  );\r\n\r\n  const { positionedEventsByWeek } = useMonthEvents(monthCalculations.weeks, monthEvents);\r\n  const { context } = useViewContext();\r\n  \r\n  if (monthEvents.length === 0) {\r\n    return (\r\n      <div className=\"h-full bg-background flex flex-col lg:flex-row\">\r\n        <div className=\"flex-1 flex flex-col min-h-0\">\r\n          <div className=\"grid grid-cols-7 border-b border-neutral-300 bg-white\">\r\n            {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\r\n              <div key={dayName} className=\"px-2 py-2 text-xs font-medium text-gray-500 text-center\">\r\n                {dayName}\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"flex-1 flex items-center justify-center\">\r\n            <NoEvents\r\n              title=\"No events this month\"\r\n              message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}\r\n              showCreateButton={canEditData}\r\n              onCreate={() => openAddEventForm(selectedDate)}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {context !== 'record_tab' && (\r\n          <CalendarSideCard\r\n            selectedDate={selectedDate}\r\n            events={events}\r\n            selectedEvent={selectedEvent}\r\n            setSelectedEvent={setSelectedEvent}\r\n            handleEventClick={handleEventClick}\r\n          />\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const renderDayCellContent = (day: Date, dayEvents: any[]) => {\r\n    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\r\n    const isCurrentDay = isToday(day);\r\n    const MAX_VISIBLE_EVENTS = 4;\r\n    const ROW_HEIGHT = 28;\r\n\r\n    const allDayEvents = monthEvents.filter(event => eventAffectsDay(event, day));\r\n    const totalEventsForDay = allDayEvents.length;\r\n    \r\n    const sortedEvents = dayEvents.sort((a, b) => a.row - b.row);\r\n    const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);\r\n    const hasMore = totalEventsForDay > MAX_VISIBLE_EVENTS;\r\n    const hiddenEventsCount = Math.max(0, totalEventsForDay - MAX_VISIBLE_EVENTS);\r\n\r\n    const maxRow = visibleEvents.reduce((max, event) => Math.max(max, event.row), -1);\r\n    const containerHeight = hasMore\r\n      ? (MAX_VISIBLE_EVENTS * ROW_HEIGHT) + 20 \r\n      : (maxRow + 1) * ROW_HEIGHT;\r\n\r\n    return (\r\n      <>\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <span className={cn(\r\n            \"inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6\",\r\n            isCurrentDay ? \"bg-black text-white\" : isCurrentMonth ? \"text-black hover:bg-neutral-100\" : \"text-neutral-400\"\r\n          )}>\r\n            {format(day, 'd')}\r\n          </span>\r\n          {canEditData && isCurrentMonth && (\r\n            <Button\r\n              variant=\"ghost\"\r\n              className=\"rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                setTimeout(() => openAddEventForm(day), 150);\r\n              }}\r\n            >\r\n              <PlusIcon className=\"h-3 w-3 text-black\" />\r\n            </Button>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"relative\" style={{ height: `${containerHeight}px` }}>\r\n          {visibleEvents.map(pe => (\r\n            <div\r\n              key={pe.event.id}\r\n              className=\"absolute\"\r\n              style={{\r\n                top: `${pe.row * ROW_HEIGHT}px`,\r\n                left: '2px',\r\n                width: pe.colSpan > 1 \r\n                  ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19.5}px)`\r\n                  : 'calc(100% - 4px)',\r\n                zIndex: 10 + pe.row,\r\n              }}\r\n            >\r\n              <CalendarEventItem\r\n                event={pe.event}\r\n                view=\"month\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  setSelectedEvent(pe.event.id);\r\n                  handleEventClick(pe.event);\r\n                }}\r\n                isDragging={activeDragData?.payload?.id === pe.event.id}\r\n                isDraggable={!pe.isMultiDay} \r\n              />\r\n            </div>\r\n          ))}\r\n\r\n          {hasMore && (\r\n            <div \r\n              className=\"text-black hover:text-black font-medium text-xs cursor-pointer\"\r\n              style={{\r\n                position: 'absolute',\r\n                top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,\r\n                left: '2px',\r\n              }}\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                setSelectedDate(day);\r\n              }}\r\n            >\r\n              + {hiddenEventsCount} more\r\n            </div>\r\n          )}\r\n        </div>\r\n      </>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-full bg-background flex flex-col lg:flex-row\">\r\n      <div className=\"flex-1 flex flex-col min-h-0\">  \r\n        <div \r\n          data-day-headers=\"true\"\r\n          className=\"grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white\"\r\n        >\r\n          {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (\r\n            <div key={dayName} className={cn(\r\n              \"text-center font-semibold text-black\",\r\n              \"py-2 text-xs\"\r\n            )}>\r\n              {dayName.substring(0, 3)}\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <ScrollArea className=\"flex-1\">\r\n          <div className=\"grid grid-cols-7 border-neutral-300 border-b\">\r\n            {monthCalculations.weeks.map((week, weekIndex) =>\r\n              week.map((day, dayIndex) => {\r\n                const weekEvents = positionedEventsByWeek.get(weekIndex) || [];\r\n                const dayEvents = weekEvents.filter(pe => pe.startDayIndex === dayIndex);\r\n                const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\r\n\r\n                return (\r\n                  <DayCell\r\n                    key={`${weekIndex}-${dayIndex}`}\r\n                    date={day}\r\n                    isCurrentMonth={isCurrentMonth}\r\n                    onClick={() => setSelectedDate(day)}\r\n                  >\r\n                    {renderDayCellContent(day, dayEvents)}\r\n                  </DayCell>\r\n                );\r\n              }),\r\n            )}\r\n          </div>\r\n        </ScrollArea>\r\n      </div>\r\n\r\n      {context !== 'record_tab' && (\r\n        <CalendarSideCard\r\n          selectedDate={selectedDate}\r\n          events={events}\r\n          selectedEvent={selectedEvent}\r\n          setSelectedEvent={setSelectedEvent}\r\n          handleEventClick={handleEventClick}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n", "import { Modifier } from '@dnd-kit/core';\r\n \r\nexport const restrictToCalendarContainer: Modifier = ({ transform, draggingNodeRect, windowRect }: any) => {\r\n  if (!draggingNodeRect || !windowRect) {\r\n    return transform;\r\n  }\r\n\r\n  const calendarContainer = document.querySelector('[data-calendar-content=\"true\"]');\r\n  if (!calendarContainer) {\r\n    return transform;\r\n  }\r\n\r\n  const containerRect = calendarContainer.getBoundingClientRect();\r\n  \r\n  const sideCard = document.querySelector('[data-side-card=\"true\"]');\r\n  let maxX = containerRect.right - draggingNodeRect.width;\r\n  \r\n  if (sideCard) {\r\n    const sideCardRect = sideCard.getBoundingClientRect();\r\n    maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);\r\n  }\r\n  \r\n  const timeLabels = document.querySelector('[data-time-labels=\"true\"]');\r\n  const dayHeaders = document.querySelector('[data-day-headers=\"true\"]');\r\n  const allDayRow = document.querySelector('[data-all-day-row=\"true\"]');\r\n  \r\n  let minX = containerRect.left;\r\n  let minY = containerRect.top;\r\n  const maxY = containerRect.bottom - draggingNodeRect.height;\r\n  \r\n  if (timeLabels) {\r\n    const timeLabelsRect = timeLabels.getBoundingClientRect();\r\n    minX = Math.max(minX, timeLabelsRect.right);\r\n  }\r\n  \r\n  if (dayHeaders) {\r\n    const dayHeadersRect = dayHeaders.getBoundingClientRect();\r\n    minY = Math.max(minY, dayHeadersRect.bottom);\r\n  }\r\n  \r\n\r\n  if (allDayRow) {\r\n    const allDayRowRect = allDayRow.getBoundingClientRect();\r\n    minY = Math.max(minY, allDayRowRect.bottom);\r\n  }\r\n\r\n// Get current pointer position.\r\n  const currentX = transform.x + draggingNodeRect.left;\r\n  const currentY = transform.y + draggingNodeRect.top;\r\n\r\n\r\n  const constrainedX = Math.min(Math.max(currentX, minX), maxX);\r\n  const constrainedY = Math.min(Math.max(currentY, minY), maxY);\r\n\r\n  return {\r\n    ...transform,\r\n    x: constrainedX - draggingNodeRect.left,\r\n    y: constrainedY - draggingNodeRect.top,\r\n  };\r\n}; ", "import React, { useState, useRef, useEffect, useCallback } from \"react\";\r\nimport { useWorkspace } from \"@/providers/workspace\";\r\nimport { Match, ProcessedDbRecord } from \"opendb-app-db-utils/lib/typings/db\";\r\nimport { PageLoader } from \"@/components/custom-ui/loader\";\r\nimport { usePage } from \"@/providers/page\";\r\nimport { useMaybeShared } from \"@/providers/shared\";\r\nimport { useMaybeTemplate } from \"@/providers/template\";\r\nimport { useViews, useViewFiltering, useViewSelection } from \"@/providers/views\";\r\nimport { filterAndSortRecords, searchFilteredRecords } from \"@/components/workspace/main/views/table\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { format, addDays, addMonths, subMonths, addWeeks, subWeeks,  startOfDay, isSameDay } from \"date-fns\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { toast } from \"sonner\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useScreenSize } from \"@/providers/screenSize\";\r\nimport { useMaybeRecord } from \"@/providers/record\";\r\nimport { useStackedPeek } from \"@/providers/stackedpeek\";\r\nimport { ContentLocked } from \"@/components/workspace/main/views/common/contentLocked\";\r\n\r\nimport { DayView } from \"./views/day\";\r\nimport { WeekView } from \"./views/week\";\r\nimport { MonthView } from \"./views/month\";\r\nimport { CalendarEventItem } from \"@/components/workspace/main/views/calendar/components/eventitem\";\r\nimport { getDatabaseTitleCol, getRecordTitle } from '@/components/workspace/main/views/form/components/element/linked';\r\nimport { CalendarViewRenderProps, CalendarEvent, CalendarViewType } from '@/typings/page';\r\nimport { CustomSelect } from '@/components/custom-ui/customSelect';\r\nimport { TagItem } from '@/components/workspace/main/views/table/renderer/common/tag';\r\nimport {DndContext, DragOverlay, PointerSensor, TouchSensor, useSensor, useSensors, Active, Over} from '@dnd-kit/core';\r\nimport { restrictToCalendarContainer } from '@/utils/dragconstraints';\r\nimport { EventSegment } from '@/utils/multiDay';\r\nimport { CalendarEventSegment } from '@/components/workspace/main/views/calendar/components/eventsegment';\r\nimport { differenceInDays, differenceInMilliseconds } from 'date-fns';\r\nimport { AngleLeftIcon, AngleRightIcon, PlusIcon, MagnifyingGlassIcon, LockIcon, FilterListIcon, ArrowUpWideShortIcon } from \"@/components/icons/FontAwesomeRegular\";\r\nimport { EllipsisHorizontalIcon } from \"@heroicons/react/24/outline\";\r\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { ViewFilter } from \"@/components/workspace/main/views/common/viewFilter\";\r\nimport { ViewSort } from \"@/components/workspace/main/views/common/viewSort\";\r\n\r\n\r\n// Custom hook to track previous value\r\nconst usePrevious = <T,>(value: T) => {\r\n  const ref = useRef<T>();\r\n  useEffect(() => {\r\n    ref.current = value;\r\n  });\r\n  return ref.current;\r\n};\r\n\r\nexport const CalendarView = (props: CalendarViewRenderProps) => {\r\n  const { databaseStore, members, workspace } = useWorkspace();\r\n  const { definition } = props;\r\n  const { accessLevel } = usePage();\r\n  const { isMobile } = useScreenSize();\r\n  const maybeRecord = useMaybeRecord(); \r\n\r\n  const maybeShared = useMaybeShared();\r\n  const maybeTemplate = useMaybeTemplate();\r\n\r\n  const [selectedDate, setSelectedDate] = useState<Date>(new Date());\r\n  const [viewType, setViewType] = useState<CalendarViewType>(\"week\");\r\n  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [showSideCalendar, setShowSideCalendar] = useState(!isMobile);\r\n  const [activeDragData, setActiveDragData] = useState<any | null>(null);\r\n  const savedScrollTop = useRef(0);\r\n  const pointerCoordinates = useRef({ x: 0, y: 0 });\r\n\r\n  const isInRecordTab = !!maybeRecord;\r\n\r\n  definition.filter = definition.filter || { conditions: [], match: Match.All };\r\n  definition.sorts = definition.sorts || [];\r\n\r\n  const databaseId = definition.databaseId;\r\n  const database = databaseStore[definition.databaseId];\r\n\r\n  const isPublishedView = !!maybeShared;\r\n  const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\r\n\r\n  let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\r\n  let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\r\n\r\n  const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords, smartUpdateViewDefinition } = useViews();\r\n  const { sorts, filter, search } = useViewFiltering();\r\n  const prevPeekRecordId = usePrevious(peekRecordId);\r\n  const { openRecord } = useStackedPeek();\r\n\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor, {\r\n      activationConstraint: {\r\n        distance: 8,\r\n      },\r\n    }),\r\n    useSensor(TouchSensor, {\r\n      activationConstraint: {\r\n        delay: 150,\r\n        tolerance: 5,\r\n      },\r\n    })\r\n  );\r\n\r\n  useEffect(() => {\r\n    const containerId = viewType === 'day' ? 'day-view-container' : 'week-view-container';\r\n    const container = document.getElementById(containerId);\r\n\r\n    if (container) {\r\n      requestAnimationFrame(() => {\r\n        container.scrollTop = savedScrollTop.current;\r\n      });\r\n    }\r\n  }, [selectedEvent, viewType]);\r\n\r\n  useEffect(() => {\r\n    setShowSideCalendar(!isMobile);\r\n  }, [isMobile]);\r\n\r\n\r\n  useEffect(() => {\r\n    if (prevPeekRecordId && !peekRecordId) {\r\n      refreshDatabase(definition.databaseId);\r\n    }\r\n  }, [peekRecordId, prevPeekRecordId, definition.databaseId, refreshDatabase]);\r\n\r\n  if (!database) return <PageLoader size=\"full\" />;\r\n\r\n  const getEvents = (): CalendarEvent[] => {\r\n    if (!database) return [];\r\n\r\n    const { rows } = filterAndSortRecords(\r\n      database,\r\n      members,\r\n      databaseStore,\r\n      definition.filter || { match: Match.All, conditions: [] },\r\n      filter,\r\n      sorts.length ? sorts : (definition.sorts || []),\r\n      workspace?.workspaceMember?.userId || '',\r\n      database?.database?.id || ''\r\n    );\r\n\r\n    const filteredRows = searchFilteredRecords(search || \"\", rows);\r\n    const titleColOpts = getDatabaseTitleCol(database.database);\r\n\r\n    return filteredRows.map(row => {\r\n      const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\r\n      let startDate: Date;\r\n\r\n      if (startValue && typeof startValue === 'string') {\r\n        startDate = new Date(startValue);\r\n      } else {\r\n        startDate = new Date();\r\n      }\r\n\r\n      let endDate: Date;\r\n      if (definition.eventEndColumnId) {\r\n        const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\r\n        if (endValue && typeof endValue === 'string') {\r\n          endDate = new Date(endValue);\r\n        } else {\r\n          endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\r\n        }\r\n      } else {\r\n        endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\r\n      }\r\n\r\n      const title = getRecordTitle(\r\n        row.record,\r\n        titleColOpts.titleColId,\r\n        titleColOpts.defaultTitle,\r\n        titleColOpts.isContacts\r\n      );\r\n\r\n      // Calculate isMultiDay and isAllDay properties\r\n      const isMultiDay = !isSameDay(startDate, endDate);\r\n      const durationHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);\r\n      const isAllDay = durationHours >= 23 || (isMultiDay && durationHours >= 22);\r\n\r\n      return {\r\n        id: row.id,\r\n        title,\r\n        start: startDate,\r\n        end: endDate,\r\n        record: row.record,\r\n        processedRecord: row.processedRecord,\r\n        isMultiDay,\r\n        isAllDay\r\n      };\r\n    });\r\n  };\r\n\r\n  const getFilteredEvents = () => {\r\n    const baseEvents = getEvents();\r\n\r\n    if (!searchTerm.trim()) {\r\n      return baseEvents;\r\n    }\r\n\r\n    return baseEvents.filter(event => {\r\n      const searchLower = searchTerm.toLowerCase();\r\n      return event.title.toLowerCase().includes(searchLower);\r\n    });\r\n  };\r\n\r\n  const onDragStart = (event: { active: Active }) => {\r\n    // Don't allow drag when content is locked\r\n    if (!canEditData || definition.lockContent) {\r\n      return;\r\n    }\r\n\r\n    if (event.active.data.current) {\r\n      const initialPointerY = pointerCoordinates.current.y;\r\n      const initialEventTop = event.active.rect.current?.translated?.top ?? 0;\r\n      const grabOffsetY = initialPointerY - initialEventTop;\r\n\r\n      // Get the exact dimensions from the DOM element\r\n      // Handle both event and segment types\r\n      const { payload, type } = event.active.data.current;\r\n      const eventId = type === 'segment' ? payload.originalEvent.id : payload.id;\r\n      const draggedElement = document.getElementById(`event-${eventId}`);\r\n      const width = draggedElement ? draggedElement.offsetWidth : event.active.rect.current.translated?.width;\r\n      const height = draggedElement ? draggedElement.offsetHeight : event.active.rect.current.translated?.height;\r\n\r\n      setActiveDragData({\r\n        ...event.active.data.current,\r\n        grabOffsetY,\r\n        width,\r\n        height,\r\n      });\r\n\r\n      document.addEventListener('mousemove', handleMouseMove);\r\n      document.addEventListener('touchmove', handleTouchMove);\r\n    }\r\n  };\r\n\r\n  const onDragEnd = async ({ active, over }: { active: Active, over: Over | null }) => {\r\n    document.removeEventListener('mousemove', handleMouseMove);\r\n    document.removeEventListener('touchmove', handleTouchMove);\r\n    setActiveDragData(null);\r\n\r\n    if (!over || !active || !canEditData || definition.lockContent || active.id === over.id) {\r\n      return;\r\n    }\r\n\r\n    const activeData = active.data.current;\r\n    const overData = over.data.current;\r\n\r\n    if (!activeData || !overData) {\r\n      return;\r\n    }\r\n\r\n    const { payload, type } = activeData;\r\n    const eventToUpdate: CalendarEvent = type === 'segment' ? payload.originalEvent : payload;\r\n    const originalStart = new Date(eventToUpdate.start);\r\n    const originalEnd = new Date(eventToUpdate.end);\r\n    const duration = differenceInMilliseconds(originalEnd, originalStart);\r\n\r\n    let newStart: Date;\r\n\r\n    if (overData.type.startsWith('allday')) {\r\n      const dayDifference = differenceInDays(overData.date, startOfDay(originalStart));\r\n      newStart = addDays(originalStart, dayDifference);\r\n      newStart.setHours(0, 0, 0, 0);\r\n    } else if (overData.type === 'timeslot-minute') {\r\n      // Handle precise minute-based drops\r\n      newStart = new Date(overData.date);\r\n      newStart.setHours(overData.hour, overData.minute, 0, 0);\r\n    } else if (overData.type === 'daycell') {\r\n      const dayDifference = differenceInDays(overData.date, startOfDay(originalStart));\r\n      newStart = addDays(originalStart, dayDifference);\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    const newEnd = new Date(newStart.getTime() + duration);\r\n\r\n    const recordId = eventToUpdate.record.id;\r\n    const newValues = {\r\n      [definition.eventStartColumnId]: newStart.toISOString(),\r\n      ...(definition.eventEndColumnId && { [definition.eventEndColumnId]: newEnd.toISOString() }),\r\n    };\r\n\r\n    try {\r\n      await updateRecordValues(definition.databaseId, [recordId], newValues);\r\n      toast.success(`Event \"${eventToUpdate.title}\" updated.`);\r\n    } catch (error) {\r\n      toast.error(\"Failed to update event.\");\r\n    }\r\n  };\r\n\r\n  const handleMouseMove = (event: MouseEvent) => {\r\n    pointerCoordinates.current = { x: event.clientX, y: event.clientY };\r\n  };\r\n  const handleTouchMove = (event: TouchEvent) => {\r\n      const touch = event.touches[0];\r\n      pointerCoordinates.current = { x: touch.clientX, y: touch.clientY };\r\n  };\r\n\r\n\r\n  const events = getFilteredEvents();\r\n\r\n  const goToToday = () => setSelectedDate(new Date());\r\n\r\n  const goToPrevious = () => {\r\n    switch (viewType) {\r\n      case \"day\":\r\n        setSelectedDate(prevDate => addDays(prevDate, -1));\r\n        break;\r\n      case \"week\":\r\n        setSelectedDate(prevDate => subWeeks(prevDate, 1));\r\n        break;\r\n      case \"month\":\r\n        setSelectedDate(prevDate => subMonths(prevDate, 1));\r\n        break;\r\n    }\r\n  };\r\n\r\n  const goToNext = () => {\r\n    switch (viewType) {\r\n      case \"day\":\r\n        setSelectedDate(prevDate => addDays(prevDate, 1));\r\n        break;\r\n      case \"week\":\r\n        setSelectedDate(prevDate => addWeeks(prevDate, 1));\r\n        break;\r\n      case \"month\":\r\n        setSelectedDate(prevDate => addMonths(prevDate, 1));\r\n        break;\r\n    }\r\n  };\r\n\r\n  const handleRequestCreateEvent = (date: Date, useSystemTime: boolean = false) => {\r\n    if (useSystemTime) {\r\n      const now = new Date();\r\n      const newDate = new Date(date);\r\n      newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\r\n      handleCreateEvent(newDate);\r\n    } else {\r\n      handleCreateEvent(date);\r\n    }\r\n  };\r\n\r\n  const handleCreateEvent = async (date: Date = new Date()) => {\r\n    // Don't allow creating events when content is locked\r\n    if (!canEditData || definition.lockContent) return;\r\n\r\n    const startTime = new Date(date);\r\n    const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\r\n    const titleColOpts = getDatabaseTitleCol(database.database);\r\n\r\n    try {\r\n      const recordValues: any = {\r\n        [definition.eventStartColumnId]: startTime.toISOString(),\r\n        ...(definition.eventEndColumnId && { [definition.eventEndColumnId]: endTime.toISOString() })\r\n      };\r\n\r\n      if (titleColOpts.titleColId) {\r\n        recordValues[titleColOpts.titleColId] = \"New Event\";\r\n      }\r\n\r\n      const result = await createRecords(definition.databaseId, [recordValues]);\r\n\r\n      if (result && result.records && result.records.length > 0) {\r\n        const newRecordId = result.records[0].id;\r\n\r\n        if (newRecordId) {\r\n          await refreshDatabase(definition.databaseId);\r\n          setPeekRecordId(newRecordId);\r\n          toast.success(\"New event created\");\r\n        } else {\r\n          toast.error(\"Error accessing the new event\");\r\n        }\r\n      } else {\r\n        toast.error(\"Failed to create event properly\");\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Failed to create event\");\r\n    }\r\n  };\r\n\r\n  const handleEventClick = (event: CalendarEvent) => {\r\n    // Don't allow event clicks when content is locked\r\n    if (definition.lockContent) {\r\n      return;\r\n    }\r\n\r\n    if (event && event.id) {\r\n      const containerId = viewType === 'day' ? 'day-view-container' : 'week-view-container';\r\n      const container = document.getElementById(containerId);\r\n      if (container) {\r\n        savedScrollTop.current = container.scrollTop;\r\n      }\r\n\r\n      openRecord(event.id, event.record.databaseId);\r\n      setSelectedEvent(event.id);\r\n    }\r\n  };\r\n\r\n  const getHeaderDateDisplay = () => {\r\n    switch (viewType) {\r\n      case \"day\":\r\n        return format(selectedDate, 'MMMM d, yyyy');\r\n      case \"week\":\r\n        return `${format(addDays(selectedDate, -selectedDate.getDay()), 'MMM d')} - ${format(addDays(selectedDate, 6-selectedDate.getDay()), 'MMM d, yyyy')}`;\r\n      case \"month\":\r\n        return format(selectedDate, 'MMMM yyyy');\r\n      default:\r\n        return format(selectedDate, 'MMMM d, yyyy');\r\n    }\r\n  };\r\n\r\n  const handleEventDelete = async (event: CalendarEvent) => {\r\n    if (!canEditData) return;\r\n    \r\n    try {\r\n      await deleteRecords(database.database.id, [event.record.id]);\r\n    } catch (error) {\r\n      toast.error(\"Failed to delete event\");\r\n    }\r\n  };\r\n\r\n  const viewTypeOptions: TagItem<string>[] = [\r\n    { id: 'day', value: 'day', title: 'Day', data: 'day' },\r\n    { id: 'week', value: 'week', title: 'Week', data: 'week' },\r\n    { id: 'month', value: 'month', title: 'Month', data: 'month' }\r\n  ];\r\n\r\n  const selectedViewOption = viewType === 'day' ? ['day'] : viewType === 'week' ? ['week'] : ['month'];\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col bg-white\">\r\n     <div className={cn(\r\n       \"border-b border-neutral-300 bg-white\",\r\n       isInRecordTab && \"py-1\" \r\n     )}>\r\n       {isMobile ? (\r\n         /* Mobile Header Layout */\r\n         <div className={cn(\"p-2\", isInRecordTab && \"py-1\")}>\r\n           <div className=\"flex items-center justify-between mb-2\">\r\n             <h1 className=\"text-xs font-semibold text-black truncate flex-1 mr-2\">\r\n               {getHeaderDateDisplay()}\r\n             </h1>\r\n             <Button\r\n               variant=\"ghost\"\r\n               onClick={() => setShowSideCalendar(!showSideCalendar)}\r\n               className=\"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\"\r\n             >\r\n               Calendar\r\n             </Button>\r\n           </div>\r\n\r\n           <div className=\"flex items-center justify-between\">\r\n             <div className=\"flex items-center space-x-1\">\r\n               <Button\r\n                 variant=\"ghost\"\r\n                 onClick={goToToday}\r\n                 className={cn(\r\n                   \"rounded-full px-3 text-xs text-black hover:bg-gray-50\",\r\n                   isInRecordTab ? \"h-6\" : \"h-8\"\r\n                 )}\r\n               >\r\n                 Today\r\n               </Button>\r\n               <div className=\"flex items-center\">\r\n                 <Button\r\n                   variant=\"ghost\"\r\n                   onClick={goToPrevious}\r\n                   className={cn(\r\n                     \"rounded-full p-1 text-black hover:bg-gray-50\",\r\n                     isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"\r\n                   )}\r\n                 >\r\n                   <AngleLeftIcon className=\"h-4 w-4\" />\r\n                 </Button>\r\n                 <Button\r\n                   variant=\"ghost\"\r\n                   onClick={goToNext}\r\n                   className={cn(\r\n                     \"rounded-full p-1 text-black hover:bg-gray-50\",\r\n                     isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"\r\n                   )}\r\n                 >\r\n                   <AngleRightIcon className=\"h-4 w-4\" />\r\n                 </Button>\r\n               </div>\r\n             </div>\r\n\r\n             <div className=\"flex items-center space-x-1\">\r\n               {/* View Type Selector */}\r\n               <CustomSelect\r\n                 options={viewTypeOptions}\r\n                 selectedIds={selectedViewOption}\r\n                 onChange={(selected) => {\r\n                   if (selected.length > 0) {\r\n                     setViewType(selected[0] as CalendarViewType);\r\n                   }\r\n                 }}\r\n                 className={cn(\r\n                   \"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\",\r\n                   isInRecordTab ? \"h-6\" : \"h-8\"\r\n                 )}\r\n                 placeholder=\"View\"\r\n                 hideSearch={true}\r\n               />\r\n\r\n               {canEditData && !definition.lockContent && (\r\n                 <Button\r\n                   onClick={() => handleRequestCreateEvent(selectedDate, true)}\r\n                   className={cn(\r\n                     \"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\",\r\n                     isInRecordTab ? \"h-6\" : \"h-8\"\r\n                   )}\r\n                 >\r\n                   <PlusIcon className=\"h-3 w-3\" />\r\n                 </Button>\r\n               )}\r\n             </div>\r\n           </div>\r\n         </div>\r\n             ) : (\r\n       <div className={cn(\r\n         \"flex items-center justify-between px-4\",\r\n         isInRecordTab ? \"py-1\" : \"py-2\"\r\n       )}>\r\n         <div className=\"flex items-center space-x-2\">\r\n           <Button\r\n             variant=\"ghost\"\r\n             onClick={goToToday}\r\n             className={cn(\r\n               \"rounded-full px-3 text-xs text-black hover:bg-gray-50\",\r\n               isInRecordTab ? \"h-6\" : \"h-8\"\r\n             )}\r\n           >\r\n             Today\r\n           </Button>\r\n           <div className=\"flex items-center\">\r\n             <Button\r\n               variant=\"ghost\"\r\n               onClick={goToPrevious}\r\n               className={cn(\r\n                 \"rounded-full p-1 text-black hover:bg-gray-50\",\r\n                 isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"\r\n               )}\r\n             >\r\n               <AngleLeftIcon className=\"h-4 w-4\" />\r\n             </Button>\r\n             <Button\r\n               variant=\"ghost\"\r\n               onClick={goToNext}\r\n               className={cn(\r\n                 \"rounded-full p-1 text-black hover:bg-gray-50\",\r\n                 isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"\r\n               )}\r\n             >\r\n               <AngleRightIcon className=\"h-4 w-4\" />\r\n             </Button>\r\n           </div>\r\n\r\n           <h1 className=\"text-xs font-semibold text-black\">\r\n             {getHeaderDateDisplay()}\r\n           </h1>\r\n         </div>\r\n\r\n         <div className=\"flex items-center space-x-2\">\r\n           {!isInRecordTab && (\r\n             <div className=\"relative\">\r\n               <Input\r\n                 placeholder=\"Search events...\"\r\n                 value={searchTerm}\r\n                 onChange={(e) => setSearchTerm(e.target.value)}\r\n                 className=\"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\r\n               />\r\n               <MagnifyingGlassIcon className=\"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\" />\r\n             </div>\r\n           )}\r\n\r\n           <CustomSelect\r\n             options={viewTypeOptions}\r\n             selectedIds={selectedViewOption}\r\n             onChange={(selected) => {\r\n               if (selected.length > 0) {\r\n                 setViewType(selected[0] as CalendarViewType);\r\n               }\r\n             }}\r\n             className={cn(\r\n               \"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\",\r\n               isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"\r\n             )}\r\n             placeholder=\"View\"\r\n             hideSearch={true}\r\n           />\r\n\r\n           {canEditData && !definition.lockContent && (\r\n             <Button\r\n               onClick={() => handleRequestCreateEvent(selectedDate, true)}\r\n               className={cn(\r\n                 \"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\",\r\n                 isInRecordTab ? \"h-6\" : \"h-8\"\r\n               )}\r\n             >\r\n               <PlusIcon className=\"h-3 w-3\" />\r\n               {!isInRecordTab && <span>Add Event</span>}\r\n             </Button>\r\n           )}\r\n\r\n           {/* Calendar View More Options - 3-dot menu */}\r\n           <DropdownMenu>\r\n             <DropdownMenuTrigger asChild>\r\n               <Button\r\n                 variant=\"ghost\"\r\n                 className={cn(\r\n                   \"rounded-full p-1 hover:bg-neutral-300 text-black\",\r\n                   isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"\r\n                 )}\r\n               >\r\n                 <EllipsisHorizontalIcon className=\"h-4 w-4\" />\r\n               </Button>\r\n             </DropdownMenuTrigger>\r\n             <DropdownMenuContent className=\"w-56 rounded-none py-2 flex flex-col gap-1\" align=\"end\">\r\n               <Label className=\"text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer\">\r\n                 <LockIcon className='size-3'/>\r\n                 <span className=\"flex-1 capitalize\">Lock content</span>\r\n                 <Switch\r\n                   className=\"h-4 w-8\"\r\n                   checked={!!definition.lockContent}\r\n                   onCheckedChange={lockContent => {\r\n                     // Update the view definition with lock content using the proper method\r\n                     smartUpdateViewDefinition(props.view.id, props.view.pageId, { lockContent });\r\n                   }}\r\n                   thumbClassName=\"!size-3\"\r\n                 />\r\n               </Label>\r\n\r\n               <ViewFilter\r\n                 database={database.database}\r\n                 trigger={\r\n                   <Button variant=\"ghost\"\r\n                           className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\">\r\n                     <FilterListIcon className=\"size-3\"/>\r\n                     Default Filters\r\n                     {definition.filter.conditions.length > 0 && `(${definition.filter.conditions.length})`}\r\n                   </Button>\r\n                 }\r\n                 filter={definition.filter}\r\n                 onChange={filter => smartUpdateViewDefinition(props.view.id, props.view.pageId, { filter } as any)}\r\n                 currentRecordId={maybeRecord?.recordInfo?.record?.id}\r\n                 currentRecordDatabaseId={maybeRecord?.recordInfo?.record?.databaseId}\r\n               />\r\n\r\n               <ViewSort\r\n                 database={database.database}\r\n                 sorts={definition.sorts}\r\n                 onChange={sorts => smartUpdateViewDefinition(props.view.id, props.view.pageId, { sorts } as any)}\r\n                 trigger={\r\n                   <Button variant=\"ghost\"\r\n                           className=\"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\">\r\n                     <ArrowUpWideShortIcon className=\"size-3\"/>\r\n                     Default Sorts\r\n                     {definition.sorts.length > 0 && `(${definition.sorts.length})`}\r\n                   </Button>\r\n                 }\r\n               />\r\n             </DropdownMenuContent>\r\n           </DropdownMenu>\r\n         </div>\r\n       </div>\r\n     )}\r\n\r\n     {isMobile && !isInRecordTab && (\r\n       <div className=\"px-2 pb-2\">\r\n         <div className=\"relative\">\r\n           <Input\r\n             placeholder=\"Search events...\"\r\n             value={searchTerm}\r\n             onChange={(e) => setSearchTerm(e.target.value)}\r\n             className=\"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\r\n           />\r\n           <MagnifyingGlassIcon className=\"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\" />\r\n         </div>\r\n       </div>\r\n     )}\r\n   </div>\r\n\r\n   {/* Content Locked Warning */}\r\n   {!isPublishedView && definition.lockContent && (\r\n     <ContentLocked />\r\n   )}\r\n\r\n   {/* Main content */}\r\n   <div className=\"flex-1 flex min-h-0\">\r\n     {showSideCalendar && (\r\n     <div className={cn(\r\n       \"flex-none bg-white\",\r\n       isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"\r\n     )}>\r\n       <div className=\"flex justify-between items-center p-2 border-b border-neutral-300\">\r\n         <h3 className=\"text-xs font-semibold text-black\">Calendar</h3>\r\n         {isMobile && (\r\n           <Button\r\n             variant=\"ghost\"\r\n             onClick={() => setShowSideCalendar(false)}\r\n             className=\"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\"\r\n           >\r\n             <span className=\"sr-only\">Close</span>\r\n             ×\r\n           </Button>\r\n         )}\r\n       </div>\r\n       <Calendar\r\n         mode=\"single\"\r\n         selected={selectedDate}\r\n         onSelect={(date) => {\r\n           if (date) {\r\n             setSelectedDate(date);\r\n             if (isMobile) {\r\n               setShowSideCalendar(false);\r\n             }\r\n           }\r\n         }}\r\n         className=\"rounded-md border-0\"\r\n       />\r\n     </div>\r\n     )}\r\n     \r\n     <DndContext\r\n       sensors={sensors}\r\n       onDragStart={onDragStart}\r\n       onDragEnd={onDragEnd}\r\n       modifiers={[restrictToCalendarContainer]}\r\n     >\r\n     <div className=\"flex-1 min-w-0\" data-calendar-content=\"true\">\r\n       {viewType === 'day' && (\r\n      <DayView\r\n        selectedDate={selectedDate}\r\n        events={events}\r\n        selectedEvent={selectedEvent}\r\n        setSelectedEvent={setSelectedEvent}\r\n        openAddEventForm={handleRequestCreateEvent}\r\n        canEditData={canEditData && !definition.lockContent}\r\n        savedScrollTop={savedScrollTop}\r\n        handleEventClick={handleEventClick}\r\n        activeDragData={activeDragData}\r\n      />\r\n    )}\r\n    {viewType === 'week' && (\r\n      <WeekView\r\n        selectedDate={selectedDate}\r\n        events={events}\r\n        selectedEvent={selectedEvent}\r\n        setSelectedEvent={setSelectedEvent}\r\n        setSelectedDate={setSelectedDate}\r\n        openAddEventForm={handleRequestCreateEvent}\r\n        canEditData={canEditData && !definition.lockContent}\r\n        savedScrollTop={savedScrollTop}\r\n        handleEventClick={handleEventClick}\r\n        activeDragData={activeDragData}\r\n      />\r\n    )}\r\n    {viewType === 'month' && (\r\n      <MonthView\r\n        selectedDate={selectedDate}\r\n        events={events}\r\n        selectedEvent={selectedEvent}\r\n        setSelectedEvent={setSelectedEvent}\r\n        setSelectedDate={setSelectedDate}\r\n        openAddEventForm={(date) => handleRequestCreateEvent(date, true)}\r\n        canEditData={canEditData && !definition.lockContent}\r\n        handleEventClick={handleEventClick}\r\n        activeDragData={activeDragData}\r\n      />\r\n    )}\r\n     </div>\r\n\r\n     <DragOverlay dropAnimation={null}>\r\n      {activeDragData && activeDragData.type === 'segment' ? (\r\n          <CalendarEventSegment\r\n            segment={activeDragData.payload as EventSegment}\r\n            view={viewType === 'day' ? 'day' : 'week'}\r\n            onClick={() => {}}\r\n            style={{\r\n              width: activeDragData.width,\r\n              height: activeDragData.height,\r\n            }}\r\n          />\r\n        ) : activeDragData && activeDragData.type === 'event' ? (\r\n          <CalendarEventItem\r\n            event={activeDragData.payload as CalendarEvent}\r\n            view={viewType}\r\n            onClick={() => {}}\r\n            style={{\r\n              width: activeDragData.width,\r\n              height: activeDragData.height,\r\n            }}\r\n          />\r\n        ) : null}\r\n      </DragOverlay>\r\n   </DndContext>\r\n   </div>\r\n  </div>\r\n  );\r\n};", "import * as React from \"react\";\nfunction EllipsisVerticalIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EllipsisVerticalIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction MagnifyingGlassCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassCircleIcon);\nexport default ForwardRef;"], "names": ["tester", "exports", "G", "email", "length", "test", "parts", "split", "domainParts", "some", "part", "Card", "React", "param", "ref", "className", "props", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "div", "cn", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "h3", "CardDescription", "p", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "Calendar", "mode", "selected", "onSelect", "jsx_runtime", "DatePicker", "onChange", "date", "event", "inline", "showMonthDropdown", "showYearDropdown", "dropdownMode", "calendarClassName", "isInstantEvent", "differenceInMinutes", "dateLeft", "dateRight", "options", "requiredArgs", "arguments", "diff", "differenceInMilliseconds", "millisecondsInMinute", "getRoundingMethod", "roundingMethod", "Date", "end", "start", "formatEventTime", "view", "isMobile", "window", "innerWidth", "shortFormat", "format", "getEventSize", "height", "compareLocalAsc", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "differenceInDays", "dirtyDateLeft", "dirtyDateRight", "toDate", "sign", "difference", "Math", "abs", "differenceInCalendarDays", "setDate", "isLastDayNotFull", "Number", "result", "isMultiDayEvent", "isSameDay", "createEventSegments", "eventStart", "eventEnd", "id", "concat", "originalEventId", "startOfDay", "startTime", "endTime", "isFirstSegment", "isLastSegment", "isMiddleSegment", "segmentIndex", "totalSegments", "originalEvent", "isAllDay", "isMultiDay", "segments", "totalDays", "endOfDay", "currentDate", "finalDate", "segmentStart", "segmentEnd", "segmentDurationHours", "getTime", "push", "addDays", "getSegmentsForDay", "filter", "segment", "getSegmentsForWeek", "weekStart", "weekEnd", "isWithinInterval", "eventsToSegments", "events", "map", "normalizeEventTimes", "flatMap", "getAllDaySegments", "getTimeSlotSegments", "getSegmentHeight", "startHour", "startMinutes", "max", "cappedEndMinutes", "min", "endHour", "getSegmentTopOffset", "getSegmentStylingClasses", "baseRounded", "roundedCorners", "continuationIndicator", "opacity", "shouldShowTimeInSegment", "getSegmentContinuationText", "normalizedEnd", "MultiDayEventBadge", "size", "isEndOfEvent", "baseClasses", "dayIndicator", "getArrowIcon", "startsThisWeek", "ArrowTurnRightIcon", "ArrowTurnLeftIcon", "ArrowsLeftRightIcon", "span", "getSizeClasses", "title", "getTooltipText", "eventTitle", "totalWeeks", "ceil", "startDay", "weekNumber", "floor", "currentDay", "ContinuationArrow", "direction", "getArrow", "CalendarEventSegment", "style", "onClick", "onContextMenu", "isDragging", "dragRef", "useRef", "attributes", "listeners", "setNodeRef", "dndIsDragging", "useDraggable", "data", "type", "payload", "disabled", "combinedIsDragging", "eventDetails", "useMemo", "eventHeight", "parseInt", "toString", "replace", "showTime", "continuationText", "eventSize", "formattedTime", "eventStyles", "denimColorInfo", "ColorInfo", "stylingClasses", "rgbMatch", "bg", "match", "opaqueBackground", "backgroundColor", "minHeight", "boxShadow", "classes", "eventClasses", "containerClasses", "titleClasses", "timeClasses", "continuationClasses", "node", "current", "renderEventContent", "jsxs", "NoEvents", "message", "showCreateButton", "onCreate", "svg", "fill", "stroke", "viewBox", "path", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "<PERSON><PERSON>", "PlusIcon", "segmentsOverlap", "segment1", "segment2", "calculateLayout", "finalLayout", "segmentLayouts", "sortedSegments", "sort", "a", "b", "startDiff", "durationB", "processedSegments", "Set", "has", "overlappingGroup", "s", "columns", "for<PERSON>ach", "groupSegment", "placed", "column", "every", "maxColumns", "eventWidth", "availableWidth", "stagger", "remainingSpace", "MIN_EVENT_WIDTH_PERCENT", "colIndex", "seg", "leftPosition", "left", "width", "zIndex", "hasOverlap", "add", "calculateAllDayLayout", "maxVisible", "visibleSegments", "moreCount", "sorted", "slice", "AllDayRow", "selectedDate", "selectedEvent", "setSelectedEvent", "handleEventClick", "canEditData", "openAddEventForm", "activeDragData", "dayViewHook", "useDroppable", "weekDay1", "startOfWeek", "weekStartsOn", "weekDay2", "weekDay3", "weekDay4", "weekViewHooks", "isOver", "e", "stopPropagation", "renderWeekView", "weekDays", "Array", "from", "_", "i", "spanningEvents", "eventGroups", "Map", "eventId", "set", "get", "spanning", "eventSegments", "firstSegmentInWeek", "lastSegmentInWeek", "startDayIndex", "findIndex", "day", "endDayIndex", "colSpan", "positionedEvents", "positioned", "rows", "sortedEvents", "assigned", "row", "rowEvent", "visibleEvents", "hasMore", "firstEventDayIndex", "maxRows", "displayRows", "data-all-day-row", "dayIndex", "hook", "onDoubleClick", "newDate", "setHours", "spanningEvent", "top", "right", "console", "log", "TimeSlot", "hour", "children", "isHovering", "setIsHovering", "useState", "mousePosition", "setMousePosition", "currentMinute", "setCurrentMinute", "containerRef", "shouldShowPrecision", "calculateMinuteFromPosition", "useCallback", "rect", "getBoundingClientRect", "y", "clientY", "handleMouseEnter", "handleMouseLeave", "handleMouseMove", "minute", "x", "clientX", "handleGlobalMouseMove", "bottom", "document", "addEventListener", "removeEventListener", "handleClick", "detail", "onMouseEnter", "onMouseLeave", "onMouseMove", "HourDropZone", "isActive", "<PERSON><PERSON><PERSON><PERSON>", "savedScrollTop", "hours", "daySegments", "allDaySegments", "timeSlotSegments", "currentTimePosition", "isToday", "minutes", "data-time-labels", "segmentHeight", "layout", "topOffset", "position", "paddingRight", "border", "container", "getElementById", "scrollTop", "WeekView", "setSelectedDate", "weekCalculations", "endOfWeek", "days", "todayIndex", "weekSegments", "data-day-headers", "toISOString", "CalendarEventItem", "showTitle", "isDraggable", "isInstant", "marginBottom", "isEventDraggable", "CalendarSideCard", "useScreenSize", "dayEvents", "data-side-card", "h2", "ScrollArea", "<PERSON><PERSON><PERSON>", "isCurrentMonth", "eventAffectsDay", "dayStart", "dayEnd", "startDate", "endDate", "useMonthEvents", "weeks", "positionedEventsByWeek", "slotUsageByDay", "week", "weekIndex", "weekEvents", "actualStart", "actualEnd", "eventData", "affectedDays", "<PERSON><PERSON><PERSON>", "currentUsage", "existingEvent", "<PERSON><PERSON>ie<PERSON>", "useMaybeRecord", "monthCalculations", "monthStart", "startOfMonth", "monthEnd", "endOfMonth", "endDay", "monthEvents", "context", "useViewContext", "day<PERSON><PERSON>", "renderDayCellContent", "isCurrentDay", "totalEventsForDay", "allDayEvents", "maxRow", "reduce", "containerHeight", "MAX_VISIBLE_EVENTS", "Fragment", "variant", "setTimeout", "pe", "substring", "restrictToCalendarContainer", "transform", "draggingNodeRect", "windowRect", "calendarContainer", "querySelector", "containerRect", "sideCard", "maxX", "sideCardRect", "time<PERSON><PERSON><PERSON>", "dayHeaders", "allDayRow", "minX", "minY", "maxY", "timeLabelsRect", "dayHeadersRect", "allDayRowRect", "currentX", "currentY", "constrainedX", "constrainedY", "usePrevious", "useEffect", "value", "CalendarView", "<PERSON><PERSON><PERSON><PERSON>", "databaseStore", "members", "workspace", "useWorkspace", "definition", "accessLevel", "usePage", "maybeShared", "useMaybeShared", "maybeTemplate", "useMaybeTemplate", "viewType", "setViewType", "searchTerm", "setSearchTerm", "showSideCalendar", "setShowSideCalendar", "setActiveDragData", "pointerCoordinates", "isInRecordTab", "conditions", "Match", "All", "sorts", "databaseId", "database", "isPublishedView", "editable", "lock<PERSON><PERSON><PERSON>", "createRecords", "updateRecordValues", "setPeekRecordId", "peekRecordId", "refreshDatabase", "deleteRecords", "smartUpdateViewDefinition", "useViews", "search", "useViewFiltering", "prevPeekRecordId", "openRecord", "useStackedPeek", "sensors", "useSensors", "useSensor", "PointerSensor", "activationConstraint", "distance", "TouchSensor", "delay", "tolerance", "containerId", "requestAnimationFrame", "<PERSON><PERSON><PERSON><PERSON>", "getEvents", "filterAndSortRecords", "workspaceMember", "userId", "filteredRows", "searchFilteredRecords", "titleColOpts", "getDatabaseTitleCol", "startValue", "processedRecord", "processedRecordValues", "eventStartColumnId", "eventEndColumnId", "endValue", "defaultDuration", "getRecordTitle", "record", "titleColId", "defaultTitle", "isContacts", "durationHours", "onDragEnd", "newStart", "active", "over", "handleTouchMove", "activeData", "overData", "eventToUpdate", "originalStart", "originalEnd", "duration", "startsWith", "dayDifference", "newEnd", "recordId", "newValues", "toast", "success", "error", "touch", "touches", "getFilteredEvents", "baseEvents", "trim", "searchLower", "toLowerCase", "includes", "goToToday", "goToPrevious", "prevDate", "subWeeks", "subMonths", "goToNext", "addWeeks", "addMonths", "handleRequestCreateEvent", "useSystemTime", "now", "handleCreateEvent", "recordValues", "records", "newRecordId", "getHeaderDateDisplay", "getDay", "viewTypeOptions", "selectedViewOption", "h1", "AngleLeftIcon", "AngleRightIcon", "CustomSelect", "selectedIds", "placeholder", "hideSearch", "Input", "target", "MagnifyingGlassIcon", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "EllipsisHorizontalIcon", "DropdownMenuContent", "align", "Label", "LockIcon", "Switch", "checked", "onCheckedChange", "pageId", "thumbClassName", "ViewFilter", "trigger", "FilterListIcon", "currentRecordId", "recordInfo", "currentRecordDatabaseId", "ViewSort", "ArrowUpWideShortIcon", "ContentLocked", "DndContext", "onDragStart", "initialPointerY", "initialEventTop", "translated", "draggedElement", "offsetWidth", "offsetHeight", "grabOffsetY", "modifiers", "data-calendar-content", "DragOverlay", "dropAnimation", "ForwardRef", "svgRef", "titleId", "Object", "assign", "xmlns", "__webpack_exports__", "Z"], "sourceRoot": ""}