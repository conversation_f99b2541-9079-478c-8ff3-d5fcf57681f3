!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="1d5bca6c-95c1-48cc-91e6-144609f39c3d",e._sentryDebugIdIdentifier="sentry-dbid-1d5bca6c-95c1-48cc-91e6-144609f39c3d")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5737],{85448:function(e,t,r){"use strict";r.d(t,{Ce:function(){return F}});var n=r(2265),i=r(54887);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}function a(e){var t=(0,n.useRef)({fn:e,curr:void 0}).current;if(t.fn=e,!t.curr){var r=Object.create(null);Object.keys(e).forEach(function(e){r[e]=function(){var r;return(r=t.fn[e]).call.apply(r,[t.fn].concat([].slice.call(arguments)))}}),t.curr=r}return t.curr}function l(e){return(0,n.useReducer)(function(e,t){return o({},e,"function"==typeof t?t(e):t)},e)}(0,n.createContext)(void 0);var c="cubic-bezier(0.25, 0.8, 0.25, 1)",u="undefined"!=typeof window&&"ontouchstart"in window,d=function(e,t,r){return Math.max(Math.min(e,r),t)},h=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=0),d(e,1*(1-r),Math.max(6,t)*(1+r))},f="undefined"==typeof window||/ServerSideRendering/.test(navigator&&navigator.userAgent)?n.useEffect:n.useLayoutEffect;function p(e,t,r){var i=(0,n.useRef)(t);i.current=t,(0,n.useEffect)(function(){function t(e){i.current(e)}return e&&window.addEventListener(e,t,r),function(){e&&window.removeEventListener(e,t)}},[e])}var g=["container"];function m(e){var t=e.container,r=void 0===t?document.body:t,a=s(e,g);return(0,i.createPortal)(n.createElement("div",o({},a)),r)}function v(e){return n.createElement("svg",o({width:"44",height:"44",viewBox:"0 0 768 768"},e),n.createElement("path",{d:"M607.5 205.5l-178.5 178.5 178.5 178.5-45 45-178.5-178.5-178.5 178.5-45-45 178.5-178.5-178.5-178.5 45-45 178.5 178.5 178.5-178.5z"}))}function y(e){return n.createElement("svg",o({width:"44",height:"44",viewBox:"0 0 768 768"},e),n.createElement("path",{d:"M640.5 352.5v63h-390l178.5 180-45 45-256.5-256.5 256.5-256.5 45 45-178.5 180h390z"}))}function w(e){return n.createElement("svg",o({width:"44",height:"44",viewBox:"0 0 768 768"},e),n.createElement("path",{d:"M384 127.5l256.5 256.5-256.5 256.5-45-45 178.5-180h-390v-63h390l-178.5-180z"}))}function b(){return(0,n.useEffect)(function(){var e=document.body.style,t=e.overflow;return e.overflow="hidden",function(){e.overflow=t}},[]),null}function x(e){var t=e.touches[0],r=t.clientX,n=t.clientY;if(e.touches.length>=2){var i=e.touches[1],o=i.clientX,s=i.clientY;return[(r+o)/2,(n+s)/2,Math.sqrt(Math.pow(o-r,2)+Math.pow(s-n,2))]}return[r,n,0]}var C=function(e,t,r,n){var i,o=r*t,s=(o-n)/2,a=e;return o<=n?(i=1,a=0):e>0&&s-e<=0?(i=2,a=s):e<0&&s+e<=0&&(i=3,a=-s),[i,a]};function _(e,t,r,n,i,o,s,a,l,c){void 0===s&&(s=innerWidth/2),void 0===a&&(a=innerHeight/2),void 0===l&&(l=0),void 0===c&&(c=0);var u=C(e,o,r,innerWidth)[0],d=C(t,o,n,innerHeight),h=innerWidth/2,f=innerHeight/2;return{x:s-o/i*(s-(h+e))-h+(n/r>=3&&r*o===innerWidth?0:u?l/2:l),y:a-o/i*(a-(f+t))-f+(d[0]?c/2:c),lastCX:s,lastCY:a}}function k(e,t,r){var n=e%180!=0;return n?[r,t,n]:[t,r,n]}function E(e,t,r){var n=k(r,innerWidth,innerHeight),i=n[0],o=n[1],s=0,a=i,l=o,c=e/t*o,u=t/e*i;return e<i&&t<o?(a=e,l=t):e<i&&t>=o?a=c:e>=i&&t<o||e/t>i/o?l=u:t/e>=3&&!n[2]?s=((l=u)-o)/2:a=c,{width:a,height:l,x:0,y:s,pause:!0}}function R(e,t){var r=t.leading,i=void 0!==r&&r,o=t.maxWait,s=t.wait,a=void 0===s?o||0:s,l=(0,n.useRef)(e);l.current=e;var c=(0,n.useRef)(0),u=(0,n.useRef)(),d=function(){return u.current&&clearTimeout(u.current)},h=(0,n.useCallback)(function(){var e=[].slice.call(arguments),t=Date.now();function r(){c.current=t,d(),l.current.apply(null,e)}var n=c.current,s=t-n;if(0===n&&(i&&r(),c.current=t),void 0!==o){if(s>o)return void r()}else s<a&&(c.current=t);d(),u.current=setTimeout(function(){r(),c.current=0},a)},[a,o,i]);return h.cancel=d,h}var S=function(e,t,r){return T(e,t,r,100,function(e){return e},function(){return T(t,e,r)})},A=function(e){return 1-Math.pow(1-e,4)};function T(e,t,r,n,i,o){void 0===n&&(n=400),void 0===i&&(i=A);var s=t-e;if(0!==s){var a=Date.now(),l=0,c=function(){var t=Math.min(1,(Date.now()-a)/n);r(e+i(t)*s)&&t<1?u():(cancelAnimationFrame(l),t>=1&&o&&o())};u()}function u(){l=requestAnimationFrame(c)}}var O={T:0,L:0,W:0,H:0,FIT:void 0},I=function(){var e=(0,n.useRef)(!1);return(0,n.useEffect)(function(){return e.current=!0,function(){e.current=!1}},[]),e},P=["className"];function N(e){var t=e.className,r=s(e,P);return n.createElement("div",o({className:"PhotoView__Spinner "+(void 0===t?"":t)},r),n.createElement("svg",{viewBox:"0 0 32 32",width:"36",height:"36",fill:"white"},n.createElement("path",{opacity:".25",d:"M16 0 A16 16 0 0 0 16 32 A16 16 0 0 0 16 0 M16 4 A12 12 0 0 1 16 28 A12 12 0 0 1 16 4"}),n.createElement("path",{d:"M16 0 A16 16 0 0 1 32 16 L28 16 A12 12 0 0 0 16 4z"})))}var L=["src","loaded","broken","className","onPhotoLoad","loadingElement","brokenElement"];function D(e){var t=e.src,r=e.loaded,i=e.broken,a=e.className,l=e.onPhotoLoad,c=e.loadingElement,u=e.brokenElement,d=s(e,L),h=I();return t&&!i?n.createElement(n.Fragment,null,n.createElement("img",o({className:"PhotoView__Photo"+(a?" "+a:""),src:t,onLoad:function(e){var t=e.target;h.current&&l({loaded:!0,naturalWidth:t.naturalWidth,naturalHeight:t.naturalHeight})},onError:function(){h.current&&l({broken:!0})},draggable:!1,alt:""},d)),!r&&(c?n.createElement("span",{className:"PhotoView__icon"},c):n.createElement(N,{className:"PhotoView__icon"}))):u?n.createElement("span",{className:"PhotoView__icon"},"function"==typeof u?u({src:t}):u):null}var j={naturalWidth:void 0,naturalHeight:void 0,width:void 0,height:void 0,loaded:void 0,broken:!1,x:0,y:0,touched:!1,maskTouched:!1,rotate:0,scale:1,CX:0,CY:0,lastX:0,lastY:0,lastCX:0,lastCY:0,lastScale:1,touchTime:0,touchLength:0,pause:!0,stopRaf:!0,reach:void 0};function B(e){var t,r,i,s,c,d,g,m,v,y,w,b,A,P,N,L,B,M,F,q,W,z,H,U,V,Y,X,K,$=e.item,G=$.src,Z=$.render,J=$.width,Q=void 0===J?0:J,ee=$.height,et=void 0===ee?0:ee,er=$.originRef,en=e.visible,ei=e.speed,eo=e.easing,es=e.wrapClassName,ea=e.className,el=e.style,ec=e.loadingElement,eu=e.brokenElement,ed=e.onPhotoTap,eh=e.onMaskTap,ef=e.onReachMove,ep=e.onReachUp,eg=e.onPhotoResize,em=e.isActive,ev=e.expose,ey=l(j),ew=ey[0],eb=ey[1],ex=(0,n.useRef)(0),eC=I(),e_=ew.naturalWidth,ek=void 0===e_?Q:e_,eE=ew.naturalHeight,eR=void 0===eE?et:eE,eS=ew.width,eA=void 0===eS?Q:eS,eT=ew.height,eO=void 0===eT?et:eT,eI=ew.loaded,eP=void 0===eI?!G:eI,eN=ew.broken,eL=ew.x,eD=ew.y,ej=ew.touched,eB=ew.stopRaf,eM=ew.maskTouched,eF=ew.rotate,eq=ew.scale,eW=ew.CX,ez=ew.CY,eH=ew.lastX,eU=ew.lastY,eV=ew.lastCX,eY=ew.lastCY,eX=ew.lastScale,eK=ew.touchTime,e$=ew.touchLength,eG=ew.pause,eZ=ew.reach,eJ=a({onScale:function(e){return eQ(h(e))},onRotate:function(e){eF!==e&&(ev({rotate:e}),eb(o({rotate:e},E(ek,eR,e))))}});function eQ(e,t,r){eq!==e&&(ev({scale:e}),eb(o({scale:e},_(eL,eD,eA,eO,eq,e,t,r),e<=1&&{x:0,y:0})))}var e0=R(function(e,t,r){if(void 0===r&&(r=0),(ej||eM)&&em){var n=k(eF,eA,eO),i=n[0],s=n[1];if(0===r&&0===ex.current){var a=20>=Math.abs(e-eW),l=20>=Math.abs(t-ez);if(a&&l)return void eb({lastCX:e,lastCY:t});ex.current=a?t>ez?3:2:1}var c,u=e-eV,d=t-eY;if(0===r){var f,p,g=C(u+eH,eq,i,innerWidth)[0],m=C(d+eU,eq,s,innerHeight);f=ex.current,p=m[0],void 0!==(c=g&&1===f||"x"===eZ?"x":p&&f>1||"y"===eZ?"y":void 0)&&ef(c,e,t,eq)}if("x"===c||eM)return void eb({reach:"x"});var v=h(eq+(r-e$)/100/2*eq,ek/eA,.2);ev({scale:v}),eb(o({touchLength:r,reach:c,scale:v},_(eL,eD,eA,eO,eq,v,e,t,u,d)))}},{maxWait:8});function e1(e){return!eB&&!ej&&(eC.current&&eb(o({},e,{pause:en})),eC.current)}var e2,e4,e5,e6,e7,e8,e3,e9=(e6=function(e){return e1({x:e})},e7=function(e){return e1({y:e})},e8=function(e){return eC.current&&(ev({scale:e}),eb({scale:e})),!ej&&eC.current},e3=a({X:function(e){return e6(e)},Y:function(e){return e7(e)},S:function(e){return e8(e)}}),function(e,t,r,n,i,o,s,a,l,c,u){var d=k(c,i,o),h=d[0],f=d[1],p=C(e,a,h,innerWidth),g=p[0],m=p[1],v=C(t,a,f,innerHeight),y=v[0],w=v[1],b=Date.now()-u;if(b>=200||a!==s||Math.abs(l-s)>1){var x=_(e,t,i,o,s,a),E=x.x,R=x.y,A=g?m:E!==e?E:null,O=y?w:R!==t?R:null;return null!==A&&T(e,A,e3.X),null!==O&&T(t,O,e3.Y),void(a!==s&&T(s,a,e3.S))}var I=(e-r)/b,P=(t-n)/b,N=Math.sqrt(Math.pow(I,2)+Math.pow(P,2)),L=!1,D=!1;!function(e,t){var r,n=e,i=0,o=0,s=function(o){r||(r=o);var s=o-r,c=Math.sign(e),u=-.001*c,d=Math.sign(-n)*Math.pow(n,2)*2e-4;i+=n*s+(u+d)*Math.pow(s,2)/2,r=o,c*(n+=(u+d)*s)<=0?l():t(i)?a():l()};function a(){o=requestAnimationFrame(s)}function l(){cancelAnimationFrame(o)}a()}(N,function(r){var n=e+I/N*r,i=t+P/N*r,o=C(n,s,h,innerWidth),a=o[0],l=o[1],c=C(i,s,f,innerHeight),u=c[0],d=c[1];if(a&&!L&&(L=!0,g?T(n,l,e3.X):S(l,n+(n-l),e3.X)),u&&!D&&(D=!0,y?T(i,d,e3.Y):S(d,i+(i-d),e3.Y)),L&&D)return!1;var p=L||e3.X(l),m=D||e3.Y(d);return p&&m})}),te=(e2=function(e,t){eZ||eQ(1!==eq?1:Math.max(2,ek/eA),e,t)},e4=(0,n.useRef)(0),e5=R(function(){e4.current=0,ed.apply(void 0,[].slice.call(arguments))},{wait:300}),function(){var e=[].slice.call(arguments);e4.current+=1,e5.apply(void 0,e),e4.current>=2&&(e5.cancel(),e4.current=0,e2.apply(void 0,e))});function tt(e,t){if(ex.current=0,(ej||eM)&&em){eb({touched:!1,maskTouched:!1,pause:!1,stopRaf:!1,reach:void 0});var r=h(eq,ek/eA);if(e9(eL,eD,eH,eU,eA,eO,eq,r,eX,eF,eK),ep(e,t),eW===e&&ez===t){if(ej)return void te(e,t);eM&&eh(e,t)}}}function tr(e,t,r){void 0===r&&(r=0),eb({touched:!0,CX:e,CY:t,lastCX:e,lastCY:t,lastX:eL,lastY:eD,lastScale:eq,touchLength:r,touchTime:Date.now()})}function tn(e){eb({maskTouched:!0,CX:e.clientX,CY:e.clientY,lastX:eL,lastY:eD})}p(u?void 0:"mousemove",function(e){e.preventDefault(),e0(e.clientX,e.clientY)}),p(u?void 0:"mouseup",function(e){tt(e.clientX,e.clientY)}),p(u?"touchmove":void 0,function(e){e.preventDefault();var t=x(e);e0.apply(void 0,t)},{passive:!1}),p(u?"touchend":void 0,function(e){var t=e.changedTouches[0];tt(t.clientX,t.clientY)},{passive:!1}),p("resize",R(function(){eP&&!ej&&(eb(E(ek,eR,eF)),eg())},{maxWait:8})),f(function(){em&&ev(o({scale:eq,rotate:eF},eJ))},[em]);var ti=(v=function(e){return eb({pause:e})},M=(y=(0,n.useRef)(!1),A=(b=(w=l({lead:!0,scale:eq}))[0]).lead,P=b.scale,N=w[1],L=R(function(e){try{return v(!0),N({lead:!1,scale:e}),Promise.resolve()}catch(e){return Promise.reject(e)}},{wait:ei}),f(function(){y.current?(v(!1),N({lead:!0}),L(eq)):y.current=!0},[eq]),B=A?[eA*P,eO*P,eq/P]:[eA*eq,eO*eq,1])[0],F=B[1],q=B[2],z=(r=(t=(0,n.useState)(O))[0],i=t[1],c=(s=(0,n.useState)(0))[0],d=s[1],g=(0,n.useRef)(),m=a({OK:function(){return en&&d(4)}}),(0,n.useEffect)(function(){if(g.current||(g.current=Date.now()),eP){if(function(e,t){var r=e&&e.current;if(r&&1===r.nodeType){var n=r.getBoundingClientRect();t({T:n.top,L:n.left,W:n.width,H:n.height,FIT:"IMG"===r.tagName?getComputedStyle(r).objectFit:void 0})}}(er,i),en)return Date.now()-g.current<250?(d(1),requestAnimationFrame(function(){d(2),requestAnimationFrame(function(){v(!1),d(3)})}),void setTimeout(m.OK,ei)):void d(4);v(!1),d(5)}},[en,eP]),W=[c,r])[0],U=(H=W[1]).W,V=H.FIT,Y=innerWidth/2,X=innerHeight/2,[(K=z<3||z>4)?U?H.L:Y:eL+(Y-eA*eq/2),K?U?H.T:X:eD+(X-eO*eq/2),M,K&&V?H.H/U*M:F,0===z?q:K?U/(eA*eq)||.01:q,K?V?1:0:1,z,V]),to=ti[4],ts=ti[6],ta="transform "+ei+"ms "+eo,tl={className:ea,onMouseDown:u?void 0:function(e){e.stopPropagation(),0===e.button&&tr(e.clientX,e.clientY,0)},onTouchStart:u?function(e){e.stopPropagation(),tr.apply(void 0,x(e))}:void 0,onWheel:function(e){if(!eZ){var t=h(eq-e.deltaY/100/2,ek/eA);eb({stopRaf:!0}),eQ(t,e.clientX,e.clientY)}},style:{width:ti[2]+"px",height:ti[3]+"px",opacity:ti[5],objectFit:4===ts?void 0:ti[7],transform:eF?"rotate("+eF+"deg)":void 0,transition:ts>2?ta+", opacity "+ei+"ms ease, height "+(ts<4?ei/2:ts>4?ei:0)+"ms "+eo:void 0}};return n.createElement("div",{className:"PhotoView__PhotoWrap"+(es?" "+es:""),style:el,onMouseDown:!u&&em?tn:void 0,onTouchStart:u&&em?function(e){return tn(e.touches[0])}:void 0},n.createElement("div",{className:"PhotoView__PhotoBox",style:{transform:"matrix("+to+", 0, 0, "+to+", "+ti[0]+", "+ti[1]+")",transition:ej||eG?void 0:ta,willChange:em?"transform":void 0}},G?n.createElement(D,o({src:G,loaded:eP,broken:eN},tl,{onPhotoLoad:function(e){eb(o({},e,e.loaded&&E(e.naturalWidth||0,e.naturalHeight||0,eF)))},loadingElement:ec,brokenElement:eu})):Z&&Z({attrs:tl,scale:to,rotate:eF})))}var M={x:0,touched:!1,pause:!1,lastCX:void 0,lastCY:void 0,bg:void 0,lastBg:void 0,overlay:!0,minimal:!0,scale:1,rotate:0};function F(e){var t,r,i,o,s=e.loop,h=void 0===s?3:s,g=e.speed,x=e.easing,C=e.photoClosable,_=e.maskClosable,k=void 0===_||_,E=e.maskOpacity,R=void 0===E?1:E,S=e.pullClosable,A=void 0===S||S,T=e.bannerVisible,O=void 0===T||T,I=e.overlayRender,P=e.toolbarRender,N=e.className,L=e.maskClassName,D=e.photoClassName,j=e.photoWrapClassName,F=e.loadingElement,q=e.brokenElement,W=e.images,z=e.index,H=e.onIndexChange,U=e.visible,V=e.onClose,Y=e.afterClose,X=e.portalContainer,K=l(M),$=K[0],G=K[1],Z=(0,n.useState)(0),J=Z[0],Q=Z[1],ee=$.x,et=$.touched,er=$.pause,en=$.lastCX,ei=$.lastCY,eo=$.bg,es=void 0===eo?R:eo,ea=$.lastBg,el=$.overlay,ec=$.minimal,eu=$.scale,ed=$.rotate,eh=$.onScale,ef=$.onRotate,ep=e.hasOwnProperty("index"),eg=ep?void 0===z?0:z:J,em=ep?H:Q,ev=(0,n.useRef)(eg),ey=W.length,ew=W[eg],eb="boolean"==typeof h?h:ey>h,ex=(t=(0,n.useReducer)(function(e){return!e},!1)[1],r=(0,n.useRef)(0),o=(i=function(e){var t=(0,n.useRef)(e);function i(e){t.current=e}return(0,n.useMemo)(function(){U?(i(U),r.current=1):r.current=2},[e]),[t.current,i]}(U))[1],[i[0],r.current,function(){t(),2===r.current&&(o(!1),Y&&Y()),r.current=0}]),eC=ex[0],e_=ex[1],ek=ex[2];f(function(){if(eC)return G({pause:!0,x:-(eg*(innerWidth+20))}),void(ev.current=eg);G(M)},[eC]);var eE=a({close:function(e){ef&&ef(0),G({overlay:!0,lastBg:es}),V(e)},changeIndex:function(e,t){void 0===t&&(t=!1);var r=eb?ev.current+(e-eg):e,n=ey-1,i=d(r,0,n),o=eb?r:i;G({touched:!1,lastCX:void 0,lastCY:void 0,x:-(innerWidth+20)*o,pause:t}),ev.current=o,em&&em(eb?e<0?n:e>n?0:e:i)}}),eR=eE.close,eS=eE.changeIndex;function eA(e){return e?eR():G({overlay:!el})}function eT(){G({x:-(innerWidth+20)*eg,lastCX:void 0,lastCY:void 0,pause:!0}),ev.current=eg}function eO(e,t,r,n){"x"===e?function(e){if(void 0!==en){var t=e-en,r=t;!eb&&(0===eg&&t>0||eg===ey-1&&t<0)&&(r=t/2),G({touched:!0,lastCX:en,x:-(innerWidth+20)*ev.current+r,pause:!1})}else G({touched:!0,lastCX:e,x:ee,pause:!1})}(t):"y"===e&&function(e,t){if(void 0!==ei){var r=null===R?null:d(R,.01,R-Math.abs(e-ei)/100/4);G({touched:!0,lastCY:ei,bg:1===t?r:R,minimal:1===t})}else G({touched:!0,lastCY:e,bg:es,minimal:!0})}(r,n)}function eI(e,t){var r=e-(null!=en?en:e),n=t-(null!=ei?ei:t),i=!1;if(r<-40)eS(eg+1);else if(r>40)eS(eg-1);else{var o=-(innerWidth+20)*ev.current;Math.abs(n)>100&&ec&&A&&(i=!0,eR()),G({touched:!1,x:o,lastCX:void 0,lastCY:void 0,bg:R,overlay:!!i||el})}}p("keydown",function(e){if(U)switch(e.key){case"ArrowLeft":eS(eg-1,!0);break;case"ArrowRight":eS(eg+1,!0);break;case"Escape":eR()}});var eP=(0,n.useMemo)(function(){var e=W.length;return eb?W.concat(W).concat(W).slice(e+eg-1,e+eg+2):W.slice(Math.max(eg-1,0),Math.min(eg+2,e+1))},[W,eg,eb]);if(!eC)return null;var eN=el&&!e_,eL=U?es:ea,eD=eh&&ef&&{images:W,index:eg,visible:U,onClose:eR,onIndexChange:eS,overlayVisible:eN,overlay:ew&&ew.overlay,scale:eu,rotate:ed,onScale:eh,onRotate:ef},ej=g?g(e_):400,eB=x?x(e_):c,eM=g?g(3):600,eF=x?x(3):c;return n.createElement(m,{className:"PhotoView-Portal"+(eN?"":" PhotoView-Slider__clean")+(U?"":" PhotoView-Slider__willClose")+(N?" "+N:""),role:"dialog",onClick:function(e){return e.stopPropagation()},container:X},U&&n.createElement(b,null),n.createElement("div",{className:"PhotoView-Slider__Backdrop"+(L?" "+L:"")+(1===e_?" PhotoView-Slider__fadeIn":2===e_?" PhotoView-Slider__fadeOut":""),style:{background:eL?"rgba(0, 0, 0, "+eL+")":void 0,transitionTimingFunction:eB,transitionDuration:(et?0:ej)+"ms",animationDuration:ej+"ms"},onAnimationEnd:ek}),O&&n.createElement("div",{className:"PhotoView-Slider__BannerWrap"},n.createElement("div",{className:"PhotoView-Slider__Counter"},eg+1," / ",ey),n.createElement("div",{className:"PhotoView-Slider__BannerRight"},P&&eD&&P(eD),n.createElement(v,{className:"PhotoView-Slider__toolbarIcon",onClick:eR}))),eP.map(function(e,t){var r=eb||0!==eg?ev.current-1+t:eg+t;return n.createElement(B,{key:eb?e.key+"/"+e.src+"/"+r:e.key,item:e,speed:ej,easing:eB,visible:U,onReachMove:eO,onReachUp:eI,onPhotoTap:function(){return eA(C)},onMaskTap:function(){return eA(k)},wrapClassName:j,className:D,style:{left:(innerWidth+20)*r+"px",transform:"translate3d("+ee+"px, 0px, 0)",transition:et||er?void 0:"transform "+eM+"ms "+eF},loadingElement:F,brokenElement:q,onPhotoResize:eT,isActive:ev.current===r,expose:G})}),!u&&O&&n.createElement(n.Fragment,null,(eb||0!==eg)&&n.createElement("div",{className:"PhotoView-Slider__ArrowLeft",onClick:function(){return eS(eg-1,!0)}},n.createElement(y,null)),(eb||eg+1<ey)&&n.createElement("div",{className:"PhotoView-Slider__ArrowRight",onClick:function(){return eS(eg+1,!0)}},n.createElement(w,null))),I&&eD&&n.createElement("div",{className:"PhotoView-Slider__Overlay"},I(eD)))}},72299:function(){},15621:function(e,t,r){"use strict";var n=r(2265);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m15 15 6-6m0 0-6-6m6 6H9a6 6 0 0 0 0 12h3"}))});t.Z=i},81801:function(e,t,r){"use strict";var n=r(2265);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))});t.Z=i},26652:function(e,t,r){"use strict";var n=r(2265);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=i},61601:function(e,t,r){"use strict";var n=r(2265);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))});t.Z=i},6394:function(e,t,r){"use strict";r.d(t,{f:function(){return a}});var n=r(2265),i=r(66840),o=r(57437),s=n.forwardRef((e,t)=>(0,o.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var a=s},27312:function(e,t,r){"use strict";r.d(t,{VY:function(){return X},ee:function(){return U},fC:function(){return H},h_:function(){return Y},xz:function(){return V}});var n=r(2265),i=r(6741),o=r(98575),s=r(73966),a=r(15278),l=r(86097),c=r(99103),u=r(99255),d=r(21107),h=r(83832),f=r(71599),p=r(66840),g=r(37053),m=r(80886),v=r(5478),y=r(60703),w=r(57437),b="Popover",[x,C]=(0,s.b)(b,[d.D7]),_=(0,d.D7)(),[k,E]=x(b),R=e=>{let{__scopePopover:t,children:r,open:i,defaultOpen:o,onOpenChange:s,modal:a=!1}=e,l=_(t),c=n.useRef(null),[h,f]=n.useState(!1),[p,g]=(0,m.T)({prop:i,defaultProp:null!=o&&o,onChange:s,caller:b});return(0,w.jsx)(d.fC,{...l,children:(0,w.jsx)(k,{scope:t,contentId:(0,u.M)(),triggerRef:c,open:p,onOpenChange:g,onOpenToggle:n.useCallback(()=>g(e=>!e),[g]),hasCustomAnchor:h,onCustomAnchorAdd:n.useCallback(()=>f(!0),[]),onCustomAnchorRemove:n.useCallback(()=>f(!1),[]),modal:a,children:r})})};R.displayName=b;var S="PopoverAnchor",A=n.forwardRef((e,t)=>{let{__scopePopover:r,...i}=e,o=E(S,r),s=_(r),{onCustomAnchorAdd:a,onCustomAnchorRemove:l}=o;return n.useEffect(()=>(a(),()=>l()),[a,l]),(0,w.jsx)(d.ee,{...s,...i,ref:t})});A.displayName=S;var T="PopoverTrigger",O=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,s=E(T,r),a=_(r),l=(0,o.e)(t,s.triggerRef),c=(0,w.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":z(s.open),...n,ref:l,onClick:(0,i.M)(e.onClick,s.onOpenToggle)});return s.hasCustomAnchor?c:(0,w.jsx)(d.ee,{asChild:!0,...a,children:c})});O.displayName=T;var I="PopoverPortal",[P,N]=x(I,{forceMount:void 0}),L=e=>{let{__scopePopover:t,forceMount:r,children:n,container:i}=e,o=E(I,t);return(0,w.jsx)(P,{scope:t,forceMount:r,children:(0,w.jsx)(f.z,{present:r||o.open,children:(0,w.jsx)(h.h,{asChild:!0,container:i,children:n})})})};L.displayName=I;var D="PopoverContent",j=n.forwardRef((e,t)=>{let r=N(D,e.__scopePopover),{forceMount:n=r.forceMount,...i}=e,o=E(D,e.__scopePopover);return(0,w.jsx)(f.z,{present:n||o.open,children:o.modal?(0,w.jsx)(M,{...i,ref:t}):(0,w.jsx)(F,{...i,ref:t})})});j.displayName=D;var B=(0,g.Z8)("PopoverContent.RemoveScroll"),M=n.forwardRef((e,t)=>{let r=E(D,e.__scopePopover),s=n.useRef(null),a=(0,o.e)(t,s),l=n.useRef(!1);return n.useEffect(()=>{let e=s.current;if(e)return(0,v.Ry)(e)},[]),(0,w.jsx)(y.Z,{as:B,allowPinchZoom:!0,children:(0,w.jsx)(q,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),l.current||null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,i.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;l.current=n},{checkForDefaultPrevented:!1}),onFocusOutside:(0,i.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),F=n.forwardRef((e,t)=>{let r=E(D,e.__scopePopover),i=n.useRef(!1),o=n.useRef(!1);return(0,w.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,s;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(i.current||null===(s=r.triggerRef.current)||void 0===s||s.focus(),t.preventDefault()),i.current=!1,o.current=!1},onInteractOutside:t=>{var n,s;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(i.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let a=t.target;(null===(s=r.triggerRef.current)||void 0===s?void 0:s.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),q=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:i,onCloseAutoFocus:o,disableOutsidePointerEvents:s,onEscapeKeyDown:u,onPointerDownOutside:h,onFocusOutside:f,onInteractOutside:p,...g}=e,m=E(D,r),v=_(r);return(0,l.EW)(),(0,w.jsx)(c.M,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,w.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:s,onInteractOutside:p,onEscapeKeyDown:u,onPointerDownOutside:h,onFocusOutside:f,onDismiss:()=>m.onOpenChange(!1),children:(0,w.jsx)(d.VY,{"data-state":z(m.open),role:"dialog",id:m.contentId,...v,...g,ref:t,style:{...g.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),W="PopoverClose";function z(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=E(W,r);return(0,w.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,i.M)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=W,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=_(r);return(0,w.jsx)(d.Eh,{...i,...n,ref:t})}).displayName="PopoverArrow";var H=R,U=A,V=O,Y=L,X=j},95600:function(e,t,r){"use strict";r.d(t,{Gt:function(){return j},W:function(){return B},ZP:function(){return ec}});var n=r(2265),i=r(54887),o=r(61994),s=r(57437);function a(e,t,r){let n="function"==typeof e.colSpan?e.colSpan(r):1;if(Number.isInteger(n)&&n>1&&(!e.frozen||e.idx+n-1<=t))return n}function l(e){e.stopPropagation()}function c(e){e?.scrollIntoView({inline:"nearest",block:"nearest"})}function u(e){let t=!1,r={...e,preventGridDefault(){t=!0},isGridDefaultPrevented:()=>t};return Object.setPrototypeOf(r,Object.getPrototypeOf(e)),r}let d=new Set(["Unidentified","Alt","AltGraph","CapsLock","Control","Fn","FnLock","Meta","NumLock","ScrollLock","Shift","Tab","ArrowDown","ArrowLeft","ArrowRight","ArrowUp","End","Home","PageDown","PageUp","Insert","ContextMenu","Escape","Pause","Play","PrintScreen","F1","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12"]);function h(e){return(e.ctrlKey||e.metaKey)&&"Control"!==e.key}function f(e,t){return null!=e.renderEditCell&&("function"==typeof e.editable?e.editable(t):e.editable)!==!1}let p="rdg-cell cj343x07-0-0-beta-41";function g(e,t){return void 0!==t?{"--rdg-grid-row-start":e,"--rdg-row-height":`${t}px`}:{"--rdg-grid-row-start":e}}function m(e,t,r){let n=t+1,i=`calc(${r-1} * var(--rdg-header-row-height))`;return void 0===e.parent?{insetBlockStart:0,gridRowStart:1,gridRowEnd:n,paddingBlockStart:i}:{insetBlockStart:`calc(${t-r} * var(--rdg-header-row-height))`,gridRowStart:n-r,gridRowEnd:n,paddingBlockStart:i}}function v(e,t=1){let r=e.idx+1;return{gridColumnStart:r,gridColumnEnd:r+t,insetInlineStart:e.frozen?`var(--rdg-frozen-left-${e.idx})`:void 0}}function y(e,...t){return(0,o.Z)(p,...t,e.frozen&&"rdg-cell-frozen csofj7r7-0-0-beta-41",e.isLastFrozenColumn&&"rdg-cell-frozen-last ch2wcw87-0-0-beta-41")}let{min:w,max:b,floor:x,sign:C,abs:_}=Math;function k(e){if("function"!=typeof e)throw Error("Please specify the rowKeyGetter prop to use selection")}function E(e,{minWidth:t,maxWidth:r}){return(e=b(e,t),"number"==typeof r&&r>=t)?w(e,r):e}function R(e,t){return void 0===e.parent?t:e.level-e.parent.level}function S({onChange:e,...t}){return(0,s.jsxs)("label",{className:(0,o.Z)("rdg-checkbox-label c1bn88vv7-0-0-beta-41",t.disabled&&"rdg-checkbox-label-disabled c1lwve4p7-0-0-beta-41"),children:[(0,s.jsx)("input",{type:"checkbox",...t,className:"rdg-checkbox-input c1qt073l7-0-0-beta-41",onChange:function(t){e(t.target.checked,t.nativeEvent.shiftKey)}}),(0,s.jsx)("div",{className:"rdg-checkbox cf71kmq7-0-0-beta-41"})]})}function A(e){try{return e.row[e.column.key]}catch{return null}}let T=(0,n.createContext)(void 0),O=T.Provider;function I(){return(0,n.useContext)(T)}let P=(0,n.createContext)(void 0),N=P.Provider,L=(0,n.createContext)(void 0),D=L.Provider;function j(){let e=(0,n.useContext)(P),t=(0,n.useContext)(L);if(void 0===e||void 0===t)throw Error("useRowSelection must be used within DataGrid cells");return[e,t]}let B="select-row",M="undefined"==typeof window?n.useEffect:n.useLayoutEffect;function F(e,t){let r=`[data-measuring-cell-key="${CSS.escape(t)}"]`,n=e.current.querySelector(r);return n?.getBoundingClientRect().width}function q(e){let t=(0,n.useRef)(e);(0,n.useEffect)(()=>{t.current=e});let r=(0,n.useCallback)((...e)=>{t.current(...e)},[]);return e?r:e}function W(e){let[t,r]=(0,n.useState)(!1);return t&&!e&&r(!1),{tabIndex:e&&!t?0:-1,childTabIndex:e?0:-1,onFocus:e?function(e){e.target!==e.currentTarget&&r(!0)}:void 0}}function z({gridRowStart:e,rows:t,column:r,columnWidth:n,maxColIdx:i,isLastRow:a,selectedPosition:l,latestDraggedOverRowIdx:c,isCellEditable:u,onRowsChange:d,onFill:h,onClick:f,setDragging:p,setDraggedOverRowIdx:g}){let{idx:m,rowIdx:y}=l;function w(e,n){let i=t[y],o=[...t],s=[];for(let a=e;a<n;a++)if(u({rowIdx:a,idx:m})){let e=h({columnKey:r.key,sourceRow:i,targetRow:t[a]});e!==t[a]&&(o[a]=e,s.push(a))}s.length>0&&d?.(o,{indexes:s,column:r})}return(0,s.jsx)("div",{style:function(){let o=r.colSpan?.({type:"ROW",row:t[y]})??1,{insetInlineStart:s,...l}=v(r,o),c="calc(var(--rdg-drag-handle-size) * -0.5 + 1px)",u=r.idx+o-1===i;return{...l,gridRowStart:e,marginInlineEnd:u?void 0:c,marginBlockEnd:a?void 0:c,insetInlineStart:s?`calc(${s} + ${n}px + var(--rdg-drag-handle-size) * -0.5 - 1px)`:void 0}}(),className:(0,o.Z)("rdg-cell-drag-handle c1w9bbhr7-0-0-beta-41",r.frozen&&"c1creorc7-0-0-beta-41"),onClick:f,onMouseDown:function(e){function t(e){1!==e.buttons&&r()}function r(){window.removeEventListener("mouseover",t),window.removeEventListener("mouseup",r),p(!1),function(){let e=c.current;void 0!==e&&(w(y<e?y+1:e,y<e?e+1:y),g(void 0))}()}e.preventDefault(),1===e.buttons&&(p(!0),window.addEventListener("mouseover",t),window.addEventListener("mouseup",r))},onDoubleClick:function(e){e.stopPropagation(),w(y+1,t.length)}})}function H({column:e,colSpan:t,row:r,rowIdx:i,onRowChange:o,closeEditor:a,onKeyDown:l,navigate:c}){let d=(0,n.useRef)(),h=e.editorOptions?.commitOnOutsideClick!==!1,f=q(()=>{g(!0,!1)});function p(){cancelAnimationFrame(d.current)}function g(e=!1,t=!0){e?o(r,!0,t):a(t)}function m(e,t=!1){o(e,t,t)}(0,n.useEffect)(()=>{if(h)return addEventListener("mousedown",e,{capture:!0}),()=>{removeEventListener("mousedown",e,{capture:!0}),p()};function e(){d.current=requestAnimationFrame(f)}},[h,f]);let{cellClass:w}=e,b=y(e,"rdg-editor-container","function"==typeof w?w(r):w,!e.editorOptions?.displayCellContent&&"cis5rrm7-0-0-beta-41");return(0,s.jsx)("div",{role:"gridcell","aria-colindex":e.idx+1,"aria-colspan":t,"aria-selected":!0,className:b,style:v(e,t),onKeyDown:function(t){if(l){let n=u(t);if(l({mode:"EDIT",row:r,column:e,rowIdx:i,navigate(){c(t)},onClose:g},n),n.isGridDefaultPrevented())return}"Escape"===t.key?g():"Enter"===t.key?g(!0):function({key:e,target:t}){return!!("Tab"===e&&(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement))&&t.closest(".rdg-editor-container")?.querySelectorAll("input, textarea, select").length===1}(t)&&c(t)},onMouseDownCapture:p,children:null!=e.renderEditCell&&(0,s.jsxs)(s.Fragment,{children:[e.renderEditCell({column:e,row:r,onRowChange:m,onClose:g}),e.editorOptions?.displayCellContent&&e.renderCell({column:e,row:r,rowIdx:i,isCellEditable:!0,tabIndex:-1,onRowChange:m})]})})}function U({column:e,rowIdx:t,isCellSelected:r,selectCell:n}){let{tabIndex:i,onFocus:a}=W(r),{colSpan:l}=e,c=R(e,t),u=e.idx+1;return(0,s.jsx)("div",{role:"columnheader","aria-colindex":u,"aria-colspan":l,"aria-rowspan":c,"aria-selected":r,tabIndex:i,className:(0,o.Z)(p,e.headerCellClass),style:{...m(e,t,c),gridColumnStart:u,gridColumnEnd:u+l},onFocus:a,onClick:function(){n({idx:e.idx,rowIdx:t})},children:e.name})}function V({column:e,sortDirection:t,priority:r}){return e.sortable?(0,s.jsx)(Y,{sortDirection:t,priority:r,children:e.name}):e.name}function Y({sortDirection:e,priority:t,children:r}){let n=I().renderSortStatus;return(0,s.jsxs)("span",{className:"h44jtk67-0-0-beta-41",children:[(0,s.jsx)("span",{className:"rdg-header-sort-name hcgkhxz7-0-0-beta-41",children:r}),(0,s.jsx)("span",{children:n({sortDirection:e,priority:t})})]})}function X({column:e,colSpan:t,rowIdx:r,isCellSelected:i,onColumnResize:o,onColumnsReorder:a,sortColumns:c,onSortColumnsChange:u,selectCell:d,shouldFocusGrid:h,direction:f,dragDropKey:p}){let g;let[w,b]=(0,n.useState)(!1),[x,C]=(0,n.useState)(!1),_="rtl"===f,k=R(e,r),{tabIndex:S,childTabIndex:A,onFocus:T}=W(i),O=c?.findIndex(t=>t.columnKey===e.key),I=void 0!==O&&O>-1?c[O]:void 0,P=I?.direction,N=void 0!==I&&c.length>1?O+1:void 0,L=P&&!N?"ASC"===P?"ascending":"descending":void 0,{sortable:D,resizable:j,draggable:B}=e,M=y(e,e.headerCellClass,D&&"c6l2wv17-0-0-beta-41",j&&"rdg-cell-resizable c1kqdw7y7-0-0-beta-41",B&&"rdg-cell-draggable",w&&"rdg-cell-dragging c1bezg5o7-0-0-beta-41",x&&"rdg-cell-drag-over c1vc96037-0-0-beta-41"),F=e.renderHeaderCell??V;function q(t){if(null==u)return;let{sortDescendingFirst:r}=e;if(void 0===I){let n={columnKey:e.key,direction:r?"DESC":"ASC"};u(c&&t?[...c,n]:[n])}else{let n;if((!0===r&&"DESC"===P||!0!==r&&"ASC"===P)&&(n={columnKey:e.key,direction:"ASC"===P?"DESC":"ASC"}),t){let e=[...c];n?e[O]=n:e.splice(O,1),u(e)}else u(n?[n]:[])}}return B&&(g={draggable:!0,onDragStart:function(t){t.dataTransfer.setData(p,e.key),t.dataTransfer.dropEffect="move",b(!0)},onDragEnd:function(){b(!1)},onDragOver:function(e){e.preventDefault(),e.dataTransfer.dropEffect="move"},onDragEnter:function(e){K(e)&&C(!0)},onDragLeave:function(e){K(e)&&C(!1)},onDrop:function(t){if(C(!1),t.dataTransfer.types.includes(p)){let r=t.dataTransfer.getData(p);r!==e.key&&(t.preventDefault(),a?.(r,e.key))}}}),(0,s.jsxs)("div",{role:"columnheader","aria-colindex":e.idx+1,"aria-colspan":t,"aria-rowspan":k,"aria-selected":i,"aria-sort":L,tabIndex:h?0:S,className:M,style:{...m(e,r,k),...v(e,t)},onFocus:function(e){T?.(e),h&&d({idx:0,rowIdx:r})},onClick:function(t){d({idx:e.idx,rowIdx:r}),D&&q(t.ctrlKey||t.metaKey)},onKeyDown:D?function(e){(" "===e.key||"Enter"===e.key)&&(e.preventDefault(),q(e.ctrlKey||e.metaKey))}:void 0,...g,children:[F({column:e,sortDirection:P,priority:N,tabIndex:A}),j&&(0,s.jsx)("div",{className:"r1y6ywlx7-0-0-beta-41",onClick:l,onDoubleClick:function(){o(e,"max-content")},onPointerDown:function(t){if("mouse"===t.pointerType&&1!==t.buttons)return;t.preventDefault();let{currentTarget:r,pointerId:n}=t,i=r.parentElement,{right:s,left:a}=i.getBoundingClientRect(),l=_?t.clientX-a:s-t.clientX;function c(t){let{right:r,left:n}=i.getBoundingClientRect(),s=_?r+l-t.clientX:t.clientX+l-n;s>0&&o(e,E(s,e))}r.setPointerCapture(n),r.addEventListener("pointermove",c),r.addEventListener("lostpointercapture",function e(){r.removeEventListener("pointermove",c),r.removeEventListener("lostpointercapture",e)})}})]})}function K(e){let t=e.relatedTarget;return!e.currentTarget.contains(t)}let $="rdg-row r1upfr807-0-0-beta-41",G="rdg-row-selected",Z="rdg-header-row h10tskcx7-0-0-beta-41",J=(0,n.memo)(function({rowIdx:e,columns:t,onColumnResize:r,onColumnsReorder:i,sortColumns:l,onSortColumnsChange:c,lastFrozenColumnIndex:u,selectedCellIdx:d,selectCell:h,shouldFocusGrid:f,direction:p}){let g=(0,n.useId)(),m=[];for(let n=0;n<t.length;n++){let o=t[n],v=a(o,u,{type:"HEADER"});void 0!==v&&(n+=v-1),m.push((0,s.jsx)(X,{column:o,colSpan:v,rowIdx:e,isCellSelected:d===o.idx,onColumnResize:r,onColumnsReorder:i,onSortColumnsChange:c,sortColumns:l,selectCell:h,shouldFocusGrid:f&&0===n,direction:p,dragDropKey:g},o.key))}return(0,s.jsx)("div",{role:"row","aria-rowindex":e,className:(0,o.Z)(Z,-1===d&&G),children:m})}),Q=(0,n.memo)(function({rowIdx:e,level:t,columns:r,selectedCellIdx:n,selectCell:i}){let o=[],a=new Set;for(let l of r){let{parent:r}=l;if(void 0!==r){for(;r.level>t&&void 0!==r.parent;)r=r.parent;if(r.level===t&&!a.has(r)){a.add(r);let{idx:t}=r;o.push((0,s.jsx)(U,{column:r,rowIdx:e,isCellSelected:n===t,selectCell:i},t))}}}return(0,s.jsx)("div",{role:"row","aria-rowindex":e,className:Z,children:o})}),ee=(0,n.memo)(function({column:e,colSpan:t,isCellSelected:r,isCopied:n,isDraggedOver:i,row:o,rowIdx:a,onClick:l,onDoubleClick:c,onContextMenu:d,onRowChange:h,selectCell:p,...g}){let{tabIndex:m,childTabIndex:w,onFocus:b}=W(r),{cellClass:x}=e,C=y(e,"function"==typeof x?x(o):x,n&&"rdg-cell-copied c6ra8a37-0-0-beta-41",i&&"rdg-cell-dragged-over cq910m07-0-0-beta-41"),_=f(e,o);function k(t){p({rowIdx:a,idx:e.idx},t)}return(0,s.jsx)("div",{role:"gridcell","aria-colindex":e.idx+1,"aria-colspan":t,"aria-selected":r,"aria-readonly":!_||void 0,tabIndex:m,className:C,style:v(e,t),onClick:function(t){if(l){let r=u(t);if(l({row:o,column:e,selectCell:k},r),r.isGridDefaultPrevented())return}k()},onDoubleClick:function(t){if(c){let r=u(t);if(c({row:o,column:e,selectCell:k},r),r.isGridDefaultPrevented())return}k(!0)},onContextMenu:function(t){if(d){let r=u(t);if(d({row:o,column:e,selectCell:k},r),r.isGridDefaultPrevented())return}k()},onFocus:b,...g,children:e.renderCell({column:e,row:o,rowIdx:a,isCellEditable:_,tabIndex:w,onRowChange:function(t){h(e,t)}})})}),et=(0,n.memo)((0,n.forwardRef)(function({className:e,rowIdx:t,gridRowStart:r,height:n,selectedCellIdx:i,isRowSelected:l,copiedCellIdx:c,draggedOverCellIdx:u,lastFrozenColumnIndex:d,row:h,viewportColumns:f,selectedCellEditor:p,onCellClick:m,onCellDoubleClick:v,onCellContextMenu:y,rowClass:w,setDraggedOverRowIdx:b,onMouseEnter:x,onRowChange:C,selectCell:_,...k},E){let R=q((e,r)=>{C(e,t,r)});e=(0,o.Z)($,`rdg-row-${t%2==0?"even":"odd"}`,w?.(h,t),e,-1===i&&G);let S=[];for(let e=0;e<f.length;e++){let r=f[e],{idx:n}=r,o=a(r,d,{type:"ROW",row:h});void 0!==o&&(e+=o-1);let l=i===n;l&&p?S.push(p):S.push((0,s.jsx)(ee,{column:r,colSpan:o,row:h,rowIdx:t,isCopied:c===n,isDraggedOver:u===n,isCellSelected:l,onClick:m,onDoubleClick:v,onContextMenu:y,onRowChange:R,selectCell:_},r.key))}return(0,s.jsx)(N,{value:l,children:(0,s.jsx)("div",{role:"row",ref:E,className:e,onMouseEnter:function(e){b?.(t),x?.(e)},style:g(r,n),...k,children:S})})}));function er(e,t){return(0,s.jsx)(et,{...t},e)}function en({scrollToPosition:{idx:e,rowIdx:t},gridElement:r,setScrollToCellPosition:i}){let o=(0,n.useRef)(null);return M(()=>{c(o.current)}),M(()=>{let e=new IntersectionObserver(function(){i(null)},{root:r,threshold:1});return e.observe(o.current),()=>{e.disconnect()}},[r,i]),(0,s.jsx)("div",{ref:o,style:{gridColumn:void 0===e?"1/-1":e+1,gridRow:void 0===t?"1/-1":t+2}})}function ei({sortDirection:e,priority:t}){return(0,s.jsxs)(s.Fragment,{children:[function({sortDirection:e}){return void 0===e?null:(0,s.jsx)("svg",{viewBox:"0 0 12 8",width:"12",height:"8",className:"rdg-sort-arrow a3ejtar7-0-0-beta-41","aria-hidden":!0,children:(0,s.jsx)("path",{d:"ASC"===e?"M0 8 6 0 12 8":"M0 0 6 8 12 0"})})}({sortDirection:e}),function({priority:e}){return e}({priority:t})]})}let eo=(0,n.memo)(function({column:e,colSpan:t,row:r,rowIdx:n,isCellSelected:i,selectCell:o}){let{tabIndex:a,childTabIndex:l,onFocus:c}=W(i),{summaryCellClass:u}=e,d=y(e,"s8wc6fl7-0-0-beta-41","function"==typeof u?u(r):u);return(0,s.jsx)("div",{role:"gridcell","aria-colindex":e.idx+1,"aria-colspan":t,"aria-selected":i,tabIndex:a,className:d,style:v(e,t),onClick:function(){o({rowIdx:n,idx:e.idx})},onFocus:c,children:e.renderSummaryCell?.({column:e,row:r,tabIndex:l})})}),es=(0,n.memo)(function({rowIdx:e,gridRowStart:t,row:r,viewportColumns:n,top:i,bottom:l,lastFrozenColumnIndex:c,selectedCellIdx:u,isTop:d,showBorder:h,selectCell:f,"aria-rowindex":p}){let m=[];for(let t=0;t<n.length;t++){let i=n[t],o=a(i,c,{type:"SUMMARY",row:r});void 0!==o&&(t+=o-1);let l=u===i.idx;m.push((0,s.jsx)(eo,{column:i,colSpan:o,row:r,rowIdx:e,isCellSelected:l,selectCell:f},i.key))}return(0,s.jsx)("div",{role:"row","aria-rowindex":p,className:(0,o.Z)($,`rdg-row-${e%2==0?"even":"odd"}`,"rdg-summary-row skuhp557-0-0-beta-41",d?["rdg-top-summary-row tf8l5ub7-0-0-beta-41",h&&"tb9ughf7-0-0-beta-41"]:["rdg-bottom-summary-row",h&&"b1yssfnt7-0-0-beta-41"],-1===u&&G),style:{...g(t),"--rdg-summary-row-top":void 0!==i?`${i}px`:void 0,"--rdg-summary-row-bottom":void 0!==l?`${l}px`:void 0},children:m})});function ea(e){return e.querySelector(':scope > [role="row"] > [tabindex="0"]')}function el(e,t){return e.idx===t.idx&&e.rowIdx===t.rowIdx}let ec=(0,n.forwardRef)(function(e,t){let{columns:r,rows:l,topSummaryRows:p,bottomSummaryRows:g,rowKeyGetter:m,onRowsChange:v,rowHeight:y,headerRowHeight:R,summaryRowHeight:T,selectedRows:P,onSelectedRowsChange:L,sortColumns:j,onSortColumnsChange:W,defaultColumnOptions:U,onCellClick:V,onCellDoubleClick:Y,onCellContextMenu:X,onCellKeyDown:K,onSelectedCellChange:$,onScroll:G,onColumnResize:Z,onColumnsReorder:ee,onFill:et,onCopy:eo,onPaste:ec,enableVirtualization:eu,renderers:ed,className:eh,style:ef,rowClass:ep,direction:eg,role:em,"aria-label":ev,"aria-labelledby":ey,"aria-describedby":ew,"aria-rowcount":eb,"data-testid":ex}=e,eC=I(),e_=em??"grid",ek=y??35,eE=R??("number"==typeof ek?ek:35),eR=T??("number"==typeof ek?ek:35),eS=ed?.renderRow??eC?.renderRow??er,eA=ed?.renderSortStatus??eC?.renderSortStatus??ei,eT=ed?.renderCheckbox??eC?.renderCheckbox??S,eO=ed?.noRowsFallback??eC?.noRowsFallback,eI=eu??!0,eP=eg??"ltr",[eN,eL]=(0,n.useState)(0),[eD,ej]=(0,n.useState)(0),[eB,eM]=(0,n.useState)(()=>new Map),[eF,eq]=(0,n.useState)(()=>new Map),[eW,ez]=(0,n.useState)(null),[eH,eU]=(0,n.useState)(!1),[eV,eY]=(0,n.useState)(void 0),[eX,eK]=(0,n.useState)(null),e$=(0,n.useCallback)(e=>eB.get(e.key)??eF.get(e.key)??e.width,[eF,eB]),[eG,eZ,eJ]=function(){let e=(0,n.useRef)(null),[t,r]=(0,n.useState)(1),[o,s]=(0,n.useState)(1);return M(()=>{let{ResizeObserver:t}=window;if(null==t)return;let{clientWidth:n,clientHeight:o,offsetWidth:a,offsetHeight:l}=e.current,{width:c,height:u}=e.current.getBoundingClientRect();r(c-a+n),s(u-l+o);let d=new t(e=>{let t=e[0].contentBoxSize[0];(0,i.flushSync)(()=>{r(t.inlineSize),s(t.blockSize)})});return d.observe(e.current),()=>{d.disconnect()}},[]),[e,t,o]}(),{columns:eQ,colSpanColumns:e0,lastFrozenColumnIndex:e1,headerRowsCount:e2,colOverscanStartIdx:e4,colOverscanEndIdx:e5,templateColumns:e6,layoutCssVars:e7,totalFrozenColumnWidth:e8}=function({rawColumns:e,defaultColumnOptions:t,getColumnWidth:r,viewportWidth:i,scrollLeft:o,enableVirtualization:s}){let a=t?.width??"auto",l=t?.minWidth??50,c=t?.maxWidth??void 0,u=t?.renderCell??A,d=t?.sortable??!1,h=t?.resizable??!1,f=t?.draggable??!1,{columns:p,colSpanColumns:g,lastFrozenColumnIndex:m,headerRowsCount:v}=(0,n.useMemo)(()=>{let t=-1,r=1,n=[];(function e(i,o,s){for(let p of i){if("children"in p){let t={name:p.name,parent:s,idx:-1,colSpan:0,level:0,headerCellClass:p.headerCellClass};e(p.children,o+1,t);continue}let i=p.frozen??!1,g={...p,parent:s,idx:0,level:0,frozen:i,isLastFrozenColumn:!1,width:p.width??a,minWidth:p.minWidth??l,maxWidth:p.maxWidth??c,sortable:p.sortable??d,resizable:p.resizable??h,draggable:p.draggable??f,renderCell:p.renderCell??u};n.push(g),i&&t++,o>r&&(r=o)}})(e,1),n.sort(({key:e,frozen:t},{key:r,frozen:n})=>e===B?-1:r===B?1:t?n?0:-1:n?1:0);let i=[];return n.forEach((e,t)=>{e.idx=t,function e(t,r,n){if(n<t.level&&(t.level=n),void 0!==t.parent){let{parent:i}=t;-1===i.idx&&(i.idx=r),i.colSpan+=1,e(i,r,n-1)}}(e,t,0),null!=e.colSpan&&i.push(e)}),-1!==t&&(n[t].isLastFrozenColumn=!0),{columns:n,colSpanColumns:i,lastFrozenColumnIndex:t,headerRowsCount:r}},[e,a,l,c,u,h,d,f]),{templateColumns:y,layoutCssVars:x,totalFrozenColumnWidth:C,columnMetrics:_}=(0,n.useMemo)(()=>{let e=new Map,t=0,n=0,i=[];for(let n of p){let o=r(n);o="number"==typeof o?E(o,n):n.minWidth,i.push(`${o}px`),e.set(n,{width:o,left:t}),t+=o}if(-1!==m){let t=e.get(p[m]);n=t.left+t.width}let o={};for(let t=0;t<=m;t++){let r=p[t];o[`--rdg-frozen-left-${r.idx}`]=`${e.get(r).left}px`}return{templateColumns:i,layoutCssVars:o,totalFrozenColumnWidth:n,columnMetrics:e}},[r,p,m]),[k,R]=(0,n.useMemo)(()=>{if(!s)return[0,p.length-1];let e=o+C,t=o+i,r=p.length-1,n=w(m+1,r);if(e>=t)return[n,n];let a=n;for(;a<r;){let{left:t,width:r}=_.get(p[a]);if(t+r>e)break;a++}let l=a;for(;l<r;){let{left:e,width:r}=_.get(p[l]);if(e+r>=t)break;l++}return[b(n,a-1),w(r,l+1)]},[_,p,m,o,C,i,s]);return{columns:p,colSpanColumns:g,colOverscanStartIdx:k,colOverscanEndIdx:R,templateColumns:y,layoutCssVars:x,headerRowsCount:v,lastFrozenColumnIndex:m,totalFrozenColumnWidth:C}}({rawColumns:r,defaultColumnOptions:U,getColumnWidth:e$,scrollLeft:eD,viewportWidth:eZ,enableVirtualization:eI}),e3=p?.length??0,e9=g?.length??0,te=e3+e9,tt=e2+e3,tr=e2-1,tn=-tt,ti=tn+tr,to=l.length+e9-1,[ts,ta]=(0,n.useState)(()=>({idx:-1,rowIdx:tn-1,mode:"SELECT"})),tl=(0,n.useRef)(ts),tc=(0,n.useRef)(eV),tu=(0,n.useRef)(-1),td=(0,n.useRef)(null),th=(0,n.useRef)(!1),tf="treegrid"===e_,tp=e2*eE,tg=eJ-tp-te*eR,tm=null!=P&&null!=L,tv="rtl"===eP,ty=tv?"ArrowRight":"ArrowLeft",tw=tv?"ArrowLeft":"ArrowRight",tb=eb??e2+l.length+te,tx=(0,n.useMemo)(()=>({renderCheckbox:eT,renderSortStatus:eA}),[eT,eA]),tC=(0,n.useMemo)(()=>{let{length:e}=l;return 0!==e&&null!=P&&null!=m&&P.size>=e&&l.every(e=>P.has(m(e)))},[l,P,m]),{rowOverscanStartIdx:t_,rowOverscanEndIdx:tk,totalRowHeight:tE,gridTemplateRows:tR,getRowTop:tS,getRowHeight:tA,findRowIdx:tT}=function({rows:e,rowHeight:t,clientHeight:r,scrollTop:i,enableVirtualization:o}){let{totalRowHeight:s,gridTemplateRows:a,getRowTop:l,getRowHeight:c,findRowIdx:u}=(0,n.useMemo)(()=>{if("number"==typeof t)return{totalRowHeight:t*e.length,gridTemplateRows:` repeat(${e.length}, ${t}px)`,getRowTop:e=>e*t,getRowHeight:()=>t,findRowIdx:e=>x(e/t)};let r=0,n=" ",i=e.map(e=>{let i=t(e),o={top:r,height:i};return n+=`${i}px `,r+=i,o}),o=t=>b(0,w(e.length-1,t));return{totalRowHeight:r,gridTemplateRows:n,getRowTop:e=>i[o(e)].top,getRowHeight:e=>i[o(e)].height,findRowIdx(e){let t=0,r=i.length-1;for(;t<=r;){let n=t+x((r-t)/2),o=i[n].top;if(o===e)return n;if(o<e?t=n+1:o>e&&(r=n-1),t>r)return r}return 0}}},[t,e]),d=0,h=e.length-1;if(o){let t=u(i),n=u(i+r);d=b(0,t-4),h=w(e.length-1,n+4)}return{rowOverscanStartIdx:d,rowOverscanEndIdx:h,totalRowHeight:s,gridTemplateRows:a,getRowTop:l,getRowHeight:c,findRowIdx:u}}({rows:l,rowHeight:ek,clientHeight:tg,scrollTop:eN,enableVirtualization:eI}),tO=function({columns:e,colSpanColumns:t,rows:r,topSummaryRows:i,bottomSummaryRows:o,colOverscanStartIdx:s,colOverscanEndIdx:l,lastFrozenColumnIndex:c,rowOverscanStartIdx:u,rowOverscanEndIdx:d}){let h=(0,n.useMemo)(()=>{if(0===s)return 0;let e=s,n=(t,r)=>void 0!==r&&t+r>s&&(e=t,!0);for(let s of t){let t=s.idx;if(t>=e||n(t,a(s,c,{type:"HEADER"})))break;for(let e=u;e<=d&&!n(t,a(s,c,{type:"ROW",row:r[e]}));e++);if(null!=i){for(let e of i)if(n(t,a(s,c,{type:"SUMMARY",row:e})))break}if(null!=o){for(let e of o)if(n(t,a(s,c,{type:"SUMMARY",row:e})))break}}return e},[u,d,r,i,o,s,c,t]);return(0,n.useMemo)(()=>{let t=[];for(let r=0;r<=l;r++){let n=e[r];(!(r<h)||n.frozen)&&t.push(n)}return t},[h,l,e])}({columns:eQ,colSpanColumns:e0,colOverscanStartIdx:e4,colOverscanEndIdx:e5,lastFrozenColumnIndex:e1,rowOverscanStartIdx:t_,rowOverscanEndIdx:tk,rows:l,topSummaryRows:p,bottomSummaryRows:g}),{gridTemplateColumns:tI,handleColumnResize:tP}=function(e,t,r,o,s,a,l,c,u,d){let h=(0,n.useRef)(s),f=e.length===t.length,p=f&&s!==h.current,g=[...r],m=[];for(let{key:e,idx:r,width:n}of t)"string"!=typeof n||!p&&l.has(e)||a.has(e)||(g[r]=n,m.push(e));let v=g.join(" ");function y(e){0!==e.length&&u(t=>{let r=new Map(t),n=!1;for(let i of e){let e=F(o,i);n||=e!==t.get(i),void 0===e?r.delete(i):r.set(i,e)}return n?r:t})}return M(()=>{h.current=s,y(m)}),{gridTemplateColumns:v,handleColumnResize:function(e,n){let{key:s}=e,l=[...r],u=[];for(let{key:e,idx:r,width:i}of t)if(s===e){let e="number"==typeof n?`${n}px`:n;l[r]=e}else f&&"string"==typeof i&&!a.has(e)&&(l[r]=i,u.push(e));o.current.style.gridTemplateColumns=l.join(" ");let h="number"==typeof n?n:F(o,s);(0,i.flushSync)(()=>{c(e=>{let t=new Map(e);return t.set(s,h),t}),y(u)}),d?.(e.idx,h)}}}(eQ,tO,e6,eG,eZ,eB,eF,eM,eq,Z),tN=tf?-1:0,tL=eQ.length-1,tD=tJ(ts),tj=tQ(ts),tB=q(tP),tM=q(ee),tF=q(W),tq=q(V),tW=q(Y),tz=q(X),tH=q(tK),tU=q(t$),tV=q(t1),tY=q(({idx:e,rowIdx:t})=>{t1({rowIdx:tn+t-1,idx:e})});M(()=>{if(!tD||el(ts,tl.current)){tl.current=ts;return}tl.current=ts,-1===ts.idx&&(td.current.focus({preventScroll:!0}),c(td.current))}),M(()=>{th.current&&(th.current=!1,t4())}),(0,n.useImperativeHandle)(t,()=>({element:eG.current,scrollToCell({idx:e,rowIdx:t}){let r=void 0!==e&&e>e1&&e<eQ.length?e:void 0,n=void 0!==t&&tZ(t)?t:void 0;(void 0!==r||void 0!==n)&&eK({idx:r,rowIdx:n})},selectCell:t1}));let tX=(0,n.useCallback)(e=>{eY(e),tc.current=e},[]);function tK(e){if(!L)return;if(k(m),"HEADER"===e.type){let t=new Set(P);for(let r of l){let n=m(r);e.checked?t.add(n):t.delete(n)}L(t);return}let{row:t,checked:r,isShiftClick:n}=e,i=new Set(P),o=m(t);if(r){i.add(o);let e=tu.current,r=l.indexOf(t);if(tu.current=r,n&&-1!==e&&e!==r){let t=C(r-e);for(let n=e+t;n!==r;n+=t){let e=l[n];i.add(m(e))}}}else i.delete(o),tu.current=-1;L(i)}function t$(e,t,r){if("function"!=typeof v||r===l[t])return;let n=[...l];n[t]=r,v(n,{indexes:[t],column:e})}function tG(){"EDIT"===ts.mode&&t$(eQ[ts.idx],ts.rowIdx,ts.row)}function tZ(e){return e>=0&&e<l.length}function tJ({idx:e,rowIdx:t}){var r;return t>=tn&&t<=to&&(r=e)>=tN&&r<=tL}function tQ({idx:e,rowIdx:t}){var r;return tZ(t)&&(r=e)>=tN&&r<=tL}function t0(e){return function({idx:e,rowIdx:t}){return tZ(t)&&e>=0&&e<=tL}(e)&&function({selectedPosition:e,columns:t,rows:r}){return f(t[e.idx],r[e.rowIdx])}({columns:eQ,rows:l,selectedPosition:e})}function t1(e,t){if(!tJ(e))return;tG();let r=l[e.rowIdx],n=el(ts,e);t&&t0(e)?ta({...e,mode:"EDIT",row:r,originalRow:r}):n?c(ea(eG.current)):(th.current=!0,ta({...e,mode:"SELECT"})),$&&!n&&$({rowIdx:e.rowIdx,row:r,column:eQ[e.idx]})}function t2(e){let{key:t,shiftKey:r}=e,n="NONE";if("Tab"===t){if(function({maxColIdx:e,minRowIdx:t,maxRowIdx:r,selectedPosition:{rowIdx:n,idx:i},shiftKey:o}){return o?0===i&&n===t:i===e&&n===r}({shiftKey:r,maxColIdx:tL,minRowIdx:tn,maxRowIdx:to,selectedPosition:ts})){tG();return}n="CHANGE_ROW"}e.preventDefault();let i=function(e,t,r){let{idx:n,rowIdx:i}=ts,o=tD&&-1===n;switch(e){case"ArrowUp":return{idx:n,rowIdx:i-1};case"ArrowDown":return{idx:n,rowIdx:i+1};case ty:return{idx:n-1,rowIdx:i};case tw:return{idx:n+1,rowIdx:i};case"Tab":return{idx:n+(r?-1:1),rowIdx:i};case"Home":if(o)return{idx:n,rowIdx:tn};return{idx:0,rowIdx:t?tn:i};case"End":if(o)return{idx:n,rowIdx:to};return{idx:tL,rowIdx:t?to:i};case"PageUp":{if(ts.rowIdx===tn)return ts;let e=tS(i)+tA(i)-tg;return{idx:n,rowIdx:e>0?tT(e):0}}case"PageDown":{if(ts.rowIdx>=l.length)return ts;let e=tS(i)+tg;return{idx:n,rowIdx:e<tE?tT(e):l.length-1}}default:return ts}}(t,h(e),r);el(ts,i)||t1(function({moveUp:e,moveNext:t,cellNavigationMode:r,columns:n,colSpanColumns:i,rows:o,topSummaryRows:s,bottomSummaryRows:l,minRowIdx:c,mainHeaderRowIdx:u,maxRowIdx:d,currentPosition:{idx:h,rowIdx:f},nextPosition:p,lastFrozenColumnIndex:g,isCellWithinBounds:m}){let{idx:v,rowIdx:y}=p,w=n.length,b=e=>{for(let t of i){let r=t.idx;if(r>v)break;let n=function({rows:e,topSummaryRows:t,bottomSummaryRows:r,rowIdx:n,mainHeaderRowIdx:i,lastFrozenColumnIndex:o,column:s}){let l=t?.length??0;return n===i?a(s,o,{type:"HEADER"}):t&&n>i&&n<=l+i?a(s,o,{type:"SUMMARY",row:t[n+l]}):n>=0&&n<e.length?a(s,o,{type:"ROW",row:e[n]}):r?a(s,o,{type:"SUMMARY",row:r[n-e.length]}):void 0}({rows:o,topSummaryRows:s,bottomSummaryRows:l,rowIdx:y,mainHeaderRowIdx:u,lastFrozenColumnIndex:g,column:t});if(n&&v>r&&v<n+r){v=r+(e?n:0);break}}},x=e=>e.level+u;if(m(p)&&(b(t),y<u&&(()=>{if(t){let e=n[v].parent;for(;void 0!==e;){let t=x(e);if(y===t){v=e.idx+e.colSpan;break}e=e.parent}}else if(e){let e=n[v].parent,t=!1;for(;void 0!==e;){let r=x(e);if(y>=r){v=e.idx,y=r,t=!0;break}e=e.parent}t||(v=h,y=f)}})()),"CHANGE_ROW"===r){let e=v===w,t=-1===v;e?y!==d&&(v=0,y+=1):t&&(y!==c&&(y-=1,v=w-1),b(!1))}if(y<u){let e=n[v].parent,t=y;for(y=u;void 0!==e;){let r=x(e);r>=t&&(y=r,v=e.idx),e=e.parent}}return{idx:v,rowIdx:y}}({moveUp:"ArrowUp"===t,moveNext:t===tw||"Tab"===t&&!r,columns:eQ,colSpanColumns:e0,rows:l,topSummaryRows:p,bottomSummaryRows:g,minRowIdx:tn,mainHeaderRowIdx:ti,maxRowIdx:to,lastFrozenColumnIndex:e1,cellNavigationMode:n,currentPosition:ts,nextPosition:i,isCellWithinBounds:tJ}))}function t4(){let e=ea(eG.current);null!==e&&(c(e),(e.querySelector('[tabindex="0"]')??e).focus({preventScroll:!0}))}function t5(e){let t=-1===ts.idx?void 0:eQ[ts.idx];return void 0===t||ts.rowIdx!==e||tO.includes(t)?tO:ts.idx>e5?[...tO,t]:[...tO.slice(0,e1+1),t,...tO.slice(e1+1)]}(ts.idx>tL||ts.rowIdx>to)&&(ta({idx:-1,rowIdx:tn-1,mode:"SELECT"}),tX(void 0));let t6=`repeat(${e2}, ${eE}px)`;e3>0&&(t6+=` repeat(${e3}, ${eR}px)`),l.length>0&&(t6+=tR),e9>0&&(t6+=` repeat(${e9}, ${eR}px)`);let t7=-1===ts.idx&&ts.rowIdx!==tn-1;return(0,s.jsxs)("div",{role:e_,"aria-label":ev,"aria-labelledby":ey,"aria-describedby":ew,"aria-multiselectable":!!tm||void 0,"aria-colcount":eQ.length,"aria-rowcount":tb,className:(0,o.Z)("rdg rnvodz57-0-0-beta-41",eh,eH&&"rdg-viewport-dragging vlqv91k7-0-0-beta-41"),style:{...ef,scrollPaddingInlineStart:ts.idx>e1||eX?.idx!==void 0?`${e8}px`:void 0,scrollPaddingBlock:tZ(ts.rowIdx)||eX?.rowIdx!==void 0?`${tp+e3*eR}px ${e9*eR}px`:void 0,gridTemplateColumns:tI,gridTemplateRows:t6,"--rdg-header-row-height":`${eE}px`,"--rdg-summary-row-height":`${eR}px`,"--rdg-sign":tv?-1:1,...e7},dir:eP,ref:eG,onScroll:function(e){let{scrollTop:t,scrollLeft:r}=e.currentTarget;(0,i.flushSync)(()=>{eL(t),ej(_(r))}),G?.(e)},onKeyDown:function(e){let{idx:t,rowIdx:r,mode:n}=ts;if("EDIT"===n)return;if(K&&tZ(r)){let n=l[r],i=u(e);if(K({mode:"SELECT",row:n,column:eQ[t],rowIdx:r,selectCell:t1},i),i.isGridDefaultPrevented())return}if(!(e.target instanceof Element))return;let i=null!==e.target.closest(".rdg-cell"),o=tf&&e.target===td.current;if(!i&&!o)return;let{keyCode:s}=e;if(tj&&(null!=ec||null!=eo)&&h(e)){if(67===s){(function(){let{idx:e,rowIdx:t}=ts,r=l[t],n=eQ[e].key;ez({row:r,columnKey:n}),eo?.({sourceRow:r,sourceColumnKey:n})})();return}if(86===s){(function(){if(!ec||!v||null===eW||!t0(ts))return;let{idx:e,rowIdx:t}=ts,r=eQ[e],n=l[t],i=ec({sourceRow:eW.row,sourceColumnKey:eW.columnKey,targetRow:n,targetColumnKey:r.key});t$(r,t,i)})();return}}switch(e.key){case"Escape":ez(null);return;case"ArrowUp":case"ArrowDown":case"ArrowLeft":case"ArrowRight":case"Tab":case"Home":case"End":case"PageUp":case"PageDown":t2(e);break;default:(function(e){if(!tj)return;let t=l[ts.rowIdx],{key:r,shiftKey:n}=e;if(tm&&n&&" "===r){k(m);let r=m(t);tK({type:"ROW",row:t,checked:!P.has(r),isShiftClick:!1}),e.preventDefault();return}t0(ts)&&!d.has(e.key)&&ta(({idx:e,rowIdx:r})=>({idx:e,rowIdx:r,mode:"EDIT",row:t,originalRow:t}))})(e)}},"data-testid":ex,children:[(0,s.jsx)(O,{value:tx,children:(0,s.jsxs)(D,{value:tH,children:[(0,s.jsxs)(N,{value:tC,children:[Array.from({length:tr},(e,t)=>(0,s.jsx)(Q,{rowIdx:t+1,level:-tr+t,columns:t5(tn+t),selectedCellIdx:ts.rowIdx===tn+t?ts.idx:void 0,selectCell:tY},t)),(0,s.jsx)(J,{rowIdx:e2,columns:t5(ti),onColumnResize:tB,onColumnsReorder:tM,sortColumns:j,onSortColumnsChange:tF,lastFrozenColumnIndex:e1,selectedCellIdx:ts.rowIdx===ti?ts.idx:void 0,selectCell:tY,shouldFocusGrid:!tD,direction:eP})]}),0===l.length&&eO?eO:(0,s.jsxs)(s.Fragment,{children:[p?.map((e,t)=>{let r=e2+1+t,n=ti+1+t,i=ts.rowIdx===n;return s.jsx(es,{"aria-rowindex":r,rowIdx:n,gridRowStart:r,row:e,top:tp+eR*t,bottom:void 0,viewportColumns:t5(n),lastFrozenColumnIndex:e1,selectedCellIdx:i?ts.idx:void 0,isTop:!0,showBorder:t===e3-1,selectCell:tV},t)}),function(){let e=[],{idx:t,rowIdx:r}=ts,n=tj&&r<t_?t_-1:t_,o=tj&&r>tk?tk+1:tk;for(let c=n;c<=o;c++){let n=c===t_-1||c===tk+1,o=n?r:c,u=tO,d=-1===t?void 0:eQ[t];void 0!==d&&(u=n?[d]:t5(o));let h=l[o],f=tt+o+1,p=o,g=!1;"function"==typeof m&&(p=m(h),g=P?.has(p)??!1),e.push(eS(p,{"aria-rowindex":tt+o+1,"aria-selected":tm?g:void 0,rowIdx:o,row:h,viewportColumns:u,isRowSelected:g,onCellClick:tq,onCellDoubleClick:tW,onCellContextMenu:tz,rowClass:ep,gridRowStart:f,height:tA(o),copiedCellIdx:null!==eW&&eW.row===h?eQ.findIndex(e=>e.key===eW.columnKey):void 0,selectedCellIdx:r===o?t:void 0,draggedOverCellIdx:function(e){if(void 0===eV)return;let{rowIdx:t}=ts;return(t<eV?t<e&&e<=eV:t>e&&e>=eV)?ts.idx:void 0}(o),setDraggedOverRowIdx:eH?tX:void 0,lastFrozenColumnIndex:e1,onRowChange:tU,selectCell:tV,selectedCellEditor:function(e){if(ts.rowIdx!==e||"SELECT"===ts.mode)return;let{idx:t,row:r}=ts,n=eQ[t],o=a(n,e1,{type:"ROW",row:r}),c=e=>{th.current=e,ta(({idx:e,rowIdx:t})=>({idx:e,rowIdx:t,mode:"SELECT"}))};return l[ts.rowIdx]!==ts.originalRow&&c(!1),(0,s.jsx)(H,{column:n,colSpan:o,row:r,rowIdx:e,onRowChange:(e,t,r)=>{t?(0,i.flushSync)(()=>{t$(n,ts.rowIdx,e),c(r)}):ta(t=>({...t,row:e}))},closeEditor:c,onKeyDown:K,navigate:t2},n.key)}(o)}))}return e}(),g?.map((e,t)=>{let r=tt+l.length+t+1,n=l.length+t,i=ts.rowIdx===n,o=tg>tE?eJ-eR*(g.length-t):void 0,a=void 0===o?eR*(g.length-1-t):void 0;return s.jsx(es,{"aria-rowindex":tb-e9+t+1,rowIdx:n,gridRowStart:r,row:e,top:o,bottom:a,viewportColumns:t5(n),lastFrozenColumnIndex:e1,selectedCellIdx:i?ts.idx:void 0,isTop:!1,showBorder:0===t,selectCell:tV},t)})]})]})}),function(){if(null==et||"EDIT"===ts.mode||!tQ(ts))return;let{idx:e,rowIdx:t}=ts,r=eQ[e];if(null==r.renderEditCell||!1===r.editable)return;let n=e$(r);return(0,s.jsx)(z,{gridRowStart:tt+t+1,rows:l,column:r,columnWidth:n,maxColIdx:tL,isLastRow:t===to,selectedPosition:ts,isCellEditable:t0,latestDraggedOverRowIdx:tc,onRowsChange:v,onClick:t4,onFill:et,setDragging:eU,setDraggedOverRowIdx:tX})}(),tO.map(({key:e,idx:t,minWidth:r,maxWidth:n})=>(0,s.jsx)("div",{className:"mlln6zg7-0-0-beta-41",style:{gridColumnStart:t+1,minWidth:r,maxWidth:n},"data-measuring-cell-key":e},e)),tf&&(0,s.jsx)("div",{ref:td,tabIndex:t7?0:-1,className:(0,o.Z)("f1lsfrzw7-0-0-beta-41",t7&&["r190mhd37-0-0-beta-41",-1!==e1&&"r139qu9m7-0-0-beta-41"],!tZ(ts.rowIdx)&&"f1cte0lg7-0-0-beta-41"),style:{gridRowStart:ts.rowIdx+tt+1}}),null!==eX&&(0,s.jsx)(en,{scrollToPosition:eX,setScrollToCellPosition:eK,gridElement:eG.current})]})})},68680:function(e,t,r){"use strict";let n,i;r.d(t,{io:function(){return eR}});var o,s,a={};r.r(a),r.d(a,{Decoder:function(){return ey},Encoder:function(){return em},PacketType:function(){return s},protocol:function(){return eg}});let l=Object.create(null);l.open="0",l.close="1",l.ping="2",l.pong="3",l.message="4",l.upgrade="5",l.noop="6";let c=Object.create(null);Object.keys(l).forEach(e=>{c[l[e]]=e});let u={type:"error",data:"parser error"},d="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),h="function"==typeof ArrayBuffer,f=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,p=({type:e,data:t},r,n)=>d&&t instanceof Blob?r?n(t):g(t,n):h&&(t instanceof ArrayBuffer||f(t))?r?n(t):g(new Blob([t]),n):n(l[e]+(t||"")),g=(e,t)=>{let r=new FileReader;return r.onload=function(){t("b"+(r.result.split(",")[1]||""))},r.readAsDataURL(e)};function m(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",y="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let e=0;e<v.length;e++)y[v.charCodeAt(e)]=e;let w=e=>{let t=.75*e.length,r=e.length,n,i=0,o,s,a,l;"="===e[e.length-1]&&(t--,"="===e[e.length-2]&&t--);let c=new ArrayBuffer(t),u=new Uint8Array(c);for(n=0;n<r;n+=4)o=y[e.charCodeAt(n)],s=y[e.charCodeAt(n+1)],a=y[e.charCodeAt(n+2)],l=y[e.charCodeAt(n+3)],u[i++]=o<<2|s>>4,u[i++]=(15&s)<<4|a>>2,u[i++]=(3&a)<<6|63&l;return c},b="function"==typeof ArrayBuffer,x=(e,t)=>{if("string"!=typeof e)return{type:"message",data:_(e,t)};let r=e.charAt(0);return"b"===r?{type:"message",data:C(e.substring(1),t)}:c[r]?e.length>1?{type:c[r],data:e.substring(1)}:{type:c[r]}:u},C=(e,t)=>b?_(w(e),t):{base64:!0,data:e},_=(e,t)=>"blob"===t?e instanceof Blob?e:new Blob([e]):e instanceof ArrayBuffer?e:e.buffer,k=(e,t)=>{let r=e.length,n=Array(r),i=0;e.forEach((e,o)=>{p(e,!1,e=>{n[o]=e,++i===r&&t(n.join("\x1e"))})})},E=(e,t)=>{let r=e.split("\x1e"),n=[];for(let e=0;e<r.length;e++){let i=x(r[e],t);if(n.push(i),"error"===i.type)break}return n};function R(e){return e.reduce((e,t)=>e+t.length,0)}function S(e,t){if(e[0].length===t)return e.shift();let r=new Uint8Array(t),n=0;for(let i=0;i<t;i++)r[i]=e[0][n++],n===e[0].length&&(e.shift(),n=0);return e.length&&n<e[0].length&&(e[0]=e[0].slice(n)),r}function A(e){if(e)return function(e){for(var t in A.prototype)e[t]=A.prototype[t];return e}(e)}A.prototype.on=A.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},A.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this},A.prototype.off=A.prototype.removeListener=A.prototype.removeAllListeners=A.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+e];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<n.length;i++)if((r=n[i])===t||r.fn===t){n.splice(i,1);break}return 0===n.length&&delete this._callbacks["$"+e],this},A.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),r=this._callbacks["$"+e],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(r){r=r.slice(0);for(var n=0,i=r.length;n<i;++n)r[n].apply(this,t)}return this},A.prototype.emitReserved=A.prototype.emit,A.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},A.prototype.hasListeners=function(e){return!!this.listeners(e).length};let T="function"==typeof Promise&&"function"==typeof Promise.resolve?e=>Promise.resolve().then(e):(e,t)=>t(e,0),O="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function I(e,...t){return t.reduce((t,r)=>(e.hasOwnProperty(r)&&(t[r]=e[r]),t),{})}let P=O.setTimeout,N=O.clearTimeout;function L(e,t){t.useNativeTimers?(e.setTimeoutFn=P.bind(O),e.clearTimeoutFn=N.bind(O)):(e.setTimeoutFn=O.setTimeout.bind(O),e.clearTimeoutFn=O.clearTimeout.bind(O))}function D(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class j extends Error{constructor(e,t,r){super(e),this.description=t,this.context=r,this.type="TransportError"}}class B extends A{constructor(e){super(),this.writable=!1,L(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,r){return super.emitReserved("error",new j(e,t,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=x(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let t=function(e){let t="";for(let r in e)e.hasOwnProperty(r)&&(t.length&&(t+="&"),t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return t}(e);return t.length?"?"+t:""}}class M extends B{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",function(){--e||t()})),this.writable||(e++,this.once("drain",function(){--e||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){E(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){let e=()=>{this.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}write(e){this.writable=!1,k(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=D()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let F=!1;try{F="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let q=F;function W(){}class z extends M{constructor(e){if(super(e),"undefined"!=typeof location){let t="https:"===location.protocol,r=location.port;r||(r=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||r!==e.port}}doWrite(e,t){let r=this.request({method:"POST",data:e});r.on("success",t),r.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class H extends A{constructor(e,t,r){super(),this.createRequest=e,L(this,r),this._opts=r,this._method=r.method||"GET",this._uri=t,this._data=void 0!==r.data?r.data:null,this._create()}_create(){var e;let t=I(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;let r=this._xhr=this.createRequest(t);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&r.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{r.setRequestHeader("Accept","*/*")}catch(e){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var e;3===r.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(r.getResponseHeader("set-cookie"))),4===r.readyState&&(200===r.status||1223===r.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof r.status?r.status:0)},0))},r.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=H.requestsCount++,H.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=W,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete H.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function U(){for(let e in H.requests)H.requests.hasOwnProperty(e)&&H.requests[e].abort()}H.requestsCount=0,H.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",U):"function"==typeof addEventListener&&addEventListener("onpagehide"in O?"pagehide":"unload",U,!1));let V=function(){let e=X({xdomain:!1});return e&&null!==e.responseType}();class Y extends z{constructor(e){super(e);let t=e&&e.forceBase64;this.supportsBinary=V&&!t}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new H(X,this.uri(),e)}}function X(e){let t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||q))return new XMLHttpRequest}catch(e){}if(!t)try{return new O[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}let K="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class $ extends B{get name(){return"websocket"}doOpen(){let e=this.uri(),t=this.opts.protocols,r=K?{}:I(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,r)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;p(r,this.supportsBinary,e=>{try{this.doWrite(r,e)}catch(e){}n&&T(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=D()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}let G=O.WebSocket||O.MozWebSocket;class Z extends ${createSocket(e,t,r){return K?new G(e,t,r):t?new G(e,t):new G(e)}doWrite(e,t){this.ws.send(t)}}class J extends B{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let t=function(e,t){i||(i=new TextDecoder);let r=[],n=0,o=-1,s=!1;return new TransformStream({transform(a,l){for(r.push(a);;){if(0===n){if(1>R(r))break;let e=S(r,1);s=(128&e[0])==128,n=(o=127&e[0])<126?3:126===o?1:2}else if(1===n){if(2>R(r))break;let e=S(r,2);o=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),n=3}else if(2===n){if(8>R(r))break;let e=S(r,8),t=new DataView(e.buffer,e.byteOffset,e.length),i=t.getUint32(0);if(i>2097151){l.enqueue(u);break}o=4294967296*i+t.getUint32(4),n=3}else{if(R(r)<o)break;let e=S(r,o);l.enqueue(x(s?e:i.decode(e),t)),n=0}if(0===o||o>e){l.enqueue(u);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=e.readable.pipeThrough(t).getReader(),o=new TransformStream({transform(e,t){var r;r=r=>{let n;let i=r.length;if(i<126)new DataView((n=new Uint8Array(1)).buffer).setUint8(0,i);else if(i<65536){let e=new DataView((n=new Uint8Array(3)).buffer);e.setUint8(0,126),e.setUint16(1,i)}else{let e=new DataView((n=new Uint8Array(9)).buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(i))}e.data&&"string"!=typeof e.data&&(n[0]|=128),t.enqueue(n),t.enqueue(r)},d&&e.data instanceof Blob?e.data.arrayBuffer().then(m).then(r):h&&(e.data instanceof ArrayBuffer||f(e.data))?r(m(e.data)):p(e,!1,e=>{n||(n=new TextEncoder),r(n.encode(e))})}});o.readable.pipeTo(e.writable),this._writer=o.writable.getWriter();let s=()=>{r.read().then(({done:e,value:t})=>{e||(this.onPacket(t),s())}).catch(e=>{})};s();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;this._writer.write(r).then(()=>{n&&T(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}}let Q={websocket:Z,webtransport:J,polling:Y},ee=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,et=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function er(e){if(e.length>8e3)throw"URI too long";let t=e,r=e.indexOf("["),n=e.indexOf("]");-1!=r&&-1!=n&&(e=e.substring(0,r)+e.substring(r,n).replace(/:/g,";")+e.substring(n,e.length));let i=ee.exec(e||""),o={},s=14;for(;s--;)o[et[s]]=i[s]||"";return -1!=r&&-1!=n&&(o.source=t,o.host=o.host.substring(1,o.host.length-1).replace(/;/g,":"),o.authority=o.authority.replace("[","").replace("]","").replace(/;/g,":"),o.ipv6uri=!0),o.pathNames=function(e,t){let r=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&r.splice(0,1),"/"==t.slice(-1)&&r.splice(r.length-1,1),r}(0,o.path),o.queryKey=function(e,t){let r={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,n){t&&(r[t]=n)}),r}(0,o.query),o}let en="function"==typeof addEventListener&&"function"==typeof removeEventListener,ei=[];en&&addEventListener("offline",()=>{ei.forEach(e=>e())},!1);class eo extends A{constructor(e,t){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){let r=er(e);t.hostname=r.host,t.secure="https"===r.protocol||"wss"===r.protocol,t.port=r.port,r.query&&(t.query=r.query)}else t.host&&(t.hostname=er(t.host).host);L(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{let t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},r=e.split("&");for(let e=0,n=r.length;e<n;e++){let n=r[e].split("=");t[decodeURIComponent(n[0])]=decodeURIComponent(n[1])}return t}(this.opts.query)),en&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ei.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){let t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);let r=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](r)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let e=this.opts.rememberUpgrade&&eo.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){this.readyState="open",eo.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let t=Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){let r=this.writeBuffer[t].data;if(r&&(e+="string"==typeof r?function(e){let t=0,r=0;for(let n=0,i=e.length;n<i;n++)(t=e.charCodeAt(n))<128?r+=1:t<2048?r+=2:t<55296||t>=57344?r+=3:(n++,r+=4);return r}(r):Math.ceil(1.33*(r.byteLength||r.size))),t>0&&e>this._maxPayload)return this.writeBuffer.slice(0,t);e+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,T(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,r){return this._sendPacket("message",e,t,r),this}send(e,t,r){return this._sendPacket("message",e,t,r),this}_sendPacket(e,t,r,n){if("function"==typeof t&&(n=t,t=void 0),"function"==typeof r&&(n=r,r=null),"closing"===this.readyState||"closed"===this.readyState)return;(r=r||{}).compress=!1!==r.compress;let i={type:e,data:t,options:r};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),n&&this.once("flush",n),this.flush()}close(){let e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},r=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():e()}):this.upgrading?r():e()),this}_onError(e){if(eo.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),en&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=ei.indexOf(this._offlineEventListener);-1!==e&&ei.splice(e,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}eo.protocol=4;class es extends eo{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),r=!1;eo.priorWebsocketSuccess=!1;let n=()=>{r||(t.send([{type:"ping",data:"probe"}]),t.once("packet",e=>{if(!r){if("pong"===e.type&&"probe"===e.data)this.upgrading=!0,this.emitReserved("upgrading",t),t&&(eo.priorWebsocketSuccess="websocket"===t.name,this.transport.pause(()=>{r||"closed"===this.readyState||(c(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}));else{let e=Error("probe error");e.transport=t.name,this.emitReserved("upgradeError",e)}}}))};function i(){r||(r=!0,c(),t.close(),t=null)}let o=e=>{let r=Error("probe error: "+e);r.transport=t.name,i(),this.emitReserved("upgradeError",r)};function s(){o("transport closed")}function a(){o("socket closed")}function l(e){t&&e.name!==t.name&&i()}let c=()=>{t.removeListener("open",n),t.removeListener("error",o),t.removeListener("close",s),this.off("close",a),this.off("upgrading",l)};t.once("open",n),t.once("error",o),t.once("close",s),this.once("close",a),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{r||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let t=[];for(let r=0;r<e.length;r++)~this.transports.indexOf(e[r])&&t.push(e[r]);return t}}class ea extends es{constructor(e,t={}){let r="object"==typeof e?e:t;(!r.transports||r.transports&&"string"==typeof r.transports[0])&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(e=>Q[e]).filter(e=>!!e)),super(e,r)}}ea.protocol;let el="function"==typeof ArrayBuffer,ec=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,eu=Object.prototype.toString,ed="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===eu.call(Blob),eh="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===eu.call(File);function ef(e){return el&&(e instanceof ArrayBuffer||ec(e))||ed&&e instanceof Blob||eh&&e instanceof File}let ep=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],eg=5;(o=s||(s={}))[o.CONNECT=0]="CONNECT",o[o.DISCONNECT=1]="DISCONNECT",o[o.EVENT=2]="EVENT",o[o.ACK=3]="ACK",o[o.CONNECT_ERROR=4]="CONNECT_ERROR",o[o.BINARY_EVENT=5]="BINARY_EVENT",o[o.BINARY_ACK=6]="BINARY_ACK";class em{constructor(e){this.replacer=e}encode(e){return(e.type===s.EVENT||e.type===s.ACK)&&function e(t,r){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let r=0,n=t.length;r<n;r++)if(e(t[r]))return!0;return!1}if(ef(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&e(t[r]))return!0;return!1}(e)?this.encodeAsBinary({type:e.type===s.EVENT?s.BINARY_EVENT:s.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===s.BINARY_EVENT||e.type===s.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){let t=function(e){let t=[],r=e.data;return e.data=function e(t,r){if(!t)return t;if(ef(t)){let e={_placeholder:!0,num:r.length};return r.push(t),e}if(Array.isArray(t)){let n=Array(t.length);for(let i=0;i<t.length;i++)n[i]=e(t[i],r);return n}if("object"==typeof t&&!(t instanceof Date)){let n={};for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=e(t[i],r));return n}return t}(r,t),e.attachments=t.length,{packet:e,buffers:t}}(e),r=this.encodeAsString(t.packet),n=t.buffers;return n.unshift(r),n}}function ev(e){return"[object Object]"===Object.prototype.toString.call(e)}class ey extends A{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let r=(t=this.decodeString(e)).type===s.BINARY_EVENT;r||t.type===s.BINARY_ACK?(t.type=r?s.EVENT:s.ACK,this.reconstructor=new ew(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(ef(e)||e.base64){if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+e)}decodeString(e){let t=0,r={type:Number(e.charAt(0))};if(void 0===s[r.type])throw Error("unknown packet type "+r.type);if(r.type===s.BINARY_EVENT||r.type===s.BINARY_ACK){let n=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let i=e.substring(n,t);if(i!=Number(i)||"-"!==e.charAt(t))throw Error("Illegal attachments");r.attachments=Number(i)}if("/"===e.charAt(t+1)){let n=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);r.nsp=e.substring(n,t)}else r.nsp="/";let n=e.charAt(t+1);if(""!==n&&Number(n)==n){let n=t+1;for(;++t;){let r=e.charAt(t);if(null==r||Number(r)!=r){--t;break}if(t===e.length)break}r.id=Number(e.substring(n,t+1))}if(e.charAt(++t)){let n=this.tryParse(e.substr(t));if(ey.isPayloadValid(r.type,n))r.data=n;else throw Error("invalid payload")}return r}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case s.CONNECT:return ev(t);case s.DISCONNECT:return void 0===t;case s.CONNECT_ERROR:return"string"==typeof t||ev(t);case s.EVENT:case s.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===ep.indexOf(t[0]));case s.ACK:case s.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class ew{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t,r;let e=(t=this.reconPack,r=this.buffers,t.data=function e(t,r){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<r.length)return r[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let n=0;n<t.length;n++)t[n]=e(t[n],r);else if("object"==typeof t)for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(t[n]=e(t[n],r));return t}(t.data,r),delete t.attachments,t);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function eb(e,t,r){return e.on(t,r),function(){e.off(t,r)}}let ex=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class eC extends A{constructor(e,t,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[eb(e,"open",this.onopen.bind(this)),eb(e,"packet",this.onpacket.bind(this)),eb(e,"error",this.onerror.bind(this)),eb(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var r,n,i;if(ex.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let o={type:s.EVENT,data:t};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){let e=this.ids++,r=t.pop();this._registerAckCallback(e,r),o.id=e}let a=null===(n=null===(r=this.io.engine)||void 0===r?void 0:r.transport)||void 0===n?void 0:n.writable,l=this.connected&&!(null===(i=this.io.engine)||void 0===i?void 0:i._hasPingExpired());return this.flags.volatile&&!a||(l?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(e,t){var r;let n=null!==(r=this.flags.timeout)&&void 0!==r?r:this._opts.ackTimeout;if(void 0===n){this.acks[e]=t;return}let i=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&this.sendBuffer.splice(t,1);t.call(this,Error("operation has timed out"))},n),o=(...e)=>{this.io.clearTimeoutFn(i),t.apply(this,e)};o.withError=!0,this.acks[e]=o}emitWithAck(e,...t){return new Promise((r,n)=>{let i=(e,t)=>e?n(e):r(t);i.withError=!0,t.push(i),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());let r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...n)=>{if(r===this._queue[0])return null!==e?r.tryCount>this._opts.retries&&(this._queue.shift(),t&&t(e)):(this._queue.shift(),t&&t(null,...n)),r.pending=!1,this._drainQueue()}),this._queue.push(r),this._drainQueue()}_drainQueue(e=!1){if(!this.connected||0===this._queue.length)return;let t=this._queue[0];(!t.pending||e)&&(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:s.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){let t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(!(e.nsp!==this.nsp))switch(e.type){case s.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case s.EVENT:case s.BINARY_EVENT:this.onevent(e);break;case s.ACK:case s.BINARY_ACK:this.onack(e);break;case s.DISCONNECT:this.ondisconnect();break;case s.CONNECT_ERROR:this.destroy();let t=Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){let t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let t of this._anyListeners.slice())t.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,r=!1;return function(...n){r||(r=!0,t.packet({type:s.ACK,id:e,data:n}))}}onack(e){let t=this.acks[e.id];"function"==typeof t&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:s.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let r=0;r<t.length;r++)if(e===t[r]){t.splice(r,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let r=0;r<t.length;r++)if(e===t[r]){t.splice(r,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}}function e_(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}e_.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),r=Math.floor(t*this.jitter*e);e=(1&Math.floor(10*t))==0?e-r:e+r}return 0|Math.min(e,this.max)},e_.prototype.reset=function(){this.attempts=0},e_.prototype.setMin=function(e){this.ms=e},e_.prototype.setMax=function(e){this.max=e},e_.prototype.setJitter=function(e){this.jitter=e};class ek extends A{constructor(e,t){var r;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,L(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(r=t.randomizationFactor)&&void 0!==r?r:.5),this.backoff=new e_({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;let n=t.parser||a;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new ea(this.uri,this.opts);let t=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;let n=eb(t,"open",function(){r.onopen(),e&&e()}),i=t=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},o=eb(t,"error",i);if(!1!==this._timeout){let e=this._timeout,r=this.setTimeoutFn(()=>{n(),i(Error("timeout")),t.close()},e);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}return this.subs.push(n),this.subs.push(o),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(eb(e,"ping",this.onping.bind(this)),eb(e,"data",this.ondata.bind(this)),eb(e,"error",this.onerror.bind(this)),eb(e,"close",this.onclose.bind(this)),eb(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){T(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let r=this.nsps[e];return r?this._autoConnect&&!r.active&&r.connect():(r=new eC(this,e,t),this.nsps[e]=r),r}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active)return;this._close()}_packet(e){let t=this.encoder.encode(e);for(let r=0;r<t.length;r++)this.engine.write(t[r],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var r;this.cleanup(),null===(r=this.engine)||void 0===r||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();this._reconnecting=!0;let r=this.setTimeoutFn(()=>{!e.skipReconnect&&(this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):e.onreconnect()}))},t);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let eE={};function eR(e,t){let r;"object"==typeof e&&(t=e,e=void 0);let n=function(e,t="",r){let n=e;r=r||"undefined"!=typeof location&&location,null==e&&(e=r.protocol+"//"+r.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?r.protocol+e:r.host+e),/^(https?|wss?):\/\//.test(e)||(e=void 0!==r?r.protocol+"//"+e:"https://"+e),n=er(e)),!n.port&&(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";let i=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+i+":"+n.port+t,n.href=n.protocol+"://"+i+(r&&r.port===n.port?"":":"+n.port),n}(e,(t=t||{}).path||"/socket.io"),i=n.source,o=n.id,s=n.path,a=eE[o]&&s in eE[o].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?r=new ek(i,t):(eE[o]||(eE[o]=new ek(i,t)),r=eE[o]),n.query&&!t.query&&(t.query=n.queryKey),r.socket(n.path,t)}Object.assign(eR,{Manager:ek,Socket:eC,io:eR,connect:eR})}}]);