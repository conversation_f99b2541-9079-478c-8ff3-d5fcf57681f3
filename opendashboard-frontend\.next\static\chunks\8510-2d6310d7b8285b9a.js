!function(){try{var t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="95270a87-eb62-4795-bb33-bcfd19d6d51b",t._sentryDebugIdIdentifier="sentry-dbid-95270a87-eb62-4795-bb33-bcfd19d6d51b")}catch(t){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8510],{53731:function(t,e){var n=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;e.G=function(t){if(!t||t.length>254||!n.test(t))return!1;var e=t.split("@");return!(e[0].length>64||e[1].split(".").some(function(t){return t.length>63}))}},23500:function(t,e){var n,i,o;e.Bq=e.Ly=e.bW=void 0,(n=e.bW||(e.bW={})).Table="table",n.Board="board",n.Form="form",n.Document="document",n.Dashboard="dashboard",n.SummaryTable="summary-table",n.ListView="list-view",n.Calendar="calendar",(i=e.Ly||(e.Ly={})).Left="left",i.Right="right",(o=e.Bq||(e.Bq={})).Infobox="infobox",o.LineChart="lineChart",o.BarChart="barChart",o.PieChart="pieChart",o.FunnelChart="funnelChart",o.Embed="embed",o.Image="image",o.Text="text"},34361:function(t,e,n){var i=n(16349),o=n(44995),r="tippy-content",s="tippy-arrow",a="tippy-svg-arrow",u={passive:!0,capture:!0},p=function(){return document.body};function l(t,e,n){if(Array.isArray(t)){var i=t[e];return null==i?Array.isArray(n)?n[e]:n:i}return t}function d(t,e){var n=({}).toString.call(t);return 0===n.indexOf("[object")&&n.indexOf(e+"]")>-1}function c(t,e){return"function"==typeof t?t.apply(void 0,e):t}function h(t,e){var n;return 0===e?t:function(i){clearTimeout(n),n=setTimeout(function(){t(i)},e)}}function f(t){return[].concat(t)}function m(t,e){-1===t.indexOf(e)&&t.push(e)}function g(t){return[].slice.call(t)}function v(t){return Object.keys(t).reduce(function(e,n){return void 0!==t[n]&&(e[n]=t[n]),e},{})}function b(){return document.createElement("div")}function y(t){return["Element","Fragment"].some(function(e){return d(t,e)})}function A(t,e){t.forEach(function(t){t&&(t.style.transitionDuration=e+"ms")})}function T(t,e){t.forEach(function(t){t&&t.setAttribute("data-state",e)})}function k(t,e,n){var i=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(e){t[i](e,n)})}function w(t,e){for(var n,i=e;i;){if(t.contains(i))return!0;i=null==i.getRootNode?void 0:null==(n=i.getRootNode())?void 0:n.host}return!1}var L={isTouch:!1},x=0;function S(){!L.isTouch&&(L.isTouch=!0,window.performance&&document.addEventListener("mousemove",M))}function M(){var t=performance.now();t-x<20&&(L.isTouch=!1,document.removeEventListener("mousemove",M)),x=t}function E(){var t=document.activeElement;if(t&&t._tippy&&t._tippy.reference===t){var e=t._tippy;t.blur&&!e.state.isVisible&&t.blur()}}var C=!!("undefined"!=typeof window&&"undefined"!=typeof document)&&!!window.msCrypto,D=Object.assign({appendTo:p,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),O=Object.keys(D);function $(t){var e=(t.plugins||[]).reduce(function(e,n){var i,o=n.name,r=n.defaultValue;return o&&(e[o]=void 0!==t[o]?t[o]:null!=(i=D[o])?i:r),e},{});return Object.assign({},t,e)}function H(t,e){var n,i=Object.assign({},e,{content:c(e.content,[t])},e.ignoreAttributes?{}:((n=e.plugins)?Object.keys($(Object.assign({},D,{plugins:n}))):O).reduce(function(e,n){var i=(t.getAttribute("data-tippy-"+n)||"").trim();if(!i)return e;if("content"===n)e[n]=i;else try{e[n]=JSON.parse(i)}catch(t){e[n]=i}return e},{}));return i.aria=Object.assign({},D.aria,i.aria),i.aria={expanded:"auto"===i.aria.expanded?e.interactive:i.aria.expanded,content:"auto"===i.aria.content?e.interactive?null:"describedby":i.aria.content},i}function B(t,e){t.innerHTML=e}function I(t){var e=b();return!0===t?e.className=s:(e.className=a,y(t)?e.appendChild(t):B(e,t)),e}function N(t,e){y(e.content)?(B(t,""),t.appendChild(e.content)):"function"!=typeof e.content&&(e.allowHTML?B(t,e.content):t.textContent=e.content)}function q(t){var e=t.firstElementChild,n=g(e.children);return{box:e,content:n.find(function(t){return t.classList.contains(r)}),arrow:n.find(function(t){return t.classList.contains(s)||t.classList.contains(a)}),backdrop:n.find(function(t){return t.classList.contains("tippy-backdrop")})}}function R(t){var e=b(),n=b();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var i=b();function o(n,i){var o=q(e),r=o.box,s=o.content,a=o.arrow;i.theme?r.setAttribute("data-theme",i.theme):r.removeAttribute("data-theme"),"string"==typeof i.animation?r.setAttribute("data-animation",i.animation):r.removeAttribute("data-animation"),i.inertia?r.setAttribute("data-inertia",""):r.removeAttribute("data-inertia"),r.style.maxWidth="number"==typeof i.maxWidth?i.maxWidth+"px":i.maxWidth,i.role?r.setAttribute("role",i.role):r.removeAttribute("role"),(n.content!==i.content||n.allowHTML!==i.allowHTML)&&N(s,t.props),i.arrow?a?n.arrow!==i.arrow&&(r.removeChild(a),r.appendChild(I(i.arrow))):r.appendChild(I(i.arrow)):a&&r.removeChild(a)}return i.className=r,i.setAttribute("data-state","hidden"),N(i,t.props),e.appendChild(n),n.appendChild(i),o(t.props,t.props),{popper:e,onUpdate:o}}R.$$tippy=!0;var P=1,j=[],V=[];function _(t,e){void 0===e&&(e={});var n=D.plugins.concat(e.plugins||[]);document.addEventListener("touchstart",S,u),window.addEventListener("blur",E);var o=Object.assign({},e,{plugins:n}),r=(y(t)?[t]:d(t,"NodeList")?g(t):Array.isArray(t)?t:g(document.querySelectorAll(t))).reduce(function(t,e){var n=e&&function(t,e){var n,o,r,s,a,y,x,S,M=H(t,Object.assign({},D,$(v(e)))),E=!1,O=!1,B=!1,I=!1,N=[],R=h(tg,M.interactiveDebounce),_=P++,z=(n=M.plugins).filter(function(t,e){return n.indexOf(t)===e}),W={id:_,reference:t,popper:b(),popperInstance:null,props:M,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:z,clearDelayTimeouts:function(){clearTimeout(o),clearTimeout(r),cancelAnimationFrame(s)},setProps:function(e){if(!W.state.isDestroyed){tn("onBeforeUpdate",[W,e]),tf();var n=W.props,i=H(t,Object.assign({},n,v(e),{ignoreAttributes:!0}));W.props=i,th(),n.interactiveDebounce!==i.interactiveDebounce&&(tr(),R=h(tg,i.interactiveDebounce)),n.triggerTarget&&!i.triggerTarget?f(n.triggerTarget).forEach(function(t){t.removeAttribute("aria-expanded")}):i.triggerTarget&&t.removeAttribute("aria-expanded"),to(),te(),K&&K(n,i),W.popperInstance&&(tA(),tk().forEach(function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)})),tn("onAfterUpdate",[W,e])}},setContent:function(t){W.setProps({content:t})},show:function(){var e,n,i,o=W.state.isVisible,r=W.state.isDestroyed,s=!W.state.isEnabled,a=L.isTouch&&!W.props.touch,u=l(W.props.duration,0,D.duration);if(!(o||r||s||a||(S||t).hasAttribute("disabled"))&&(tn("onShow",[W],!1),!1!==W.props.onShow(W))){if(W.state.isVisible=!0,Y()&&(Z.style.visibility="visible"),te(),tp(),W.state.isMounted||(Z.style.transition="none"),Y()){var d=q(Z);A([d.box,d.content],0)}x=function(){var t;if(W.state.isVisible&&!I){if(I=!0,Z.offsetHeight,Z.style.transition=W.props.moveTransition,Y()&&W.props.animation){var e=q(Z),n=e.box,i=e.content;A([n,i],u),T([n,i],"visible")}ti(),to(),m(V,W),null==(t=W.popperInstance)||t.forceUpdate(),tn("onMount",[W]),W.props.animation&&Y()&&td(u,function(){W.state.isShown=!0,tn("onShown",[W])})}},n=W.props.appendTo,i=S||t,(e=W.props.interactive&&n===p||"parent"===n?i.parentNode:c(n,[i])).contains(Z)||e.appendChild(Z),W.state.isMounted=!0,tA()}},hide:function(){var t,e=!W.state.isVisible,n=W.state.isDestroyed,i=!W.state.isEnabled,o=l(W.props.duration,1,D.duration);if(!e&&!n&&!i&&(tn("onHide",[W],!1),!1!==W.props.onHide(W))){if(W.state.isVisible=!1,W.state.isShown=!1,I=!1,E=!1,Y()&&(Z.style.visibility="hidden"),tr(),tl(),te(!0),Y()){var r=q(Z),s=r.box,a=r.content;W.props.animation&&(A([s,a],o),T([s,a],"hidden"))}(ti(),to(),W.props.animation)?Y()&&(t=W.unmount,td(o,function(){!W.state.isVisible&&Z.parentNode&&Z.parentNode.contains(Z)&&t()})):W.unmount()}},hideWithInteractivity:function(t){X().addEventListener("mousemove",R),m(j,R),R(t)},enable:function(){W.state.isEnabled=!0},disable:function(){W.hide(),W.state.isEnabled=!1},unmount:function(){W.state.isVisible&&W.hide(),W.state.isMounted&&(tT(),tk().forEach(function(t){t._tippy.unmount()}),Z.parentNode&&Z.parentNode.removeChild(Z),V=V.filter(function(t){return t!==W}),W.state.isMounted=!1,tn("onHidden",[W]))},destroy:function(){W.state.isDestroyed||(W.clearDelayTimeouts(),W.unmount(),tf(),delete t._tippy,W.state.isDestroyed=!0,tn("onDestroy",[W]))}};if(!M.render)return W;var Q=M.render(W),Z=Q.popper,K=Q.onUpdate;Z.setAttribute("data-tippy-root",""),Z.id="tippy-"+W.id,W.popper=Z,t._tippy=W,Z._tippy=W;var U=z.map(function(t){return t.fn(W)}),F=t.hasAttribute("aria-expanded");return th(),to(),te(),tn("onCreate",[W]),M.showOnCreate&&tw(),Z.addEventListener("mouseenter",function(){W.props.interactive&&W.state.isVisible&&W.clearDelayTimeouts()}),Z.addEventListener("mouseleave",function(){W.props.interactive&&W.props.trigger.indexOf("mouseenter")>=0&&X().addEventListener("mousemove",R)}),W;function G(){var t=W.props.touch;return Array.isArray(t)?t:[t,0]}function J(){return"hold"===G()[0]}function Y(){var t;return!!(null!=(t=W.props.render)&&t.$$tippy)}function X(){var e,n,i=(S||t).parentNode;return i&&null!=(n=f(i)[0])&&null!=(e=n.ownerDocument)&&e.body?n.ownerDocument:document}function tt(t){return W.state.isMounted&&!W.state.isVisible||L.isTouch||a&&"focus"===a.type?0:l(W.props.delay,t?0:1,D.delay)}function te(t){void 0===t&&(t=!1),Z.style.pointerEvents=W.props.interactive&&!t?"":"none",Z.style.zIndex=""+W.props.zIndex}function tn(t,e,n){if(void 0===n&&(n=!0),U.forEach(function(n){n[t]&&n[t].apply(n,e)}),n){var i;(i=W.props)[t].apply(i,e)}}function ti(){var e=W.props.aria;if(e.content){var n="aria-"+e.content,i=Z.id;f(W.props.triggerTarget||t).forEach(function(t){var e=t.getAttribute(n);if(W.state.isVisible)t.setAttribute(n,e?e+" "+i:i);else{var o=e&&e.replace(i,"").trim();o?t.setAttribute(n,o):t.removeAttribute(n)}})}}function to(){!F&&W.props.aria.expanded&&f(W.props.triggerTarget||t).forEach(function(e){W.props.interactive?e.setAttribute("aria-expanded",W.state.isVisible&&e===(S||t)?"true":"false"):e.removeAttribute("aria-expanded")})}function tr(){X().removeEventListener("mousemove",R),j=j.filter(function(t){return t!==R})}function ts(e){if(!L.isTouch||!B&&"mousedown"!==e.type){var n=e.composedPath&&e.composedPath()[0]||e.target;if(!(W.props.interactive&&w(Z,n))){if(f(W.props.triggerTarget||t).some(function(t){return w(t,n)})){if(L.isTouch||W.state.isVisible&&W.props.trigger.indexOf("click")>=0)return}else tn("onClickOutside",[W,e]);!0!==W.props.hideOnClick||(W.clearDelayTimeouts(),W.hide(),O=!0,setTimeout(function(){O=!1}),W.state.isMounted||tl())}}}function ta(){B=!0}function tu(){B=!1}function tp(){var t=X();t.addEventListener("mousedown",ts,!0),t.addEventListener("touchend",ts,u),t.addEventListener("touchstart",tu,u),t.addEventListener("touchmove",ta,u)}function tl(){var t=X();t.removeEventListener("mousedown",ts,!0),t.removeEventListener("touchend",ts,u),t.removeEventListener("touchstart",tu,u),t.removeEventListener("touchmove",ta,u)}function td(t,e){var n=q(Z).box;function i(t){t.target===n&&(k(n,"remove",i),e())}if(0===t)return e();k(n,"remove",y),k(n,"add",i),y=i}function tc(e,n,i){void 0===i&&(i=!1),f(W.props.triggerTarget||t).forEach(function(t){t.addEventListener(e,n,i),N.push({node:t,eventType:e,handler:n,options:i})})}function th(){J()&&(tc("touchstart",tm,{passive:!0}),tc("touchend",tv,{passive:!0})),W.props.trigger.split(/\s+/).filter(Boolean).forEach(function(t){if("manual"!==t)switch(tc(t,tm),t){case"mouseenter":tc("mouseleave",tv);break;case"focus":tc(C?"focusout":"blur",tb);break;case"focusin":tc("focusout",tb)}})}function tf(){N.forEach(function(t){var e=t.node,n=t.eventType,i=t.handler,o=t.options;e.removeEventListener(n,i,o)}),N=[]}function tm(t){var e,n=!1;if(!(!W.state.isEnabled||ty(t))&&!O){var i=(null==(e=a)?void 0:e.type)==="focus";a=t,S=t.currentTarget,to(),!W.state.isVisible&&d(t,"MouseEvent")&&j.forEach(function(e){return e(t)}),"click"===t.type&&(0>W.props.trigger.indexOf("mouseenter")||E)&&!1!==W.props.hideOnClick&&W.state.isVisible?n=!0:tw(t),"click"===t.type&&(E=!n),n&&!i&&tL(t)}}function tg(e){var n,i,o,r=e.target,s=(S||t).contains(r)||Z.contains(r);("mousemove"!==e.type||!s)&&(n=tk().concat(Z).map(function(t){var e,n=null==(e=t._tippy.popperInstance)?void 0:e.state;return n?{popperRect:t.getBoundingClientRect(),popperState:n,props:M}:null}).filter(Boolean),i=e.clientX,o=e.clientY,n.every(function(t){var e=t.popperRect,n=t.popperState,r=t.props.interactiveBorder,s=n.placement.split("-")[0],a=n.modifiersData.offset;if(!a)return!0;var u="bottom"===s?a.top.y:0,p="top"===s?a.bottom.y:0,l="right"===s?a.left.x:0,d="left"===s?a.right.x:0,c=e.top-o+u>r,h=o-e.bottom-p>r,f=e.left-i+l>r,m=i-e.right-d>r;return c||h||f||m})&&(tr(),tL(e)))}function tv(t){if(!(ty(t)||W.props.trigger.indexOf("click")>=0&&E)){if(W.props.interactive){W.hideWithInteractivity(t);return}tL(t)}}function tb(e){0>W.props.trigger.indexOf("focusin")&&e.target!==(S||t)||W.props.interactive&&e.relatedTarget&&Z.contains(e.relatedTarget)||tL(e)}function ty(t){return!!L.isTouch&&J()!==t.type.indexOf("touch")>=0}function tA(){tT();var e=W.props,n=e.popperOptions,o=e.placement,r=e.offset,s=e.getReferenceClientRect,a=e.moveTransition,u=Y()?q(Z).arrow:null,p=s?{getBoundingClientRect:s,contextElement:s.contextElement||S||t}:t,l=[{name:"offset",options:{offset:r}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!a}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(Y()){var n=q(Z).box;["placement","reference-hidden","escaped"].forEach(function(t){"placement"===t?n.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?n.setAttribute("data-"+t,""):n.removeAttribute("data-"+t)}),e.attributes.popper={}}}}];Y()&&u&&l.push({name:"arrow",options:{element:u,padding:3}}),l.push.apply(l,(null==n?void 0:n.modifiers)||[]),W.popperInstance=(0,i.fi)(p,Z,Object.assign({},n,{placement:o,onFirstUpdate:x,modifiers:l}))}function tT(){W.popperInstance&&(W.popperInstance.destroy(),W.popperInstance=null)}function tk(){return g(Z.querySelectorAll("[data-tippy-root]"))}function tw(t){W.clearDelayTimeouts(),t&&tn("onTrigger",[W,t]),tp();var e=tt(!0),n=G(),i=n[0],r=n[1];L.isTouch&&"hold"===i&&r&&(e=r),e?o=setTimeout(function(){W.show()},e):W.show()}function tL(t){if(W.clearDelayTimeouts(),tn("onUntrigger",[W,t]),!W.state.isVisible){tl();return}if(!(W.props.trigger.indexOf("mouseenter")>=0&&W.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0)||!E){var e=tt(!1);e?r=setTimeout(function(){W.state.isVisible&&W.hide()},e):s=requestAnimationFrame(function(){W.hide()})}}}(e,o);return n&&t.push(n),t},[]);return y(t)?r[0]:r}_.defaultProps=D,_.setDefaultProps=function(t){Object.keys(t).forEach(function(e){D[e]=t[e]})},_.currentInput=L,Object.assign({},o.Z,{effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow)}}),_.setDefaultProps({render:R}),e.ZP=_},21726:function(t,e,n){var i=n(2265);let o=i.forwardRef(function(t,e){let{title:n,titleId:o,...r}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":o},r),n?i.createElement("title",{id:o},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});e.Z=o},69613:function(t,e,n){var i=n(2265);let o=i.forwardRef(function(t,e){let{title:n,titleId:o,...r}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":o},r),n?i.createElement("title",{id:o},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m18.375 12.739-7.693 7.693a4.5 4.5 0 0 1-6.364-6.364l10.94-10.94A3 3 0 1 1 19.5 7.372L8.552 18.32m.009-.01-.01.01m5.699-9.941-7.81 7.81a1.5 1.5 0 0 0 2.112 2.13"}))});e.Z=o},63676:function(t,e,n){n.d(e,{ZP:function(){return r}});var i=n(94725);let o=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,r=i.NB.create({name:"image",addOptions:()=>({inline:!1,allowBase64:!1,HTMLAttributes:{}}),inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes:()=>({src:{default:null},alt:{default:null},title:{default:null}}),parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:t}){return["img",(0,i.P1)(this.options.HTMLAttributes,t)]},addCommands(){return{setImage:t=>({commands:e})=>e.insertContent({type:this.name,attrs:t})}},addInputRules(){return[(0,i.x2)({find:o,type:this.type,getAttributes:t=>{let[,,e,n,i]=t;return{src:n,alt:e,title:i}}})]}})},5172:function(t,e,n){n.d(e,{Z:function(){return s}});var i=n(94725),o=n(17213),r=n(79006);let s=i.hj.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new o.Sy({key:new o.H$("placeholder"),props:{decorations:({doc:t,selection:e})=>{let n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:o}=e,s=[];if(!n)return null;let a=this.editor.isEmpty;return t.descendants((t,e)=>{let n=o>=e&&o<=e+t.nodeSize,u=!t.isLeaf&&(0,i.bR)(t);if((n||!this.options.showOnlyCurrent)&&u){let i=[this.options.emptyNodeClass];a&&i.push(this.options.emptyEditorClass);let o=r.p.node(e,e+t.nodeSize,{class:i.join(" "),"data-placeholder":"function"==typeof this.options.placeholder?this.options.placeholder({editor:this.editor,node:t,pos:e,hasAnchor:n}):this.options.placeholder});s.push(o)}return this.options.includeChildren}),r.EH.create(t,s)}}})]}})},26147:function(t,e,n){n.d(e,{ZT:function(){return S}});var i=n(94725);let o=t=>(0,i.DS)({find:/--$/,replace:null!=t?t:"—"}),r=t=>(0,i.DS)({find:/\.\.\.$/,replace:null!=t?t:"…"}),s=t=>(0,i.DS)({find:/(?:^|[\s{[(<'"\u2018\u201C])(")$/,replace:null!=t?t:"“"}),a=t=>(0,i.DS)({find:/"$/,replace:null!=t?t:"”"}),u=t=>(0,i.DS)({find:/(?:^|[\s{[(<'"\u2018\u201C])(')$/,replace:null!=t?t:"‘"}),p=t=>(0,i.DS)({find:/'$/,replace:null!=t?t:"’"}),l=t=>(0,i.DS)({find:/<-$/,replace:null!=t?t:"←"}),d=t=>(0,i.DS)({find:/->$/,replace:null!=t?t:"→"}),c=t=>(0,i.DS)({find:/\(c\)$/,replace:null!=t?t:"\xa9"}),h=t=>(0,i.DS)({find:/\(tm\)$/,replace:null!=t?t:"™"}),f=t=>(0,i.DS)({find:/\(sm\)$/,replace:null!=t?t:"℠"}),m=t=>(0,i.DS)({find:/\(r\)$/,replace:null!=t?t:"\xae"}),g=t=>(0,i.DS)({find:/(?:^|\s)(1\/2)\s$/,replace:null!=t?t:"\xbd"}),v=t=>(0,i.DS)({find:/\+\/-$/,replace:null!=t?t:"\xb1"}),b=t=>(0,i.DS)({find:/!=$/,replace:null!=t?t:"≠"}),y=t=>(0,i.DS)({find:/<<$/,replace:null!=t?t:"\xab"}),A=t=>(0,i.DS)({find:/>>$/,replace:null!=t?t:"\xbb"}),T=t=>(0,i.DS)({find:/\d+\s?([*x])\s?\d+$/,replace:null!=t?t:"\xd7"}),k=t=>(0,i.DS)({find:/\^2$/,replace:null!=t?t:"\xb2"}),w=t=>(0,i.DS)({find:/\^3$/,replace:null!=t?t:"\xb3"}),L=t=>(0,i.DS)({find:/(?:^|\s)(1\/4)\s$/,replace:null!=t?t:"\xbc"}),x=t=>(0,i.DS)({find:/(?:^|\s)(3\/4)\s$/,replace:null!=t?t:"\xbe"}),S=i.hj.create({name:"typography",addOptions:()=>({closeDoubleQuote:"”",closeSingleQuote:"’",copyright:"\xa9",ellipsis:"…",emDash:"—",laquo:"\xab",leftArrow:"←",multiplication:"\xd7",notEqual:"≠",oneHalf:"\xbd",oneQuarter:"\xbc",openDoubleQuote:"“",openSingleQuote:"‘",plusMinus:"\xb1",raquo:"\xbb",registeredTrademark:"\xae",rightArrow:"→",servicemark:"℠",superscriptThree:"\xb3",superscriptTwo:"\xb2",threeQuarters:"\xbe",trademark:"™"}),addInputRules(){let t=[];return!1!==this.options.emDash&&t.push(o(this.options.emDash)),!1!==this.options.ellipsis&&t.push(r(this.options.ellipsis)),!1!==this.options.openDoubleQuote&&t.push(s(this.options.openDoubleQuote)),!1!==this.options.closeDoubleQuote&&t.push(a(this.options.closeDoubleQuote)),!1!==this.options.openSingleQuote&&t.push(u(this.options.openSingleQuote)),!1!==this.options.closeSingleQuote&&t.push(p(this.options.closeSingleQuote)),!1!==this.options.leftArrow&&t.push(l(this.options.leftArrow)),!1!==this.options.rightArrow&&t.push(d(this.options.rightArrow)),!1!==this.options.copyright&&t.push(c(this.options.copyright)),!1!==this.options.trademark&&t.push(h(this.options.trademark)),!1!==this.options.servicemark&&t.push(f(this.options.servicemark)),!1!==this.options.registeredTrademark&&t.push(m(this.options.registeredTrademark)),!1!==this.options.oneHalf&&t.push(g(this.options.oneHalf)),!1!==this.options.plusMinus&&t.push(v(this.options.plusMinus)),!1!==this.options.notEqual&&t.push(b(this.options.notEqual)),!1!==this.options.laquo&&t.push(y(this.options.laquo)),!1!==this.options.raquo&&t.push(A(this.options.raquo)),!1!==this.options.multiplication&&t.push(T(this.options.multiplication)),!1!==this.options.superscriptTwo&&t.push(k(this.options.superscriptTwo)),!1!==this.options.superscriptThree&&t.push(w(this.options.superscriptThree)),!1!==this.options.oneQuarter&&t.push(L(this.options.oneQuarter)),!1!==this.options.threeQuarters&&t.push(x(this.options.threeQuarters)),t}})},78314:function(t,e,n){n.d(e,{V:function(){return D}});var i=n(94725);let o=/^\s*>\s$/,r=i.NB.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:t}){return["blockquote",(0,i.P1)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBlockquote:()=>({commands:t})=>t.wrapIn(this.name),toggleBlockquote:()=>({commands:t})=>t.toggleWrap(this.name),unsetBlockquote:()=>({commands:t})=>t.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,i.S0)({find:o,type:this.type})]}});var s=n(60808);let a="textStyle",u=/^\s*([-+*])\s$/,p=i.NB.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:t}){return["ul",(0,i.P1)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(a)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=(0,i.S0)({find:u,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,i.S0)({find:u,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(a),editor:this.editor})),[t]}});var l=n(37011),d=n(17213);let c=/^```([a-z]+)?[\s\n]$/,h=/^~~~([a-z]+)?[\s\n]$/,f=i.NB.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:t=>{var e;let{languageClassPrefix:n}=this.options;return[...(null===(e=t.firstElementChild)||void 0===e?void 0:e.classList)||[]].filter(t=>t.startsWith(n)).map(t=>t.replace(n,""))[0]||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:t,HTMLAttributes:e}){return["pre",(0,i.P1)(this.options.HTMLAttributes,e),["code",{class:t.attrs.language?this.options.languageClassPrefix+t.attrs.language:null},0]]},addCommands(){return{setCodeBlock:t=>({commands:e})=>e.setNode(this.name,t),toggleCodeBlock:t=>({commands:e})=>e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:t,$anchor:e}=this.editor.state.selection,n=1===e.pos;return!!t&&e.parent.type.name===this.name&&(!!n||!e.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:t})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:e}=t,{selection:n}=e,{$from:i,empty:o}=n;if(!o||i.parent.type!==this.type)return!1;let r=i.parentOffset===i.parent.nodeSize-2,s=i.parent.textContent.endsWith("\n\n");return!!r&&!!s&&t.chain().command(({tr:t})=>(t.delete(i.pos-2,i.pos),!0)).exitCode().run()},ArrowDown:({editor:t})=>{if(!this.options.exitOnArrowDown)return!1;let{state:e}=t,{selection:n,doc:i}=e,{$from:o,empty:r}=n;if(!r||o.parent.type!==this.type||o.parentOffset!==o.parent.nodeSize-2)return!1;let s=o.after();return void 0!==s&&(i.nodeAt(s)?t.commands.command(({tr:t})=>(t.setSelection(d.Y1.near(i.resolve(s))),!0)):t.commands.exitCode())}}},addInputRules(){return[(0,i.zK)({find:c,type:this.type,getAttributes:t=>({language:t[1]})}),(0,i.zK)({find:h,type:this.type,getAttributes:t=>({language:t[1]})})]},addProseMirrorPlugins(){return[new d.Sy({key:new d.H$("codeBlockVSCodeHandler"),props:{handlePaste:(t,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;let n=e.clipboardData.getData("text/plain"),i=e.clipboardData.getData("vscode-editor-data"),o=i?JSON.parse(i):void 0,r=null==o?void 0:o.mode;if(!n||!r)return!1;let{tr:s,schema:a}=t.state,u=a.text(n.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:r},u)),s.selection.$from.parent.type!==this.type&&s.setSelection(d.Bs.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),t.dispatch(s),!0}}})]}}),m=i.NB.create({name:"doc",topNode:!0,content:"block+"});var g=n(4e3),v=n(12147),b=n(10105);let y=i.NB.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(t=>({tag:`h${t}`,attrs:{level:t}}))},renderHTML({node:t,HTMLAttributes:e}){let n=this.options.levels.includes(t.attrs.level)?t.attrs.level:this.options.levels[0];return[`h${n}`,(0,i.P1)(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.setNode(this.name,t),toggleHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return this.options.levels.reduce((t,e)=>({...t,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(t=>(0,i.zK)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${t}})\\s$`),type:this.type,getAttributes:{level:t}}))}});var A=n(66563);let T=i.NB.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:t}){return["hr",(0,i.P1)(this.options.HTMLAttributes,t)]},addCommands(){return{setHorizontalRule:()=>({chain:t,state:e})=>{let{selection:n}=e,{$from:o,$to:r}=n,s=t();return 0===o.parentOffset?s.insertContentAt({from:Math.max(o.pos-1,0),to:r.pos},{type:this.name}):(0,i.EG)(n)?s.insertContentAt(r.pos,{type:this.name}):s.insertContent({type:this.name}),s.command(({tr:t,dispatch:e})=>{var n;if(e){let{$to:e}=t.selection,i=e.end();if(e.nodeAfter)e.nodeAfter.isTextblock?t.setSelection(d.Bs.create(t.doc,e.pos+1)):e.nodeAfter.isBlock?t.setSelection(d.qv.create(t.doc,e.pos)):t.setSelection(d.Bs.create(t.doc,e.pos));else{let o=null===(n=e.parent.type.contentMatch.defaultType)||void 0===n?void 0:n.create();o&&(t.insert(i,o),t.setSelection(d.Bs.create(t.doc,i+1)))}t.scrollIntoView()}return!0}).run()}}},addInputRules(){return[(0,i.x2)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}});var k=n(68096);let w=i.NB.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",(0,i.P1)(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),L="textStyle",x=/^(\d+)\.\s$/,S=i.NB.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:t=>t.hasAttribute("start")?parseInt(t.getAttribute("start")||"",10):1},type:{default:null,parseHTML:t=>t.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:t}){let{start:e,...n}=t;return 1===e?["ol",(0,i.P1)(this.options.HTMLAttributes,n),0]:["ol",(0,i.P1)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleOrderedList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(L)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let t=(0,i.S0)({find:x,type:this.type,getAttributes:t=>({start:+t[1]}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,i.S0)({find:x,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:t=>({start:+t[1],...this.editor.getAttributes(L)}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1],editor:this.editor})),[t]}}),M=i.NB.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:t}){return["p",(0,i.P1)(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}});var E=n(72673),C=n(95380);let D=i.hj.create({name:"starterKit",addExtensions(){let t=[];return!1!==this.options.bold&&t.push(s.d8.configure(this.options.bold)),!1!==this.options.blockquote&&t.push(r.configure(this.options.blockquote)),!1!==this.options.bulletList&&t.push(p.configure(this.options.bulletList)),!1!==this.options.code&&t.push(l.EK.configure(this.options.code)),!1!==this.options.codeBlock&&t.push(f.configure(this.options.codeBlock)),!1!==this.options.document&&t.push(m.configure(this.options.document)),!1!==this.options.dropcursor&&t.push(g.m.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&t.push(v.f.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&t.push(b.U.configure(this.options.hardBreak)),!1!==this.options.heading&&t.push(y.configure(this.options.heading)),!1!==this.options.history&&t.push(A.A.configure(this.options.history)),!1!==this.options.horizontalRule&&t.push(T.configure(this.options.horizontalRule)),!1!==this.options.italic&&t.push(k.Tx.configure(this.options.italic)),!1!==this.options.listItem&&t.push(w.configure(this.options.listItem)),!1!==this.options.orderedList&&t.push(S.configure(this.options.orderedList)),!1!==this.options.paragraph&&t.push(M.configure(this.options.paragraph)),!1!==this.options.strike&&t.push(E.Re.configure(this.options.strike)),!1!==this.options.text&&t.push(C.x.configure(this.options.text)),t}})},20075:function(t,e,n){n.d(e,{ZP:function(){return u}});var i=n(17213),o=n(79006),r=n(94725);function s(t){var e;let{char:n,allowSpaces:i,allowToIncludeChar:o,allowedPrefixes:s,startOfLine:a,$position:u}=t,p=i&&!o,l=(0,r.Ov)(n),d=RegExp(`\\s${l}$`),c=a?"^":"",h=o?"":l,f=p?RegExp(`${c}${l}.*?(?=\\s${h}|$)`,"gm"):RegExp(`${c}(?:^)?${l}[^\\s${h}]*`,"gm"),m=(null===(e=u.nodeBefore)||void 0===e?void 0:e.isText)&&u.nodeBefore.text;if(!m)return null;let g=u.pos-m.length,v=Array.from(m.matchAll(f)).pop();if(!v||void 0===v.input||void 0===v.index)return null;let b=v.input.slice(Math.max(0,v.index-1),v.index),y=RegExp(`^[${null==s?void 0:s.join("")}\0]?$`).test(b);if(null!==s&&!y)return null;let A=g+v.index,T=A+v[0].length;return(p&&d.test(m.slice(T-1,T+1))&&(v[0]+=" ",T+=1),A<u.pos&&T>=u.pos)?{range:{from:A,to:T},query:v[0].slice(n.length),text:v[0]}:null}let a=new i.H$("suggestion");function u({pluginKey:t=a,editor:e,char:n="@",allowSpaces:r=!1,allowToIncludeChar:u=!1,allowedPrefixes:p=[" "],startOfLine:l=!1,decorationTag:d="span",decorationClass:c="suggestion",command:h=()=>null,items:f=()=>[],render:m=()=>({}),allow:g=()=>!0,findSuggestionMatch:v=s}){let b;let y=null==m?void 0:m(),A=new i.Sy({key:t,view(){return{update:async(t,n)=>{var i,o,r,s,a,u,p;let l=null===(i=this.key)||void 0===i?void 0:i.getState(n),d=null===(o=this.key)||void 0===o?void 0:o.getState(t.state),c=l.active&&d.active&&l.range.from!==d.range.from,m=!l.active&&d.active,g=l.active&&!d.active,v=!m&&!g&&l.query!==d.query,A=m||c&&v,T=v||c,k=g||c&&v;if(!A&&!T&&!k)return;let w=k&&!A?l:d,L=t.dom.querySelector(`[data-decoration-id="${w.decorationId}"]`);b={editor:e,range:w.range,query:w.query,text:w.text,items:[],command:t=>h({editor:e,range:w.range,props:t}),decorationNode:L,clientRect:L?()=>{var n;let{decorationId:i}=null===(n=this.key)||void 0===n?void 0:n.getState(e.state),o=t.dom.querySelector(`[data-decoration-id="${i}"]`);return(null==o?void 0:o.getBoundingClientRect())||null}:null},A&&(null===(r=null==y?void 0:y.onBeforeStart)||void 0===r||r.call(y,b)),T&&(null===(s=null==y?void 0:y.onBeforeUpdate)||void 0===s||s.call(y,b)),(T||A)&&(b.items=await f({editor:e,query:w.query})),k&&(null===(a=null==y?void 0:y.onExit)||void 0===a||a.call(y,b)),T&&(null===(u=null==y?void 0:y.onUpdate)||void 0===u||u.call(y,b)),A&&(null===(p=null==y?void 0:y.onStart)||void 0===p||p.call(y,b))},destroy:()=>{var t;b&&(null===(t=null==y?void 0:y.onExit)||void 0===t||t.call(y,b))}}},state:{init:()=>({active:!1,range:{from:0,to:0},query:null,text:null,composing:!1}),apply(t,i,o,s){let{isEditable:a}=e,{composing:d}=e.view,{selection:c}=t,{empty:h,from:f}=c,m={...i};if(m.composing=d,a&&(h||e.view.composing)){(f<i.range.from||f>i.range.to)&&!d&&!i.composing&&(m.active=!1);let t=v({char:n,allowSpaces:r,allowToIncludeChar:u,allowedPrefixes:p,startOfLine:l,$position:c.$from}),o=`id_${Math.floor(4294967295*Math.random())}`;t&&g({editor:e,state:s,range:t.range,isActive:i.active})?(m.active=!0,m.decorationId=i.decorationId?i.decorationId:o,m.range=t.range,m.query=t.query,m.text=t.text):m.active=!1}else m.active=!1;return m.active||(m.decorationId=null,m.range={from:0,to:0},m.query=null,m.text=null),m}},props:{handleKeyDown(t,e){var n;let{active:i,range:o}=A.getState(t.state);return!!i&&((null===(n=null==y?void 0:y.onKeyDown)||void 0===n?void 0:n.call(y,{view:t,event:e,range:o}))||!1)},decorations(t){let{active:e,range:n,decorationId:i}=A.getState(t);return e?o.EH.create(t.doc,[o.p.inline(n.from,n.to,{nodeName:d,class:c,"data-decoration-id":i})]):null}}});return A}}}]);