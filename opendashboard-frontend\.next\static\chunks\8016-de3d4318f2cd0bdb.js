!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d719678c-bf74-4783-89f4-8eab281ae0e0",e._sentryDebugIdIdentifier="sentry-dbid-d719678c-bf74-4783-89f4-8eab281ae0e0")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8016],{22867:function(e,t,n){var r,o=n(40257);"undefined"!=typeof self&&self,r=e=>(()=>{var t={2:(e,t,n)=>{var r=n(2199),o=n(4664),a=n(5950);e.exports=function(e){return r(e,a,o)}},79:(e,t,n)=>{var r=n(3702),o=n(80),a=n(4739),i=n(8655),u=n(1175);function l(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=o,l.prototype.get=a,l.prototype.has=i,l.prototype.set=u,e.exports=l},80:(e,t,n)=>{var r=n(6025),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=r(t,e);return!(n<0||(n==t.length-1?t.pop():o.call(t,n,1),--this.size,0))}},270:(e,t,n)=>{var r=n(7068),o=n(346);e.exports=function e(t,n,a,i,u){return t===n||(null!=t&&null!=n&&(o(t)||o(n))?r(t,n,a,i,e,u):t!=t&&n!=n)}},289:(e,t,n)=>{var r=n(2651);e.exports=function(e){return r(this,e).get(e)}},294:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},317:e=>{e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach(function(e,r){n[++t]=[r,e]}),n}},346:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},361:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,n){var r=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==r||"symbol"!=r&&t.test(e))&&e>-1&&e%1==0&&e<n}},392:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},659:(e,t,n)=>{var r=n(1873),o=Object.prototype,a=o.hasOwnProperty,i=o.toString,u=r?r.toStringTag:void 0;e.exports=function(e){var t=a.call(e,u),n=e[u];try{e[u]=void 0;var r=!0}catch(e){}var o=i.call(e);return r&&(t?e[u]=n:delete e[u]),o}},689:(e,t,n)=>{var r=n(2),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,a,i,u){var l=1&n,s=r(e),c=s.length;if(c!=r(t).length&&!l)return!1;for(var d=c;d--;){var f=s[d];if(!(l?f in t:o.call(t,f)))return!1}var p=u.get(e),v=u.get(t);if(p&&v)return p==t&&v==e;var h=!0;u.set(e,t),u.set(t,e);for(var m=l;++d<c;){var y=e[f=s[d]],g=t[f];if(a)var b=l?a(g,y,f,t,e,u):a(y,g,f,e,t,u);if(!(void 0===b?y===g||i(y,g,n,a,u):b)){h=!1;break}m||(m="constructor"==f)}if(h&&!m){var E=e.constructor,w=t.constructor;E==w||!("constructor"in e)||!("constructor"in t)||"function"==typeof E&&E instanceof E&&"function"==typeof w&&w instanceof w||(h=!1)}return u.delete(e),u.delete(t),h}},695:(e,t,n)=>{var r=n(8096),o=n(2428),a=n(6449),i=n(3656),u=n(361),l=n(7167),s=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=a(e),c=!n&&o(e),d=!n&&!c&&i(e),f=!n&&!c&&!d&&l(e),p=n||c||d||f,v=p?r(e.length,String):[],h=v.length;for(var m in e)!t&&!s.call(e,m)||p&&("length"==m||d&&("offset"==m||"parent"==m)||f&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||u(m,h))||v.push(m);return v}},938:e=>{e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},945:(e,t,n)=>{var r=n(79),o=n(8223),a=n(3661);e.exports=function(e,t){var n=this.__data__;if(n instanceof r){var i=n.__data__;if(!o||i.length<199)return i.push([e,t]),this.size=++n.size,this;n=this.__data__=new a(i)}return n.set(e,t),this.size=n.size,this}},1042:(e,t,n)=>{var r=n(6110)(Object,"create");e.exports=r},1175:(e,t,n)=>{var r=n(6025);e.exports=function(e,t){var n=this.__data__,o=r(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this}},1380:e=>{e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},1420:(e,t,n)=>{var r=n(79);e.exports=function(){this.__data__=new r,this.size=0}},1459:e=>{e.exports=function(e){return this.__data__.has(e)}},1549:(e,t,n)=>{var r=n(2032),o=n(3862),a=n(6721),i=n(2749),u=n(5749);function l(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=o,l.prototype.get=a,l.prototype.has=i,l.prototype.set=u,e.exports=l},1873:(e,t,n)=>{var r=n(9325).Symbol;e.exports=r},1882:(e,t,n)=>{var r=n(2552),o=n(3805);e.exports=function(e){if(!o(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},1986:(e,t,n)=>{var r=n(1873),o=n(7828),a=n(5288),i=n(5911),u=n(317),l=n(4247),s=r?r.prototype:void 0,c=s?s.valueOf:void 0;e.exports=function(e,t,n,r,s,d,f){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!d(new o(e),new o(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return a(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var p=u;case"[object Set]":var v=1&r;if(p||(p=l),e.size!=t.size&&!v)break;var h=f.get(e);if(h)return h==t;r|=2,f.set(e,t);var m=i(p(e),p(t),r,s,d,f);return f.delete(e),m;case"[object Symbol]":if(c)return c.call(e)==c.call(t)}return!1}},2032:(e,t,n)=>{var r=n(1042);e.exports=function(){this.__data__=r?r(null):{},this.size=0}},2199:(e,t,n)=>{var r=n(4528),o=n(6449);e.exports=function(e,t,n){var a=t(e);return o(e)?a:r(a,n(e))}},2404:(e,t,n)=>{var r=n(270);e.exports=function(e,t){return r(e,t)}},2428:(e,t,n)=>{var r=n(7534),o=n(346),a=Object.prototype,i=a.hasOwnProperty,u=a.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(e){return o(e)&&i.call(e,"callee")&&!u.call(e,"callee")};e.exports=l},2552:(e,t,n)=>{var r=n(1873),o=n(659),a=n(9350),i=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?o(e):a(e)}},2651:(e,t,n)=>{var r=n(4218);e.exports=function(e,t){var n=e.__data__;return r(t)?n["string"==typeof t?"string":"hash"]:n.map}},2749:(e,t,n)=>{var r=n(1042),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return r?void 0!==t[e]:o.call(t,e)}},2804:(e,t,n)=>{var r=n(6110)(n(9325),"Promise");e.exports=r},2949:(e,t,n)=>{var r=n(2651);e.exports=function(e,t){var n=r(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this}},3040:(e,t,n)=>{var r=n(1549),o=n(79),a=n(8223);e.exports=function(){this.size=0,this.__data__={hash:new r,map:new(a||o),string:new r}}},3146:(e,t,n)=>{for(var r=n(3491),o="undefined"==typeof window?n.g:window,a=["moz","webkit"],i="AnimationFrame",u=o["request"+i],l=o["cancel"+i]||o["cancelRequest"+i],s=0;!u&&s<a.length;s++)u=o[a[s]+"Request"+i],l=o[a[s]+"Cancel"+i]||o[a[s]+"CancelRequest"+i];if(!u||!l){var c=0,d=0,f=[],p=1e3/60;u=function(e){if(0===f.length){var t=r(),n=Math.max(0,p-(t-c));c=n+t,setTimeout(function(){var e=f.slice(0);f.length=0;for(var t=0;t<e.length;t++)if(!e[t].cancelled)try{e[t].callback(c)}catch(e){setTimeout(function(){throw e},0)}},Math.round(n))}return f.push({handle:++d,callback:e,cancelled:!1}),d},l=function(e){for(var t=0;t<f.length;t++)f[t].handle===e&&(f[t].cancelled=!0)}}e.exports=function(e){return u.call(o,e)},e.exports.cancel=function(){l.apply(o,arguments)},e.exports.polyfill=function(e){e||(e=o),e.requestAnimationFrame=u,e.cancelAnimationFrame=l}},3345:e=>{e.exports=function(){return[]}},3491:function(e){(function(){var t,n,r,a;"undefined"!=typeof performance&&null!==performance&&performance.now?e.exports=function(){return performance.now()}:null!=o&&o.hrtime?(e.exports=function(){return(t()-a)/1e6},n=o.hrtime,a=(t=function(){var e;return 1e9*(e=n())[0]+e[1]})()-1e9*o.uptime()):Date.now?(e.exports=function(){return Date.now()-r},r=Date.now()):(e.exports=function(){return(new Date).getTime()-r},r=(new Date).getTime())}).call(this)},3605:e=>{e.exports=function(e){return this.__data__.get(e)}},3650:(e,t,n)=>{var r=n(4335)(Object.keys,Object);e.exports=r},3656:(e,t,n)=>{e=n.nmd(e);var r=n(9325),o=n(9935),a=t&&!t.nodeType&&t,i=a&&e&&!e.nodeType&&e,u=i&&i.exports===a?r.Buffer:void 0,l=(u?u.isBuffer:void 0)||o;e.exports=l},3661:(e,t,n)=>{var r=n(3040),o=n(7670),a=n(289),i=n(4509),u=n(2949);function l(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=o,l.prototype.get=a,l.prototype.has=i,l.prototype.set=u,e.exports=l},3702:e=>{e.exports=function(){this.__data__=[],this.size=0}},3805:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},3862:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},4218:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},4247:e=>{e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}},4248:e=>{e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}},4335:e=>{e.exports=function(e,t){return function(n){return e(t(n))}}},4509:(e,t,n)=>{var r=n(2651);e.exports=function(e){return r(this,e).has(e)}},4528:e=>{e.exports=function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}},4664:(e,t,n)=>{var r=n(9770),o=n(3345),a=Object.prototype.propertyIsEnumerable,i=Object.getOwnPropertySymbols,u=i?function(e){return null==e?[]:r(i(e=Object(e)),function(t){return a.call(e,t)})}:o;e.exports=u},4739:(e,t,n)=>{var r=n(6025);e.exports=function(e){var t=this.__data__,n=r(t,e);return n<0?void 0:t[n][1]}},4840:(e,t,n)=>{var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=r},4894:(e,t,n)=>{var r=n(1882),o=n(294);e.exports=function(e){return null!=e&&o(e.length)&&!r(e)}},4901:(e,t,n)=>{var r=n(2552),o=n(294),a=n(346),i={};i["[object Float32Array]"]=i["[object Float64Array]"]=i["[object Int8Array]"]=i["[object Int16Array]"]=i["[object Int32Array]"]=i["[object Uint8Array]"]=i["[object Uint8ClampedArray]"]=i["[object Uint16Array]"]=i["[object Uint32Array]"]=!0,i["[object Arguments]"]=i["[object Array]"]=i["[object ArrayBuffer]"]=i["[object Boolean]"]=i["[object DataView]"]=i["[object Date]"]=i["[object Error]"]=i["[object Function]"]=i["[object Map]"]=i["[object Number]"]=i["[object Object]"]=i["[object RegExp]"]=i["[object Set]"]=i["[object String]"]=i["[object WeakMap]"]=!1,e.exports=function(e){return a(e)&&o(e.length)&&!!i[r(e)]}},5083:(e,t,n)=>{var r=n(1882),o=n(7296),a=n(3805),i=n(7473),u=/^\[object .+?Constructor\]$/,l=Object.prototype,s=Function.prototype.toString,c=l.hasOwnProperty,d=RegExp("^"+s.call(c).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!a(e)||o(e))&&(r(e)?d:u).test(i(e))}},5288:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},5481:(e,t,n)=>{var r=n(9325)["__core-js_shared__"];e.exports=r},5527:e=>{var t=Object.prototype;e.exports=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}},5580:(e,t,n)=>{var r=n(6110)(n(9325),"DataView");e.exports=r},5749:(e,t,n)=>{var r=n(1042);e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=r&&void 0===t?"__lodash_hash_undefined__":t,this}},5861:(e,t,n)=>{var r=n(5580),o=n(8223),a=n(2804),i=n(6545),u=n(8303),l=n(2552),s=n(7473),c="[object Map]",d="[object Promise]",f="[object Set]",p="[object WeakMap]",v="[object DataView]",h=s(r),m=s(o),y=s(a),g=s(i),b=s(u),E=l;(r&&E(new r(new ArrayBuffer(1)))!=v||o&&E(new o)!=c||a&&E(a.resolve())!=d||i&&E(new i)!=f||u&&E(new u)!=p)&&(E=function(e){var t=l(e),n="[object Object]"==t?e.constructor:void 0,r=n?s(n):"";if(r)switch(r){case h:return v;case m:return c;case y:return d;case g:return f;case b:return p}return t}),e.exports=E},5911:(e,t,n)=>{var r=n(8859),o=n(4248),a=n(9219);e.exports=function(e,t,n,i,u,l){var s=1&n,c=e.length,d=t.length;if(c!=d&&!(s&&d>c))return!1;var f=l.get(e),p=l.get(t);if(f&&p)return f==t&&p==e;var v=-1,h=!0,m=2&n?new r:void 0;for(l.set(e,t),l.set(t,e);++v<c;){var y=e[v],g=t[v];if(i)var b=s?i(g,y,v,t,e,l):i(y,g,v,e,t,l);if(void 0!==b){if(b)continue;h=!1;break}if(m){if(!o(t,function(e,t){if(!a(m,t)&&(y===e||u(y,e,n,i,l)))return m.push(t)})){h=!1;break}}else if(y!==g&&!u(y,g,n,i,l)){h=!1;break}}return l.delete(e),l.delete(t),h}},5950:(e,t,n)=>{var r=n(695),o=n(8984),a=n(4894);e.exports=function(e){return a(e)?r(e):o(e)}},6009:(e,t,n)=>{e=n.nmd(e);var r=n(4840),o=t&&!t.nodeType&&t,a=o&&e&&!e.nodeType&&e,i=a&&a.exports===o&&r.process,u=function(){try{return a&&a.require&&a.require("util").types||i&&i.binding&&i.binding("util")}catch(e){}}();e.exports=u},6025:(e,t,n)=>{var r=n(5288);e.exports=function(e,t){for(var n=e.length;n--;)if(r(e[n][0],t))return n;return -1}},6110:(e,t,n)=>{var r=n(5083),o=n(392);e.exports=function(e,t){var n=o(e,t);return r(n)?n:void 0}},6449:e=>{var t=Array.isArray;e.exports=t},6545:(e,t,n)=>{var r=n(6110)(n(9325),"Set");e.exports=r},6721:(e,t,n)=>{var r=n(1042),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(r){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return o.call(t,e)?t[e]:void 0}},7068:(e,t,n)=>{var r=n(7217),o=n(5911),a=n(1986),i=n(689),u=n(5861),l=n(6449),s=n(3656),c=n(7167),d="[object Arguments]",f="[object Array]",p="[object Object]",v=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,h,m,y){var g=l(e),b=l(t),E=g?f:u(e),w=b?f:u(t),T=(E=E==d?p:E)==p,S=(w=w==d?p:w)==p,P=E==w;if(P&&s(e)){if(!s(t))return!1;g=!0,T=!1}if(P&&!T)return y||(y=new r),g||c(e)?o(e,t,n,h,m,y):a(e,t,E,n,h,m,y);if(!(1&n)){var O=T&&v.call(e,"__wrapped__"),_=S&&v.call(t,"__wrapped__");if(O||_){var x=O?e.value():e,A=_?t.value():t;return y||(y=new r),m(x,A,n,h,y)}}return!!P&&(y||(y=new r),i(e,t,n,h,m,y))}},7167:(e,t,n)=>{var r=n(4901),o=n(7301),a=n(6009),i=a&&a.isTypedArray,u=i?o(i):r;e.exports=u},7217:(e,t,n)=>{var r=n(79),o=n(1420),a=n(938),i=n(3605),u=n(9817),l=n(945);function s(e){var t=this.__data__=new r(e);this.size=t.size}s.prototype.clear=o,s.prototype.delete=a,s.prototype.get=i,s.prototype.has=u,s.prototype.set=l,e.exports=s},7296:(e,t,n)=>{var r,o=n(5481),a=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";e.exports=function(e){return!!a&&a in e}},7301:e=>{e.exports=function(e){return function(t){return e(t)}}},7473:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},7534:(e,t,n)=>{var r=n(2552),o=n(346);e.exports=function(e){return o(e)&&"[object Arguments]"==r(e)}},7670:(e,t,n)=>{var r=n(2651);e.exports=function(e){var t=r(this,e).delete(e);return this.size-=t?1:0,t}},7828:(e,t,n)=>{var r=n(9325).Uint8Array;e.exports=r},8096:e=>{e.exports=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}},8223:(e,t,n)=>{var r=n(6110)(n(9325),"Map");e.exports=r},8303:(e,t,n)=>{var r=n(6110)(n(9325),"WeakMap");e.exports=r},8655:(e,t,n)=>{var r=n(6025);e.exports=function(e){return r(this.__data__,e)>-1}},8859:(e,t,n)=>{var r=n(3661),o=n(1380),a=n(1459);function i(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new r;++t<n;)this.add(e[t])}i.prototype.add=i.prototype.push=o,i.prototype.has=a,e.exports=i},8984:(e,t,n)=>{var r=n(5527),o=n(3650),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return o(e);var t=[];for(var n in Object(e))a.call(e,n)&&"constructor"!=n&&t.push(n);return t}},9155:t=>{"use strict";t.exports=e},9219:e=>{e.exports=function(e,t){return e.has(t)}},9325:(e,t,n)=>{var r=n(4840),o="object"==typeof self&&self&&self.Object===Object&&self,a=r||o||Function("return this")();e.exports=a},9350:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},9770:e=>{e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,o=0,a=[];++n<r;){var i=e[n];t(i,n,e)&&(a[o++]=i)}return a}},9817:e=>{e.exports=function(e){return this.__data__.has(e)}},9905:(e,t,n)=>{"use strict";n.d(t,{default:()=>_});var r=n(3146),o=n.n(r);let a=function(e){return new RegExp(/<[a-z][\s\S]*>/i).test(e)},i=function(e,t){return Math.floor(Math.random()*(t-e+1))+e};var u="TYPE_CHARACTER",l="REMOVE_CHARACTER",s="REMOVE_ALL",c="REMOVE_LAST_VISIBLE_NODE",d="PAUSE_FOR",f="CALL_FUNCTION",p="ADD_HTML_TAG_ELEMENT",v="CHANGE_DELETE_SPEED",h="CHANGE_DELAY",m="CHANGE_CURSOR",y="PASTE_STRING",g="HTML_TAG";function b(e){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach(function(t){P(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function T(e){return function(e){if(Array.isArray(e))return S(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return S(e,void 0);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?S(e,void 0):void 0}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function P(e,t,n){return(t=O(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function O(e){var t=function(e){if("object"!=b(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=b(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==b(t)?t:t+""}let _=function(){var e;function t(e,n){var b=this;if(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,t),P(this,"state",{cursorAnimation:null,lastFrameTime:null,pauseUntil:null,eventQueue:[],eventLoop:null,eventLoopPaused:!1,reverseCalledEvents:[],calledEvents:[],visibleNodes:[],initialOptions:null,elements:{container:null,wrapper:document.createElement("span"),cursor:document.createElement("span")}}),P(this,"options",{strings:null,cursor:"|",delay:"natural",pauseFor:1500,deleteSpeed:"natural",loop:!1,autoStart:!1,devMode:!1,skipAddStyles:!1,wrapperClassName:"Typewriter__wrapper",cursorClassName:"Typewriter__cursor",stringSplitter:null,onCreateTextNode:null,onRemoveNode:null}),P(this,"setupWrapperElement",function(){b.state.elements.container&&(b.state.elements.wrapper.className=b.options.wrapperClassName,b.state.elements.cursor.className=b.options.cursorClassName,b.state.elements.cursor.innerHTML=b.options.cursor,b.state.elements.container.innerHTML="",b.state.elements.container.appendChild(b.state.elements.wrapper),b.state.elements.container.appendChild(b.state.elements.cursor))}),P(this,"start",function(){return b.state.eventLoopPaused=!1,b.runEventLoop(),b}),P(this,"pause",function(){return b.state.eventLoopPaused=!0,b}),P(this,"stop",function(){return b.state.eventLoop&&((0,r.cancel)(b.state.eventLoop),b.state.eventLoop=null),b}),P(this,"pauseFor",function(e){return b.addEventToQueue(d,{ms:e}),b}),P(this,"typeOutAllStrings",function(){return"string"==typeof b.options.strings?b.typeString(b.options.strings).pauseFor(b.options.pauseFor):b.options.strings.forEach(function(e){b.typeString(e).pauseFor(b.options.pauseFor).deleteAll(b.options.deleteSpeed)}),b}),P(this,"typeString",function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(a(e))return b.typeOutHTMLString(e,t);if(e){var n=(b.options||{}).stringSplitter,r="function"==typeof n?n(e):e.split("");b.typeCharacters(r,t)}return b}),P(this,"pasteString",function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return a(e)?b.typeOutHTMLString(e,t,!0):(e&&b.addEventToQueue(y,{character:e,node:t}),b)}),P(this,"typeOutHTMLString",function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2?arguments[2]:void 0,o=((t=document.createElement("div")).innerHTML=e,t.childNodes);if(o.length>0)for(var a=0;a<o.length;a++){var i=o[a],u=i.innerHTML;i&&3!==i.nodeType?(i.innerHTML="",b.addEventToQueue(p,{node:i,parentNode:n}),r?b.pasteString(u,i):b.typeString(u,i)):i.textContent&&(r?b.pasteString(i.textContent,n):b.typeString(i.textContent,n))}return b}),P(this,"deleteAll",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"natural";return b.addEventToQueue(s,{speed:e}),b}),P(this,"changeDeleteSpeed",function(e){if(!e)throw Error("Must provide new delete speed");return b.addEventToQueue(v,{speed:e}),b}),P(this,"changeDelay",function(e){if(!e)throw Error("Must provide new delay");return b.addEventToQueue(h,{delay:e}),b}),P(this,"changeCursor",function(e){if(!e)throw Error("Must provide new cursor");return b.addEventToQueue(m,{cursor:e}),b}),P(this,"deleteChars",function(e){if(!e)throw Error("Must provide amount of characters to delete");for(var t=0;t<e;t++)b.addEventToQueue(l);return b}),P(this,"callFunction",function(e,t){if(!e||"function"!=typeof e)throw Error("Callback must be a function");return b.addEventToQueue(f,{cb:e,thisArg:t}),b}),P(this,"typeCharacters",function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!e||!Array.isArray(e))throw Error("Characters must be an array");return e.forEach(function(e){b.addEventToQueue(u,{character:e,node:t})}),b}),P(this,"removeCharacters",function(e){if(!e||!Array.isArray(e))throw Error("Characters must be an array");return e.forEach(function(){b.addEventToQueue(l)}),b}),P(this,"addEventToQueue",function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return b.addEventToStateProperty(e,t,n,"eventQueue")}),P(this,"addReverseCalledEvent",function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return b.options.loop?b.addEventToStateProperty(e,t,n,"reverseCalledEvents"):b}),P(this,"addEventToStateProperty",function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3?arguments[3]:void 0,o={eventName:e,eventArgs:t||{}};return b.state[r]=n?[o].concat(T(b.state[r])):[].concat(T(b.state[r]),[o]),b}),P(this,"runEventLoop",function(){b.state.lastFrameTime||(b.state.lastFrameTime=Date.now());var e=Date.now(),t=e-b.state.lastFrameTime;if(!b.state.eventQueue.length){if(!b.options.loop)return;b.state.eventQueue=T(b.state.calledEvents),b.state.calledEvents=[],b.options=w({},b.state.initialOptions)}if(b.state.eventLoop=o()(b.runEventLoop),!b.state.eventLoopPaused){if(b.state.pauseUntil){if(e<b.state.pauseUntil)return;b.state.pauseUntil=null}var n,r=T(b.state.eventQueue),a=r.shift();if(!(t<=(n=a.eventName===c||a.eventName===l?"natural"===b.options.deleteSpeed?i(40,80):b.options.deleteSpeed:"natural"===b.options.delay?i(120,160):b.options.delay))){var E=a.eventName,S=a.eventArgs;switch(b.logInDevMode({currentEvent:a,state:b.state,delay:n}),E){case y:case u:var P=S.character,O=S.node,_=document.createTextNode(P),x=_;b.options.onCreateTextNode&&"function"==typeof b.options.onCreateTextNode&&(x=b.options.onCreateTextNode(P,_)),x&&(O?O.appendChild(x):b.state.elements.wrapper.appendChild(x)),b.state.visibleNodes=[].concat(T(b.state.visibleNodes),[{type:"TEXT_NODE",character:P,node:x}]);break;case l:r.unshift({eventName:c,eventArgs:{removingCharacterNode:!0}});break;case d:var A=a.eventArgs.ms;b.state.pauseUntil=Date.now()+parseInt(A);break;case f:var C=a.eventArgs,j=C.cb,N=C.thisArg;j.call(N,{elements:b.state.elements});break;case p:var M=a.eventArgs,k=M.node,R=M.parentNode;R?R.appendChild(k):b.state.elements.wrapper.appendChild(k),b.state.visibleNodes=[].concat(T(b.state.visibleNodes),[{type:g,node:k,parentNode:R||b.state.elements.wrapper}]);break;case s:var L=b.state.visibleNodes,D=S.speed,F=[];D&&F.push({eventName:v,eventArgs:{speed:D,temp:!0}});for(var I=0,H=L.length;I<H;I++)F.push({eventName:c,eventArgs:{removingCharacterNode:!1}});D&&F.push({eventName:v,eventArgs:{speed:b.options.deleteSpeed,temp:!0}}),r.unshift.apply(r,F);break;case c:var z=a.eventArgs.removingCharacterNode;if(b.state.visibleNodes.length){var B=b.state.visibleNodes.pop(),Y=B.type,V=B.node,U=B.character;b.options.onRemoveNode&&"function"==typeof b.options.onRemoveNode&&b.options.onRemoveNode({node:V,character:U}),V&&V.parentNode.removeChild(V),Y===g&&z&&r.unshift({eventName:c,eventArgs:{}})}break;case v:b.options.deleteSpeed=a.eventArgs.speed;break;case h:b.options.delay=a.eventArgs.delay;break;case m:b.options.cursor=a.eventArgs.cursor,b.state.elements.cursor.innerHTML=a.eventArgs.cursor}b.options.loop&&(a.eventName===c||a.eventArgs&&a.eventArgs.temp||(b.state.calledEvents=[].concat(T(b.state.calledEvents),[a]))),b.state.eventQueue=r,b.state.lastFrameTime=e}}}),e){if("string"==typeof e){var E=document.querySelector(e);if(!E)throw Error("Could not find container element");this.state.elements.container=E}else this.state.elements.container=e}n&&(this.options=w(w({},this.options),n)),this.state.initialOptions=w({},this.options),this.init()}return e=[{key:"init",value:function(){var e,t;this.setupWrapperElement(),this.addEventToQueue(m,{cursor:this.options.cursor},!0),this.addEventToQueue(s,null,!0),!window||window.___TYPEWRITER_JS_STYLES_ADDED___||this.options.skipAddStyles||(e=".Typewriter__cursor{-webkit-animation:Typewriter-cursor 1s infinite;animation:Typewriter-cursor 1s infinite;margin-left:1px}@-webkit-keyframes Typewriter-cursor{0%{opacity:0}50%{opacity:1}100%{opacity:0}}@keyframes Typewriter-cursor{0%{opacity:0}50%{opacity:1}100%{opacity:0}}",(t=document.createElement("style")).appendChild(document.createTextNode(e)),document.head.appendChild(t),window.___TYPEWRITER_JS_STYLES_ADDED___=!0),!0===this.options.autoStart&&this.options.strings&&this.typeOutAllStrings().start()}},{key:"logInDevMode",value:function(e){this.options.devMode&&console.log(e)}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,O(r.key),r)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}()},9935:e=>{e.exports=function(){return!1}}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var a=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(a.exports,a,a.exports,r),a.loaded=!0,a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e);var a={};return(()=>{"use strict";r.d(a,{default:()=>v});var e=r(9155),t=r.n(e),n=r(9905),o=r(2404),i=r.n(o);function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(e,t){return(l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function s(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function c(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(c=function(){return!!e})()}function d(e){return(d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function f(e){var t=function(e){if("object"!=u(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==u(t)?t:t+""}var p=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}(p,e);var r,o,a=(r=c(),function(){var e,t=d(p);return e=r?Reflect.construct(t,arguments,d(this).constructor):t.apply(this,arguments),function(e,t){if(t&&("object"==u(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return s(e)}(this,e)});function p(){var e,t,n,r;!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,p);for(var o=arguments.length,i=Array(o),u=0;u<o;u++)i[u]=arguments[u];return t=s(e=a.call.apply(a,[this].concat(i))),r={instance:null},(n=f(n="state"))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,e}return o=[{key:"componentDidMount",value:function(){var e=this,t=new n.default(this.typewriter,this.props.options);this.setState({instance:t},function(){var n=e.props.onInit;n&&n(t)})}},{key:"componentDidUpdate",value:function(e){i()(this.props.options,e.options)||this.setState({instance:new n.default(this.typewriter,this.props.options)})}},{key:"componentWillUnmount",value:function(){this.state.instance&&this.state.instance.stop()}},{key:"render",value:function(){var e=this,n=this.props.component;return t().createElement(n,{ref:function(t){return e.typewriter=t},className:"Typewriter","data-testid":"typewriter-wrapper"})}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,f(r.key),r)}}(p.prototype,o),Object.defineProperty(p,"prototype",{writable:!1}),p}(e.Component);p.defaultProps={component:"div"};let v=p})(),a.default})(),e.exports=r(n(2265))},46624:function(e,t,n){"use strict";let r,o,a,i,u,l;n.d(t,{V:function(){return ed}});var s,c,d=n(2265),f=n.t(d,2),p=n(90945),v=n(13323),h=n(96802),m=n(93394),y=n(99417),g=n(40048),b=n(72238),E=n(93689),w=n(18369);function T(e,t){let n=(0,d.useRef)([]),r=(0,v.z)(e);(0,d.useEffect)(()=>{let e=[...n.current];for(let[o,a]of t.entries())if(n.current[o]!==a){let o=r(t,e);return n.current=t,o}},[r,...t])}var S=n(38198);let P=[];!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&P[0]!==e.target&&(P.unshift(e.target),(P=P.filter(e=>null!=e&&e.isConnected)).splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var O=n(37105),_=n(24536),x=n(96822),A=n(27847);function C(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}var j=((r=j||{})[r.None=1]="None",r[r.InitialFocus=2]="InitialFocus",r[r.TabLock=4]="TabLock",r[r.FocusLock=8]="FocusLock",r[r.RestoreFocus=16]="RestoreFocus",r[r.All=30]="All",r);let N=Object.assign((0,A.yV)(function(e,t){let n=(0,d.useRef)(null),r=(0,E.T)(n,t),{initialFocus:o,containers:a,features:i=30,...u}=e;(0,b.H)()||(i=1);let l=(0,g.i)(n);(function(e,t){let{ownerDocument:n}=e,r=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,d.useRef)(P.slice());return T((e,n)=>{let[r]=e,[o]=n;!0===o&&!1===r&&(0,x.Y)(()=>{t.current.splice(0)}),!1===o&&!0===r&&(t.current=P.slice())},[e,P,t]),(0,v.z)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(t);T(()=>{t||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&(0,O.C5)(r())},[t]),(0,y.L)(()=>{t&&(0,O.C5)(r())})})({ownerDocument:l},!!(16&i));let s=function(e,t){let{ownerDocument:n,container:r,initialFocus:o}=e,a=(0,d.useRef)(null),i=(0,m.t)();return T(()=>{if(!t)return;let e=r.current;e&&(0,x.Y)(()=>{if(!i.current)return;let t=null==n?void 0:n.activeElement;if(null!=o&&o.current){if((null==o?void 0:o.current)===t){a.current=t;return}}else if(e.contains(t)){a.current=t;return}null!=o&&o.current?(0,O.C5)(o.current):(0,O.jA)(e,O.TO.First)===O.fE.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),a.current=null==n?void 0:n.activeElement})},[t]),a}({ownerDocument:l,container:n,initialFocus:o},!!(2&i));(function(e,t){let{ownerDocument:n,container:r,containers:o,previousActiveElement:a}=e,i=(0,m.t)();(0,h.O)(null==n?void 0:n.defaultView,"focus",e=>{if(!t||!i.current)return;let n=C(o);r.current instanceof HTMLElement&&n.add(r.current);let u=a.current;if(!u)return;let l=e.target;l&&l instanceof HTMLElement?M(n,l)?(a.current=l,(0,O.C5)(l)):(e.preventDefault(),e.stopPropagation(),(0,O.C5)(u)):(0,O.C5)(a.current)},!0)})({ownerDocument:l,container:n,containers:a,previousActiveElement:s},!!(8&i));let c=(0,w.l)(),f=(0,v.z)(e=>{let t=n.current;t&&(0,_.E)(c.current,{[w.N.Forwards]:()=>{(0,O.jA)(t,O.TO.First,{skipElements:[e.relatedTarget]})},[w.N.Backwards]:()=>{(0,O.jA)(t,O.TO.Last,{skipElements:[e.relatedTarget]})}})}),j=(0,p.G)(),N=(0,d.useRef)(!1);return d.createElement(d.Fragment,null,!!(4&i)&&d.createElement(S._,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:S.A.Focusable}),(0,A.sY)({ourProps:{ref:r,onKeyDown(e){"Tab"==e.key&&(N.current=!0,j.requestAnimationFrame(()=>{N.current=!1}))},onBlur(e){let t=C(a);n.current instanceof HTMLElement&&t.add(n.current);let r=e.relatedTarget;r instanceof HTMLElement&&"true"!==r.dataset.headlessuiFocusGuard&&(M(t,r)||(N.current?(0,O.jA)(n.current,(0,_.E)(c.current,{[w.N.Forwards]:()=>O.TO.Next,[w.N.Backwards]:()=>O.TO.Previous})|O.TO.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&(0,O.C5)(e.target)))}},theirProps:u,defaultTag:"div",name:"FocusTrap"}),!!(4&i)&&d.createElement(S._,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:S.A.Focusable}))}),{features:j});function M(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var k=n(31094);let{useState:R,useEffect:L,useLayoutEffect:D,useDebugValue:F}=f;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;let I=f.useSyncExternalStore;var H=n(64518),z=n(16015),B=n(52108);let Y=(s=()=>new Map,c={PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,z.k)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let t,{doc:n,d:r,meta:o}=e,a={doc:n,d:r,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(o)},i=[(0,B.gn)()?{before(e){let{doc:t,d:n,meta:r}=e;function o(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}n.microTask(()=>{var e;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=(0,z.k)();e.style(t.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>e.dispose()))}let r=null!=(e=window.scrollY)?e:window.pageYOffset,a=null;n.addEventListener(t,"click",e=>{if(e.target instanceof HTMLElement)try{let n=e.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),i=t.querySelector(r);i&&!o(i)&&(a=i)}catch(e){}},!0),n.addEventListener(t,"touchstart",e=>{if(e.target instanceof HTMLElement){if(o(e.target)){let t=e.target;for(;t.parentElement&&o(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")}}),n.addEventListener(t,"touchmove",e=>{if(e.target instanceof HTMLElement){if(o(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),n.add(()=>{var e;r!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,r),a&&a.isConnected&&(a.scrollIntoView({block:"nearest"}),a=null)})})}}:{},{before(e){var n;let{doc:r}=e,o=r.documentElement;t=(null!=(n=r.defaultView)?n:window).innerWidth-o.clientWidth},after(e){let{doc:n,d:r}=e,o=n.documentElement,a=o.clientWidth-o.offsetWidth,i=t-a;r.style(o,"paddingRight","".concat(i,"px"))}},{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];i.forEach(e=>{let{before:t}=e;return null==t?void 0:t(a)}),i.forEach(e=>{let{after:t}=e;return null==t?void 0:t(a)})},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}},o=s(),a=new Set,{getSnapshot:()=>o,subscribe:e=>(a.add(e),()=>a.delete(e)),dispatch(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let i=c[e].call(o,...n);i&&(o=i,a.forEach(e=>e()))}});Y.subscribe(()=>{let e=Y.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&Y.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&Y.dispatch("TEARDOWN",n)}});var V=n(17684);let U=new Map,G=new Map;function W(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];(0,H.e)(()=>{var n;if(!t)return;let r="function"==typeof e?e():e.current;if(!r)return;let o=null!=(n=G.get(r))?n:0;return G.set(r,o+1),0!==o||(U.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),function(){var e;if(!r)return;let t=null!=(e=G.get(r))?e:1;if(1===t?G.delete(r):G.set(r,t-1),1!==t)return;let n=U.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,U.delete(r))}},[e,t])}var Z=n(32539),q=n(28851),Q=n(37863),K=n(27988);let J=(0,d.createContext)(()=>{});J.displayName="StackContext";var $=((i=$||{})[i.Add=0]="Add",i[i.Remove=1]="Remove",i);function X(e){let{children:t,onUpdate:n,type:r,element:o,enabled:a}=e,i=(0,d.useContext)(J),u=(0,v.z)(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];null==n||n(...t),i(...t)});return(0,H.e)(()=>{let e=void 0===a||!0===a;return e&&u(0,r,o),()=>{e&&u(1,r,o)}},[u,r,o,a]),d.createElement(J.Provider,{value:u},t)}var ee=n(47634);let et=(0,d.createContext)(null),en=Object.assign((0,A.yV)(function(e,t){let n=(0,V.M)(),{id:r="headlessui-description-".concat(n),...o}=e,a=function e(){let t=(0,d.useContext)(et);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),i=(0,E.T)(t);(0,H.e)(()=>a.register(r),[r,a.register]);let u={ref:i,...a.props,id:r};return(0,A.sY)({ourProps:u,theirProps:o,slot:a.slot||{},defaultTag:"p",name:a.name||"Description"})}),{});var er=n(37388),eo=((u=eo||{})[u.Open=0]="Open",u[u.Closed=1]="Closed",u),ea=((l=ea||{})[l.SetTitleId=0]="SetTitleId",l);let ei={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},eu=(0,d.createContext)(null);function el(e){let t=(0,d.useContext)(eu);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Dialog /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,el),t}return t}function es(e,t){return(0,_.E)(t.type,ei,e,t)}eu.displayName="DialogContext";let ec=A.AN.RenderStrategy|A.AN.Static,ed=Object.assign((0,A.yV)(function(e,t){let n=(0,V.M)(),{id:r="headlessui-dialog-".concat(n),open:o,onClose:a,initialFocus:i,role:u="dialog",__demoMode:l=!1,...s}=e,[c,f]=(0,d.useState)(0),p=(0,d.useRef)(!1);u="dialog"===u||"alertdialog"===u?u:(p.current||(p.current=!0,console.warn("Invalid role [".concat(u,"] passed to <Dialog />. Only `dialog` and and `alertdialog` are supported. Using `dialog` instead."))),"dialog");let m=(0,Q.oJ)();void 0===o&&null!==m&&(o=(m&Q.ZM.Open)===Q.ZM.Open);let y=(0,d.useRef)(null),w=(0,E.T)(y,t),T=(0,g.i)(y),S=e.hasOwnProperty("open")||null!==m,P=e.hasOwnProperty("onClose");if(!S&&!P)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!S)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!P)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof o)throw Error("You provided an `open` prop to the `Dialog`, but the value is not a boolean. Received: ".concat(o));if("function"!=typeof a)throw Error("You provided an `onClose` prop to the `Dialog`, but the value is not a function. Received: ".concat(a));let O=o?0:1,[x,C]=(0,d.useReducer)(es,{titleId:null,descriptionId:null,panelRef:(0,d.createRef)()}),j=(0,v.z)(()=>a(!1)),M=(0,v.z)(e=>C({type:0,id:e})),R=!!(0,b.H)()&&!l&&0===O,L=c>1,D=null!==(0,d.useContext)(eu),[F,z]=(0,k.k)(),{resolveContainers:B,mainTreeNodeRef:U,MainTreeNode:G}=(0,q.v)({portals:F,defaultContainers:[{get current(){var J;return null!=(J=x.panelRef.current)?J:y.current}}]}),ee=null!==m&&(m&Q.ZM.Closing)===Q.ZM.Closing,en=!D&&!ee&&R;W((0,d.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==T?void 0:T.querySelectorAll("body > *"))?e:[]).find(e=>"headlessui-portal-root"!==e.id&&e.contains(U.current)&&e instanceof HTMLElement))?t:null},[U]),en);let eo=!!L||R;W((0,d.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==T?void 0:T.querySelectorAll("[data-headlessui-portal]"))?e:[]).find(e=>e.contains(U.current)&&e instanceof HTMLElement))?t:null},[U]),eo);let ea=!(!R||L);(0,Z.O)(B,e=>{e.preventDefault(),j()},ea);let ei=!(L||0!==O);(0,h.O)(null==T?void 0:T.defaultView,"keydown",e=>{ei&&(e.defaultPrevented||e.key===er.R.Escape&&(e.preventDefault(),e.stopPropagation(),j()))}),function(e,t){var n;let r,o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];n=e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],a]}},r=I(Y.subscribe,Y.getSnapshot,Y.getSnapshot),(o=e?r.get(e):void 0)&&o.count,(0,H.e)(()=>{if(!(!e||!t))return Y.dispatch("PUSH",e,n),()=>Y.dispatch("POP",e,n)},[t,e])}(T,!(ee||0!==O||D),B),(0,d.useEffect)(()=>{if(0!==O||!y.current)return;let e=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&j()}});return e.observe(y.current),()=>e.disconnect()},[O,y,j]);let[el,ed]=function(){let[e,t]=(0,d.useState)([]);return[e.length>0?e.join(" "):void 0,(0,d.useMemo)(()=>function(e){let n=(0,v.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,d.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props}),[n,e.slot,e.name,e.props]);return d.createElement(et.Provider,{value:r},e.children)},[t])]}(),ef=(0,d.useMemo)(()=>[{dialogState:O,close:j,setTitleId:M},x],[O,x,j,M]),ep=(0,d.useMemo)(()=>({open:0===O}),[O]),ev={ref:w,id:r,role:u,"aria-modal":0===O||void 0,"aria-labelledby":x.titleId,"aria-describedby":el};return d.createElement(X,{type:"Dialog",enabled:0===O,element:y,onUpdate:(0,v.z)((e,t)=>{"Dialog"===t&&(0,_.E)(e,{[$.Add]:()=>f(e=>e+1),[$.Remove]:()=>f(e=>e-1)})})},d.createElement(K.O,{force:!0},d.createElement(k.h,null,d.createElement(eu.Provider,{value:ef},d.createElement(k.h.Group,{target:y},d.createElement(K.O,{force:!1},d.createElement(ed,{slot:ep,name:"Dialog.Description"},d.createElement(N,{initialFocus:i,containers:B,features:R?(0,_.E)(L?"parent":"leaf",{parent:N.features.RestoreFocus,leaf:N.features.All&~N.features.FocusLock}):N.features.None},d.createElement(z,null,(0,A.sY)({ourProps:ev,theirProps:s,slot:ep,defaultTag:"div",features:ec,visible:0===O,name:"Dialog"}))))))))),d.createElement(G,null))}),{Backdrop:(0,A.yV)(function(e,t){let n=(0,V.M)(),{id:r="headlessui-dialog-backdrop-".concat(n),...o}=e,[{dialogState:a},i]=el("Dialog.Backdrop"),u=(0,E.T)(t);(0,d.useEffect)(()=>{if(null===i.panelRef.current)throw Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[i.panelRef]);let l=(0,d.useMemo)(()=>({open:0===a}),[a]);return d.createElement(K.O,{force:!0},d.createElement(k.h,null,(0,A.sY)({ourProps:{ref:u,id:r,"aria-hidden":!0},theirProps:o,slot:l,defaultTag:"div",name:"Dialog.Backdrop"})))}),Panel:(0,A.yV)(function(e,t){let n=(0,V.M)(),{id:r="headlessui-dialog-panel-".concat(n),...o}=e,[{dialogState:a},i]=el("Dialog.Panel"),u=(0,E.T)(t,i.panelRef),l=(0,d.useMemo)(()=>({open:0===a}),[a]),s=(0,v.z)(e=>{e.stopPropagation()});return(0,A.sY)({ourProps:{ref:u,id:r,onClick:s},theirProps:o,slot:l,defaultTag:"div",name:"Dialog.Panel"})}),Overlay:(0,A.yV)(function(e,t){let n=(0,V.M)(),{id:r="headlessui-dialog-overlay-".concat(n),...o}=e,[{dialogState:a,close:i}]=el("Dialog.Overlay"),u=(0,E.T)(t),l=(0,v.z)(e=>{if(e.target===e.currentTarget){if((0,ee.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),i()}}),s=(0,d.useMemo)(()=>({open:0===a}),[a]);return(0,A.sY)({ourProps:{ref:u,id:r,"aria-hidden":!0,onClick:l},theirProps:o,slot:s,defaultTag:"div",name:"Dialog.Overlay"})}),Title:(0,A.yV)(function(e,t){let n=(0,V.M)(),{id:r="headlessui-dialog-title-".concat(n),...o}=e,[{dialogState:a,setTitleId:i}]=el("Dialog.Title"),u=(0,E.T)(t);(0,d.useEffect)(()=>(i(r),()=>i(null)),[r,i]);let l=(0,d.useMemo)(()=>({open:0===a}),[a]);return(0,A.sY)({ourProps:{ref:u,id:r},theirProps:o,slot:l,defaultTag:"h2",name:"Dialog.Title"})}),Description:en})},21886:function(e,t,n){"use strict";let r,o;n.d(t,{p:function(){return A}});var a,i=n(2265),u=n(13323),l=n(17684),s=n(80004),c=n(93689),d=n(37863),f=n(47634),p=n(24536),v=n(40293),h=n(27847);let m=null!=(a=i.startTransition)?a:function(e){e()};var y=n(37388),g=((r=g||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),b=((o=b||{})[o.ToggleDisclosure=0]="ToggleDisclosure",o[o.CloseDisclosure=1]="CloseDisclosure",o[o.SetButtonId=2]="SetButtonId",o[o.SetPanelId=3]="SetPanelId",o[o.LinkPanel=4]="LinkPanel",o[o.UnlinkPanel=5]="UnlinkPanel",o);let E={0:e=>({...e,disclosureState:(0,p.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},w=(0,i.createContext)(null);function T(e){let t=(0,i.useContext)(w);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Disclosure /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,T),t}return t}w.displayName="DisclosureContext";let S=(0,i.createContext)(null);S.displayName="DisclosureAPIContext";let P=(0,i.createContext)(null);function O(e,t){return(0,p.E)(t.type,E,e,t)}P.displayName="DisclosurePanelContext";let _=i.Fragment,x=h.AN.RenderStrategy|h.AN.Static,A=Object.assign((0,h.yV)(function(e,t){let{defaultOpen:n=!1,...r}=e,o=(0,i.useRef)(null),a=(0,c.T)(t,(0,c.h)(e=>{o.current=e},void 0===e.as||e.as===i.Fragment)),l=(0,i.useRef)(null),s=(0,i.useRef)(null),f=(0,i.useReducer)(O,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:s,panelRef:l,buttonId:null,panelId:null}),[{disclosureState:m,buttonId:y},g]=f,b=(0,u.z)(e=>{g({type:1});let t=(0,v.r)(o);if(!t||!y)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(y):t.getElementById(y);null==n||n.focus()}),E=(0,i.useMemo)(()=>({close:b}),[b]),T=(0,i.useMemo)(()=>({open:0===m,close:b}),[m,b]);return i.createElement(w.Provider,{value:f},i.createElement(S.Provider,{value:E},i.createElement(d.up,{value:(0,p.E)(m,{0:d.ZM.Open,1:d.ZM.Closed})},(0,h.sY)({ourProps:{ref:a},theirProps:r,slot:T,defaultTag:_,name:"Disclosure"}))))}),{Button:(0,h.yV)(function(e,t){let n=(0,l.M)(),{id:r="headlessui-disclosure-button-".concat(n),...o}=e,[a,d]=T("Disclosure.Button"),p=(0,i.useContext)(P),v=null!==p&&p===a.panelId,m=(0,i.useRef)(null),g=(0,c.T)(m,t,v?null:a.buttonRef),b=(0,h.Y2)();(0,i.useEffect)(()=>{if(!v)return d({type:2,buttonId:r}),()=>{d({type:2,buttonId:null})}},[r,d,v]);let E=(0,u.z)(e=>{var t;if(v){if(1===a.disclosureState)return;switch(e.key){case y.R.Space:case y.R.Enter:e.preventDefault(),e.stopPropagation(),d({type:0}),null==(t=a.buttonRef.current)||t.focus()}}else switch(e.key){case y.R.Space:case y.R.Enter:e.preventDefault(),e.stopPropagation(),d({type:0})}}),w=(0,u.z)(e=>{e.key===y.R.Space&&e.preventDefault()}),S=(0,u.z)(t=>{var n;(0,f.P)(t.currentTarget)||e.disabled||(v?(d({type:0}),null==(n=a.buttonRef.current)||n.focus()):d({type:0}))}),O=(0,i.useMemo)(()=>({open:0===a.disclosureState}),[a]),_=(0,s.f)(e,m),x=v?{ref:g,type:_,onKeyDown:E,onClick:S}:{ref:g,id:r,type:_,"aria-expanded":0===a.disclosureState,"aria-controls":a.linkedPanel?a.panelId:void 0,onKeyDown:E,onKeyUp:w,onClick:S};return(0,h.sY)({mergeRefs:b,ourProps:x,theirProps:o,slot:O,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,h.yV)(function(e,t){let n=(0,l.M)(),{id:r="headlessui-disclosure-panel-".concat(n),...o}=e,[a,u]=T("Disclosure.Panel"),{close:s}=function e(t){let n=(0,i.useContext)(S);if(null===n){let n=Error("<".concat(t," /> is missing a parent <Disclosure /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(n,e),n}return n}("Disclosure.Panel"),f=(0,h.Y2)(),p=(0,c.T)(t,a.panelRef,e=>{m(()=>u({type:e?4:5}))});(0,i.useEffect)(()=>(u({type:3,panelId:r}),()=>{u({type:3,panelId:null})}),[r,u]);let v=(0,d.oJ)(),y=null!==v?(v&d.ZM.Open)===d.ZM.Open:0===a.disclosureState,g=(0,i.useMemo)(()=>({open:0===a.disclosureState,close:s}),[a,s]);return i.createElement(P.Provider,{value:a.panelId},(0,h.sY)({mergeRefs:f,ourProps:{ref:p,id:r},theirProps:o,slot:g,defaultTag:"div",features:x,visible:y,name:"Disclosure.Panel"}))})})},37388:function(e,t,n){"use strict";let r;n.d(t,{R:function(){return o}});var o=((r=o||{}).Space=" ",r.Enter="Enter",r.Escape="Escape",r.Backspace="Backspace",r.Delete="Delete",r.ArrowLeft="ArrowLeft",r.ArrowUp="ArrowUp",r.ArrowRight="ArrowRight",r.ArrowDown="ArrowDown",r.Home="Home",r.End="End",r.PageUp="PageUp",r.PageDown="PageDown",r.Tab="Tab",r)},62756:function(e,t,n){"use strict";let r,o;n.d(t,{J:function(){return H}});var a=n(2265),i=n(31094),u=n(13323),l=n(96802),s=n(17684),c=n(64518),d=n(31948),f=n(32539),p=n(40048),v=n(80004),h=n(28851),m=n(93689),y=n(18369),g=n(38198),b=n(37863),E=n(47634),w=n(37105),T=n(24536),S=n(40293),P=n(27847),O=n(37388),_=((r=_||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),x=((o=x||{})[o.TogglePopover=0]="TogglePopover",o[o.ClosePopover=1]="ClosePopover",o[o.SetButton=2]="SetButton",o[o.SetButtonId=3]="SetButtonId",o[o.SetPanel=4]="SetPanel",o[o.SetPanelId=5]="SetPanelId",o);let A={0:e=>{let t={...e,popoverState:(0,T.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},C=(0,a.createContext)(null);function j(e){let t=(0,a.useContext)(C);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Popover /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,j),t}return t}C.displayName="PopoverContext";let N=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(N);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Popover /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}N.displayName="PopoverAPIContext";let k=(0,a.createContext)(null);function R(){return(0,a.useContext)(k)}k.displayName="PopoverGroupContext";let L=(0,a.createContext)(null);function D(e,t){return(0,T.E)(t.type,A,e,t)}L.displayName="PopoverPanelContext";let F=P.AN.RenderStrategy|P.AN.Static,I=P.AN.RenderStrategy|P.AN.Static,H=Object.assign((0,P.yV)(function(e,t){var n;let{__demoMode:r=!1,...o}=e,s=(0,a.useRef)(null),c=(0,m.T)(t,(0,m.h)(e=>{s.current=e})),v=(0,a.useRef)([]),y=(0,a.useReducer)(D,{__demoMode:r,popoverState:r?0:1,buttons:v,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,a.createRef)(),afterPanelSentinel:(0,a.createRef)()}),[{popoverState:g,button:E,buttonId:S,panel:O,panelId:_,beforePanelSentinel:x,afterPanelSentinel:A},j]=y,M=(0,p.i)(null!=(n=s.current)?n:E),k=(0,a.useMemo)(()=>{if(!E||!O)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(E))^Number(null==e?void 0:e.contains(O)))return!0;let e=(0,w.GO)(),t=e.indexOf(E),n=(t+e.length-1)%e.length,r=(t+1)%e.length,o=e[n],a=e[r];return!O.contains(o)&&!O.contains(a)},[E,O]),F=(0,d.E)(S),I=(0,d.E)(_),H=(0,a.useMemo)(()=>({buttonId:F,panelId:I,close:()=>j({type:1})}),[F,I,j]),z=R(),B=null==z?void 0:z.registerPopover,Y=(0,u.z)(()=>{var e;return null!=(e=null==z?void 0:z.isFocusWithinPopoverGroup())?e:(null==M?void 0:M.activeElement)&&((null==E?void 0:E.contains(M.activeElement))||(null==O?void 0:O.contains(M.activeElement)))});(0,a.useEffect)(()=>null==B?void 0:B(H),[B,H]);let[V,U]=(0,i.k)(),G=(0,h.v)({mainTreeNodeRef:null==z?void 0:z.mainTreeNodeRef,portals:V,defaultContainers:[E,O]});(0,l.O)(null==M?void 0:M.defaultView,"focus",e=>{var t,n,r,o;e.target!==window&&e.target instanceof HTMLElement&&0===g&&(Y()||E&&O&&(G.contains(e.target)||null!=(n=null==(t=x.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(o=null==(r=A.current)?void 0:r.contains)&&o.call(r,e.target)||j({type:1})))},!0),(0,f.O)(G.resolveContainers,(e,t)=>{j({type:1}),(0,w.sP)(t,w.tJ.Loose)||(e.preventDefault(),null==E||E.focus())},0===g);let W=(0,u.z)(e=>{j({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:E:E;null==t||t.focus()}),Z=(0,a.useMemo)(()=>({close:W,isPortalled:k}),[W,k]),q=(0,a.useMemo)(()=>({open:0===g,close:W}),[g,W]);return a.createElement(L.Provider,{value:null},a.createElement(C.Provider,{value:y},a.createElement(N.Provider,{value:Z},a.createElement(b.up,{value:(0,T.E)(g,{0:b.ZM.Open,1:b.ZM.Closed})},a.createElement(U,null,(0,P.sY)({ourProps:{ref:c},theirProps:o,slot:q,defaultTag:"div",name:"Popover"}),a.createElement(G.MainTreeNode,null))))))}),{Button:(0,P.yV)(function(e,t){let n=(0,s.M)(),{id:r="headlessui-popover-button-".concat(n),...o}=e,[i,l]=j("Popover.Button"),{isPortalled:c}=M("Popover.Button"),d=(0,a.useRef)(null),f="headlessui-focus-sentinel-".concat((0,s.M)()),h=R(),b=null==h?void 0:h.closeOthers,S=null!==(0,a.useContext)(L);(0,a.useEffect)(()=>{if(!S)return l({type:3,buttonId:r}),()=>{l({type:3,buttonId:null})}},[S,r,l]);let[_]=(0,a.useState)(()=>Symbol()),x=(0,m.T)(d,t,S?null:e=>{if(e)i.buttons.current.push(_);else{let e=i.buttons.current.indexOf(_);-1!==e&&i.buttons.current.splice(e,1)}i.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&l({type:2,button:e})}),A=(0,m.T)(d,t),C=(0,p.i)(d),N=(0,u.z)(e=>{var t,n,r;if(S){if(1===i.popoverState)return;switch(e.key){case O.R.Space:case O.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),l({type:1}),null==(r=i.button)||r.focus()}}else switch(e.key){case O.R.Space:case O.R.Enter:e.preventDefault(),e.stopPropagation(),1===i.popoverState&&(null==b||b(i.buttonId)),l({type:0});break;case O.R.Escape:if(0!==i.popoverState)return null==b?void 0:b(i.buttonId);if(!d.current||null!=C&&C.activeElement&&!d.current.contains(C.activeElement))return;e.preventDefault(),e.stopPropagation(),l({type:1})}}),k=(0,u.z)(e=>{S||e.key===O.R.Space&&e.preventDefault()}),D=(0,u.z)(t=>{var n,r;(0,E.P)(t.currentTarget)||e.disabled||(S?(l({type:1}),null==(n=i.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===i.popoverState&&(null==b||b(i.buttonId)),l({type:0}),null==(r=i.button)||r.focus()))}),F=(0,u.z)(e=>{e.preventDefault(),e.stopPropagation()}),I=0===i.popoverState,H=(0,a.useMemo)(()=>({open:I}),[I]),z=(0,v.f)(e,d),B=S?{ref:A,type:z,onKeyDown:N,onClick:D}:{ref:x,id:i.buttonId,type:z,"aria-expanded":0===i.popoverState,"aria-controls":i.panel?i.panelId:void 0,onKeyDown:N,onKeyUp:k,onClick:D,onMouseDown:F},Y=(0,y.l)(),V=(0,u.z)(()=>{let e=i.panel;e&&(0,T.E)(Y.current,{[y.N.Forwards]:()=>(0,w.jA)(e,w.TO.First),[y.N.Backwards]:()=>(0,w.jA)(e,w.TO.Last)})===w.fE.Error&&(0,w.jA)((0,w.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,T.E)(Y.current,{[y.N.Forwards]:w.TO.Next,[y.N.Backwards]:w.TO.Previous}),{relativeTo:i.button})});return a.createElement(a.Fragment,null,(0,P.sY)({ourProps:B,theirProps:o,slot:H,defaultTag:"button",name:"Popover.Button"}),I&&!S&&c&&a.createElement(g._,{id:f,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:V}))}),Overlay:(0,P.yV)(function(e,t){let n=(0,s.M)(),{id:r="headlessui-popover-overlay-".concat(n),...o}=e,[{popoverState:i},l]=j("Popover.Overlay"),c=(0,m.T)(t),d=(0,b.oJ)(),f=null!==d?(d&b.ZM.Open)===b.ZM.Open:0===i,p=(0,u.z)(e=>{if((0,E.P)(e.currentTarget))return e.preventDefault();l({type:1})}),v=(0,a.useMemo)(()=>({open:0===i}),[i]);return(0,P.sY)({ourProps:{ref:c,id:r,"aria-hidden":!0,onClick:p},theirProps:o,slot:v,defaultTag:"div",features:F,visible:f,name:"Popover.Overlay"})}),Panel:(0,P.yV)(function(e,t){let n=(0,s.M)(),{id:r="headlessui-popover-panel-".concat(n),focus:o=!1,...i}=e,[l,d]=j("Popover.Panel"),{close:f,isPortalled:v}=M("Popover.Panel"),h="headlessui-focus-sentinel-before-".concat((0,s.M)()),E="headlessui-focus-sentinel-after-".concat((0,s.M)()),S=(0,a.useRef)(null),_=(0,m.T)(S,t,e=>{d({type:4,panel:e})}),x=(0,p.i)(S),A=(0,P.Y2)();(0,c.e)(()=>(d({type:5,panelId:r}),()=>{d({type:5,panelId:null})}),[r,d]);let C=(0,b.oJ)(),N=null!==C?(C&b.ZM.Open)===b.ZM.Open:0===l.popoverState,k=(0,u.z)(e=>{var t;if(e.key===O.R.Escape){if(0!==l.popoverState||!S.current||null!=x&&x.activeElement&&!S.current.contains(x.activeElement))return;e.preventDefault(),e.stopPropagation(),d({type:1}),null==(t=l.button)||t.focus()}});(0,a.useEffect)(()=>{var t;e.static||1===l.popoverState&&(null==(t=e.unmount)||t)&&d({type:4,panel:null})},[l.popoverState,e.unmount,e.static,d]),(0,a.useEffect)(()=>{if(l.__demoMode||!o||0!==l.popoverState||!S.current)return;let e=null==x?void 0:x.activeElement;S.current.contains(e)||(0,w.jA)(S.current,w.TO.First)},[l.__demoMode,o,S,l.popoverState]);let R=(0,a.useMemo)(()=>({open:0===l.popoverState,close:f}),[l,f]),D={ref:_,id:r,onKeyDown:k,onBlur:o&&0===l.popoverState?e=>{var t,n,r,o,a;let i=e.relatedTarget;i&&S.current&&(null!=(t=S.current)&&t.contains(i)||(d({type:1}),(null!=(r=null==(n=l.beforePanelSentinel.current)?void 0:n.contains)&&r.call(n,i)||null!=(a=null==(o=l.afterPanelSentinel.current)?void 0:o.contains)&&a.call(o,i))&&i.focus({preventScroll:!0})))}:void 0,tabIndex:-1},F=(0,y.l)(),H=(0,u.z)(()=>{let e=S.current;e&&(0,T.E)(F.current,{[y.N.Forwards]:()=>{var t;(0,w.jA)(e,w.TO.First)===w.fE.Error&&(null==(t=l.afterPanelSentinel.current)||t.focus())},[y.N.Backwards]:()=>{var e;null==(e=l.button)||e.focus({preventScroll:!0})}})}),z=(0,u.z)(()=>{let e=S.current;e&&(0,T.E)(F.current,{[y.N.Forwards]:()=>{var e;if(!l.button)return;let t=(0,w.GO)(),n=t.indexOf(l.button),r=t.slice(0,n+1),o=[...t.slice(n+1),...r];for(let t of o.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=l.panel)&&e.contains(t)){let e=o.indexOf(t);-1!==e&&o.splice(e,1)}(0,w.jA)(o,w.TO.First,{sorted:!1})},[y.N.Backwards]:()=>{var t;(0,w.jA)(e,w.TO.Previous)===w.fE.Error&&(null==(t=l.button)||t.focus())}})});return a.createElement(L.Provider,{value:r},N&&v&&a.createElement(g._,{id:h,ref:l.beforePanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:H}),(0,P.sY)({mergeRefs:A,ourProps:D,theirProps:i,slot:R,defaultTag:"div",features:I,visible:N,name:"Popover.Panel"}),N&&v&&a.createElement(g._,{id:E,ref:l.afterPanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:z}))}),Group:(0,P.yV)(function(e,t){let n=(0,a.useRef)(null),r=(0,m.T)(n,t),[o,i]=(0,a.useState)([]),l=(0,h.H)(),s=(0,u.z)(e=>{i(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),c=(0,u.z)(e=>(i(t=>[...t,e]),()=>s(e))),d=(0,u.z)(()=>{var e;let t=(0,S.r)(n);if(!t)return!1;let r=t.activeElement;return!!(null!=(e=n.current)&&e.contains(r))||o.some(e=>{var n,o;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(r))||(null==(o=t.getElementById(e.panelId.current))?void 0:o.contains(r))})}),f=(0,u.z)(e=>{for(let t of o)t.buttonId.current!==e&&t.close()}),p=(0,a.useMemo)(()=>({registerPopover:c,unregisterPopover:s,isFocusWithinPopoverGroup:d,closeOthers:f,mainTreeNodeRef:l.mainTreeNodeRef}),[c,s,d,f,l.mainTreeNodeRef]),v=(0,a.useMemo)(()=>({}),[]);return a.createElement(k.Provider,{value:p},(0,P.sY)({ourProps:{ref:r},theirProps:e,slot:v,defaultTag:"div",name:"Popover.Group"}),a.createElement(l.MainTreeNode,null))})})},31094:function(e,t,n){"use strict";n.d(t,{h:function(){return b},k:function(){return g}});var r=n(2265),o=n(54887),a=n(13323),i=n(64518),u=n(99417),l=n(40048),s=n(72238),c=n(93689),d=n(27988),f=n(61424),p=n(27847);let v=r.Fragment,h=r.Fragment,m=(0,r.createContext)(null),y=(0,r.createContext)(null);function g(){let e=(0,r.useContext)(y),t=(0,r.useRef)([]),n=(0,a.z)(n=>(t.current.push(n),e&&e.register(n),()=>o(n))),o=(0,a.z)(n=>{let r=t.current.indexOf(n);-1!==r&&t.current.splice(r,1),e&&e.unregister(n)}),i=(0,r.useMemo)(()=>({register:n,unregister:o,portals:t}),[n,o,t]);return[t,(0,r.useMemo)(()=>function(e){let{children:t}=e;return r.createElement(y.Provider,{value:i},t)},[i])]}let b=Object.assign((0,p.yV)(function(e,t){let n=(0,r.useRef)(null),a=(0,c.T)((0,c.h)(e=>{n.current=e}),t),h=(0,l.i)(n),g=function(e){let t=(0,d.n)(),n=(0,r.useContext)(m),o=(0,l.i)(e),[a,i]=(0,r.useState)(()=>{if(!t&&null!==n||f.O.isServer)return null;let e=null==o?void 0:o.getElementById("headlessui-portal-root");if(e)return e;if(null===o)return null;let r=o.createElement("div");return r.setAttribute("id","headlessui-portal-root"),o.body.appendChild(r)});return(0,r.useEffect)(()=>{null!==a&&(null!=o&&o.body.contains(a)||null==o||o.body.appendChild(a))},[a,o]),(0,r.useEffect)(()=>{t||null!==n&&i(n.current)},[n,i,t]),a}(n),[b]=(0,r.useState)(()=>{var e;return f.O.isServer?null:null!=(e=null==h?void 0:h.createElement("div"))?e:null}),E=(0,r.useContext)(y),w=(0,s.H)();return(0,i.e)(()=>{!g||!b||g.contains(b)||(b.setAttribute("data-headlessui-portal",""),g.appendChild(b))},[g,b]),(0,i.e)(()=>{if(b&&E)return E.register(b)},[E,b]),(0,u.L)(()=>{var e;g&&b&&(b instanceof Node&&g.contains(b)&&g.removeChild(b),g.childNodes.length<=0&&(null==(e=g.parentElement)||e.removeChild(g)))}),w&&g&&b?(0,o.createPortal)((0,p.sY)({ourProps:{ref:a},theirProps:e,defaultTag:v,name:"Portal"}),b):null}),{Group:(0,p.yV)(function(e,t){let{target:n,...o}=e,a={ref:(0,c.T)(t)};return r.createElement(m.Provider,{value:n},(0,p.sY)({ourProps:a,theirProps:o,defaultTag:h,name:"Popover.Group"}))})})},33044:function(e,t,n){"use strict";let r;n.d(t,{u:function(){return M}});var o=n(2265),a=n(90945),i=n(13323),u=n(93394),l=n(64518),s=n(31948),c=n(72238),d=n(93689),f=n(16015),p=n(24536);function v(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];e&&n.length>0&&e.classList.add(...n)}function h(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];e&&n.length>0&&e.classList.remove(...n)}var m=n(37863),y=n(42120),g=n(27847);function b(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.split(/\s+/).filter(e=>e.length>1)}let E=(0,o.createContext)(null);E.displayName="TransitionContext";var w=((r=w||{}).Visible="visible",r.Hidden="hidden",r);let T=(0,o.createContext)(null);function S(e){return"children"in e?S(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function P(e,t){let n=(0,s.E)(e),r=(0,o.useRef)([]),l=(0,u.t)(),c=(0,a.G)(),d=(0,i.z)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:g.l4.Hidden,o=r.current.findIndex(t=>{let{el:n}=t;return n===e});-1!==o&&((0,p.E)(t,{[g.l4.Unmount](){r.current.splice(o,1)},[g.l4.Hidden](){r.current[o].state="hidden"}}),c.microTask(()=>{var e;!S(r)&&l.current&&(null==(e=n.current)||e.call(n))}))}),f=(0,i.z)(e=>{let t=r.current.find(t=>{let{el:n}=t;return n===e});return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,g.l4.Unmount)}),v=(0,o.useRef)([]),h=(0,o.useRef)(Promise.resolve()),m=(0,o.useRef)({enter:[],leave:[],idle:[]}),y=(0,i.z)((e,n,r)=>{v.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(t=>{let[n]=t;return n!==e})),null==t||t.chains.current[n].push([e,new Promise(e=>{v.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(m.current[n].map(e=>{let[t,n]=e;return n})).then(()=>e())})]),"enter"===n?h.current=h.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),b=(0,i.z)((e,t,n)=>{Promise.all(m.current[t].splice(0).map(e=>{let[t,n]=e;return n})).then(()=>{var e;null==(e=v.current.shift())||e()}).then(()=>n(t))});return(0,o.useMemo)(()=>({children:r,register:f,unregister:d,onStart:y,onStop:b,wait:h,chains:m}),[f,d,r,y,b,m,h])}function O(){}T.displayName="NestingContext";let _=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function x(e){var t;let n={};for(let r of _)n[r]=null!=(t=e[r])?t:O;return n}let A=g.AN.RenderStrategy,C=(0,g.yV)(function(e,t){let{show:n,appear:r=!1,unmount:a=!0,...u}=e,s=(0,o.useRef)(null),f=(0,d.T)(s,t);(0,c.H)();let p=(0,m.oJ)();if(void 0===n&&null!==p&&(n=(p&m.ZM.Open)===m.ZM.Open),![!0,!1].includes(n))throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[v,h]=(0,o.useState)(n?"visible":"hidden"),y=P(()=>{h("hidden")}),[b,w]=(0,o.useState)(!0),O=(0,o.useRef)([n]);(0,l.e)(()=>{!1!==b&&O.current[O.current.length-1]!==n&&(O.current.push(n),w(!1))},[O,n]);let _=(0,o.useMemo)(()=>({show:n,appear:r,initial:b}),[n,r,b]);(0,o.useEffect)(()=>{if(n)h("visible");else if(S(y)){let e=s.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&h("hidden")}else h("hidden")},[n,y]);let x={unmount:a},C=(0,i.z)(()=>{var t;b&&w(!1),null==(t=e.beforeEnter)||t.call(e)}),N=(0,i.z)(()=>{var t;b&&w(!1),null==(t=e.beforeLeave)||t.call(e)});return o.createElement(T.Provider,{value:y},o.createElement(E.Provider,{value:_},(0,g.sY)({ourProps:{...x,as:o.Fragment,children:o.createElement(j,{ref:f,...x,...u,beforeEnter:C,beforeLeave:N})},theirProps:{},defaultTag:o.Fragment,features:A,visible:"visible"===v,name:"Transition"})))}),j=(0,g.yV)(function(e,t){var n,r,w;let O;let{beforeEnter:_,afterEnter:C,beforeLeave:j,afterLeave:N,enter:M,enterFrom:k,enterTo:R,entered:L,leave:D,leaveFrom:F,leaveTo:I,...H}=e,z=(0,o.useRef)(null),B=(0,d.T)(z,t),Y=null==(n=H.unmount)||n?g.l4.Unmount:g.l4.Hidden,{show:V,appear:U,initial:G}=function(){let e=(0,o.useContext)(E);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[W,Z]=(0,o.useState)(V?"visible":"hidden"),q=function(){let e=(0,o.useContext)(T);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:Q,unregister:K}=q;(0,o.useEffect)(()=>Q(z),[Q,z]),(0,o.useEffect)(()=>{if(Y===g.l4.Hidden&&z.current){if(V&&"visible"!==W){Z("visible");return}return(0,p.E)(W,{hidden:()=>K(z),visible:()=>Q(z)})}},[W,z,Q,K,V,Y]);let J=(0,s.E)({base:b(H.className),enter:b(M),enterFrom:b(k),enterTo:b(R),entered:b(L),leave:b(D),leaveFrom:b(F),leaveTo:b(I)}),$=(w={beforeEnter:_,afterEnter:C,beforeLeave:j,afterLeave:N},O=(0,o.useRef)(x(w)),(0,o.useEffect)(()=>{O.current=x(w)},[w]),O),X=(0,c.H)();(0,o.useEffect)(()=>{if(X&&"visible"===W&&null===z.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[z,W,X]);let ee=U&&V&&G,et=X&&(!G||U)?V?"enter":"leave":"idle",en=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,o.useState)(e),r=(0,u.t)(),a=(0,o.useCallback)(e=>{r.current&&n(t=>t|e)},[t,r]),i=(0,o.useCallback)(e=>!!(t&e),[t]);return{flags:t,addFlag:a,hasFlag:i,removeFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t&~e)},[n,r]),toggleFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t^e)},[n])}}(0),er=(0,i.z)(e=>(0,p.E)(e,{enter:()=>{en.addFlag(m.ZM.Opening),$.current.beforeEnter()},leave:()=>{en.addFlag(m.ZM.Closing),$.current.beforeLeave()},idle:()=>{}})),eo=(0,i.z)(e=>(0,p.E)(e,{enter:()=>{en.removeFlag(m.ZM.Opening),$.current.afterEnter()},leave:()=>{en.removeFlag(m.ZM.Closing),$.current.afterLeave()},idle:()=>{}})),ea=P(()=>{Z("hidden"),K(z)},q),ei=(0,o.useRef)(!1);!function(e){let{immediate:t,container:n,direction:r,classes:o,onStart:i,onStop:c}=e,d=(0,u.t)(),m=(0,a.G)(),y=(0,s.E)(r);(0,l.e)(()=>{t&&(y.current="enter")},[t]),(0,l.e)(()=>{let e=(0,f.k)();m.add(e.dispose);let t=n.current;if(t&&"idle"!==y.current&&d.current){var r,a,u;let n,l,s,d,m,g,b;return e.dispose(),i.current(y.current),e.add((r=o.current,a="enter"===y.current,u=()=>{e.dispose(),c.current(y.current)},l=a?"enter":"leave",s=(0,f.k)(),d=void 0!==u?(n={called:!1},function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!n.called)return n.called=!0,u(...t)}):()=>{},"enter"===l&&(t.removeAttribute("hidden"),t.style.display=""),m=(0,p.E)(l,{enter:()=>r.enter,leave:()=>r.leave}),g=(0,p.E)(l,{enter:()=>r.enterTo,leave:()=>r.leaveTo}),b=(0,p.E)(l,{enter:()=>r.enterFrom,leave:()=>r.leaveFrom}),h(t,...r.base,...r.enter,...r.enterTo,...r.enterFrom,...r.leave,...r.leaveFrom,...r.leaveTo,...r.entered),v(t,...r.base,...m,...b),s.nextFrame(()=>{h(t,...r.base,...m,...b),v(t,...r.base,...m,...g),function(e,t){let n=(0,f.k)();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[a,i]=[r,o].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t}),u=a+i;if(0!==u){n.group(n=>{n.setTimeout(()=>{t(),n.dispose()},u),n.addEventListener(e,"transitionrun",e=>{e.target===e.currentTarget&&n.dispose()})});let r=n.addEventListener(e,"transitionend",e=>{e.target===e.currentTarget&&(t(),r())})}else t();n.add(()=>t()),n.dispose}(t,()=>(h(t,...r.base,...m),v(t,...r.base,...r.entered),d()))}),s.dispose)),e.dispose}},[r])}({immediate:ee,container:z,classes:J,direction:et,onStart:(0,s.E)(e=>{ei.current=!0,ea.onStart(z,e,er)}),onStop:(0,s.E)(e=>{ei.current=!1,ea.onStop(z,e,eo),"leave"!==e||S(ea)||(Z("hidden"),K(z))})});let eu=H;return ee?eu={...eu,className:(0,y.A)(H.className,...J.current.enter,...J.current.enterFrom)}:ei.current&&(eu.className=(0,y.A)(H.className,null==(r=z.current)?void 0:r.className),""===eu.className&&delete eu.className),o.createElement(T.Provider,{value:ea},o.createElement(m.up,{value:(0,p.E)(W,{visible:m.ZM.Open,hidden:m.ZM.Closed})|en.flags},(0,g.sY)({ourProps:{ref:B},theirProps:eu,defaultTag:"div",features:A,visible:"visible"===W,name:"Transition.Child"})))}),N=(0,g.yV)(function(e,t){let n=null!==(0,o.useContext)(E),r=null!==(0,m.oJ)();return o.createElement(o.Fragment,null,!n&&r?o.createElement(C,{ref:t,...e}):o.createElement(j,{ref:t,...e}))}),M=Object.assign(C,{Child:N,Root:C})},90945:function(e,t,n){"use strict";n.d(t,{G:function(){return a}});var r=n(2265),o=n(16015);function a(){let[e]=(0,r.useState)(o.k);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},96802:function(e,t,n){"use strict";n.d(t,{O:function(){return a}});var r=n(2265),o=n(31948);function a(e,t,n,a){let i=(0,o.E)(n);(0,r.useEffect)(()=>{function n(e){i.current(e)}return(e=null!=e?e:window).addEventListener(t,n,a),()=>e.removeEventListener(t,n,a)},[e,t,a])}},13323:function(e,t,n){"use strict";n.d(t,{z:function(){return a}});var r=n(2265),o=n(31948);let a=function(e){let t=(0,o.E)(e);return r.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.current(...n)},[t])}},17684:function(e,t,n){"use strict";n.d(t,{M:function(){return l}});var r,o=n(2265),a=n(61424),i=n(64518),u=n(72238);let l=null!=(r=o.useId)?r:function(){let e=(0,u.H)(),[t,n]=o.useState(e?()=>a.O.nextId():null);return(0,i.e)(()=>{null===t&&n(a.O.nextId())},[t]),null!=t?""+t:void 0}},93394:function(e,t,n){"use strict";n.d(t,{t:function(){return a}});var r=n(2265),o=n(64518);function a(){let e=(0,r.useRef)(!1);return(0,o.e)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},64518:function(e,t,n){"use strict";n.d(t,{e:function(){return a}});var r=n(2265),o=n(61424);let a=(e,t)=>{o.O.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},31948:function(e,t,n){"use strict";n.d(t,{E:function(){return a}});var r=n(2265),o=n(64518);function a(e){let t=(0,r.useRef)(e);return(0,o.e)(()=>{t.current=e},[e]),t}},99417:function(e,t,n){"use strict";n.d(t,{L:function(){return i}});var r=n(2265),o=n(96822),a=n(13323);function i(e){let t=(0,a.z)(e),n=(0,r.useRef)(!1);(0,r.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,o.Y)(()=>{n.current&&t()})}),[t])}},32539:function(e,t,n){"use strict";n.d(t,{O:function(){return s}});var r=n(2265),o=n(37105),a=n(52108),i=n(31948);function u(e,t,n){let o=(0,i.E)(t);(0,r.useEffect)(()=>{function t(e){o.current(e)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)},[e,n])}var l=n(3141);function s(e,t){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],i=(0,r.useRef)(!1);function s(n,r){if(!i.current||n.defaultPrevented)return;let a=r(n);if(null!==a&&a.getRootNode().contains(a)&&a.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e)){if(null===t)continue;let e=t instanceof HTMLElement?t:t.current;if(null!=e&&e.contains(a)||n.composed&&n.composedPath().includes(e))return}return(0,o.sP)(a,o.tJ.Loose)||-1===a.tabIndex||n.preventDefault(),t(n,a)}}(0,r.useEffect)(()=>{requestAnimationFrame(()=>{i.current=n})},[n]);let c=(0,r.useRef)(null);u("pointerdown",e=>{var t,n;i.current&&(c.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),u("mousedown",e=>{var t,n;i.current&&(c.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),u("click",e=>{(0,a.tq)()||c.current&&(s(e,()=>c.current),c.current=null)},!0),u("touchend",e=>s(e,()=>e.target instanceof HTMLElement?e.target:null),!0),(0,l.s)("blur",e=>s(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}},40048:function(e,t,n){"use strict";n.d(t,{i:function(){return a}});var r=n(2265),o=n(40293);function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>(0,o.r)(...t),[...t])}},80004:function(e,t,n){"use strict";n.d(t,{f:function(){return i}});var r=n(2265),o=n(64518);function a(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";if("string"==typeof n&&"button"===n.toLowerCase())return"button"}function i(e,t){let[n,i]=(0,r.useState)(()=>a(e));return(0,o.e)(()=>{i(a(e))},[e.type,e.as]),(0,o.e)(()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&i("button")},[n,t]),n}},28851:function(e,t,n){"use strict";n.d(t,{H:function(){return l},v:function(){return u}});var r=n(2265),o=n(38198),a=n(13323),i=n(40048);function u(){var e;let{defaultContainers:t=[],portals:n,mainTreeNodeRef:u}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},l=(0,r.useRef)(null!=(e=null==u?void 0:u.current)?e:null),s=(0,i.i)(l),c=(0,a.z)(()=>{var e,r,o;let a=[];for(let e of t)null!==e&&(e instanceof HTMLElement?a.push(e):"current"in e&&e.current instanceof HTMLElement&&a.push(e.current));if(null!=n&&n.current)for(let e of n.current)a.push(e);for(let t of null!=(e=null==s?void 0:s.querySelectorAll("html > *, body > *"))?e:[])t!==document.body&&t!==document.head&&t instanceof HTMLElement&&"headlessui-portal-root"!==t.id&&(t.contains(l.current)||t.contains(null==(o=null==(r=l.current)?void 0:r.getRootNode())?void 0:o.host)||a.some(e=>t.contains(e))||a.push(t));return a});return{resolveContainers:c,contains:(0,a.z)(e=>c().some(t=>t.contains(e))),mainTreeNodeRef:l,MainTreeNode:(0,r.useMemo)(()=>function(){return null!=u?null:r.createElement(o._,{features:o.A.Hidden,ref:l})},[l,u])}}function l(){let e=(0,r.useRef)(null);return{mainTreeNodeRef:e,MainTreeNode:(0,r.useMemo)(()=>function(){return r.createElement(o._,{features:o.A.Hidden,ref:e})},[e])}}},72238:function(e,t,n){"use strict";n.d(t,{H:function(){return i}});var r,o=n(2265),a=n(61424);function i(){let e;let t=(e="undefined"==typeof document,(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[i,u]=o.useState(a.O.isHandoffComplete);return i&&!1===a.O.isHandoffComplete&&u(!1),o.useEffect(()=>{!0!==i&&u(!0)},[i]),o.useEffect(()=>a.O.handoff(),[]),!t&&i}},93689:function(e,t,n){"use strict";n.d(t,{T:function(){return u},h:function(){return i}});var r=n(2265),o=n(13323);let a=Symbol();function i(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return Object.assign(e,{[a]:t})}function u(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let i=(0,r.useRef)(t);(0,r.useEffect)(()=>{i.current=t},[t]);let u=(0,o.z)(e=>{for(let t of i.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[a]))?void 0:u}},18369:function(e,t,n){"use strict";let r;n.d(t,{N:function(){return i},l:function(){return u}});var o=n(2265),a=n(3141),i=((r=i||{})[r.Forwards=0]="Forwards",r[r.Backwards=1]="Backwards",r);function u(){let e=(0,o.useRef)(0);return(0,a.s)("keydown",t=>{"Tab"===t.key&&(e.current=t.shiftKey?1:0)},!0),e}},3141:function(e,t,n){"use strict";n.d(t,{s:function(){return a}});var r=n(2265),o=n(31948);function a(e,t,n){let a=(0,o.E)(t);(0,r.useEffect)(()=>{function t(e){a.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)},[e,n])}},38198:function(e,t,n){"use strict";let r;n.d(t,{A:function(){return a},_:function(){return i}});var o=n(27847),a=((r=a||{})[r.None=1]="None",r[r.Focusable=2]="Focusable",r[r.Hidden=4]="Hidden",r);let i=(0,o.yV)(function(e,t){var n;let{features:r=1,...a}=e,i={ref:t,"aria-hidden":(2&r)==2||(null!=(n=a["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,o.sY)({ourProps:i,theirProps:a,slot:{},defaultTag:"div",name:"Hidden"})})},37863:function(e,t,n){"use strict";let r;n.d(t,{ZM:function(){return i},oJ:function(){return u},up:function(){return l}});var o=n(2265);let a=(0,o.createContext)(null);a.displayName="OpenClosedContext";var i=((r=i||{})[r.Open=1]="Open",r[r.Closed=2]="Closed",r[r.Closing=4]="Closing",r[r.Opening=8]="Opening",r);function u(){return(0,o.useContext)(a)}function l(e){let{value:t,children:n}=e;return o.createElement(a.Provider,{value:t},n)}},27988:function(e,t,n){"use strict";n.d(t,{O:function(){return i},n:function(){return a}});var r=n(2265);let o=(0,r.createContext)(!1);function a(){return(0,r.useContext)(o)}function i(e){return r.createElement(o.Provider,{value:e.force},e.children)}},47634:function(e,t,n){"use strict";function r(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(null==t?void 0:t.getAttribute("disabled"))==="";return!(r&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}n.d(t,{P:function(){return r}})},42120:function(e,t,n){"use strict";function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}n.d(t,{A:function(){return r}})},16015:function(e,t,n){"use strict";n.d(t,{k:function(){return function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(o))},nextFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=setTimeout(...t);return n.add(()=>clearTimeout(o))},microTask(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];let a={current:!0};return(0,r.Y)(()=>{a.current&&t[0]()}),n.add(()=>{a.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}}});var r=n(96822)},61424:function(e,t,n){"use strict";n.d(t,{O:function(){return u}});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,a=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class i{set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}constructor(){a(this,"current",this.detect()),a(this,"handoffState","pending"),a(this,"currentId",0)}}let u=new i},37105:function(e,t,n){"use strict";let r,o,a,i,u;n.d(t,{C5:function(){return g},GO:function(){return v},TO:function(){return d},fE:function(){return f},jA:function(){return b},sP:function(){return m},tJ:function(){return h}});var l=n(24536),s=n(40293);let c=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(",");var d=((r=d||{})[r.First=1]="First",r[r.Previous=2]="Previous",r[r.Next=4]="Next",r[r.Last=8]="Last",r[r.WrapAround=16]="WrapAround",r[r.NoScroll=32]="NoScroll",r),f=((o=f||{})[o.Error=0]="Error",o[o.Overflow=1]="Overflow",o[o.Success=2]="Success",o[o.Underflow=3]="Underflow",o),p=((a=p||{})[a.Previous=-1]="Previous",a[a.Next=1]="Next",a);function v(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(c)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=((i=h||{})[i.Strict=0]="Strict",i[i.Loose=1]="Loose",i);function m(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e!==(null==(t=(0,s.r)(e))?void 0:t.body)&&(0,l.E)(n,{0:()=>e.matches(c),1(){let t=e;for(;null!==t;){if(t.matches(c))return!0;t=t.parentElement}return!1}})}var y=((u=y||{})[u.Keyboard=0]="Keyboard",u[u.Mouse=1]="Mouse",u);function g(e){null==e||e.focus({preventScroll:!0})}function b(e,t){var n,r,o;let{sorted:a=!0,relativeTo:i=null,skipElements:u=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?a?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let a=r.compareDocumentPosition(o);return a&Node.DOCUMENT_POSITION_FOLLOWING?-1:a&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:v(e);u.length>0&&s.length>1&&(s=s.filter(e=>!u.includes(e))),i=null!=i?i:l.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(i))-1;if(4&t)return Math.max(0,s.indexOf(i))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},p=0,h=s.length,m;do{if(p>=h||p+h<=0)return 0;let e=d+p;if(16&t)e=(e+h)%h;else{if(e<0)return 3;if(e>=h)return 1}null==(m=s[e])||m.focus(f),p+=c}while(m!==l.activeElement);return 6&t&&null!=(o=null==(r=null==(n=m)?void 0:n.matches)?void 0:r.call(n,"textarea,input"))&&o&&m.select(),2}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0))},24536:function(e,t,n){"use strict";function r(e,t){for(var n=arguments.length,o=Array(n>2?n-2:0),a=2;a<n;a++)o[a-2]=arguments[a];if(e in t){let n=t[e];return"function"==typeof n?n(...o):n}let i=Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map(e=>'"'.concat(e,'"')).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(i,r),i}n.d(t,{E:function(){return r}})},96822:function(e,t,n){"use strict";function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}n.d(t,{Y:function(){return r}})},40293:function(e,t,n){"use strict";n.d(t,{r:function(){return o}});var r=n(61424);function o(e){return r.O.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}},52108:function(e,t,n){"use strict";function r(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function o(){return r()||/Android/gi.test(window.navigator.userAgent)}n.d(t,{gn:function(){return r},tq:function(){return o}})},27847:function(e,t,n){"use strict";let r,o;n.d(t,{AN:function(){return l},Y2:function(){return f},l4:function(){return s},sY:function(){return c},yV:function(){return h}});var a=n(2265),i=n(42120),u=n(24536),l=((r=l||{})[r.None=0]="None",r[r.RenderStrategy=1]="RenderStrategy",r[r.Static=2]="Static",r),s=((o=s||{})[o.Unmount=0]="Unmount",o[o.Hidden=1]="Hidden",o);function c(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:o,features:a,visible:i=!0,name:l,mergeRefs:s}=e;s=null!=s?s:p;let c=v(n,t);if(i)return d(c,r,o,l,s);let f=null!=a?a:0;if(2&f){let{static:e=!1,...t}=c;if(e)return d(t,r,o,l,s)}if(1&f){let{unmount:e=!0,...t}=c;return(0,u.E)(e?0:1,{0:()=>null,1:()=>d({...t,hidden:!0,style:{display:"none"}},r,o,l,s)})}return d(c,r,o,l,s)}function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0,{as:u=n,children:l,refName:s="ref",...c}=y(e,["unmount","static"]),d=void 0!==e.ref?{[s]:e.ref}:{},f="function"==typeof l?l(t):l;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t));let p={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r);e&&(p["data-headlessui-state"]=n.join(" "))}if(u===a.Fragment&&Object.keys(m(c)).length>0){if(!(0,a.isValidElement)(f)||Array.isArray(f)&&f.length>1)throw Error(['Passing props on "Fragment"!',"","The current component <".concat(r,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(c).map(e=>"  - ".concat(e)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>"  - ".concat(e)).join("\n")].join("\n"));let e=f.props,t="function"==typeof(null==e?void 0:e.className)?function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return(0,i.A)(null==e?void 0:e.className(...n),c.className)}:(0,i.A)(null==e?void 0:e.className,c.className);return(0,a.cloneElement)(f,Object.assign({},v(f.props,m(y(c,["ref"]))),p,d,{ref:o(f.ref,d.ref)},t?{className:t}:{}))}return(0,a.createElement)(u,Object.assign({},y(c,["ref"]),u!==a.Fragment&&d,u!==a.Fragment&&p),f)}function f(){let e=(0,a.useRef)([]),t=(0,a.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]);return function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];if(!r.every(e=>null==e))return e.current=r,t}}function p(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every(e=>null==e)?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function v(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},o={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=o[t]||(o[t]=[]),o[t].push(e[t])):r[t]=e[t];if(r.disabled||r["aria-disabled"])return Object.assign(r,Object.fromEntries(Object.keys(o).map(e=>[e,void 0])));for(let e in o)Object.assign(r,{[e](t){for(var n=arguments.length,r=Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];for(let n of o[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;n(t,...r)}}});return r}function h(e){var t;return Object.assign((0,a.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function m(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},4355:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...a}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});t.Z=o},81801:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...a}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))});t.Z=o},77165:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...a}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=o}}]);