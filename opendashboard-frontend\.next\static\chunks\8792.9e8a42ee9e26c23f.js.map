{"version": 3, "file": "static/chunks/8792.9e8a42ee9e26c23f.js", "mappings": "swBAwCA,IAAMA,EAAoB,OAAC,CACAC,MAAAA,CAAK,CACLC,IAAAA,CAAG,CACHC,WAAAA,CAAU,CAOpC,CAAAC,EASSC,EAAc,CAChBC,OAAQ,CACJC,IAAKN,EAAMO,EAAE,CACbC,SAXc,CACDN,WAAYA,EACZG,OAAQL,EACRS,YAAa,GACbC,aAAc,GACdC,cAAe,EACpC,EAMQC,IAAK,EACLC,KAAMb,EAAMc,KAAK,CACjBC,OAAQ,GACRC,UAAW,GACXC,SAAU,GACVC,MAAO,IACPC,SAAU,GACVC,SAAUC,KAAAA,EACVC,UAAWD,KAAAA,EACXE,gBAAiBF,KAAAA,EACjBG,SAAU,EACd,EACAvB,IAAKA,EACLwB,OAAQ,EACRC,SAAU,GACVC,YAAa,KAAO,EACpBC,eAAgB,GAChBC,WAAY,KAAO,EACnBC,cAAe,EACnB,EAEIC,EAAoBC,EAAAA,EAAYA,CAEpC,OAAQhC,EAAMiC,IAAI,EACd,KAAKC,EAAAA,qBAAqBA,CAACC,EAAE,CACzBJ,EAAoBK,EAAAA,CAAUA,CAC9B,KACJ,MAAKF,EAAAA,qBAAqBA,CAACG,IAAI,CAC3BN,EAAoBO,EAAAA,EAAYA,CAChC,KACJ,MAAKJ,EAAAA,qBAAqBA,CAACK,MAAM,CACjC,KAAKL,EAAAA,qBAAqBA,CAACM,IAAI,CAC/B,KAAKN,EAAAA,qBAAqBA,CAACO,OAAO,CAC9BV,EAAoBC,EAAAA,EAAYA,CAChC,KACJ,MAAKE,EAAAA,qBAAqBA,CAACQ,MAAM,CAC7BX,EAAoBY,EAAAA,EAAcA,CAClC,KACJ,MAAKT,EAAAA,qBAAqBA,CAACU,SAAS,CAChCb,EAAoBc,EAAAA,EAAiBA,CACrC,KACJ,MAAKX,EAAAA,qBAAqBA,CAACY,MAAM,CAC7Bf,EAAoBgB,EAAAA,EAAcA,CAClC,KACJ,MAAKb,EAAAA,qBAAqBA,CAACc,QAAQ,CAC/BjB,EAAoBkB,EAAAA,CAAgBA,CACpC,KACJ,MAAKf,EAAAA,qBAAqBA,CAACgB,IAAI,CAC/B,KAAKhB,EAAAA,qBAAqBA,CAACiB,SAAS,CACpC,KAAKjB,EAAAA,qBAAqBA,CAACkB,SAAS,CAChCrB,EAAoBsB,EAAAA,EAAYA,CAChC,KACJ,MAAKnB,EAAAA,qBAAqBA,CAACoB,MAAM,CACjC,KAAKpB,EAAAA,qBAAqBA,CAACqB,SAAS,CACpC,KAAKrB,EAAAA,qBAAqBA,CAACsB,SAAS,CAChCzB,EAAoB0B,EAAAA,EAAcA,CAClC,KACJ,MAAKvB,EAAAA,qBAAqBA,CAACwB,KAAK,CAC5B3B,EAAoB4B,EAAAA,EAAYA,CAChC,KACJ,MAAKzB,EAAAA,qBAAqBA,CAAC0B,aAAa,CACpC7B,EAAoB8B,EAAAA,EAAqBA,CACzC,KACJ,MAAK3B,EAAAA,qBAAqBA,CAAC4B,WAAW,CAClC/B,EAAoBgC,EAAAA,EAAmBA,CACvC,KACJ,SACIhC,EAAoBC,EAAAA,EAAYA,CAIxC,MAAO,GAAAgC,EAAAC,GAAA,EAAClC,EAAAA,CAAmB,GAAG3B,CAAW,EAC7C,EAEa8D,EAAW,QA+HIC,EAAAA,EA9HxB,GAAM,CAACC,cAAAA,CAAa,CAAEC,mBAAAA,CAAkB,CAAEC,QAAAA,CAAO,CAAEC,UAAAA,CAAS,CAAEC,IAAAA,CAAG,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAC/D,CAACC,WAAAA,CAAU,CAAC,CAAGC,EACf,CAACC,MAAAA,CAAK,CAAEC,gBAAAA,CAAe,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAC3B,CAACC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAC1B,CAACC,YAAAA,CAAW,CAAEC,eAAAA,CAAc,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAChC,CAACC,YAAAA,CAAW,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAEhBC,EAAcC,CAAAA,EAAAA,EAAAA,EAAAA,IACdtB,EAAcuB,CAAAA,EAAAA,EAAAA,EAAAA,IACLC,CAAAA,EAAAA,EAAAA,SAAAA,IACf,IAAMC,EAAWC,CAAAA,EAAAA,EAAAA,WAAAA,IACX,CAACC,WAAAA,CAAU,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IAEfC,EAAmBC,CAAAA,EAAAA,EAAAA,MAAAA,EAAuB,MAC1CC,EAAsBD,CAAAA,EAAAA,EAAAA,MAAAA,EAAuB,KAEnDvB,CAAAA,EAAWK,MAAM,CAAGL,EAAWK,MAAM,EAAI,CAACoB,WAAY,EAAE,CAAEC,MAAOC,EAAAA,KAAKA,CAACC,GAAG,EAC1E5B,EAAWM,KAAK,CAAGN,EAAWM,KAAK,EAAI,EAAE,CAEzC,IAAMuB,EAAWnC,CAAa,CAACM,EAAWxE,UAAU,CAAC,CAE/CsG,EAAkB,CAAC,CAAChB,CACRd,CAAAA,EAAW+B,WAAW,CAExC,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,EAAAA,GAEI,CAACD,GAAkBlB,GAAgBgB,GAAoB9B,EAAW+B,WAAW,CACzE,GAAmBjB,GAAgBgB,GAAoB9B,EAAW+B,WAAW,CAG3GG,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACN,IAAMC,EAAiBb,EAAiBc,OAAO,CACzCC,EAAmBb,EAAoBY,OAAO,CAEpD,GAAI,CAACD,GAAkB,CAACE,EAAkB,OAE1C,IAAMC,EAAoB,KACtBD,EAAiBE,UAAU,CAAGJ,EAAeI,UAAU,EAGrDC,EAAsB,KACxBL,EAAeI,UAAU,CAAGF,EAAiBE,UAAU,EAM3D,OAHAJ,EAAeM,gBAAgB,CAAC,SAAUH,GAC1CD,EAAiBI,gBAAgB,CAAC,SAAUD,GAErC,KACHL,EAAeO,mBAAmB,CAAC,SAAUJ,GAC7CD,EAAiBK,mBAAmB,CAAC,SAAUF,EACnD,CACJ,EAAG,EAAE,EA0DL,IAAMG,EAAeC,CA7BI,KACrB,GAAI,CAACf,EAAU,MAAO,EAAE,CAExB,IAAMgB,EAA8B,EAAE,CAClCvC,EAAMwC,MAAM,CAAG,EACfD,EAAYE,IAAI,IAAIzC,GACbN,EAAWM,KAAK,CAACwC,MAAM,CAAG,GACjCD,EAAYE,IAAI,IAAI/C,EAAWM,KAAK,EAEb,IAAvBuC,EAAYC,MAAM,EAAQD,EAAYE,IAAI,CAAC,CAACC,SAAUC,EAAAA,WAAWA,CAACxE,SAAS,CAAEyE,MAAOC,EAAAA,IAAIA,CAACC,GAAG,GAEhG,IAAMC,EAA0BnD,EAAMoD,QAAQ,CAAC,uBACzCC,EAA0BF,GAAUG,MAAMC,OAAO,CAACJ,GAAUA,EAAS,EAAE,CAEvE,CAACK,KAAAA,CAAI,CAAC,CAAGC,CAAAA,EAAAA,EAAAA,oBAAAA,EACX9B,EACAjC,EACAF,EACAM,EAAWK,MAAM,CACjBA,EACAwC,EACAhD,EAAU+D,eAAe,CAACC,MAAM,CAChC,GACApE,MAAAA,EAAAA,KAAAA,EAAAA,EAAaqE,UAAU,CAACC,MAAM,CAAClI,EAAE,CACjC0H,GAEJ,OAAOG,CACX,KAGMA,EAAOM,CAAAA,EAAAA,EAAAA,qBAAAA,EAAsBzD,EAAQoC,GACrCsB,EAAkBC,CA1DG,KACvB,IAAMD,EAAoC,EAAE,CAE5C,GAAI,CAACpC,EAAU,OAAOoC,EACtB,IAAME,EAAetC,EAASA,QAAQ,CAAC7B,UAAU,CACjD,GAAI,CAACmE,EAAc,OAAOF,EAE1B,GAAI,CAACG,aAAAA,CAAY,CAAEC,eAAAA,CAAc,CAAC,CAAGrE,EAIrC,IAAK,IAAMpE,KAHXwI,EAAeZ,MAAMC,OAAO,CAACW,GAAgBA,EAAe,EAAE,CAC9DC,EAAiBA,GAAkB,CAAC,EAElBF,EAAaG,SAAS,EAC/BF,EAAaG,QAAQ,CAAC3I,IAAMwI,EAAarB,IAAI,CAACnH,GAC9CyI,CAAc,CAACzI,EAAI,EAAEyI,CAAAA,CAAc,CAACzI,EAAI,CAAG,CAAC,GAGrD,IAAK,IAAMC,KAAMuI,EAAc,CAC3B,IAAMI,EAAQL,EAAaM,UAAU,CAAC5I,EAAG,CACpC2I,IACDH,CAAc,CAACxI,EAAG,CAAC6I,QAAQ,EAE/BT,EAAgBlB,IAAI,CAACyB,GACzB,CAEA,OAAOP,CACX,KAmCA,GAAI,CAACpC,EACD,MACI,GAAAvC,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,iDACX,GAAAtF,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,uBACX,GAAAtF,EAAAC,GAAA,EAACsF,IAAAA,CAAED,UAAU,iCAAwB,mBAMrD,IAAME,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,EAAoBlD,EAASA,QAAQ,EAGpDmD,EAAkBvF,MAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAaqE,UAAU,GAAvBrE,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,OAAAA,CAAAA,EAAAA,EAAyBsE,MAAM,GAA/BtE,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAiC5D,EAAE,CAEpCqF,EAASqD,QAAQ,CAAC,cAAgBrD,EAAS+D,QAAQ,CAAC,YAA4BC,MAAA,CAAhBF,IAEvF,IAAMG,EAAoB,CAACC,EAAkBC,KACrCrF,EAAW+B,WAAW,EAE1BX,EAAWgE,EAAUC,EACzB,EAEA,MACI,GAAA/F,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,kDACX,GAAAtF,EAAAgG,IAAA,EAACX,MAAAA,CAAIC,UAAU,oDACV,CAAC9C,GAAmB9B,EAAW+B,WAAW,EACvC,GAAAzC,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,wFAA+E,qDAKlG,GAAAtF,EAAAgG,IAAA,EAACX,MAAAA,CAAIC,UAAU,kDACX,GAAAtF,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,6BACX,GAAAtF,EAAAC,GAAA,EAACoF,MAAAA,CACGY,IAAKjE,EACLsD,UAAU,qCAEV,GAAAtF,EAAAgG,IAAA,EAACX,MAAAA,CAAIC,UAAU,6BACX,GAAAtF,EAAAgG,IAAA,EAACX,MAAAA,CAAIC,UAAU,2DACX,GAAAtF,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,qDAEf,GAAAtF,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,uDAA8C,UAC5DX,EAAgBuB,GAAG,CAAC,GACjB,GAAAlG,EAAAC,GAAA,EAACoF,MAAAA,CAAmBC,UAAU,iDACzBtJ,EAAMc,KAAK,EADNd,EAAMO,EAAE,MAMzB6H,IAAAA,EAAKZ,MAAM,CACR,KAEAY,EAAK8B,GAAG,CAAC,IACL,IAAMpJ,EAAQqJ,CAAAA,EAAAA,EAAAA,EAAAA,EACVlK,EAAIwI,MAAM,CACVe,EAAaY,UAAU,CACvBZ,EAAaa,YAAY,CACzBb,EAAac,UAAU,CACvB/D,EAASA,QAAQ,CACjBjC,GAGJ,MACI,GAAAN,EAAAgG,IAAA,EAACX,MAAAA,CAEGC,UAAW,oBAAsGM,MAAA,CAAlFlF,EAAW+B,WAAW,CAAG,iBAAmB,uCAC3E8D,QAAS,IACL,IAAMC,EAASC,EAAED,MAAM,CACjBE,EAAqBF,EAAOG,OAAO,CAAC,yEAE1CC,QAAQC,GAAG,CAAC,aAAc,CAAEL,OAAQA,EAAOM,OAAO,CAAEJ,mBAAAA,EAAoBpB,UAAWkB,EAAOlB,SAAS,GAE9FoB,GACDb,EAAkB5J,EAAIwI,MAAM,CAAClI,EAAE,CAAEN,EAAIwI,MAAM,CAACvI,UAAU,CAE9D,YAEA,GAAA8D,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,uBAEf,GAAAtF,EAAAgG,IAAA,EAACX,MAAAA,CAAIC,UAAU,wCACX,GAAAtF,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,4CACVxI,GAAS,aAEd,GAAAkD,EAAAgG,IAAA,EAACX,MAAAA,CAAIC,UAAU,0DACX,GAAAtF,EAAAC,GAAA,EAAC8G,OAAAA,CAAKzB,UAAU,oBAAY/C,EAASA,QAAQ,CAAC1F,IAAI,GAClD,GAAAmD,EAAAC,GAAA,EAAC8G,OAAAA,CAAKzB,UAAU,yBAAgB,MAChC,GAAAtF,EAAAC,GAAA,EAAC8G,OAAAA,CAAKzB,UAAU,oBAAY0B,CAAAA,EAAAA,EAAAA,CAAAA,EAAQ,IAAI9H,KAAKjD,EAAIgL,SAAS,WAIjEtC,EAAgBuB,GAAG,CAAC,GACjB,GAAAlG,EAAAC,GAAA,EAACoF,MAAAA,CAAmBC,UAAU,4BAC1B,GAAAtF,EAAAC,GAAA,EAAClE,EAAAA,CACGC,MAAOA,EACPC,IAAKA,EACLC,WAAYwE,EAAWxE,UAAU,CACjCsG,gBAAiBA,EACjBC,YAAa/B,EAAW+B,WAAW,EAAI,MANrCzG,EAAMO,EAAE,KA3BjBN,EAAIM,EAAE,CAuCvB,UAMhB,GAAAyD,EAAAC,GAAA,EAACoF,MAAAA,CACGY,IAAK/D,EACLoD,UAAU,uCAEV,GAAAtF,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,qCACX,GAAAtF,EAAAgG,IAAA,EAACX,MAAAA,CAAIC,UAAU,UAAU4B,MAAO,CAAEC,WAAY,SAAUC,OAAQ,KAAM,YAClE,GAAApH,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,eACf,GAAAtF,EAAAC,GAAA,EAACoF,MAAAA,CAAIC,UAAU,UACdX,EAAgBuB,GAAG,CAAC,GACjB,GAAAlG,EAAAC,GAAA,EAACoF,MAAAA,CAAAA,EAASrJ,EAAMO,EAAE,iBAStD", "sources": ["webpack://_N_E/./src/components/workspace/main/views/list/index.tsx", "webpack://_N_E/./src/components/workspace/main/views/list/list.css"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useRef, useEffect } from \"react\";\r\nimport {useWorkspace} from \"@/providers/workspace\";\r\nimport {DatabaseColumn, DatabaseFieldDataType, DbRecordSort, MagicColumn, Match, Sort} from \"opendb-app-db-utils/lib/typings/db\";\r\nimport { useViews, useViewFiltering, useViewSelection } from \"@/providers/views\";\r\nimport {filterAndSortRecords, searchFilteredRecords, DataViewRow, RGDMeta} from \"@/components/workspace/main/views/table\";\r\nimport {ListViewDefinition, ViewDefinition} from \"opendb-app-db-utils/lib/typings/view\";\r\nimport {Text<PERSON><PERSON><PERSON>, UUIDRenderer} from \"@/components/workspace/main/views/table/renderer/fields/text\";\r\nimport {CheckboxRenderer} from \"@/components/workspace/main/views/table/renderer/fields/checkbox\";\r\nimport {DateRenderer} from \"@/components/workspace/main/views/table/renderer/fields/date\";\r\nimport {<PERSON><PERSON><PERSON><PERSON>} from \"@/components/workspace/main/views/table/renderer/fields/person\";\r\nimport {File<PERSON><PERSON><PERSON>} from \"@/components/workspace/main/views/table/renderer/fields/files\";\r\nimport {AIRenderer} from \"@/components/workspace/main/views/table/renderer/fields/ai\";\r\nimport {SelectRenderer} from \"@/components/workspace/main/views/table/renderer/fields/select\";\r\nimport {LinkedRenderer} from \"@/components/workspace/main/views/table/renderer/fields/linked\";\r\nimport {usePage} from \"@/providers/page\";\r\nimport {SummarizeRenderer} from \"@/components/workspace/main/views/table/renderer/fields/summarize\";\r\nimport {ButtonGroupRenderer} from \"@/components/workspace/main/views/table/renderer/fields/buttonGroup\";\r\nimport {ScannableCodeRenderer} from \"@/components/workspace/main/views/table/renderer/fields/scannableCode\";\r\nimport {useMaybeRecord} from \"@/providers/record\";\r\nimport {useMaybeShared} from \"@/providers/shared\";\r\nimport {useMaybeTemplate} from \"@/providers/template\";\r\nimport {View} from \"@/typings/page\";\r\nimport {ScrollArea} from \"@/components/ui/scroll-area\";\r\nimport {getDatabaseTitleCol, getRecordTitle} from \"@/components/workspace/main/views/form/components/element/linked\";\r\nimport {timeAgo} from \"@/utils/timeAgo\";\r\nimport {useRouter, usePathname} from \"next/navigation\";\r\nimport {useStackedPeek} from \"@/providers/stackedpeek\";\r\nimport \"./list.css\";\r\n\r\nexport interface ViewRenderProps {\r\n    view: View\r\n    definition: ViewDefinition\r\n}\r\n\r\nexport interface ListViewRenderProps extends ViewRenderProps {\r\n    definition: ListViewDefinition\r\n}\r\n\r\nconst ListFieldRenderer = ({ \r\n                           field, \r\n                           row, \r\n                           databaseId\r\n}: { \r\n    field: DatabaseColumn, \r\n    row: DataViewRow,\r\n    databaseId: string,\r\n    isPublishedView: boolean,\r\n    lockContent: boolean\r\n}) => {\r\n    const meta: RGDMeta = {\r\n                         databaseId: databaseId,\r\n                         column: field,\r\n                         triggerEdit: false,\r\n                         headerLocked: true,\r\n                         contentLocked: true,\r\n    };\r\n\r\n    const renderProps = {\r\n        column: {\r\n            key: field.id,\r\n            __meta__: meta,\r\n            idx: 0,\r\n            name: field.title,\r\n            frozen: false,\r\n            resizable: false,\r\n            sortable: false,\r\n            width: 150,\r\n            minWidth: 50,\r\n            maxWidth: undefined,\r\n            cellClass: undefined,\r\n            headerCellClass: undefined,\r\n            editable: false\r\n        },\r\n        row: row,\r\n        rowIdx: 0,\r\n        tabIndex: -1,\r\n        onRowChange: () => {},\r\n        isCellSelected: false,\r\n        selectCell: () => {},\r\n        isRowSelected: false\r\n    };\r\n\r\n    let RendererComponent = TextRenderer;\r\n    \r\n    switch (field.type) {\r\n        case DatabaseFieldDataType.AI:\r\n            RendererComponent = AIRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.UUID:\r\n            RendererComponent = UUIDRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.Number:\r\n        case DatabaseFieldDataType.Text:\r\n        case DatabaseFieldDataType.Derived:\r\n            RendererComponent = TextRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.Linked:\r\n            RendererComponent = LinkedRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.Summarize:\r\n            RendererComponent = SummarizeRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.Select:\r\n            RendererComponent = SelectRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.Checkbox:\r\n            RendererComponent = CheckboxRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.Date:\r\n        case DatabaseFieldDataType.CreatedAt:\r\n        case DatabaseFieldDataType.UpdatedAt:\r\n            RendererComponent = DateRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.Person:\r\n        case DatabaseFieldDataType.CreatedBy:\r\n        case DatabaseFieldDataType.UpdatedBy:\r\n            RendererComponent = PersonRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.Files:\r\n            RendererComponent = FileRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.ScannableCode:\r\n            RendererComponent = ScannableCodeRenderer;\r\n            break;\r\n        case DatabaseFieldDataType.ButtonGroup:\r\n            RendererComponent = ButtonGroupRenderer;\r\n            break;\r\n        default:\r\n            RendererComponent = TextRenderer;\r\n    }\r\n    \r\n    // @ts-ignore\r\n    return <RendererComponent {...renderProps} />;\r\n};\r\n\r\nexport const ListView = (props: ListViewRenderProps) => {\r\n    const {databaseStore, databaseErrorStore, members, workspace, url} = useWorkspace()\r\n    const {definition} = props\r\n    const {cache, setPeekRecordId} = useViews()\r\n    const {filter, sorts, search} = useViewFiltering()\r\n    const {selectedIds, setSelectedIds} = useViewSelection()\r\n    const {accessLevel} = usePage()\r\n\r\n    const maybeShared = useMaybeShared()\r\n    const maybeRecord = useMaybeRecord()\r\n    const router = useRouter()\r\n    const pathname = usePathname()\r\n    const {openRecord} = useStackedPeek()\r\n\r\n    const contentScrollRef = useRef<HTMLDivElement>(null)\r\n    const horizontalScrollRef = useRef<HTMLDivElement>(null)\r\n\r\n    definition.filter = definition.filter || {conditions: [], match: Match.All}\r\n    definition.sorts = definition.sorts || []\r\n\r\n    const database = databaseStore[definition.databaseId]\r\n\r\n    const isPublishedView = !!maybeShared\r\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel\r\n\r\n    const maybeTemplate = useMaybeTemplate()\r\n\r\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable)\r\n    let canEditData: boolean = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable)\r\n\r\n    // Sync horizontal scrolling between content and scrollbar\r\n    useEffect(() => {\r\n        const contentElement = contentScrollRef.current\r\n        const scrollbarElement = horizontalScrollRef.current\r\n\r\n        if (!contentElement || !scrollbarElement) return\r\n\r\n        const syncContentScroll = () => {\r\n            scrollbarElement.scrollLeft = contentElement.scrollLeft\r\n        }\r\n\r\n        const syncScrollbarScroll = () => {\r\n            contentElement.scrollLeft = scrollbarElement.scrollLeft\r\n        }\r\n\r\n        contentElement.addEventListener('scroll', syncContentScroll)\r\n        scrollbarElement.addEventListener('scroll', syncScrollbarScroll)\r\n\r\n        return () => {\r\n            contentElement.removeEventListener('scroll', syncContentScroll)\r\n            scrollbarElement.removeEventListener('scroll', syncScrollbarScroll)\r\n        }\r\n    }, [])\r\n\r\n    const getFieldsToDisplay = (): DatabaseColumn[] => {\r\n        const fieldsToDisplay: DatabaseColumn[] = []\r\n\r\n        if (!database) return fieldsToDisplay\r\n        const dbDefinition = database.database.definition\r\n        if (!dbDefinition) return fieldsToDisplay\r\n\r\n        let {columnsOrder, columnPropsMap} = definition\r\n        columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : []\r\n        columnPropsMap = columnPropsMap || {}\r\n\r\n        for (const key of dbDefinition.columnIds) {\r\n            if (!columnsOrder.includes(key)) columnsOrder.push(key)\r\n            if (!columnPropsMap[key]) columnPropsMap[key] = {}\r\n        }\r\n\r\n        for (const id of columnsOrder) {\r\n            const dbCol = dbDefinition.columnsMap[id]\r\n            if (!dbCol) continue\r\n            if (columnPropsMap[id].isHidden) continue\r\n            \r\n            fieldsToDisplay.push(dbCol)\r\n        }\r\n\r\n        return fieldsToDisplay\r\n    }\r\n\r\n    const getProcessedRows = (): DataViewRow[] => {\r\n        if (!database) return []\r\n\r\n        const sortOptions: DbRecordSort[] = []\r\n        if (sorts.length > 0) {\r\n            sortOptions.push(...sorts)\r\n        } else if (definition.sorts.length > 0) {\r\n            sortOptions.push(...definition.sorts)\r\n        }\r\n        if (sortOptions.length === 0) sortOptions.push({columnId: MagicColumn.CreatedAt, order: Sort.Asc})\r\n\r\n        const colIds: string[] | null = cache.getCache('newlyCreatedRecords')\r\n        const createdColIds: string[] = colIds && Array.isArray(colIds) ? colIds : []\r\n\r\n        const {rows} = filterAndSortRecords(\r\n            database,\r\n            members,\r\n            databaseStore,\r\n            definition.filter,\r\n            filter,\r\n            sortOptions,\r\n            workspace.workspaceMember.userId,\r\n            '',\r\n            maybeRecord?.recordInfo.record.id,\r\n            createdColIds\r\n        )\r\n        return rows\r\n    }\r\n\r\n    const filteredRows = getProcessedRows()\r\n    const rows = searchFilteredRecords(search, filteredRows)\r\n    const fieldsToDisplay = getFieldsToDisplay()\r\n\r\n    if (!database) {\r\n        return (\r\n            <div className=\"h-64 flex items-center justify-center\">\r\n                <div className=\"text-center\">\r\n                    <p className=\"text-sm text-gray-500\">Loading...</p>\r\n                </div>\r\n            </div>\r\n        )\r\n    }\r\n\r\n    const titleColOpts = getDatabaseTitleCol(database.database);\r\n\r\n    const isInRecordTab = !!maybeRecord;\r\n    const currentRecordId = maybeRecord?.recordInfo?.record?.id;\r\n    \r\n    const isOnRecordPage = pathname.includes('/records/') && pathname.endsWith(`/records/${currentRecordId}`);\r\n    \r\n    const handleRecordClick = (recordId: string, recordDatabaseId: string) => {\r\n        if (definition.lockContent) return;\r\n        \r\n        openRecord(recordId, recordDatabaseId);\r\n    };\r\n\r\n    return (\r\n        <div className=\"w-full h-full overflow-hidden listView\">\r\n            <div className=\"overflow-hidden size-full flex flex-col\">\r\n                {!isPublishedView && definition.lockContent && (\r\n                    <div className='p-2 border-b bg-yellow-50 text-xs text-center border-neutral-300 font-medium'>\r\n                        Content is locked, record navigation is disabled\r\n                    </div>\r\n                )}\r\n                \r\n                <div className=\"flex-1 overflow-hidden scroll-wrapper\">\r\n                    <div className=\"content-container\">\r\n                        <div \r\n                            ref={contentScrollRef}\r\n                            className=\"content-horizontal-scroll\"\r\n                        >\r\n                            <div className=\"scroll-container\">\r\n                                <div className=\"border-b rowGrid border-neutral-200 header-row\">\r\n                                    <div className=\"text-xs text-black font-bold bg-white check !w-1\">\r\n                                    </div>\r\n                                    <div className=\"text-xs text-black font-bold bg-white fluid\">Title</div>\r\n                                    {fieldsToDisplay.map((field) => (\r\n                                        <div key={field.id} className=\"text-xs text-black font-bold bg-white\">\r\n                                            {field.title}\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n\r\n                                {rows.length === 0 ? (\r\n                                    null\r\n                                ) : (\r\n                                    rows.map((row) => {\r\n                                        const title = getRecordTitle(\r\n                                            row.record,\r\n                                            titleColOpts.titleColId,\r\n                                            titleColOpts.defaultTitle,\r\n                                            titleColOpts.isContacts,\r\n                                            database.database,\r\n                                            members\r\n                                        );\r\n\r\n                                        return (\r\n                                            <div \r\n                                                key={row.id} \r\n                                                className={`rowGrid border-b ${definition.lockContent ? 'cursor-default' : 'hover:bg-neutral-100 cursor-pointer'}`}\r\n                                                onClick={(e) => {\r\n                                                    const target = e.target as HTMLElement;\r\n                                                    const isInteractiveField = target.closest('.r-button-group, .r-scannable-code, .r-files, button, [role=\"button\"]');\r\n                                                    \r\n                                                    console.log('Row click:', { target: target.tagName, isInteractiveField, className: target.className });\r\n                                                    \r\n                                                    if (!isInteractiveField) {\r\n                                                        handleRecordClick(row.record.id, row.record.databaseId);\r\n                                                    }\r\n                                                }}\r\n                                            >\r\n                                                <div className=\"text-xs check !w-1\">\r\n                                                </div>\r\n                                                <div className=\"text-xs flex flex-col fluid\">\r\n                                                    <div className=\"title-text text-xs font-semibold\">\r\n                                                        {title || \"Untitled\"}\r\n                                                    </div>\r\n                                                    <div className=\"flex gap-2 text-xs text-muted-foreground pt-1\">\r\n                                                        <span className=\"truncate\">{database.database.name}</span>\r\n                                                        <span className=\"flex-shrink-0\">&bull;</span>\r\n                                                        <span className=\"truncate\">{timeAgo(new Date(row.updatedAt))}</span>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                {fieldsToDisplay.map((field) => (\r\n                                                    <div key={field.id} className=\"text-xs truncate\">\r\n                                                        <ListFieldRenderer\r\n                                                            field={field}\r\n                                                            row={row}\r\n                                                            databaseId={definition.databaseId}\r\n                                                            isPublishedView={isPublishedView}\r\n                                                            lockContent={definition.lockContent || false}\r\n                                                        />\r\n                                                    </div>\r\n                                                ))}\r\n                                            </div>\r\n                                        );\r\n                                    })\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div \r\n                        ref={horizontalScrollRef}\r\n                        className=\"horizontal-scroll-container\"\r\n                    >\r\n                        <div className=\"horizontal-scroll-content\">\r\n                            <div className=\"rowGrid\" style={{ visibility: 'hidden', height: '1px' }}>\r\n                                <div className=\"check !w-1\"></div>\r\n                                <div className=\"fluid\"></div>\r\n                                {fieldsToDisplay.map((field) => (\r\n                                    <div key={field.id}></div>\r\n                                ))}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}", "// extracted by mini-css-extract-plugin"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "field", "row", "databaseId", "param", "renderProps", "column", "key", "id", "__meta__", "triggerEdit", "headerLocked", "contentLocked", "idx", "name", "title", "frozen", "resizable", "sortable", "width", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "undefined", "cellClass", "headerCellClass", "editable", "rowIdx", "tabIndex", "onRowChange", "isCellSelected", "selectCell", "isRowSelected", "RendererComponent", "<PERSON><PERSON><PERSON><PERSON>", "type", "DatabaseFieldDataType", "AI", "<PERSON><PERSON><PERSON><PERSON>", "UUID", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Number", "Text", "Derived", "Linked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Summarize", "Summariz<PERSON><PERSON><PERSON><PERSON>", "Select", "Select<PERSON><PERSON>er", "Checkbox", "CheckboxRenderer", "Date", "CreatedAt", "UpdatedAt", "<PERSON><PERSON><PERSON><PERSON>", "Person", "CreatedBy", "UpdatedBy", "<PERSON><PERSON><PERSON><PERSON>", "Files", "<PERSON><PERSON><PERSON><PERSON>", "ScannableCode", "ScannableCodeRenderer", "ButtonGroup", "ButtonGroup<PERSON><PERSON><PERSON>", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "ListView", "<PERSON><PERSON><PERSON><PERSON>", "databaseStore", "databaseErrorStore", "members", "workspace", "url", "useWorkspace", "definition", "props", "cache", "setPeekRecordId", "useViews", "filter", "sorts", "search", "useViewFiltering", "selectedIds", "setSelectedIds", "useViewSelection", "accessLevel", "usePage", "maybeShared", "useMaybeShared", "useMaybeRecord", "useRouter", "pathname", "usePathname", "openRecord", "useStackedPeek", "contentScrollRef", "useRef", "horizontalScrollRef", "conditions", "match", "Match", "All", "database", "isPublishedView", "lock<PERSON><PERSON><PERSON>", "maybeTemplate", "useMaybeTemplate", "useEffect", "contentElement", "current", "scrollbarElement", "syncContentScroll", "scrollLeft", "syncScrollbarScroll", "addEventListener", "removeEventListener", "filteredRows", "getProcessedRows", "sortOptions", "length", "push", "columnId", "MagicColumn", "order", "Sort", "Asc", "colIds", "getCache", "createdColIds", "Array", "isArray", "rows", "filterAndSortRecords", "workspaceMember", "userId", "recordInfo", "record", "searchFilteredRecords", "fieldsToDisplay", "getFieldsToDisplay", "dbDefinition", "columnsOrder", "columnPropsMap", "columnIds", "includes", "dbCol", "columnsMap", "isHidden", "div", "className", "p", "titleColOpts", "getDatabaseTitleCol", "currentRecordId", "endsWith", "concat", "handleRecordClick", "recordId", "recordDatabaseId", "jsxs", "ref", "map", "getRecordTitle", "titleColId", "defaultTitle", "isContacts", "onClick", "target", "e", "isInteractiveField", "closest", "console", "log", "tagName", "span", "timeAgo", "updatedAt", "style", "visibility", "height"], "sourceRoot": ""}