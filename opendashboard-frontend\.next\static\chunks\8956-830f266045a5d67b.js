!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ca92d571-74be-4c4e-8fee-efc71214215e",e._sentryDebugIdIdentifier="sentry-dbid-ca92d571-74be-4c4e-8fee-efc71214215e")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8956],{38956:function(e,t,n){Promise.resolve().then(n.bind(n,16158))},30166:function(e,t,n){"use strict";n.d(t,{default:function(){return r.a}});var a=n(55775),r=n.n(a)},99376:function(e,t,n){"use strict";var a=n(35475);n.o(a,"redirect")&&n.d(t,{redirect:function(){return a.redirect}}),n.o(a,"useParams")&&n.d(t,{useParams:function(){return a.useParams}}),n.o(a,"usePathname")&&n.d(t,{usePathname:function(){return a.usePathname}}),n.o(a,"useRouter")&&n.d(t,{useRouter:function(){return a.useRouter}}),n.o(a,"useSearchParams")&&n.d(t,{useSearchParams:function(){return a.useSearchParams}})},55775:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let a=n(47043);n(57437),n(2265);let r=a._(n(15602));function i(e,t){var n;let a={loading:e=>{let{error:t,isLoading:n,pastDelay:a}=e;return null}};"function"==typeof e&&(a.loader=e);let i={...a,...t};return(0,r.default)({...i,modules:null==(n=i.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81523:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}});let a=n(18993);function r(e){let{reason:t,children:n}=e;if("undefined"==typeof window)throw new a.BailoutToCSRError(t);return n}},15602:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let a=n(57437),r=n(2265),i=n(81523),s=n(70049);function o(e){return{default:e&&"default"in e?e.default:e}}let d={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},l=function(e){let t={...d,...e},n=(0,r.lazy)(()=>t.loader().then(o)),l=t.loading;function u(e){let o=l?(0,a.jsx)(l,{isLoading:!0,pastDelay:!0,error:null}):null,d=t.ssr?(0,a.jsxs)(a.Fragment,{children:["undefined"==typeof window?(0,a.jsx)(s.PreloadCss,{moduleIds:t.modules}):null,(0,a.jsx)(n,{...e})]}):(0,a.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(n,{...e})});return(0,a.jsx)(r.Suspense,{fallback:o,children:d})}return u.displayName="LoadableComponent",u}},70049:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return i}});let a=n(57437),r=n(20544);function i(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let n=(0,r.getExpectedRequestStore)("next/dynamic css"),i=[];if(n.reactLoadableManifest&&t){let e=n.reactLoadableManifest;for(let n of t){if(!e[n])continue;let t=e[n].files.filter(e=>e.endsWith(".css"));i.push(...t)}}return 0===i.length?null:(0,a.jsx)(a.Fragment,{children:i.map(e=>(0,a.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:n.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},23500:function(e,t){"use strict";var n,a,r;t.Bq=t.Ly=t.bW=void 0,(n=t.bW||(t.bW={})).Table="table",n.Board="board",n.Form="form",n.Document="document",n.Dashboard="dashboard",n.SummaryTable="summary-table",n.ListView="list-view",n.Calendar="calendar",(a=t.Ly||(t.Ly={})).Left="left",a.Right="right",(r=t.Bq||(t.Bq={})).Infobox="infobox",r.LineChart="lineChart",r.BarChart="barChart",r.PieChart="pieChart",r.FunnelChart="funnelChart",r.Embed="embed",r.Image="image",r.Text="text"},54921:function(e,t,n){"use strict";n.d(t,{o:function(){return p}});var a=n(57437),r=n(23500),i=n(2265),s=n(30166),o=n(29119);let d=(0,s.default)(()=>Promise.all([n.e(1092),n.e(6018),n.e(8025),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(6462),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(7674),n.e(1506),n.e(7561),n.e(663),n.e(3879),n.e(4376),n.e(4239),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3139),n.e(7515),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(8267),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(6240)]).then(n.bind(n,26240)).then(e=>e.DashboardView),{loadableGenerated:{webpack:()=>[26240]},ssr:!1}),l=(0,s.default)(()=>Promise.all([n.e(8025),n.e(6018),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818)]).then(n.bind(n,18626)).then(e=>e.TableView),{loadableGenerated:{webpack:()=>[18626]},ssr:!1}),u=(0,s.default)(()=>Promise.all([n.e(6018),n.e(7360),n.e(696),n.e(8025),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(6640),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(6326),n.e(4805),n.e(8849),n.e(8603)]).then(n.bind(n,89034)).then(e=>e.BoardView),{loadableGenerated:{webpack:()=>[89034]},ssr:!1}),c=(0,s.default)(()=>Promise.all([n.e(6018),n.e(7360),n.e(696),n.e(8025),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(631)]).then(n.bind(n,60631)).then(e=>e.FormView),{loadableGenerated:{webpack:()=>[60631]},ssr:!1}),f=(0,s.default)(()=>Promise.all([n.e(6018),n.e(2191),n.e(7190),n.e(696),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(8107),n.e(5737),n.e(794),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(7918)]).then(n.bind(n,87918)).then(e=>e.DocumentView),{loadableGenerated:{webpack:()=>[87918]},ssr:!1}),b=(0,s.default)(()=>Promise.all([n.e(8025),n.e(6018),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(9625)]).then(n.bind(n,79625)).then(e=>e.SummaryTableView),{loadableGenerated:{webpack:()=>[79625]},ssr:!1}),h=(0,s.default)(()=>Promise.all([n.e(6018),n.e(8025),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(826),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(8792)]).then(n.bind(n,68792)).then(e=>e.ListView),{loadableGenerated:{webpack:()=>[68792]},ssr:!1}),m=(0,s.default)(()=>Promise.all([n.e(6018),n.e(8025),n.e(7360),n.e(696),n.e(7698),n.e(2191),n.e(7190),n.e(8310),n.e(8218),n.e(9442),n.e(9900),n.e(3572),n.e(7902),n.e(5501),n.e(1425),n.e(6137),n.e(7648),n.e(311),n.e(2534),n.e(4451),n.e(1107),n.e(85),n.e(3493),n.e(3139),n.e(7515),n.e(8107),n.e(5737),n.e(794),n.e(9175),n.e(7353),n.e(6640),n.e(7900),n.e(2211),n.e(2212),n.e(6208),n.e(3818),n.e(6326),n.e(4805),n.e(8849),n.e(2949)]).then(n.bind(n,39910)).then(e=>e.CalendarView),{loadableGenerated:{webpack:()=>[39910]},ssr:!1}),p=e=>{let{viewsMap:t}=(0,o.qt)(),n=e.id,s=e.view||t[n],p=s.type;return(0,i.useEffect)(()=>{document.title=(null==s?void 0:s.name)||"Untitled"},[]),(0,a.jsx)(a.Fragment,{children:p===r.bW.Table?(0,a.jsx)(l,{view:s,definition:s.definition}):p===r.bW.Board?(0,a.jsx)(u,{view:s,definition:s.definition}):p===r.bW.Form?(0,a.jsx)(c,{view:s,definition:s.definition}):p===r.bW.Document?(0,a.jsx)(f,{view:s,definition:s.definition}):p===r.bW.SummaryTable?(0,a.jsx)(b,{view:s,definition:s.definition}):p===r.bW.Dashboard?(0,a.jsx)(d,{view:s,definition:s.definition}):p===r.bW.ListView?(0,a.jsx)(h,{view:s,definition:s.definition}):p===r.bW.Calendar?(0,a.jsx)(m,{view:s,definition:s.definition}):(0,a.jsx)(a.Fragment,{})})}},16158:function(e,t,n){"use strict";n.d(t,{ViewWrapper:function(){return b}});var a=n(57437),r=n(27648),i=n(12381),s=n(3163),o=n(84440),d=n(2265),l=n(14803),u=n(54921),c=n(41426),f=n(99376);let b=e=>{let{response:t,opts:n={}}=e,[r,i]=(0,d.useState)(!1),l="ok"!==t.data.status?t.error||(0,s.E)():"";(0,d.useEffect)(()=>{i(!0)},[]);let u=(0,f.useSearchParams)();return n.isEmbed&&(n.embedHash=u.get("hash")||""),(0,a.jsx)(a.Fragment,{children:r?!t||l?(0,a.jsx)(o.PageLoader,{showLogo:!0,error:l||(0,s.E)(),size:"screen"}):(0,a.jsx)(h,{props:t.data.data,opts:n}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(o.PageLoader,{size:"screen"})})})},h=e=>{let{props:t,opts:n}=e,{page:s,view:o}=t,f=s.icon&&s.icon.type===c.ObjectType.Emoji?s.icon.emoji:"\uD83D\uDCD5";return(0,d.useEffect)(()=>{if(!n.isEmbed)return;let e=document.getElementsByTagName("body")[0].scrollHeight+70;parent.postMessage([n.embedHash||"","setHeight",e],"*")},[n.embedHash,n.isEmbed]),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(l.CL.Provider,{value:t,children:(0,a.jsx)(l.UU,{children:(0,a.jsxs)("div",{className:"fixed size-full flex flex-col overflow-hidden",children:[!n.isEmbed&&(0,a.jsx)("header",{className:"bg-white sticky top-0 z-10",children:(0,a.jsxs)("nav",{className:"h-14 px-4 py-0 lg:py-4 lg:px-20 flex items-center justify-start gap-2 overflow-hidden border-b",children:[(0,a.jsx)("span",{className:"text-base lg:text-xl inline-block",children:f}),(0,a.jsx)("h1",{className:"text-sm font-semibold lg:text-lg lg:font-bold flex-1 truncate",children:o.name||"Untitled View"}),(0,a.jsx)(i.z,{className:"rounded-full font-medium text-xs gap-1",variant:"outline",asChild:!0,children:(0,a.jsxs)(r.default,{href:"/",target:"_blank",children:["Built with",(0,a.jsx)("img",{className:"h-4 max-h-full w-auto",src:"/android-chrome-192x192.png",alt:"Opendashboard Logo"})]})})]})}),(0,a.jsxs)("div",{className:"flex-1 overflow-hidden p-2 lg:px-20 relative",children:[(0,a.jsx)("div",{className:"size-full overflow-hidden",children:(0,a.jsx)(u.o,{id:t.view.id})}),n.isEmbed&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("footer",{className:"bg-white py-2 text-center opacity-30 hover:opacity-100 absolute bottom-4 right-4",children:(0,a.jsxs)(r.default,{href:"/",className:"mx-auto flex gap-2 items-center font-medium text-xs justify-center",target:"_blank",children:["Built with",(0,a.jsx)("img",{className:"h-4 max-h-full w-auto",src:"/assets/opendashboard-black.png",alt:"Opendashboard Logo"})]})})})]})]})})})})}},29119:function(e,t,n){"use strict";n.d(t,{Ti:function(){return i},ol:function(){return o},qt:function(){return d}});var a=n(57437),r=n(2265);let i=e=>{let{page:t,views:n,accessLevel:r,permissions:i}=e.permissiblePage,o={};for(let e of n)o[e.id]=e;return(0,a.jsx)(s.Provider,{value:{page:t,views:n,accessLevel:r,permissions:i,viewsMap:o},children:e.children})},s=(0,r.createContext)(void 0),o=()=>(0,r.useContext)(s)||null,d=()=>{let e=(0,r.useContext)(s);if(!e)throw Error("usePage must be used within a PageProvider");return e}},14803:function(e,t,n){"use strict";n.d(t,{CL:function(){return g},UU:function(){return w},cL:function(){return x}});var a=n(57437),r=n(2265),i=n(6770),s=n(68738),o=n(95473),d=n(42212),l=n(84977),u=n(89282),c=n(49299),f=n(3163),b=n(66312),h=n(29119),m=n(25144),p=n(99376);let g=(0,r.createContext)(void 0),x=()=>(0,r.useContext)(g),v=()=>{let e=(0,r.useContext)(g);if(!e)throw Error("useShared must be used within a SharedViewProvider");return e},y=e=>({createdAt:new Date,deletedAt:void 0,id:0,invitedById:"",role:i.eB.Collaborator,updatedAt:new Date,userId:"anonymous",workspaceId:e}),w=e=>{let{children:t}=e,{workspace:n,view:i,database:s,databaseMap:o,documents:l,members:u,page:c}=v(),f=n.domain,b={membersCount:0,planId:"",workspace:n,workspaceMember:y(n.id)},[m,p]=(0,r.useState)([]),[g,x]=(0,r.useState)([]),w={};for(let e of Object.values(o)){let t=e.database,n=e.recordsMap;w[t.id]={database:t,recordsIdMap:n,updatedAt:new Date,recordsLoaded:!0}}let P={id:0,userId:"",workspaceId:n.id,settings:{},createdAt:"",updatedAt:""},[,C]=(0,r.useState)(0);(0,r.useCallback)(()=>{C(e=>e+1)},[]);let S={};if(u&&Array.isArray(u))for(let e of u)S[e.workspaceMember.userId]=e;return(0,a.jsx)(d.C5.Provider,{value:{domain:f,invitations:[],members:u,url:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"/".concat(f).concat(e)},workspace:b,workspaceMeta:{},updateWorkspace:e=>{},pageStore:{},databasePageStore:{},databaseStore:w,memberSettings:P,databaseErrorStore:{},updateDatabaseErrorState:function(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2]},addPagePermissions:(e,t,n)=>{},updatePagePermission:(e,t,n)=>{},deletePagePermissions:(e,t,n)=>{},membersMap:S,deletePage:function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1]},deleteDatabase:function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1]},addPage:e=>{},addDatabase:e=>{},updatePageStore:function(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2]},updateDatabaseStore:function(e,t,n){arguments.length>3&&void 0!==arguments[3]&&arguments[3]},updateDatabasePageStore:function(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2]},addAdjacentDatabases:e=>{},updateMemberSettings:e=>{},updateDatabaseRecordValues:function(e,t,n){arguments.length>3&&void 0!==arguments[3]&&arguments[3],arguments.length>4&&void 0!==arguments[4]&&arguments[4],arguments.length>5&&void 0!==arguments[5]&&arguments[5]},pagesId:m,databasePagesId:g,updateMembers:e=>{},updateInvitations:e=>{},updateMyWorkspace:e=>{},updatePageViews:(e,t,n,a)=>{},refreshPagesAndDatabases:()=>{}},children:(0,a.jsx)(j,{children:(0,a.jsx)(h.Ti,{id:c.id,permissiblePage:{accessLevel:void 0,page:c,permissions:[],views:[i]},children:t})})})},j=e=>{let{children:t}=e,[n,i]=(0,r.useState)([]),h=(0,p.useParams)().viewId||"",{toast:g,confirm:x,promptUpgrade:v}=(0,l.V)(),{workspace:y,databaseStore:w,updateDatabasePageStore:j,updateDatabaseStore:P,addAdjacentDatabases:C,databaseErrorStore:S,updateDatabaseErrorState:k}=(0,d.cF)(),{previewFiles:D}=(0,u.u)(),[E,_]=(0,r.useState)(),[L,I]=(0,r.useState)(0),[M,N]=(0,r.useState)({match:s.Match.All,conditions:[]}),[V,W]=(0,r.useState)([]),[B,O]=(0,r.useState)(""),[T,F]=(0,r.useState)(""),A=(0,c.Y)(),[,R]=(0,r.useState)(0),G=(0,r.useCallback)(()=>{R(e=>e+1)},[]),U=(0,r.useRef)({}),q=async function(e,t,n){arguments.length>3&&void 0!==arguments[3]&&arguments[3]},z=async(e,t,n,a)=>{},H=async(e,t)=>{},Q=async(e,t,n,a)=>{},Y=async(e,t)=>{i([]);let n=await (0,m.c)("post","/api/form-submit",{"Content-Type":"application/json"},{viewId:h,data:{valuesList:t}}),a=(0,f.fq)(n);if(a.error){g.error(a.error||(0,f.E)());return}return{records:[]}},J=async(e,t)=>{},K=async(e,t)=>{},X=async(e,t,n)=>{},Z=async(e,t)=>{},$=async e=>{},ee=async(e,t,n)=>[],et=async(e,t,n)=>{},en=async(e,t)=>{},ea=async(e,t)=>{},er=async(e,t,n)=>{},ei=async(e,t)=>{},es=async(e,t,n)=>!0;return(0,a.jsx)(o.Oh.Provider,{value:{selectedIds:n,setSelectedIds:i,peekRecordId:T,setPeekRecordId:F,sorts:V,filter:M,setFilter:N,setSorts:W,search:B,setSearch:O,pushDashboardTransactions:er,refreshDatabase:ei,uploadWorkspaceFile:(e,t,n,a,r)=>{let i=()=>(U.current[e]=U.current[e]||{},U.current[e][t]=U.current[e][t]||{},U.current[e][t][n]=U.current[e][t][n]||{},U.current[e][t][n]),s=(0,b.generateUUID)(),o={id:s,progress:0},d={onStart(){i()[s]=o,G()},onComplete:e=>{let t=i();if(t[s]&&delete t[s],e.error){let t=e.error||(0,f.E)();g.error(t),G();return}G(),r(e)},onProgress:e=>{let t=i();t[s]&&(t[s].progress=e,G())}},l=d.onComplete;d.onComplete=e=>{l((0,f.fq)(e))},(0,f.G1)("post","/api/form-upload","","file",a,d,{viewId:h})},updateRecordValues:q,updateDatabaseColumn:z,updateDatabaseTitleColumn:H,uploadQueue:U.current,makeDatabaseColumnUnique:es,updateView:Z,uploadFileToColumn:Q,previewFiles:D,createRecords:Y,requestFullAdjacentDatabase:K,updateViewDefinition:X,deleteRecords:J,cache:A,generateAIField:et,getActivities:ee,deleteDatabaseColumn:en,addDatabaseColumn:ea,deleteView:$},children:t})}}}]);