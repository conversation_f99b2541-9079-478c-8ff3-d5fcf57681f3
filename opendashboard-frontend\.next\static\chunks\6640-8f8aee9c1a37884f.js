!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="b5542146-96b1-44f0-bbbc-7a723be3a0dc",e._sentryDebugIdIdentifier="sentry-dbid-b5542146-96b1-44f0-bbbc-7a723be3a0dc")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6640],{40802:function(e){let t={};t.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)},t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map(e=>e.trim())},t.splitSections=function(e){return e.split("\nm=").map((e,t)=>(t>0?"m="+e:e).trim()+"\r\n")},t.getDescription=function(e){let r=t.splitSections(e);return r&&r[0]},t.getMediaSections=function(e){let r=t.splitSections(e);return r.shift(),r},t.matchPrefix=function(e,r){return t.splitLines(e).filter(e=>0===e.indexOf(r))},t.parseCandidate=function(e){let t;let r={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":r.relatedAddress=t[e+1];break;case"rport":r.relatedPort=parseInt(t[e+1],10);break;case"tcptype":r.tcpType=t[e+1];break;case"ufrag":r.ufrag=t[e+1],r.usernameFragment=t[e+1];break;default:void 0===r[t[e]]&&(r[t[e]]=t[e+1])}return r},t.writeCandidate=function(e){let t=[];t.push(e.foundation);let r=e.component;"rtp"===r?t.push(1):"rtcp"===r?t.push(2):t.push(r),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);let n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substring(14).split(" ")},t.parseRtpMap=function(e){let t=e.substring(9).split(" "),r={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),r.name=t[0],r.clockRate=parseInt(t[1],10),r.channels=3===t.length?parseInt(t[2],10):1,r.numChannels=r.channels,r},t.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);let r=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==r?"/"+r:"")+"\r\n"},t.parseExtmap=function(e){let t=e.substring(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1],attributes:t.slice(2).join(" ")}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+(e.attributes?" "+e.attributes:"")+"\r\n"},t.parseFmtp=function(e){let t;let r={},n=e.substring(e.indexOf(" ")+1).split(";");for(let e=0;e<n.length;e++)r[(t=n[e].trim().split("="))[0].trim()]=t[1];return r},t.writeFmtp=function(e){let t="",r=e.payloadType;if(void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){let n=[];Object.keys(e.parameters).forEach(t=>{void 0!==e.parameters[t]?n.push(t+"="+e.parameters[t]):n.push(t)}),t+="a=fmtp:"+r+" "+n.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){let t=e.substring(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){let t="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(e=>{t+="a=rtcp-fb:"+r+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},t.parseSsrcMedia=function(e){let t=e.indexOf(" "),r={ssrc:parseInt(e.substring(7,t),10)},n=e.indexOf(":",t);return n>-1?(r.attribute=e.substring(t+1,n),r.value=e.substring(n+1)):r.attribute=e.substring(t+1),r},t.parseSsrcGroup=function(e){let t=e.substring(13).split(" ");return{semantics:t.shift(),ssrcs:t.map(e=>parseInt(e,10))}},t.getMid=function(e){let r=t.matchPrefix(e,"a=mid:")[0];if(r)return r.substring(6)},t.parseFingerprint=function(e){let t=e.substring(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},t.getDtlsParameters=function(e,r){return{role:"auto",fingerprints:t.matchPrefix(e+r,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){let r="a=setup:"+t+"\r\n";return e.fingerprints.forEach(e=>{r+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),r},t.parseCryptoLine=function(e){let t=e.substring(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;let t=e.substring(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,r){return t.matchPrefix(e+r,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,r){let n=t.matchPrefix(e+r,"a=ice-ufrag:")[0],i=t.matchPrefix(e+r,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substring(12),password:i.substring(10)}:null},t.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},t.parseRtpParameters=function(e){let r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=t.splitLines(e)[0].split(" ");r.profile=n[2];for(let i=3;i<n.length;i++){let o=n[i],a=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(a){let n=t.parseRtpMap(a),i=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(n.parameters=i.length?t.parseFmtp(i[0]):{},n.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),r.codecs.push(n),n.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(n.name.toUpperCase())}}}t.matchPrefix(e,"a=extmap:").forEach(e=>{r.headerExtensions.push(t.parseExtmap(e))});let i=t.matchPrefix(e,"a=rtcp-fb:* ").map(t.parseRtcpFb);return r.codecs.forEach(e=>{i.forEach(t=>{e.rtcpFeedback.find(e=>e.type===t.type&&e.parameter===t.parameter)||e.rtcpFeedback.push(t)})}),r},t.writeRtpDescription=function(e,r){let n="";n+="m="+e+" "+(r.codecs.length>0?"9":"0")+" "+(r.profile||"UDP/TLS/RTP/SAVPF")+" "+r.codecs.map(e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType).join(" ")+"\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\n",r.codecs.forEach(e=>{n+=t.writeRtpMap(e)+t.writeFmtp(e)+t.writeRtcpFb(e)});let i=0;return r.codecs.forEach(e=>{e.maxptime>i&&(i=e.maxptime)}),i>0&&(n+="a=maxptime:"+i+"\r\n"),r.headerExtensions&&r.headerExtensions.forEach(e=>{n+=t.writeExtmap(e)}),n},t.parseRtpEncodingParameters=function(e){let r;let n=[],i=t.parseRtpParameters(e),o=-1!==i.fecMechanisms.indexOf("RED"),a=-1!==i.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map(e=>t.parseSsrcMedia(e)).filter(e=>"cname"===e.attribute),c=s.length>0&&s[0].ssrc,l=t.matchPrefix(e,"a=ssrc-group:FID").map(e=>e.substring(17).split(" ").map(e=>parseInt(e,10)));l.length>0&&l[0].length>1&&l[0][0]===c&&(r=l[0][1]),i.codecs.forEach(e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let t={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&r&&(t.rtx={ssrc:r}),n.push(t),o&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:c,mechanism:a?"red+ulpfec":"red"},n.push(t))}}),0===n.length&&c&&n.push({ssrc:c});let d=t.matchPrefix(e,"b=");return d.length&&(d=0===d[0].indexOf("b=TIAS:")?parseInt(d[0].substring(7),10):0===d[0].indexOf("b=AS:")?950*parseInt(d[0].substring(5),10)-16e3:void 0,n.forEach(e=>{e.maxBitrate=d})),n},t.parseRtcpParameters=function(e){let r={},n=t.matchPrefix(e,"a=ssrc:").map(e=>t.parseSsrcMedia(e)).filter(e=>"cname"===e.attribute)[0];n&&(r.cname=n.value,r.ssrc=n.ssrc);let i=t.matchPrefix(e,"a=rtcp-rsize");r.reducedSize=i.length>0,r.compound=0===i.length;let o=t.matchPrefix(e,"a=rtcp-mux");return r.mux=o.length>0,r},t.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},t.parseMsid=function(e){let r;let n=t.matchPrefix(e,"a=msid:");if(1===n.length)return{stream:(r=n[0].substring(7).split(" "))[0],track:r[1]};let i=t.matchPrefix(e,"a=ssrc:").map(e=>t.parseSsrcMedia(e)).filter(e=>"msid"===e.attribute);if(i.length>0)return{stream:(r=i[0].value.split(" "))[0],track:r[1]}},t.parseSctpDescription=function(e){let r;let n=t.parseMLine(e),i=t.matchPrefix(e,"a=max-message-size:");i.length>0&&(r=parseInt(i[0].substring(19),10)),isNaN(r)&&(r=65536);let o=t.matchPrefix(e,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substring(12),10),protocol:n.fmt,maxMessageSize:r};let a=t.matchPrefix(e,"a=sctpmap:");if(a.length>0){let e=a[0].substring(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:r}}},t.writeSctpDescription=function(e,t){let r=[];return r="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&r.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),r.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,22)},t.writeSessionBoilerplate=function(e,r,n){return"v=0\r\no="+(n||"thisisadapterortc")+" "+(e||t.generateSessionId())+" "+(void 0!==r?r:2)+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.getDirection=function(e,r){let n=t.splitLines(e);for(let e=0;e<n.length;e++)switch(n[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[e].substring(2)}return r?t.getDirection(r):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substring(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){let r=t.splitLines(e)[0].substring(2).split(" ");return{kind:r[0],port:parseInt(r[1],10),protocol:r[2],fmt:r.slice(3).join(" ")}},t.parseOLine=function(e){let r=t.matchPrefix(e,"o=")[0].substring(2).split(" ");return{username:r[0],sessionId:r[1],sessionVersion:parseInt(r[2],10),netType:r[3],addressType:r[4],address:r[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;let r=t.splitLines(e);for(let e=0;e<r.length;e++)if(r[e].length<2||"="!==r[e].charAt(1))return!1;return!0},e.exports=t},55026:function(e,t,r){var n=r(2265);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))});t.Z=i},9734:function(e,t,r){var n=r(2265);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))});t.Z=i},89399:function(e,t,r){var n=r(2265);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))});t.Z=i},12087:function(e,t,r){var n=r(2265);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))});t.Z=i},962:function(e,t,r){r.d(t,{Fw:function(){return v},VY:function(){return I},fC:function(){return R},p_:function(){return f},wy:function(){return y},xz:function(){return M}});var n=r(2265),i=r(6741),o=r(73966),a=r(80886),s=r(61188),c=r(98575),l=r(66840),d=r(71599),p=r(99255),u=r(57437),h="Collapsible",[g,f]=(0,o.b)(h),[m,C]=g(h),w=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:i,defaultOpen:o,disabled:s,onOpenChange:c,...d}=e,[g,f]=(0,a.T)({prop:i,defaultProp:null!=o&&o,onChange:c,caller:h});return(0,u.jsx)(m,{scope:r,disabled:s,contentId:(0,p.M)(),open:g,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),children:(0,u.jsx)(l.WV.div,{"data-state":B(g),"data-disabled":s?"":void 0,...d,ref:t})})});w.displayName=h;var A="CollapsibleTrigger",y=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,o=C(A,r);return(0,u.jsx)(l.WV.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":B(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...n,ref:t,onClick:(0,i.M)(e.onClick,o.onOpenToggle)})});y.displayName=A;var E="CollapsibleContent",v=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,i=C(E,e.__scopeCollapsible);return(0,u.jsx)(d.z,{present:r||i.open,children:e=>{let{present:r}=e;return(0,u.jsx)(T,{...n,ref:t,present:r})}})});v.displayName=E;var T=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:i,children:o,...a}=e,d=C(E,r),[p,h]=n.useState(i),g=n.useRef(null),f=(0,c.e)(t,g),m=n.useRef(0),w=m.current,A=n.useRef(0),y=A.current,v=d.open||p,T=n.useRef(v),R=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>T.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.b)(()=>{let e=g.current;if(e){R.current=R.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();m.current=t.height,A.current=t.width,T.current||(e.style.transitionDuration=R.current.transitionDuration,e.style.animationName=R.current.animationName),h(i)}},[d.open,i]),(0,u.jsx)(l.WV.div,{"data-state":B(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!v,...a,ref:f,style:{"--radix-collapsible-content-height":w?"".concat(w,"px"):void 0,"--radix-collapsible-content-width":y?"".concat(y,"px"):void 0,...e.style},children:v&&o})});function B(e){return e?"open":"closed"}var R=w,M=y,I=v},90175:function(e,t,r){r.d(t,{ck:function(){return I},fC:function(){return M}});var n=r(2265),i=r(73966),o=r(66840),a=r(1353),s=r(9987),c=r(80886),l=r(29114),d=r(57437),p="ToggleGroup",[u,h]=(0,i.b)(p,[a.Pc]),g=(0,a.Pc)(),f=n.forwardRef((e,t)=>{let{type:r,...n}=e;if("single"===r)return(0,d.jsx)(w,{...n,ref:t});if("multiple"===r)return(0,d.jsx)(A,{...n,ref:t});throw Error("Missing prop `type` expected on `".concat(p,"`"))});f.displayName=p;var[m,C]=u(p),w=n.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:o=()=>{},...a}=e,[s,l]=(0,c.T)({prop:r,defaultProp:null!=i?i:"",onChange:o,caller:p});return(0,d.jsx)(m,{scope:e.__scopeToggleGroup,type:"single",value:n.useMemo(()=>s?[s]:[],[s]),onItemActivate:l,onItemDeactivate:n.useCallback(()=>l(""),[l]),children:(0,d.jsx)(v,{...a,ref:t})})}),A=n.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:o=()=>{},...a}=e,[s,l]=(0,c.T)({prop:r,defaultProp:null!=i?i:[],onChange:o,caller:p}),u=n.useCallback(e=>l(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[l]),h=n.useCallback(e=>l(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[l]);return(0,d.jsx)(m,{scope:e.__scopeToggleGroup,type:"multiple",value:s,onItemActivate:u,onItemDeactivate:h,children:(0,d.jsx)(v,{...a,ref:t})})});f.displayName=p;var[y,E]=u(p),v=n.forwardRef((e,t)=>{let{__scopeToggleGroup:r,disabled:n=!1,rovingFocus:i=!0,orientation:s,dir:c,loop:p=!0,...u}=e,h=g(r),f=(0,l.gm)(c),m={role:"group",dir:f,...u};return(0,d.jsx)(y,{scope:r,rovingFocus:i,disabled:n,children:i?(0,d.jsx)(a.fC,{asChild:!0,...h,orientation:s,dir:f,loop:p,children:(0,d.jsx)(o.WV.div,{...m,ref:t})}):(0,d.jsx)(o.WV.div,{...m,ref:t})})}),T="ToggleGroupItem",B=n.forwardRef((e,t)=>{let r=C(T,e.__scopeToggleGroup),i=E(T,e.__scopeToggleGroup),o=g(e.__scopeToggleGroup),s=r.value.includes(e.value),c=i.disabled||e.disabled,l={...e,pressed:s,disabled:c},p=n.useRef(null);return i.rovingFocus?(0,d.jsx)(a.ck,{asChild:!0,...o,focusable:!c,active:s,ref:p,children:(0,d.jsx)(R,{...l,ref:t})}):(0,d.jsx)(R,{...l,ref:t})});B.displayName=T;var R=n.forwardRef((e,t)=>{let{__scopeToggleGroup:r,value:n,...i}=e,o=C(T,r),a={role:"radio","aria-checked":e.pressed,"aria-pressed":void 0},c="single"===o.type?a:void 0;return(0,d.jsx)(s.Z,{...c,...i,ref:t,onPressedChange:e=>{e?o.onItemActivate(n):o.onItemDeactivate(n)}})}),M=f,I=B},9987:function(e,t,r){r.d(t,{Z:function(){return l},f:function(){return d}});var n=r(2265),i=r(6741),o=r(80886),a=r(66840),s=r(57437),c="Toggle",l=n.forwardRef((e,t)=>{let{pressed:r,defaultPressed:n,onPressedChange:l,...d}=e,[p,u]=(0,o.T)({prop:r,onChange:l,defaultProp:null!=n&&n,caller:c});return(0,s.jsx)(a.WV.button,{type:"button","aria-pressed":p,"data-state":p?"on":"off","data-disabled":e.disabled?"":void 0,...d,ref:t,onClick:(0,i.M)(e.onClick,()=>{e.disabled||u(!p)})})});l.displayName=c;var d=l},24190:function(e,t,r){r.d(t,{ZY:function(){return td}});var n,i={};r.r(i),r.d(i,{fixNegotiationNeeded:function(){return I},shimAddTrackRemoveTrack:function(){return R},shimAddTrackRemoveTrackWithNative:function(){return B},shimGetSendersWithDtmf:function(){return v},shimGetUserMedia:function(){return A},shimMediaStream:function(){return y},shimOnTrack:function(){return E},shimPeerConnection:function(){return M},shimSenderReceiverGetStats:function(){return T}});var o={};r.r(o),r.d(o,{shimAddTransceiver:function(){return V},shimCreateAnswer:function(){return N},shimCreateOffer:function(){return x},shimGetDisplayMedia:function(){return b},shimGetParameters:function(){return S},shimGetUserMedia:function(){return D},shimOnTrack:function(){return F},shimPeerConnection:function(){return q},shimRTCDataChannel:function(){return Y},shimReceiverGetStats:function(){return G},shimRemoveStream:function(){return P},shimSenderGetStats:function(){return k}});var a={};r.r(a),r.d(a,{shimAudioContext:function(){return Z},shimCallbacksAPI:function(){return O},shimConstraints:function(){return U},shimCreateOfferLegacy:function(){return W},shimGetUserMedia:function(){return K},shimLocalStreamsAPI:function(){return Q},shimRTCIceServerUrls:function(){return j},shimRemoteStreamsAPI:function(){return L},shimTrackEventTransceiver:function(){return J}});var s={};r.r(s),r.d(s,{removeExtmapAllowMixed:function(){return er},shimAddIceCandidateNullOrEmpty:function(){return en},shimConnectionState:function(){return et},shimMaxMessageSize:function(){return $},shimParameterlessSetLocalDescription:function(){return ei},shimRTCIceCandidate:function(){return z},shimRTCIceCandidateRelayProtocol:function(){return _},shimSendThrowTypeError:function(){return ee}});var c=r(2265);let l=!0,d=!0;function p(e,t,r){let n=e.match(t);return n&&n.length>=r&&parseFloat(n[r],10)}function u(e,t,r){if(!e.RTCPeerConnection)return;let n=e.RTCPeerConnection.prototype,i=n.addEventListener;n.addEventListener=function(e,n){if(e!==t)return i.apply(this,arguments);let o=e=>{let t=r(e);t&&(n.handleEvent?n.handleEvent(t):n(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(n,o),i.apply(this,[e,o])};let o=n.removeEventListener;n.removeEventListener=function(e,r){if(e!==t||!this._eventMap||!this._eventMap[t]||!this._eventMap[t].has(r))return o.apply(this,arguments);let n=this._eventMap[t].get(r);return this._eventMap[t].delete(r),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,n])},Object.defineProperty(n,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function h(e){return"boolean"!=typeof e?Error("Argument type: "+typeof e+". Please use a boolean."):(l=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function g(e){return"boolean"!=typeof e?Error("Argument type: "+typeof e+". Please use a boolean."):(d=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function f(){"object"!=typeof window||l||"undefined"==typeof console||"function"!=typeof console.log||console.log.apply(console,arguments)}function m(e,t){d&&console.warn(e+" is deprecated, please use "+t+" instead.")}function C(e){return"[object Object]"===Object.prototype.toString.call(e)}function w(e,t,r){let n=r?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;let o=[];return e.forEach(e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)}),o.forEach(t=>{e.forEach(r=>{r.type===n&&r.trackId===t.id&&function e(t,r,n){!r||n.has(r.id)||(n.set(r.id,r),Object.keys(r).forEach(i=>{i.endsWith("Id")?e(t,t.get(r[i]),n):i.endsWith("Ids")&&r[i].forEach(r=>{e(t,t.get(r),n)})}))}(e,r,i)})}),i}function A(e,t){let r=e&&e.navigator;if(!r.mediaDevices)return;let n=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;let t={};return Object.keys(e).forEach(r=>{if("require"===r||"advanced"===r||"mediaSource"===r)return;let n="object"==typeof e[r]?e[r]:{ideal:e[r]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);let i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==n.ideal){t.optional=t.optional||[];let e={};"number"==typeof n.ideal?(e[i("min",r)]=n.ideal,t.optional.push(e),(e={})[i("max",r)]=n.ideal):e[i("",r)]=n.ideal,t.optional.push(e)}void 0!==n.exact&&"number"!=typeof n.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",r)]=n.exact):["min","max"].forEach(e=>{void 0!==n[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,r)]=n[e])})}),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},i=function(e,i){if(t.version>=61)return i(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){let t=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=n(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});let a=t.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&!(r.mediaDevices.getSupportedConstraints&&r.mediaDevices.getSupportedConstraints().facingMode&&!a)){let t;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?t=["back","rear"]:("user"===o.exact||"user"===o.ideal)&&(t=["front"]),t)return r.mediaDevices.enumerateDevices().then(r=>{let a=(r=r.filter(e=>"videoinput"===e.kind)).find(e=>t.some(t=>e.label.toLowerCase().includes(t)));return!a&&r.length&&t.includes("back")&&(a=r[r.length-1]),a&&(e.video.deviceId=o.exact?{exact:a.deviceId}:{ideal:a.deviceId}),e.video=n(e.video),f("chrome: "+JSON.stringify(e)),i(e)})}e.video=n(e.video)}return f("chrome: "+JSON.stringify(e)),i(e)},o=function(e){return t.version>=64?e:{name:({PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"})[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(r.getUserMedia=(function(e,t,n){i(e,e=>{r.webkitGetUserMedia(e,t,e=>{n&&n(o(e))})})}).bind(r),r.mediaDevices.getUserMedia){let e=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(t){return i(t,t=>e(t).then(e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(e=>{e.stop()}),new DOMException("","NotFoundError");return e},e=>Promise.reject(o(e))))}}}function y(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function E(e){if("object"!=typeof e||!e.RTCPeerConnection||"ontrack"in e.RTCPeerConnection.prototype)u(e,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e));else{Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});let t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===r.track.id):{track:r.track};let i=new Event("track");i.track=r.track,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)}),t.stream.getTracks().forEach(r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===r.id):{track:r};let i=new Event("track");i.track=r,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}}function v(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){let t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};let r=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){let i=r.apply(this,arguments);return i||(i=t(this,e),this._senders.push(i)),i};let n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){n.apply(this,arguments);let t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}let r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach(e=>{this._senders.push(t(this,e))})};let n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach(e=>{let t=this._senders.find(t=>t.track===e);t&&this._senders.splice(this._senders.indexOf(t),1)})}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){let t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function T(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){let t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});let r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){let e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){let e=this;return this._pc.getStats().then(t=>w(t,e.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){let t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),u(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){let e=this;return this._pc.getStats().then(t=>w(t,e.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype))return;let t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){let e,t,r;let n=arguments[0];return(this.getSenders().forEach(t=>{t.track===n&&(e?r=!0:e=t)}),this.getReceivers().forEach(e=>(e.track===n&&(t?r=!0:t=e),e.track===n)),r||e&&t)?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):e?e.getStats():t?t.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function B(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(e=>this._shimmedLocalStreams[e][0])};let t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){if(!r)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};let n=t.apply(this,arguments);return this._shimmedLocalStreams[r.id]?-1===this._shimmedLocalStreams[r.id].indexOf(n)&&this._shimmedLocalStreams[r.id].push(n):this._shimmedLocalStreams[r.id]=[r,n],n};let r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")});let t=this.getSenders();r.apply(this,arguments);let n=this.getSenders().filter(e=>-1===t.indexOf(e));this._shimmedLocalStreams[e.id]=[e].concat(n)};let n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],n.apply(this,arguments)};let i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach(t=>{let r=this._shimmedLocalStreams[t].indexOf(e);-1!==r&&this._shimmedLocalStreams[t].splice(r,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]}),i.apply(this,arguments)}}function R(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return B(e);let r=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){let e=r.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(e=>this._reverseStreams[e.id])};let n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[t.id]){let r=new e.MediaStream(t.getTracks());this._streams[t.id]=r,this._reverseStreams[r.id]=t,t=r}n.apply(this,[t])};let i=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let r=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{let n=e._reverseStreams[t],i=e._streams[n.id];r=r.replace(RegExp(i.id,"g"),n.id)}),new RTCSessionDescription({type:t.type,sdp:r})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,r){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");let n=[].slice.call(arguments,1);if(1!==n.length||!n[0].getTracks().find(e=>e===t))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(e=>e.track===t))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};let i=this._streams[r.id];if(i)i.addTrack(t),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{let n=new e.MediaStream([t]);this._streams[r.id]=n,this._reverseStreams[n.id]=r,this.addStream(n)}return this.getSenders().find(e=>e.track===t)},["createOffer","createAnswer"].forEach(function(t){let r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=({[t](){let e=arguments,t=arguments.length&&"function"==typeof arguments[0];return t?r.apply(this,[t=>{let r=o(this,t);e[0].apply(null,[r])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):r.apply(this,arguments).then(e=>o(this,e))}})[t]});let a=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){var e,t;let r;return arguments.length&&arguments[0].type&&(arguments[0]=(e=this,t=arguments[0],r=t.sdp,Object.keys(e._reverseStreams||[]).forEach(t=>{let n=e._reverseStreams[t],i=e._streams[n.id];r=r.replace(RegExp(n.id,"g"),i.id)}),new RTCSessionDescription({type:t.type,sdp:r}))),a.apply(this,arguments)};let s=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){let e=s.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){let t;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(e._pc!==this)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{},Object.keys(this._streams).forEach(r=>{this._streams[r].getTracks().find(t=>e.track===t)&&(t=this._streams[r])}),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function M(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){let r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=({[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}})[t]})}function I(e,t){u(e,"negotiationneeded",e=>{let r=e.target;if(!(t.version<72)&&(!r.getConfiguration||"plan-b"!==r.getConfiguration().sdpSemantics)||"stable"===r.signalingState)return e})}function D(e,t){let r=e&&e.navigator,n=e&&e.MediaStreamTrack;if(r.getUserMedia=function(e,t,n){m("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(e).then(t,n)},!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){let e=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])},t=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(r){return"object"==typeof r&&"object"==typeof r.audio&&(e((r=JSON.parse(JSON.stringify(r))).audio,"autoGainControl","mozAutoGainControl"),e(r.audio,"noiseSuppression","mozNoiseSuppression")),t(r)},n&&n.prototype.getSettings){let t=n.prototype.getSettings;n.prototype.getSettings=function(){let r=t.apply(this,arguments);return e(r,"mozAutoGainControl","autoGainControl"),e(r,"mozNoiseSuppression","noiseSuppression"),r}}if(n&&n.prototype.applyConstraints){let t=n.prototype.applyConstraints;n.prototype.applyConstraints=function(r){return"audio"===this.kind&&"object"==typeof r&&(e(r=JSON.parse(JSON.stringify(r)),"autoGainControl","mozAutoGainControl"),e(r,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[r])}}}}function b(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||!e.navigator.mediaDevices||(e.navigator.mediaDevices.getDisplayMedia=function(r){if(!(r&&r.video)){let e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===r.video?r.video={mediaSource:t}:r.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(r)})}function F(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function q(e,t){if("object"!=typeof e||!(e.RTCPeerConnection||e.mozRTCPeerConnection))return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){let r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=({[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}})[t]});let r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){let[e,i,o]=arguments;return n.apply(this,[e||null]).then(e=>{if(t.version<53&&!i)try{e.forEach(e=>{e.type=r[e.type]||e.type})}catch(t){if("TypeError"!==t.name)throw t;e.forEach((t,n)=>{e.set(n,Object.assign({},t,{type:r[t.type]||t.type}))})}return e}).then(i,o)}}function k(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;let t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});let r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){let e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function G(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;let t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){let e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),u(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function P(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){m("removeStream","removeTrack"),this.getSenders().forEach(t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)})})}function Y(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function V(e){if(!("object"==typeof e&&e.RTCPeerConnection))return;let t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let e=arguments[1]&&arguments[1].sendEncodings;void 0===e&&(e=[]);let r=(e=[...e]).length>0;r&&e.forEach(e=>{if("rid"in e&&!/^[a-z0-9]{0,16}$/i.test(e.rid))throw TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw RangeError("max_framerate must be >= 0.0")});let n=t.apply(this,arguments);if(r){let{sender:t}=n,r=t.getParameters();"encodings"in r&&(1!==r.encodings.length||0!==Object.keys(r.encodings[0]).length)||(r.encodings=e,t.sendEncodings=e,this.setParametersPromises.push(t.setParameters(r).then(()=>{delete t.sendEncodings}).catch(()=>{delete t.sendEncodings})))}return n})}function S(e){if(!("object"==typeof e&&e.RTCRtpSender))return;let t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){let e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function x(e){if(!("object"==typeof e&&e.RTCPeerConnection))return;let t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function N(e){if(!("object"==typeof e&&e.RTCPeerConnection))return;let t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function Q(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){let t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach(r=>t.call(this,r,e)),e.getVideoTracks().forEach(r=>t.call(this,r,e))},e.RTCPeerConnection.prototype.addTrack=function(e,...r){return r&&r.forEach(e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);let t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);let r=e.getTracks();this.getSenders().forEach(e=>{r.includes(e.track)&&this.removeTrack(e)})})}}function L(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach(e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);let t=new Event("addstream");t.stream=e,this.dispatchEvent(t)})})}});let t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){let e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach(t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);let r=new Event("addstream");r.stream=t,e.dispatchEvent(r)})}),t.apply(e,arguments)}}}function O(e){if("object"!=typeof e||!e.RTCPeerConnection)return;let t=e.RTCPeerConnection.prototype,r=t.createOffer,n=t.createAnswer,i=t.setLocalDescription,o=t.setRemoteDescription,a=t.addIceCandidate;t.createOffer=function(e,t){let n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){let r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i};let s=function(e,t,r){let n=i.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n};t.setLocalDescription=s,s=function(e,t,r){let n=o.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.setRemoteDescription=s,s=function(e,t,r){let n=a.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.addIceCandidate=s}function K(e){let t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){let e=t.mediaDevices,r=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>r(U(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=(function(e,r,n){t.mediaDevices.getUserMedia(e).then(r,n)}).bind(t))}function U(e){return e&&void 0!==e.video?Object.assign({},e,{video:function e(t){return C(t)?Object.keys(t).reduce(function(r,n){let i=C(t[n]),o=i?e(t[n]):t[n],a=i&&!Object.keys(o).length;return void 0===o||a?r:Object.assign(r,{[n]:o})},{}):t}(e.video)}):e}function j(e){if(!e.RTCPeerConnection)return;let t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,r){if(e&&e.iceServers){let t=[];for(let r=0;r<e.iceServers.length;r++){let n=e.iceServers[r];void 0===n.urls&&n.url?(m("RTCIceServer.url","RTCIceServer.urls"),(n=JSON.parse(JSON.stringify(n))).urls=n.url,delete n.url,t.push(n)):t.push(e.iceServers[r])}e.iceServers=t}return new t(e,r)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function J(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function W(e){let t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);let t=this.getTransceivers().find(e=>"audio"===e.receiver.track.kind);!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);let r=this.getTransceivers().find(e=>"video"===e.receiver.track.kind);!1===e.offerToReceiveVideo&&r?"sendrecv"===r.direction?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":"recvonly"===r.direction&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):!0!==e.offerToReceiveVideo||r||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function Z(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var H=r(40802),X=r.n(H);function z(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;let t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substring(2)),e.candidate&&e.candidate.length){let r=new t(e),n=X().parseCandidate(e.candidate);for(let e in n)e in r||Object.defineProperty(r,e,{value:n[e]});return r.toJSON=function(){return{candidate:r.candidate,sdpMid:r.sdpMid,sdpMLineIndex:r.sdpMLineIndex,usernameFragment:r.usernameFragment}},r}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,u(e,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t))}function _(e){!e.RTCIceCandidate||e.RTCIceCandidate&&"relayProtocol"in e.RTCIceCandidate.prototype||u(e,"icecandidate",e=>{if(e.candidate){let t=X().parseCandidate(e.candidate.candidate);"relay"===t.type&&(e.candidate.relayProtocol=({0:"tls",1:"tcp",2:"udp"})[t.priority>>24])}return e})}function $(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});let r=function(e){if(!e||!e.sdp)return!1;let t=X().splitSections(e.sdp);return t.shift(),t.some(e=>{let t=X().parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")})},n=function(e){let t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return -1;let r=parseInt(t[1],10);return r!=r?-1:r},i=function(e){let r=65536;return"firefox"===t.browser&&(r=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),r},o=function(e,r){let n=65536;"firefox"===t.browser&&57===t.version&&(n=65535);let i=X().matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?n=parseInt(i[0].substring(19),10):"firefox"===t.browser&&-1!==r&&(n=2147483637),n},a=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){let{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(r(arguments[0])){let e;let t=n(arguments[0]),r=i(t),a=o(arguments[0],t);e=0===r&&0===a?Number.POSITIVE_INFINITY:0===r||0===a?Math.max(r,a):Math.min(r,a);let s={};Object.defineProperty(s,"maxMessageSize",{get:()=>e}),this._sctp=s}return a.apply(this,arguments)}}function ee(e){if(!(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){let r=e.send;e.send=function(){let n=arguments[0],i=n.length||n.size||n.byteLength;if("open"===e.readyState&&t.sctp&&i>t.sctp.maxMessageSize)throw TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return r.apply(e,arguments)}}let r=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){let e=r.apply(this,arguments);return t(e,this),e},u(e,"datachannel",e=>(t(e.channel,e.target),e))}function et(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;let t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return({completed:"connected",checking:"connecting"})[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(e=>{let r=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{let t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;let r=new Event("connectionstatechange",e);t.dispatchEvent(r)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),r.apply(this,arguments)}})}function er(e,t){if(!e.RTCPeerConnection||"chrome"===t.browser&&t.version>=71||"safari"===t.browser&&t._safariVersion>=13.1)return;let r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){let r=t.sdp.split("\n").filter(e=>"a=extmap-allow-mixed"!==e.trim()).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:r}):t.sdp=r}return r.apply(this,arguments)}}function en(e,t){if(!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype))return;let r=e.RTCPeerConnection.prototype.addIceCandidate;r&&0!==r.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function ei(e,t){if(!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype))return;let r=e.RTCPeerConnection.prototype.setLocalDescription;r&&0!==r.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return r.apply(this,arguments);if(!(e={type:e.type,sdp:e.sdp}).type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}return e.sdp||"offer"!==e.type&&"answer"!==e.type?r.apply(this,[e]):("offer"===e.type?this.createOffer:this.createAnswer).apply(this).then(e=>r.apply(this,[e]))})}!function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){let r=function(e){let t={browser:null,version:null};if(void 0===e||!e.navigator||!e.navigator.userAgent)return t.browser="Not a browser.",t;let{navigator:r}=e;if(r.userAgentData&&r.userAgentData.brands){let e=r.userAgentData.brands.find(e=>"Chromium"===e.brand);if(e)return{browser:"chrome",version:parseInt(e.version,10)}}return r.mozGetUserMedia?(t.browser="firefox",t.version=parseInt(p(r.userAgent,/Firefox\/(\d+)\./,1))):r.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection?(t.browser="chrome",t.version=parseInt(p(r.userAgent,/Chrom(e|ium)\/(\d+)\./,2))):e.RTCPeerConnection&&r.userAgent.match(/AppleWebKit\/(\d+)\./)?(t.browser="safari",t.version=parseInt(p(r.userAgent,/AppleWebKit\/(\d+)\./,1)),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype,t._safariVersion=p(r.userAgent,/Version\/(\d+(\.?\d+))/,1)):t.browser="Not a supported browser.",t}(e),n={browserDetails:r,commonShim:s,extractVersion:p,disableLog:h,disableWarnings:g,sdp:H};switch(r.browser){case"chrome":if(!i||!M||!t.shimChrome){f("Chrome shim is not included in this adapter release.");break}if(null===r.version){f("Chrome shim can not determine version, not shimming.");break}f("adapter.js shimming chrome."),n.browserShim=i,en(e,r),ei(e,r),A(e,r),y(e,r),M(e,r),E(e,r),R(e,r),v(e,r),T(e,r),I(e,r),z(e,r),_(e,r),et(e,r),$(e,r),ee(e,r),er(e,r);break;case"firefox":if(!o||!q||!t.shimFirefox){f("Firefox shim is not included in this adapter release.");break}f("adapter.js shimming firefox."),n.browserShim=o,en(e,r),ei(e,r),D(e,r),q(e,r),F(e,r),P(e,r),k(e,r),G(e,r),Y(e,r),V(e,r),S(e,r),x(e,r),N(e,r),z(e,r),et(e,r),$(e,r),ee(e,r);break;case"safari":if(!a||!t.shimSafari){f("Safari shim is not included in this adapter release.");break}f("adapter.js shimming safari."),n.browserShim=a,en(e,r),ei(e,r),j(e,r),W(e,r),O(e,r),Q(e,r),L(e,r),J(e,r),K(e,r),Z(e,r),z(e,r),_(e,r),$(e,r),ee(e,r),er(e,r);break;default:f("Unsupported browser!")}}({window:"undefined"==typeof window?void 0:window});var eo=r(40257),ea=e=>{throw TypeError(e)},es=(e,t,r)=>t.has(e)||ea("Cannot "+r),ec=(e,t,r)=>(es(e,t,"read from private field"),r?r.call(e):t.get(e)),el=(e,t,r)=>t.has(e)?ea("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),ed=(e,t,r,n)=>(es(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);let ep=[["Aztec","M"],["Codabar","L"],["Code39","L"],["Code93","L"],["Code128","L"],["DataBar","L"],["DataBarExpanded","L"],["DataMatrix","M"],["EAN-8","L"],["EAN-13","L"],["ITF","L"],["MaxiCode","M"],["PDF417","M"],["QRCode","M"],["UPC-A","L"],["UPC-E","L"],["MicroQRCode","M"],["rMQRCode","M"],["DXFilmEdge","L"],["DataBarLimited","L"]],eu=ep.map(([e])=>e),eh=eu.filter((e,t)=>"L"===ep[t][1]),eg=eu.filter((e,t)=>"M"===ep[t][1]),ef=["LocalAverage","GlobalHistogram","FixedThreshold","BoolCast"],em=["Unknown","ASCII","ISO8859_1","ISO8859_2","ISO8859_3","ISO8859_4","ISO8859_5","ISO8859_6","ISO8859_7","ISO8859_8","ISO8859_9","ISO8859_10","ISO8859_11","ISO8859_13","ISO8859_14","ISO8859_15","ISO8859_16","Cp437","Cp1250","Cp1251","Cp1252","Cp1256","Shift_JIS","Big5","GB2312","GB18030","EUC_JP","EUC_KR","UTF16BE","UTF16BE","UTF8","UTF16LE","UTF32BE","UTF32LE","BINARY"],eC=["Text","Binary","Mixed","GS1","ISO15434","UnknownECI"],ew=["Ignore","Read","Require"],eA=["Plain","ECI","HRI","Hex","Escaped"],ey={formats:[],tryHarder:!0,tryRotate:!0,tryInvert:!0,tryDownscale:!0,tryDenoise:!1,binarizer:"LocalAverage",isPure:!1,downscaleFactor:3,downscaleThreshold:500,minLineCount:2,maxNumberOfSymbols:255,tryCode39ExtendedMode:!0,returnErrors:!1,eanAddOnSymbol:"Ignore",textMode:"HRI",characterSet:"Unknown"};function eE(e){var t,r,n,i;return{...e,formats:e.formats.reduce((e,t)=>e|function e(t){switch(t){case"Linear-Codes":return eh.reduce((t,r)=>t|e(r),0);case"Matrix-Codes":return eg.reduce((t,r)=>t|e(r),0);case"Any":return(1<<ep.length)-1;case"None":return 0;default:return 1<<eu.indexOf(t)}}(t),0),binarizer:(t=e.binarizer,ef.indexOf(t)),eanAddOnSymbol:(r=e.eanAddOnSymbol,ew.indexOf(r)),textMode:(n=e.textMode,eA.indexOf(n)),characterSet:"UnicodeBig"===(i=e.characterSet)?em.indexOf("UTF16BE"):em.indexOf(i)}}let ev={locateFile:(e,t)=>{let r=e.match(/_(.+?)\.wasm$/);return r?`https://fastly.jsdelivr.net/npm/zxing-wasm@2.1.2/dist/${r[1]}/${e}`:t+e}},eT=new WeakMap;function eB(e,t){return Object.is(e,t)||Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>Object.prototype.hasOwnProperty.call(t,r)&&e[r]===t[r])}function eR(e,{overrides:t,equalityFn:r=eB,fireImmediately:n=!1}={}){var i;let o;let[a,s]=null!=(i=eT.get(e))?i:[ev],c=null!=t?t:a;if(n){if(s&&(o=r(a,c)))return s;let t=e({...c});return eT.set(e,[c,t]),t}(null!=o?o:r(a,c))||eT.set(e,[c])}async function eM(e,t,r=ey){let n,i;let o={...ey,...r},a=await eR(e,{fireImmediately:!0});if("width"in t&&"height"in t&&"data"in t){let{data:e,data:{byteLength:r},width:s,height:c}=t;i=a._malloc(r),a.HEAPU8.set(e,i),n=a.readBarcodesFromPixmap(i,s,c,eE(o))}else{let e,r;if("buffer"in t)[e,r]=[t.byteLength,t];else if("byteLength"in t)[e,r]=[t.byteLength,new Uint8Array(t)];else if("size"in t)[e,r]=[t.size,new Uint8Array(await t.arrayBuffer())];else throw TypeError("Invalid input type");i=a._malloc(e),a.HEAPU8.set(r,i),n=a.readBarcodesFromImage(i,e,eE(o))}a._free(i);let s=[];for(let e=0;e<n.size();++e)s.push(function(e){var t;return{...e,format:0===(t=e.format)?"None":eu[31-Math.clz32(t)],contentType:eC[e.contentType],eccLevel:e.ecLevel}}(n.get(e)));return s}({...ey,formats:[...ey.formats]});var eI=async function(e={}){var t,r,n=new Promise((e,n)=>{t=e,r=n}),i="object"==typeof window,o="u">typeof Bun,a="u">typeof WorkerGlobalScope;"object"==typeof eo&&"object"==typeof eo.versions&&"string"==typeof eo.versions.node&&eo.type;var s="./this.program",c="";(i||a||o)&&(a?c=self.location.href:"u">typeof document&&document.currentScript&&(c=document.currentScript.src),c=c.startsWith("blob:")?"":c.slice(0,c.replace(/[?#].*/,"").lastIndexOf("/")+1),a&&(d=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),l=async e=>{var t=await fetch(e,{credentials:"same-origin"});if(t.ok)return t.arrayBuffer();throw Error(t.status+" : "+t.url)});var l,d,p,u,h,g,f,m,C,w,A,y,E=console.log.bind(console),v=console.error.bind(console),T=!1;function B(){var t=u.buffer;h=new Int8Array(t),f=new Int16Array(t),e.HEAPU8=g=new Uint8Array(t),m=new Uint16Array(t),C=new Int32Array(t),w=new Uint32Array(t),A=new Float32Array(t),y=new Float64Array(t)}var R=0,M=null;function I(t){null===(n=e.onAbort)||void 0===n||n.call(e,t),v(t="Aborted("+t+")"),T=!0,t+=". Build with -sASSERTIONS for more info.";var n,i=new WebAssembly.RuntimeError(t);throw r(i),i}async function D(e){if(!p)try{var t=await l(e);return new Uint8Array(t)}catch{}return function(e){if(e==Z&&p)return new Uint8Array(p);if(d)return d(e);throw"both async and sync fetching of the wasm failed"}(e)}async function b(e,t){try{var r=await D(e);return await WebAssembly.instantiate(r,t)}catch(e){v(`failed to asynchronously prepare wasm: ${e}`),I(e)}}async function F(e,t,r){if(!e&&"function"==typeof WebAssembly.instantiateStreaming)try{var n=fetch(t,{credentials:"same-origin"});return await WebAssembly.instantiateStreaming(n,r)}catch(e){v(`wasm streaming compile failed: ${e}`),v("falling back to ArrayBuffer instantiation")}return b(t,r)}async function q(){function t(t,r){return u=(tA=t.exports).xa,B(),eq=tA.Ba,function(){var t;if(R--,null===(t=e.monitorRunDependencies)||void 0===t||t.call(e,R),0==R&&M){var r=M;M=null,r()}}(),tA}R++,null===(n=e.monitorRunDependencies)||void 0===n||n.call(e,R);var n,i,o,a={a:tw};if(e.instantiateWasm)return new Promise((r,n)=>{e.instantiateWasm(a,(e,n)=>{r(t(e))})});null!=Z||(i="zxing_reader.wasm",Z=e.locateFile?e.locateFile(i,c):c+i);try{return o=await F(p,Z,a),t(o.instance)}catch(e){return r(e),Promise.reject(e)}}var k=t=>{for(;t.length>0;)t.shift()(e)},G=[],P=e=>G.push(e),Y=[],V=e=>Y.push(e),S=e=>tM(e),x=()=>tI(),N=[],Q=0,L=0;class O{constructor(e){this.excPtr=e,this.ptr=e-24}set_type(e){w[this.ptr+4>>2]=e}get_type(){return w[this.ptr+4>>2]}set_destructor(e){w[this.ptr+8>>2]=e}get_destructor(){return w[this.ptr+8>>2]}set_caught(e){e=e?1:0,h[this.ptr+12]=e}get_caught(){return 0!=h[this.ptr+12]}set_rethrown(e){e=e?1:0,h[this.ptr+13]=e}get_rethrown(){return 0!=h[this.ptr+13]}init(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t)}set_adjusted_ptr(e){w[this.ptr+16>>2]=e}get_adjusted_ptr(){return w[this.ptr+16>>2]}}var K=e=>tR(e),U=e=>{var t=L;if(!t)return K(0),0;var r=new O(t);r.set_adjusted_ptr(t);var n=r.get_type();if(!n)return K(0),t;for(var i of e){if(0===i||i===n)break;if(tF(i,n,r.ptr+16))return K(i),t}return K(n),t},j={},J=e=>{for(;e.length;){var t=e.pop();e.pop()(t)}};function W(e){return this.fromWireType(w[e>>2])}var Z,H,X={},z={},_={},$=e.InternalError=class extends Error{constructor(e){super(e),this.name="InternalError"}},ee=e=>{throw new $(e)},et=(e,t,r)=>{function n(t){var n=r(t);n.length!==e.length&&ee("Mismatched type converter count");for(var i=0;i<e.length;++i)ea(e[i],n[i])}e.forEach(e=>_[e]=t);var i=Array(t.length),o=[],a=0;t.forEach((e,t)=>{z.hasOwnProperty(e)?i[t]=z[e]:(o.push(e),X.hasOwnProperty(e)||(X[e]=[]),X[e].push(()=>{i[t]=z[e],++a===o.length&&n(i)}))}),0===o.length&&n(i)},er=e=>{for(var t="",r=e;g[r];)t+=H[g[r++]];return t},en=e.BindingError=class extends Error{constructor(e){super(e),this.name="BindingError"}},ei=e=>{throw new en(e)};function ea(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var n=t.name;if(e||ei(`type "${n}" must have a positive integer typeid pointer`),z.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;ei(`Cannot register type '${n}' twice`)}if(z[e]=t,delete _[e],X.hasOwnProperty(e)){var i=X[e];delete X[e],i.forEach(e=>e())}}(e,t,r)}var es=e=>({count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}),ec=e=>{ei(e.$$.ptrType.registeredClass.name+" instance already deleted")},el=!1,ed=e=>{},ep=e=>{e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)},eu=e=>{e.count.value-=1,0===e.count.value&&ep(e)},eh=e=>typeof FinalizationRegistry>"u"?(eh=e=>e,e):(el=new FinalizationRegistry(e=>{eu(e.$$)}),eh=e=>{var t=e.$$;return t.smartPtr&&el.register(e,{$$:t},e),e},ed=e=>el.unregister(e),eh(e));function eg(){}var ef=(e,t)=>Object.defineProperty(t,"name",{value:e}),em={},eC=(e,t,r)=>{if(void 0===e[t].overloadTable){var n=e[t];e[t]=function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e[t].overloadTable.hasOwnProperty(i.length)||ei(`Function '${r}' called with an invalid number of arguments (${i.length}) - expects one of (${e[t].overloadTable})!`),e[t].overloadTable[i.length].apply(this,i)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n}},ew=(t,r,n)=>{e.hasOwnProperty(t)?((void 0===n||void 0!==e[t].overloadTable&&void 0!==e[t].overloadTable[n])&&ei(`Cannot register public name '${t}' twice`),eC(e,t,t),e[t].overloadTable.hasOwnProperty(n)&&ei(`Cannot register multiple overloads of a function with the same number of arguments (${n})!`),e[t].overloadTable[n]=r):(e[t]=r,e[t].argCount=n)},eA=e=>{var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=48&&t<=57?`_${e}`:e};function ey(e,t,r,n,i,o,a,s){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=i,this.getActualType=o,this.upcast=a,this.downcast=s,this.pureVirtualFunctions=[]}var eE=(e,t,r)=>{for(;t!==r;)t.upcast||ei(`Expected null or instance of ${r.name}, got an instance of ${t.name}`),e=t.upcast(e),t=t.baseClass;return e};function ev(e,t){if(null===t)return this.isReference&&ei(`null is not a valid ${this.name}`),0;t.$$||ei(`Cannot pass "${embindRepr(t)}" as a ${this.name}`),t.$$.ptr||ei(`Cannot pass deleted object as a pointer of type ${this.name}`);var r=t.$$.ptrType.registeredClass;return eE(t.$$.ptr,r,this.registeredClass)}function eT(e,t){if(null===t)return this.isReference&&ei(`null is not a valid ${this.name}`),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t&&t.$$||ei(`Cannot pass "${embindRepr(t)}" as a ${this.name}`),t.$$.ptr||ei(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&t.$$.ptrType.isConst&&ei(`Cannot convert argument of type ${t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name} to parameter type ${this.name}`);var r,n=t.$$.ptrType.registeredClass;if(r=eE(t.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&ei("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:ei(`Cannot convert argument of type ${t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var i=t.clone();r=this.rawShare(r,eW.toHandle(()=>i.delete())),null!==e&&e.push(this.rawDestructor,r)}break;default:ei("Unsupporting sharing policy")}return r}function eB(e,t){if(null===t)return this.isReference&&ei(`null is not a valid ${this.name}`),0;t.$$||ei(`Cannot pass "${embindRepr(t)}" as a ${this.name}`),t.$$.ptr||ei(`Cannot pass deleted object as a pointer of type ${this.name}`),t.$$.ptrType.isConst&&ei(`Cannot convert argument of type ${t.$$.ptrType.name} to parameter type ${this.name}`);var r=t.$$.ptrType.registeredClass;return eE(t.$$.ptr,r,this.registeredClass)}var eR=(e,t,r)=>{if(t===r)return e;if(void 0===r.baseClass)return null;var n=eR(e,t,r.baseClass);return null===n?null:r.downcast(n)},eM={},eI=(e,t)=>{for(void 0===t&&ei("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t},eD=(e,t)=>eM[t=eI(e,t)],eb=(e,t)=>(t.ptrType&&t.ptr||ee("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&ee("Both smartPtrType and smartPtr must be specified"),t.count={value:1},eh(Object.create(e,{$$:{value:t,writable:!0}})));function eF(e,t,r,n,i,o,a,s,c,l,d){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=i,this.pointeeType=o,this.sharingPolicy=a,this.rawGetPointee=s,this.rawConstructor=c,this.rawShare=l,this.rawDestructor=d,i||void 0!==t.baseClass?this.toWireType=eT:(n?this.toWireType=ev:this.toWireType=eB,this.destructorFunction=null)}var eq,ek=(t,r,n)=>{e.hasOwnProperty(t)||ee("Replacing nonexistent public symbol"),void 0!==e[t].overloadTable&&void 0!==n?e[t].overloadTable[n]=r:(e[t]=r,e[t].argCount=n)},eG=[],eP=e=>{var t=eG[e];return t||(eG[e]=t=eq.get(e)),t},eY=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return e.includes("j")?dynCallLegacy(e,t,r):eP(t)(...r)},eV=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return eY(e,t,i,r)}},eS=function(e,t){var r=(e=er(e)).includes("j")?eV(e,t):eP(t);return"function"!=typeof r&&ei(`unknown function pointer with signature ${e}: ${t}`),r};class ex extends Error{}var eN=e=>{var t=ty(e),r=er(t);return tE(t),r},eQ=(e,t)=>{var r=[],n={};throw t.forEach(function e(t){if(!n[t]&&!z[t]){if(_[t]){_[t].forEach(e);return}r.push(t),n[t]=!0}}),new ex(`${e}: `+r.map(eN).join([", "]))},eL=(e,t)=>{for(var r=[],n=0;n<e;n++)r.push(w[t+4*n>>2]);return r};function eO(e,t,r,n,i,o){var a=t.length;a<2&&ei("argTypes array size mismatch! Must at least get return value and 'this' types!");var s=null!==t[1]&&null!==r,c=function(e){for(var t=1;t<e.length;++t)if(null!==e[t]&&void 0===e[t].destructorFunction)return!0;return!1}(t),l="void"!==t[0].name,d=a-2,p=Array(d),u=[],h=[];return ef(e,function(){h.length=0,u.length=s?2:1,u[0]=i,s&&(e=t[1].toWireType(h,this),u[1]=e);for(var e,r=0;r<d;++r)p[r]=t[r+2].toWireType(h,r<0||arguments.length<=r?void 0:arguments[r]),u.push(p[r]);return function(r){if(c)J(h);else for(var n=s?1:2;n<t.length;n++){var i=1===n?e:p[n-2];null!==t[n].destructorFunction&&t[n].destructorFunction(i)}if(l)return t[0].fromWireType(r)}(n(...u))})}var eK=e=>{let t=(e=e.trim()).indexOf("(");return -1===t?e:e.slice(0,t)},eU=[],ej=[],eJ=e=>{e>9&&0==--ej[e+1]&&(ej[e]=void 0,eU.push(e))},eW={toValue:e=>(e||ei(`Cannot use deleted val. handle = ${e}`),ej[e]),toHandle:e=>{switch(e){case void 0:return 2;case null:return 4;case!0:return 6;case!1:return 8;default:{let t=eU.pop()||ej.length;return ej[t]=e,ej[t+1]=1,t}}}},eZ={name:"emscripten::val",fromWireType:e=>{var t=eW.toValue(e);return eJ(e),t},toWireType:(e,t)=>eW.toHandle(t),argPackAdvance:8,readValueFromPointer:W,destructorFunction:null},eH=(e,t)=>{switch(t){case 4:return function(e){return this.fromWireType(A[e>>2])};case 8:return function(e){return this.fromWireType(y[e>>3])};default:throw TypeError(`invalid float width (${t}): ${e}`)}},eX=(e,t,r)=>{switch(t){case 1:return r?e=>h[e]:e=>g[e];case 2:return r?e=>f[e>>1]:e=>m[e>>1];case 4:return r?e=>C[e>>2]:e=>w[e>>2];default:throw TypeError(`invalid integer width (${t}): ${e}`)}},ez=Object.assign({optional:!0},eZ),e_=(e,t,r,n)=>{if(!(n>0))return 0;for(var i=r,o=r+n-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a)),s<=127){if(r>=o)break;t[r++]=s}else if(s<=2047){if(r+1>=o)break;t[r++]=192|s>>6,t[r++]=128|63&s}else if(s<=65535){if(r+2>=o)break;t[r++]=224|s>>12,t[r++]=128|s>>6&63,t[r++]=128|63&s}else{if(r+3>=o)break;t[r++]=240|s>>18,t[r++]=128|s>>12&63,t[r++]=128|s>>6&63,t[r++]=128|63&s}}return t[r]=0,r-i},e$=(e,t,r)=>e_(e,g,t,r),e0=e=>{for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n<=127?t++:n<=2047?t+=2:n>=55296&&n<=57343?(t+=4,++r):t+=3}return t},e1="u">typeof TextDecoder?new TextDecoder:void 0,e4=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:NaN;for(var n=t+r,i=t;e[i]&&!(i>=n);)++i;if(i-t>16&&e.buffer&&e1)return e1.decode(e.subarray(t,i));for(var o="";t<i;){var a=e[t++];if(!(128&a)){o+=String.fromCharCode(a);continue}var s=63&e[t++];if((224&a)==192){o+=String.fromCharCode((31&a)<<6|s);continue}var c=63&e[t++];if((a=(240&a)==224?(15&a)<<12|s<<6|c:(7&a)<<18|s<<12|c<<6|63&e[t++])<65536)o+=String.fromCharCode(a);else{var l=a-65536;o+=String.fromCharCode(55296|l>>10,56320|1023&l)}}return o},e2=(e,t)=>e?e4(g,e,t):"",e8="u">typeof TextDecoder?new TextDecoder("utf-16le"):void 0,e5=(e,t)=>{for(var r=e,n=r>>1,i=n+t/2;!(n>=i)&&m[n];)++n;if((r=n<<1)-e>32&&e8)return e8.decode(g.subarray(e,r));for(var o="",a=0;!(a>=t/2);++a){var s=f[e+2*a>>1];if(0==s)break;o+=String.fromCharCode(s)}return o},e6=(e,t,r)=>{if(null!=r||(r=2147483647),r<2)return 0;r-=2;for(var n=t,i=r<2*e.length?r/2:e.length,o=0;o<i;++o){var a=e.charCodeAt(o);f[t>>1]=a,t+=2}return f[t>>1]=0,t-n},e3=e=>2*e.length,e9=(e,t)=>{for(var r=0,n="";!(r>=t/4);){var i=C[e+4*r>>2];if(0==i)break;if(++r,i>=65536){var o=i-65536;n+=String.fromCharCode(55296|o>>10,56320|1023&o)}else n+=String.fromCharCode(i)}return n},e7=(e,t,r)=>{if(null!=r||(r=2147483647),r<4)return 0;for(var n=t,i=n+r-4,o=0;o<e.length;++o){var a=e.charCodeAt(o);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o)),C[t>>2]=a,(t+=4)+4>i)break}return C[t>>2]=0,t-n},te=e=>{for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&++r,t+=4}return t},tt=[],tr={},tn=e=>{var t=tr[e];return void 0===t?er(e):t},ti=()=>{if("object"==typeof globalThis)return globalThis;function e(e){e.$$$embind_global$$$=e;var t="object"==typeof $$$embind_global$$$&&e.$$$embind_global$$$==e;return t||delete e.$$$embind_global$$$,t}if("object"==typeof $$$embind_global$$$||("object"==typeof global&&e(global)?$$$embind_global$$$=global:"object"==typeof self&&e(self)&&($$$embind_global$$$=self),"object"==typeof $$$embind_global$$$))return $$$embind_global$$$;throw Error("unable to get global object.")},to=e=>{var t=tt.length;return tt.push(e),t},ta=(e,t)=>{var r=z[e];return void 0===r&&ei(`${t} has unknown type ${eN(e)}`),r},ts=(e,t)=>{for(var r=Array(e),n=0;n<e;++n)r[n]=ta(w[t+4*n>>2],`parameter ${n}`);return r},tc=(e,t,r)=>{var n=[],i=e.toWireType(n,r);return n.length&&(w[t>>2]=eW.toHandle(n)),i},tl=Reflect.construct,td=()=>2147483648,tp=(e,t)=>Math.ceil(e/t)*t,tu=e=>{var t=(e-u.buffer.byteLength+65535)/65536|0;try{return u.grow(t),B(),1}catch{}},th={},tg=()=>s||"./this.program",tf=()=>{if(!tf.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:tg()};for(var t in th)void 0===th[t]?delete e[t]:e[t]=th[t];var r=[];for(var t in e)r.push(`${t}=${e[t]}`);tf.strings=r}return tf.strings},tm=[null,[],[]],tC=(e,t)=>{var r=tm[e];0===t||10===t?((1===e?E:v)(e4(r)),r.length=0):r.push(t)};(()=>{for(var e=Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);H=e})(),(()=>{let e=eg.prototype;Object.assign(e,{isAliasOf(e){if(!(this instanceof eg)||!(e instanceof eg))return!1;var t=this.$$.ptrType.registeredClass,r=this.$$.ptr;e.$$=e.$$;for(var n=e.$$.ptrType.registeredClass,i=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)i=n.upcast(i),n=n.baseClass;return t===n&&r===i},clone(){if(this.$$.ptr||ec(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e=eh(Object.create(Object.getPrototypeOf(this),{$$:{value:es(this.$$)}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e},delete(){this.$$.ptr||ec(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ei("Object already scheduled for deletion"),ed(this),eu(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted(){return!this.$$.ptr},deleteLater(){return this.$$.ptr||ec(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ei("Object already scheduled for deletion"),this.$$.deleteScheduled=!0,this}});let t=Symbol.dispose;t&&(e[t]=e.delete)})(),Object.assign(eF.prototype,{getPointee(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e},destructor(e){var t;null===(t=this.rawDestructor)||void 0===t||t.call(this,e)},argPackAdvance:8,readValueFromPointer:W,fromWireType:function(e){var t,r=this.getPointee(e);if(!r)return this.destructor(e),null;var n=eD(this.registeredClass,r);if(void 0!==n){if(0===n.$$.count.value)return n.$$.ptr=r,n.$$.smartPtr=e,n.clone();var i=n.clone();return this.destructor(e),i}function o(){return this.isSmartPointer?eb(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:e}):eb(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var a=em[this.registeredClass.getActualType(r)];if(!a)return o.call(this);t=this.isConst?a.constPointerType:a.pointerType;var s=eR(r,this.registeredClass,t.registeredClass);return null===s?o.call(this):this.isSmartPointer?eb(t.registeredClass.instancePrototype,{ptrType:t,ptr:s,smartPtrType:this,smartPtr:e}):eb(t.registeredClass.instancePrototype,{ptrType:t,ptr:s})}}),ej.push(0,1,void 0,1,null,1,!0,1,!1,1),e.count_emval_handles=()=>ej.length/2-5-eU.length,e.noExitRuntime&&e.noExitRuntime,e.print&&(E=e.print),e.printErr&&(v=e.printErr),e.wasmBinary&&(p=e.wasmBinary),e.arguments&&e.arguments,e.thisProgram&&(s=e.thisProgram);var tw={s:e=>{var t=new O(e);return t.get_caught()||(t.set_caught(!0),Q--),t.set_rethrown(!1),N.push(t),tb(e),tT(e)},w:()=>{tB(0,0),tD(N.pop().excPtr),L=0},a:()=>U([]),j:e=>U([e]),m:(e,t)=>U([e,t]),N:()=>{var e=N.pop();e||I("no exception to throw");var t=e.excPtr;throw e.get_rethrown()||(N.push(e),e.set_rethrown(!0),e.set_caught(!1),Q++),L=t},p:(e,t,r)=>{throw new O(e).init(t,r),L=e,Q++,L},da:()=>Q,d:e=>{throw L||(L=e),L},_:()=>I(""),sa:e=>{var t=j[e];delete j[e];var r=t.rawConstructor,n=t.rawDestructor,i=t.fields;et([e],i.map(e=>e.getterReturnType).concat(i.map(e=>e.setterArgumentType)),e=>{var o={};return i.forEach((t,r)=>{var n=t.fieldName,a=e[r],s=e[r].optional,c=t.getter,l=t.getterContext,d=e[r+i.length],p=t.setter,u=t.setterContext;o[n]={read:e=>a.fromWireType(c(l,e)),write:(e,t)=>{var r=[];p(u,e,d.toWireType(r,t)),J(r)},optional:s}}),[{name:t.name,fromWireType:e=>{var t={};for(var r in o)t[r]=o[r].read(e);return n(e),t},toWireType:(e,t)=>{for(var i in o)if(!(i in t)&&!o[i].optional)throw TypeError(`Missing field: "${i}"`);var a=r();for(i in o)o[i].write(a,t[i]);return null!==e&&e.push(n,a),a},argPackAdvance:8,readValueFromPointer:W,destructorFunction:n}]})},Z:(e,t,r,n,i)=>{},na:(e,t,r,n)=>{ea(e,{name:t=er(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?r:n},argPackAdvance:8,readValueFromPointer:function(e){return this.fromWireType(g[e])},destructorFunction:null})},qa:(e,t,r,n,i,o,a,s,c,l,d,p,u)=>{d=er(d),o=eS(i,o),s&&(s=eS(a,s)),l&&(l=eS(c,l)),u=eS(p,u);var h=eA(d);ew(h,function(){eQ(`Cannot construct ${d} due to unbound types`,[n])}),et([e,t,r],n?[n]:[],t=>{t=t[0],i=n?(r=t.registeredClass).instancePrototype:eg.prototype;var r,i,a,c,p=ef(d,function(){if(Object.getPrototypeOf(this)!==g)throw new en(`Use 'new' to construct ${d}`);if(void 0===f.constructor_body)throw new en(`${d} has no accessible constructor`);for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=f.constructor_body[t.length];if(void 0===n)throw new en(`Tried to invoke ctor of ${d} with invalid number of parameters (${t.length}) - expected (${Object.keys(f.constructor_body).toString()}) parameters instead!`);return n.apply(this,t)}),g=Object.create(i,{constructor:{value:p}});p.prototype=g;var f=new ey(d,p,g,u,r,o,s,l);f.baseClass&&(null!==(c=(a=f.baseClass).__derivedClasses)&&void 0!==c||(a.__derivedClasses=[]),f.baseClass.__derivedClasses.push(f));var m=new eF(d,f,!0,!1,!1),C=new eF(d+"*",f,!1,!1,!1),w=new eF(d+" const*",f,!1,!0,!1);return em[e]={pointerType:C,constPointerType:w},ek(h,p),[m,C,w]})},pa:(e,t,r,n,i,o)=>{var a=eL(t,r);i=eS(n,i),et([],[e],e=>{e=e[0];var r=`constructor ${e.name}`;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new en(`Cannot register multiple constructors with identical number of parameters (${t-1}) for class '${e.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return e.registeredClass.constructor_body[t-1]=()=>{eQ(`Cannot construct ${e.name} due to unbound types`,a)},et([],a,n=>(n.splice(1,0,null),e.registeredClass.constructor_body[t-1]=eO(r,n,null,i,o),[])),[]})},F:(e,t,r,n,i,o,a,s,c,l)=>{var d=eL(r,n);t=eK(t=er(t)),o=eS(i,o),et([],[e],e=>{e=e[0];var n=`${e.name}.${t}`;function i(){eQ(`Cannot call ${n} due to unbound types`,d)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(t);var c=e.registeredClass.instancePrototype,l=c[t];return void 0===l||void 0===l.overloadTable&&l.className!==e.name&&l.argCount===r-2?(i.argCount=r-2,i.className=e.name,c[t]=i):(eC(c,t,n),c[t].overloadTable[r-2]=i),et([],d,i=>{var s=eO(n,i,e,o,a);return void 0===c[t].overloadTable?(s.argCount=r-2,c[t]=s):c[t].overloadTable[r-2]=s,[]}),[]})},la:e=>ea(e,eZ),R:(e,t,r)=>{ea(e,{name:t=er(t),fromWireType:e=>e,toWireType:(e,t)=>t,argPackAdvance:8,readValueFromPointer:eH(t,r),destructorFunction:null})},S:(e,t,r,n,i,o,a,s)=>{var c=eL(t,r);e=eK(e=er(e)),i=eS(n,i),ew(e,function(){eQ(`Cannot call ${e} due to unbound types`,c)},t-1),et([],c,r=>{var n=[r[0],null].concat(r.slice(1));return ek(e,eO(e,n,null,i,o),t-1),[]})},y:(e,t,r,n,i)=>{t=er(t);var o=e=>e;if(0===n){var a=32-8*r;o=e=>e<<a>>>a}var s=t.includes("unsigned"),c=(e,t)=>{};ea(e,{name:t,fromWireType:o,toWireType:s?function(e,t){return c(t,this.name),t>>>0}:function(e,t){return c(t,this.name),t},argPackAdvance:8,readValueFromPointer:eX(t,r,0!==n),destructorFunction:null})},t:(e,t,r)=>{var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function i(e){var t=w[e>>2],r=w[e+4>>2];return new n(h.buffer,r,t)}ea(e,{name:r=er(r),fromWireType:i,argPackAdvance:8,readValueFromPointer:i},{ignoreDuplicateRegistrations:!0})},ra:(e,t)=>{ea(e,ez)},ma:(e,t)=>{ea(e,{name:t=er(t),fromWireType(e){for(var t,r,n=w[e>>2],i=e+4,o=i,r=0;r<=n;++r){var a=i+r;if(r==n||0==g[a]){var s=a-o,c=e2(o,s);void 0===t?t=c:t+="\0"+c,o=a+1}}return tE(e),t},toWireType(e,t){t instanceof ArrayBuffer&&(t=new Uint8Array(t));var r,n="string"==typeof t;n||ArrayBuffer.isView(t)&&1==t.BYTES_PER_ELEMENT||ei("Cannot pass non-string to std::string");var i=tv(4+(r=n?e0(t):t.length)+1),o=i+4;return w[i>>2]=r,n?e$(t,o,r+1):g.set(t,o),null!==e&&e.push(tE,i),i},argPackAdvance:8,readValueFromPointer:W,destructorFunction(e){tE(e)}})},O:(e,t,r)=>{var n,i,o,a;r=er(r),2===t?(n=e5,i=e6,a=e3,o=e=>m[e>>1]):4===t&&(n=e9,i=e7,a=te,o=e=>w[e>>2]),ea(e,{name:r,fromWireType:e=>{for(var r,i=w[e>>2],a=e+4,s=0;s<=i;++s){var c=e+4+s*t;if(s==i||0==o(c)){var l=c-a,d=n(a,l);void 0===r?r=d:r+="\0"+d,a=c+t}}return tE(e),r},toWireType:(e,n)=>{"string"!=typeof n&&ei(`Cannot pass non-string to C++ string type ${r}`);var o=a(n),s=tv(4+o+t);return w[s>>2]=o/t,i(n,s+4,o+t),null!==e&&e.push(tE,s),s},argPackAdvance:8,readValueFromPointer:W,destructorFunction(e){tE(e)}})},K:(e,t,r,n,i,o)=>{j[e]={name:er(t),rawConstructor:eS(r,n),rawDestructor:eS(i,o),fields:[]}},ta:(e,t,r,n,i,o,a,s,c,l)=>{j[e].fields.push({fieldName:er(t),getterReturnType:r,getter:eS(n,i),getterContext:o,setterArgumentType:a,setter:eS(s,c),setterContext:l})},oa:(e,t)=>{ea(e,{isVoid:!0,name:t=er(t),argPackAdvance:0,fromWireType:()=>{},toWireType:(e,t)=>{}})},V:(e,t,r,n)=>(e=tt[e])(null,t=eW.toValue(t),r,n),ua:eJ,wa:e=>0===e?eW.toHandle(ti()):(e=tn(e),eW.toHandle(ti()[e])),$:(e,t,r)=>{var n=ts(e,t),i=n.shift(),o=Array(--e);return to(ef(`methodCaller<(${n.map(e=>e.name).join(", ")}) => ${i.name}>`,(t,a,s,c)=>{for(var l=0,d=0;d<e;++d)o[d]=n[d].readValueFromPointer(c+l),l+=n[d].argPackAdvance;return tc(i,s,1===r?tl(a,o):a.apply(t,o))}))},T:e=>{e>9&&(ej[e+1]+=1)},va:e=>{J(eW.toValue(e)),eJ(e)},ka:(e,t)=>{var r=(e=ta(e,"_emval_take_value")).readValueFromPointer(t);return eW.toHandle(r)},aa:(e,t,r,n)=>{var i=new Date().getFullYear(),o=new Date(i,0,1),a=new Date(i,6,1),s=o.getTimezoneOffset(),c=a.getTimezoneOffset(),l=Math.max(s,c);w[e>>2]=60*l,C[t>>2]=+(s!=c);var d=e=>{var t=Math.abs(e),r=String(Math.floor(t/60)).padStart(2,"0"),n=String(t%60).padStart(2,"0");return`UTC${e>=0?"-":"+"}${r}${n}`},p=d(s),u=d(c);c<s?(e$(p,r,17),e$(u,n,17)):(e$(p,n,17),e$(u,r,17))},ea:e=>{var t=g.length;e>>>=0;var r=td();if(e>r)return!1;for(var n=1;n<=4;n*=2){var i=t*(1+.2/n);if(i=Math.min(i,e+100663296),tu(Math.min(r,tp(Math.max(e,i),65536))))return!0}return!1},ba:(e,t)=>{var r=0,n=0;for(var i of tf()){var o=t+r;w[e+n>>2]=o,r+=e$(i,o,1/0)+1,n+=4}return 0},ca:(e,t)=>{var r=tf();w[e>>2]=r.length;var n=0;for(var i of r)n+=e0(i)+1;return w[t>>2]=n,0},fa:e=>52,X:function(e,t,r,n,i){return 70},Q:(e,t,r,n)=>{for(var i=0,o=0;o<r;o++){var a=w[t>>2],s=w[t+4>>2];t+=8;for(var c=0;c<s;c++)tC(e,g[a+c]);i+=s}return w[n>>2]=i,0},I:function(e,t,r,n){var i=x();try{return eP(e)(t,r,n)}catch(e){if(S(i),e!==e+0)throw e;tB(1,0)}},C:function(e,t,r,n,i,o){var a=x();try{return eP(e)(t,r,n,i,o)}catch(e){if(S(a),e!==e+0)throw e;tB(1,0)}},U:function(e,t,r,n,i){var o=x();try{return eP(e)(t,r,n,i)}catch(e){if(S(o),e!==e+0)throw e;tB(1,0)}},P:function(e,t,r,n){var i=x();try{return eP(e)(t,r,n)}catch(e){if(S(i),e!==e+0)throw e;tB(1,0)}},q:function(e){var t=x();try{return eP(e)()}catch(e){if(S(t),e!==e+0)throw e;tB(1,0)}},b:function(e,t){var r=x();try{return eP(e)(t)}catch(e){if(S(r),e!==e+0)throw e;tB(1,0)}},D:function(e,t,r,n){var i=x();try{return eP(e)(t,r,n)}catch(e){if(S(i),e!==e+0)throw e;tB(1,0)}},ia:function(e,t,r){var n=x();try{return eP(e)(t,r)}catch(e){if(S(n),e!==e+0)throw e;tB(1,0)}},c:function(e,t,r){var n=x();try{return eP(e)(t,r)}catch(e){if(S(n),e!==e+0)throw e;tB(1,0)}},ha:function(e,t,r,n,i){var o=x();try{return eP(e)(t,r,n,i)}catch(e){if(S(o),e!==e+0)throw e;tB(1,0)}},h:function(e,t,r,n){var i=x();try{return eP(e)(t,r,n)}catch(e){if(S(i),e!==e+0)throw e;tB(1,0)}},i:function(e,t,r,n,i){var o=x();try{return eP(e)(t,r,n,i)}catch(e){if(S(o),e!==e+0)throw e;tB(1,0)}},r:function(e,t,r,n,i,o){var a=x();try{return eP(e)(t,r,n,i,o)}catch(e){if(S(a),e!==e+0)throw e;tB(1,0)}},M:function(e,t,r,n,i,o,a){var s=x();try{return eP(e)(t,r,n,i,o,a)}catch(e){if(S(s),e!==e+0)throw e;tB(1,0)}},v:function(e,t,r,n,i,o,a){var s=x();try{return eP(e)(t,r,n,i,o,a)}catch(e){if(S(s),e!==e+0)throw e;tB(1,0)}},E:function(e,t,r,n,i,o,a,s){var c=x();try{return eP(e)(t,r,n,i,o,a,s)}catch(e){if(S(c),e!==e+0)throw e;tB(1,0)}},J:function(e,t,r,n,i,o,a,s,c){var l=x();try{return eP(e)(t,r,n,i,o,a,s,c)}catch(e){if(S(l),e!==e+0)throw e;tB(1,0)}},A:function(e,t,r,n,i,o,a,s,c,l){var d=x();try{return eP(e)(t,r,n,i,o,a,s,c,l)}catch(e){if(S(d),e!==e+0)throw e;tB(1,0)}},H:function(e,t,r,n,i,o,a,s,c,l,d,p){var u=x();try{return eP(e)(t,r,n,i,o,a,s,c,l,d,p)}catch(e){if(S(u),e!==e+0)throw e;tB(1,0)}},W:function(e,t,r,n,i){var o=x();try{return tk(e,t,r,n,i)}catch(e){if(S(o),e!==e+0)throw e;tB(1,0)}},k:function(e){var t=x();try{eP(e)()}catch(e){if(S(t),e!==e+0)throw e;tB(1,0)}},f:function(e,t){var r=x();try{eP(e)(t)}catch(e){if(S(r),e!==e+0)throw e;tB(1,0)}},e:function(e,t,r){var n=x();try{eP(e)(t,r)}catch(e){if(S(n),e!==e+0)throw e;tB(1,0)}},g:function(e,t,r,n){var i=x();try{eP(e)(t,r,n)}catch(e){if(S(i),e!==e+0)throw e;tB(1,0)}},L:function(e,t,r,n,i,o,a){var s=x();try{eP(e)(t,r,n,i,o,a)}catch(e){if(S(s),e!==e+0)throw e;tB(1,0)}},l:function(e,t,r,n,i){var o=x();try{eP(e)(t,r,n,i)}catch(e){if(S(o),e!==e+0)throw e;tB(1,0)}},ja:function(e,t,r,n,i,o,a,s){var c=x();try{eP(e)(t,r,n,i,o,a,s)}catch(e){if(S(c),e!==e+0)throw e;tB(1,0)}},o:function(e,t,r,n,i,o){var a=x();try{eP(e)(t,r,n,i,o)}catch(e){if(S(a),e!==e+0)throw e;tB(1,0)}},x:function(e,t,r,n,i,o,a){var s=x();try{eP(e)(t,r,n,i,o,a)}catch(e){if(S(s),e!==e+0)throw e;tB(1,0)}},u:function(e,t,r,n,i,o,a,s){var c=x();try{eP(e)(t,r,n,i,o,a,s)}catch(e){if(S(c),e!==e+0)throw e;tB(1,0)}},ga:function(e,t,r,n,i,o,a,s,c){var l=x();try{eP(e)(t,r,n,i,o,a,s,c)}catch(e){if(S(l),e!==e+0)throw e;tB(1,0)}},B:function(e,t,r,n,i,o,a,s,c,l){var d=x();try{eP(e)(t,r,n,i,o,a,s,c,l)}catch(e){if(S(d),e!==e+0)throw e;tB(1,0)}},n:function(e,t,r,n,i,o,a,s,c,l,d){var p=x();try{eP(e)(t,r,n,i,o,a,s,c,l,d)}catch(e){if(S(p),e!==e+0)throw e;tB(1,0)}},G:function(e,t,r,n,i,o,a,s,c,l,d,p,u,h,g,f){var m=x();try{eP(e)(t,r,n,i,o,a,s,c,l,d,p,u,h,g,f)}catch(e){if(S(m),e!==e+0)throw e;tB(1,0)}},Y:function(e,t,r,n){var i=x();try{tq(e,t,r,n)}catch(e){if(S(i),e!==e+0)throw e;tB(1,0)}},z:e=>e},tA=await q();tA.ya;var ty=tA.za,tE=e._free=tA.Aa,tv=e._malloc=tA.Ca,tT=tA.Da,tB=tA.Ea,tR=tA.Fa,tM=tA.Ga,tI=tA.Ha,tD=tA.Ia,tb=tA.Ja,tF=tA.Ka;e.dynCall_viijii=tA.La;var tq=e.dynCall_vij=tA.Ma;e.dynCall_jiji=tA.Na;var tk=e.dynCall_jiiii=tA.Oa;return e.dynCall_iiiiij=tA.Pa,e.dynCall_iiiiijj=tA.Qa,e.dynCall_iiiiiijj=tA.Ra,function(){if(e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.shift()()}(),function r(){if(R>0||(function(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)V(e.preRun.shift());k(Y)}(),R>0)){M=r;return}function n(){var r;e.calledRun=!0,T||(tA.ya(),t(e),null===(r=e.onRuntimeInitialized)||void 0===r||r.call(e),function(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)P(e.postRun.shift());k(G)}())}e.setStatus?(e.setStatus("Running..."),setTimeout(()=>{setTimeout(()=>e.setStatus(""),1),n()},1)):n()}(),n};async function eD(e,t){return eM(eI,e,t)}let eb=[["aztec","Aztec"],["code_128","Code128"],["code_39","Code39"],["code_93","Code93"],["codabar","Codabar"],["databar","DataBar"],["databar_expanded","DataBarExpanded"],["databar_limited","DataBarLimited"],["data_matrix","DataMatrix"],["dx_film_edge","DXFilmEdge"],["ean_13","EAN-13"],["ean_8","EAN-8"],["itf","ITF"],["maxi_code","MaxiCode"],["micro_qr_code","MicroQRCode"],["pdf417","PDF417"],["qr_code","QRCode"],["rm_qr_code","rMQRCode"],["upc_a","UPC-A"],["upc_e","UPC-E"],["linear_codes","Linear-Codes"],["matrix_codes","Matrix-Codes"],["any","Any"]],eF=[...eb,["unknown"]].map(e=>e[0]),eq=new Map(eb);function ek(e){var t,r;try{return e instanceof(null==(r=null==(t=null==e?void 0:e.ownerDocument)?void 0:t.defaultView)?void 0:r.HTMLImageElement)}catch{return!1}}function eG(e){var t,r;try{return e instanceof(null==(r=null==(t=null==e?void 0:e.ownerDocument)?void 0:t.defaultView)?void 0:r.SVGImageElement)}catch{return!1}}function eP(e){var t,r;try{return e instanceof(null==(r=null==(t=null==e?void 0:e.ownerDocument)?void 0:t.defaultView)?void 0:r.HTMLVideoElement)}catch{return!1}}function eY(e){var t,r;try{return e instanceof(null==(r=null==(t=null==e?void 0:e.ownerDocument)?void 0:t.defaultView)?void 0:r.HTMLCanvasElement)}catch{return!1}}function eV(e){try{return e instanceof ImageBitmap||"[object ImageBitmap]"===Object.prototype.toString.call(e)}catch{return!1}}function eS(e){try{return e instanceof OffscreenCanvas||"[object OffscreenCanvas]"===Object.prototype.toString.call(e)}catch{return!1}}function ex(e){try{return e instanceof VideoFrame||"[object VideoFrame]"===Object.prototype.toString.call(e)}catch{return!1}}async function eN(e){var t;if(ek(e)&&!await eO(e))throw new DOMException("Failed to load or decode HTMLImageElement.","InvalidStateError");if(eG(e)&&!await eK(e))throw new DOMException("Failed to load or decode SVGImageElement.","InvalidStateError");if(ex(e)&&null===e.format)throw new DOMException("VideoFrame is closed.","InvalidStateError");if(eP(e)&&(0===e.readyState||1===e.readyState))throw new DOMException("Invalid element or state.","InvalidStateError");if(eV(e)&&0===(t=e).width&&0===t.height)throw new DOMException("The image source is detached.","InvalidStateError");let{width:r,height:n}=function(e){if(ek(e))return{width:e.naturalWidth,height:e.naturalHeight};if(eG(e))return{width:e.width.baseVal.value,height:e.height.baseVal.value};if(eP(e))return{width:e.videoWidth,height:e.videoHeight};if(eV(e))return{width:e.width,height:e.height};if(ex(e))return{width:e.displayWidth,height:e.displayHeight};if(eY(e)||eS(e))return{width:e.width,height:e.height};throw TypeError("The provided value is not of type '(Blob or HTMLCanvasElement or HTMLImageElement or HTMLVideoElement or ImageBitmap or ImageData or OffscreenCanvas or SVGImageElement or VideoFrame)'.")}(e);if(0===r||0===n)return null;let i=(function(e,t){try{let r=new OffscreenCanvas(e,t);if(r.getContext("2d") instanceof OffscreenCanvasRenderingContext2D)return r;throw void 0}catch{let r=document.createElement("canvas");return r.width=e,r.height=t,r}})(r,n).getContext("2d");i.drawImage(e,0,0);try{return i.getImageData(0,0,r,n)}catch{throw new DOMException("Source would taint origin.","SecurityError")}}async function eQ(e){let t;try{t=await createImageBitmap(e)}catch{try{if(!globalThis.Image)return e;{t=new Image;let r="";try{r=URL.createObjectURL(e),t.src=r,await t.decode()}finally{URL.revokeObjectURL(r)}}}catch{throw new DOMException("Failed to load or decode Blob.","InvalidStateError")}}return await eN(t)}async function eL(e){if(function(e){try{return e instanceof Blob||"[object Blob]"===Object.prototype.toString.call(e)}catch{return!1}}(e))return await eQ(e);if(function(e){try{return e instanceof ImageData||"[object ImageData]"===Object.prototype.toString.call(e)}catch{return!1}}(e)){if(0===e.data.buffer.byteLength)throw new DOMException("The image data has been detached.","InvalidStateError");return e}return eY(e)||eS(e)?function(e){let{width:t,height:r}=e;if(0===t||0===r)return null;let n=e.getContext("2d");try{return n.getImageData(0,0,t,r)}catch{throw new DOMException("Source would taint origin.","SecurityError")}}(e):await eN(e)}async function eO(e){try{return await e.decode(),!0}catch{return!1}}async function eK(e){var t;try{return await (null==(t=e.decode)?void 0:t.call(e)),!0}catch{return!1}}function eU(e,t){return e instanceof DOMException||"[object DOMException]"===Object.prototype.toString.call(e)?new DOMException(`${t}: ${e.message}`,e.name):e instanceof Error||"[object Error]"===Object.prototype.toString.call(e)?new e.constructor(`${t}: ${e.message}`):Error(`${t}: ${e}`)}class ej{constructor(e={}){var t,r;el(this,n);try{let i=null==(t=null==e?void 0:e.formats)?void 0:t.filter(e=>"unknown"!==e);if((null==i?void 0:i.length)===0)throw TypeError("Hint option provided, but is empty.");for(let e of null!=i?i:[])if(!eq.has(e))throw TypeError(`Failed to read the 'formats' property from 'BarcodeDetectorOptions': The provided value '${e}' is not a valid enum value of type BarcodeFormat.`);ed(this,n,null!=i?i:[]),(r={fireImmediately:!0},eR(eI,r)).catch(()=>{})}catch(e){throw eU(e,"Failed to construct 'BarcodeDetector'")}}static async getSupportedFormats(){return eF.filter(e=>"unknown"!==e)}async detect(e){try{let t;let r=await eL(e);if(null===r)return[];let i={tryCode39ExtendedMode:!1,textMode:"Plain",formats:ec(this,n).map(e=>eq.get(e))};try{t=await eD(r,i)}catch(e){throw console.error(e),new DOMException("Barcode detection service unavailable.","NotSupportedError")}return t.map(e=>{let{topLeft:{x:t,y:r},topRight:{x:n,y:i},bottomLeft:{x:o,y:a},bottomRight:{x:s,y:c}}=e.position,l=Math.min(t,n,o,s),d=Math.min(r,i,a,c),p=Math.max(t,n,o,s),u=Math.max(r,i,a,c);return{boundingBox:new DOMRectReadOnly(l,d,p-l,u-d),rawValue:e.text,format:function(e){for(let[t,r]of eq)if(e===r)return t;return"unknown"}(e.format),cornerPoints:[{x:t,y:r},{x:n,y:i},{x:s,y:c},{x:o,y:a}]}})}catch(e){throw eU(e,"Failed to execute 'detect' on 'BarcodeDetector'")}}}function eJ(e){let{onClick:t,disabled:r,className:n}=e,i={cursor:r?"default":"pointer",stroke:r?"grey":"yellow",strokeLineJoin:"round",strokeLineCap:"round",strokeWidth:1.5,...e.style};return c.createElement("svg",{onClick:r?void 0:t,className:n,style:i,width:"28px",height:"28px",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},c.createElement("path",{d:"M3 3L6.00007 6.00007M21 21L19.8455 19.8221M9.74194 4.06811C9.83646 4.04279 9.93334 4.02428 10.0319 4.01299C10.1453 4 10.2683 4 10.5141 4H13.5327C13.7786 4 13.9015 4 14.015 4.01299C14.6068 4.08078 15.1375 4.40882 15.4628 4.90782C15.5252 5.00345 15.5802 5.11345 15.6901 5.33333C15.7451 5.44329 15.7726 5.49827 15.8037 5.54609C15.9664 5.79559 16.2318 5.95961 16.5277 5.9935C16.5844 6 16.6459 6 16.7688 6H17.8234C18.9435 6 19.5036 6 19.9314 6.21799C20.3077 6.40973 20.6137 6.71569 20.8055 7.09202C21.0234 7.51984 21.0234 8.0799 21.0234 9.2V15.3496M19.8455 19.8221C19.4278 20 18.8702 20 17.8234 20H6.22344C5.10333 20 4.54328 20 4.11546 19.782C3.73913 19.5903 3.43317 19.2843 3.24142 18.908C3.02344 18.4802 3.02344 17.9201 3.02344 16.8V9.2C3.02344 8.0799 3.02344 7.51984 3.24142 7.09202C3.43317 6.71569 3.73913 6.40973 4.11546 6.21799C4.51385 6.015 5.0269 6.00103 6.00007 6.00007M19.8455 19.8221L14.5619 14.5619M14.5619 14.5619C14.0349 15.4243 13.0847 16 12 16C10.3431 16 9 14.6569 9 13C9 11.9153 9.57566 10.9651 10.4381 10.4381M14.5619 14.5619L10.4381 10.4381M10.4381 10.4381L6.00007 6.00007"}))}function eW(e){let{onClick:t,disabled:r,className:n}=e,i={cursor:r?"default":"pointer",stroke:r?"grey":"yellow",strokeLineJoin:"round",strokeLineCap:"round",strokeWidth:1.5,...e.style};return c.createElement("svg",{onClick:r?void 0:t,className:n,style:i,width:"28px",height:"28px",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},c.createElement("path",{d:"M12 16C13.6569 16 15 14.6569 15 13C15 11.3431 13.6569 10 12 10C10.3431 10 9 11.3431 9 13C9 14.6569 10.3431 16 12 16Z"}),c.createElement("path",{d:"M3 16.8V9.2C3 8.0799 3 7.51984 3.21799 7.09202C3.40973 6.71569 3.71569 6.40973 4.09202 6.21799C4.51984 6 5.0799 6 6.2 6H7.25464C7.37758 6 7.43905 6 7.49576 5.9935C7.79166 5.95961 8.05705 5.79559 8.21969 5.54609C8.25086 5.49827 8.27836 5.44328 8.33333 5.33333C8.44329 5.11342 8.49827 5.00346 8.56062 4.90782C8.8859 4.40882 9.41668 4.08078 10.0085 4.01299C10.1219 4 10.2448 4 10.4907 4H13.5093C13.7552 4 13.8781 4 13.9915 4.01299C14.5833 4.08078 15.1141 4.40882 15.4394 4.90782C15.5017 5.00345 15.5567 5.11345 15.6667 5.33333C15.7216 5.44329 15.7491 5.49827 15.7803 5.54609C15.943 5.79559 16.2083 5.95961 16.5042 5.9935C16.561 6 16.6224 6 16.7454 6H17.8C18.9201 6 19.4802 6 19.908 6.21799C20.2843 6.40973 20.5903 6.71569 20.782 7.09202C21 7.51984 21 8.0799 21 9.2V16.8C21 17.9201 21 18.4802 20.782 18.908C20.5903 19.2843 20.2843 19.5903 19.908 19.782C19.4802 20 18.9201 20 17.8 20H6.2C5.0799 20 4.51984 20 4.09202 19.782C3.71569 19.5903 3.40973 19.2843 3.21799 18.908C3 18.4802 3 17.9201 3 16.8Z"}))}function eZ(e){let{scanning:t,startScanning:r,stopScanning:n}=e,[i,o]=(0,c.useState)(!1);function a(){o(!0),t?n():r(),setTimeout(()=>o(!1),1e3)}return c.createElement("div",{style:{bottom:85,right:8,position:"absolute",zIndex:2,cursor:i?"default":"pointer"}},t?c.createElement(eJ,{disabled:i,onClick:a}):c.createElement(eW,{disabled:i,onClick:a}))}function eH(e){let{onClick:t,className:r,style:n}=e;return c.createElement("svg",{onClick:t,width:"30px",height:"30px",viewBox:"0 0 24 24",className:r,style:n,xmlns:"http://www.w3.org/2000/svg"},c.createElement("path",{strokeWidth:.2,stroke:"yellow",fill:"yellow",d:"M13.225 9l5.025-7h-7.972l-3.3 11h5.359l-2.452 8.648.75.364L20.374 9zm.438 3H8.322l2.7-9H16.3l-5.025 7h7.101l-6.7 8.953z"}))}function eX(e){let{onClick:t,className:r,style:n}=e;return c.createElement("svg",{onClick:t,width:"30px",height:"30px",viewBox:"0 0 24 24",className:r,style:n,xmlns:"http://www.w3.org/2000/svg"},c.createElement("path",{strokeWidth:.2,stroke:"yellow",fill:"yellow",d:"M14.516 15.158l.714.714-4.595 6.14-.75-.364L12.337 13H6.978L8.22 8.861l.803.803L8.322 12h3.036l1.793 1.792-1.475 5.16zm5.984 4.05L4.793 3.5l.707-.707 3.492 3.492L10.278 2h7.972l-5.025 7h7.149l-3.71 4.957 4.543 4.543zM12.707 10l3.243 3.243L18.376 10zM9.795 7.088l2.079 2.079L16.3 3h-5.278z"}))}function ez(e){let{status:t,scanning:r,torchToggle:n}=e;return r&&n?c.createElement("div",{style:{bottom:35,right:8,position:"absolute",zIndex:2,cursor:"pointer"}},t?c.createElement(eX,{onClick:()=>{n(!1)}}):c.createElement(eH,{onClick:()=>{n(!0)}})):null}function e_(e){let{onClick:t,className:r,disabled:n=!1}=e,i={cursor:n?"default":"pointer",stroke:n?"grey":"yellow",fill:n?"grey":"yellow",...e.style};return c.createElement("svg",{onClick:n?void 0:t,width:"30px",height:"30px",viewBox:"0 0 24 24",className:r,style:i,xmlns:"http://www.w3.org/2000/svg"},c.createElement("path",{strokeWidth:.3,d:"M16.279,17.039c-1.396,1.209 -3.216,1.941 -5.206,1.941c-4.393,0 -7.96,-3.567 -7.96,-7.96c-0,-4.393 3.567,-7.96 7.96,-7.96c4.393,0 7.96,3.567 7.96,7.96c-0,2.044 -0.772,3.909 -2.04,5.319l0.165,0.165c1.194,1.194 2.388,2.388 3.583,3.582c0.455,0.456 -0.252,1.163 -0.707,0.708l-3.755,-3.755Zm1.754,-6.019c-0,-3.841 -3.119,-6.96 -6.96,-6.96c-3.842,0 -6.96,3.119 -6.96,6.96c-0,3.841 3.118,6.96 6.96,6.96c3.841,0 6.96,-3.119 6.96,-6.96Zm-7.46,0.5l-1.5,0c-0.645,0 -0.643,-1 -0,-1l1.5,0l-0,-1.5c-0,-0.645 1,-0.643 1,0l-0,1.5l1.5,0c0.645,0 0.643,1 -0,1l-1.5,0l-0,1.5c-0,0.645 -1,0.643 -1,0l-0,-1.5Z"}))}function e$(e){let{onClick:t,className:r,disabled:n=!1}=e,i={cursor:n?"default":"pointer",stroke:n?"grey":"yellow",fill:n?"grey":"yellow",...e.style};return c.createElement("svg",{onClick:n?void 0:t,width:"30px",height:"30px",viewBox:"0 0 24 24",className:r,style:i,xmlns:"http://www.w3.org/2000/svg"},c.createElement("path",{strokeWidth:.3,d:"M16.279,17.039c-1.396,1.209 -3.216,1.941 -5.206,1.941c-4.393,0 -7.96,-3.567 -7.96,-7.96c-0,-4.393 3.567,-7.96 7.96,-7.96c4.393,0 7.96,3.567 7.96,7.96c-0,2.044 -0.772,3.909 -2.04,5.319l0.165,0.165c1.194,1.194 2.388,2.388 3.583,3.582c0.455,0.456 -0.252,1.163 -0.707,0.708l-3.755,-3.755Zm1.754,-6.019c-0,-3.841 -3.119,-6.96 -6.96,-6.96c-3.842,0 -6.96,3.119 -6.96,6.96c-0,3.841 3.118,6.96 6.96,6.96c3.841,0 6.96,-3.119 6.96,-6.96Zm-4.96,-0.5c0.645,0 0.643,1 -0,1l-4,0c-0.645,0 -0.643,-1 -0,-1l4,0Z"}))}function e0(e){let{scanning:t,capabilities:r,onZoom:n,value:i}=e;if(!t||!n)return null;let o=(r.max-r.min)/3;return c.createElement(c.Fragment,null,c.createElement("div",{style:{bottom:130,right:8,position:"absolute",zIndex:2,cursor:"pointer"}},c.createElement(e$,{disabled:i<=r.min,onClick:function(){n(Math.max(i-o,r.min))}})),c.createElement("div",{style:{bottom:180,right:8,position:"absolute",zIndex:2,cursor:"pointer"}},c.createElement(e_,{disabled:i>=r.max,onClick:function(){n(Math.min(i+o,r.max))}})))}n=new WeakMap,null!=globalThis.BarcodeDetector||(globalThis.BarcodeDetector=ej);let e1={width:"100%",height:"100%",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",overflow:"hidden"},e4={width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",position:"relative"},e2={position:"absolute",top:0,right:0,bottom:0,left:0,pointerEvents:"none",display:"flex",alignItems:"center",justifyContent:"center"},e8={position:"relative",width:"70%",aspectRatio:"1 / 1",border:"2px dashed rgba(239, 68, 68, 0.4)",borderRadius:"0.5rem"},e5={position:"absolute",width:"15%",height:"15%",border:"4px solid #ef4444",top:0,left:0,borderBottomColor:"transparent",borderRightColor:"transparent",borderTopLeftRadius:"0.5rem"},e6={position:"absolute",width:"15%",height:"15%",border:"4px solid #ef4444",top:0,right:0,borderBottomColor:"transparent",borderLeftColor:"transparent",borderTopRightRadius:"0.5rem"},e3={position:"absolute",width:"15%",height:"15%",border:"4px solid #ef4444",bottom:0,left:0,borderTopColor:"transparent",borderRightColor:"transparent",borderBottomLeftRadius:"0.5rem"},e9={position:"absolute",width:"15%",height:"15%",border:"4px solid #ef4444",bottom:0,right:0,borderTopColor:"transparent",borderLeftColor:"transparent",borderBottomRightRadius:"0.5rem"};function e7(e){let{scanning:t,capabilities:r,onOff:n,torch:i,zoom:o,startScanning:a,stopScanning:s}=e;return c.createElement("div",{style:e1},c.createElement("div",{style:e4},c.createElement("div",{style:e2},c.createElement("div",{style:e8},c.createElement("div",{style:e5}),c.createElement("div",{style:e6}),c.createElement("div",{style:e3}),c.createElement("div",{style:e9}))),n&&c.createElement(eZ,{scanning:t,startScanning:a,stopScanning:s}),i&&r.torch&&c.createElement(ez,{scanning:t,status:i.status,torchToggle:i.toggle}),o&&r.zoom&&c.createElement(e0,{scanning:t,capabilities:r.zoom,value:o.value,onZoom:o.onChange})))}var te=e=>e instanceof Date,tt=e=>null==e;let tr=e=>"object"==typeof e;var tn=e=>!tt(e)&&!Array.isArray(e)&&tr(e)&&!te(e),ti=e=>tt(e)||!tr(e);let to={facingMode:"environment",width:{min:640,ideal:720,max:1920},height:{min:640,ideal:720,max:1080}},ta={finder:!0,torch:!0,tracker:void 0,onOff:!1,zoom:!1},ts={width:"100%",height:"100%",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",overflow:"hidden",aspectRatio:"1/1"},tc={width:"100%",height:"100%",objectFit:"cover",overflow:"hidden"};function tl(e){if(null===e)throw Error("Canvas should always be defined when component is mounted.");let t=e.getContext("2d");if(null===t)throw Error("Canvas 2D context should be non-null");t.clearRect(0,0,e.width,e.height)}function td(e){var t;let{onScan:r,constraints:n,formats:i=["qr_code"],paused:o=!1,components:a,children:s,styles:l,classNames:d,allowMultiple:p,scanDelay:u,onError:h,sound:g}=e,f=(0,c.useRef)(null),m=(0,c.useRef)(null),C=(0,c.useRef)(null),w=(0,c.useMemo)(()=>({...to,...n}),[n]),A=(0,c.useMemo)(()=>({...ta,...a}),[a]),[y,E]=(0,c.useState)(!1),[v,T]=(0,c.useState)(!0),[B,R]=(0,c.useState)(w),M=function(){let e=(0,c.useRef)(Promise.resolve({type:"stop",data:{}})),t=(0,c.useRef)(null),r=(0,c.useRef)(null),[n,i]=(0,c.useState)({}),[o,a]=(0,c.useState)({}),s=(0,c.useCallback)(async(e,n)=>{var o,s,c;if(!window.isSecureContext)throw Error("camera access is only permitted in secure context. Use HTTPS or localhost rather than HTTP.");if(void 0===(null===(o=null==navigator?void 0:navigator.mediaDevices)||void 0===o?void 0:o.getUserMedia))throw Error("this browser has no Stream API support");let l=await navigator.mediaDevices.getUserMedia({audio:!1,video:n});void 0!==e.srcObject?e.srcObject=l:void 0!==e.mozSrcObject?e.mozSrcObject=l:window.URL.createObjectURL?e.src=window.URL.createObjectURL(l):window.webkitURL?e.src=window.webkitURL.createObjectURL(l):e.src=l.id,await Promise.race([e.play(),new Promise(e=>setTimeout(e,3e3)).then(()=>{throw Error("Loading camera stream timed out after 3 seconds.")})]),await new Promise(e=>setTimeout(e,500));let[d]=l.getVideoTracks();return a(d.getSettings()),i(null!==(c=null===(s=null==d?void 0:d.getCapabilities)||void 0===s?void 0:s.call(d))&&void 0!==c?c:{}),t.current=l,r.current=d,{type:"start",data:{videoEl:e,stream:l,constraints:n}}},[]),l=(0,c.useCallback)(async(e,n)=>{for(let t of(e.src="",e.srcObject=null,e.load(),n.getTracks()))n.removeTrack(t),t.stop();return t.current=null,r.current=null,a({}),{type:"stop",data:{}}},[]),d=(0,c.useCallback)(async(t,{constraints:r,restart:n=!1})=>{if(e.current=e.current.then(e=>{if("start"===e.type){let{data:{videoEl:i,stream:o,constraints:a}}=e;return n||t!==i||r!==a?l(i,o).then(()=>s(t,r)):e}return s(t,r)}),"stop"===(await e.current).type)throw Error("Something went wrong with the camera task queue (start task).")},[s,l]),p=(0,c.useCallback)(async()=>{if(e.current=e.current.then(e=>{if("stop"===e.type)return e;let{data:{videoEl:t,stream:r}}=e;return l(t,r)}),"start"===(await e.current).type)throw Error("Something went wrong with the camera task queue (stop task).")},[l]),u=(0,c.useCallback)(async e=>{let t=r.current;if(!t)throw Error("No active video track found.");{e.advanced&&e.advanced[0].zoom&&t.getCapabilities().torch&&await t.applyConstraints({advanced:[{torch:!1}]}),await t.applyConstraints(e);let r=t.getCapabilities(),n=t.getSettings();i(r),a(n)}},[]);return(0,c.useEffect)(()=>()=>{(async()=>{await p()})()},[p]),{capabilities:n,settings:o,startCamera:d,stopCamera:p,updateConstraints:u}}(),{startScanning:I,stopScanning:D}=function(e){let{videoElementRef:t,onScan:r,onFound:n,retryDelay:i=100,scanDelay:o=0,formats:a=[],allowMultiple:s=!1,sound:l=!0}=e,d=(0,c.useRef)(new ej({formats:a})),p=(0,c.useRef)(null),u=(0,c.useRef)(null);(0,c.useEffect)(()=>{d.current=new ej({formats:a})},[a]),(0,c.useEffect)(()=>{"undefined"!=typeof window&&l&&(p.current=new Audio("string"==typeof l?l:"data:audio/mp3;base64,//PkZAAhghE0AKToAJra/n0FQygAAAGIkYJgmCYXBMAAGCTJz3zhCEM//z//1hz//8MMMMN08ssV6e5DDWIQreAgCvFKy8bXgIKMkUDDBgzJwIBtkRMQAocxIFdxghQGKDoEziAzQxOBOdH92i/iGi+zDCAEIX46a73HrSybZw1x3JZjXp7dSNy/P68rjcbt7p7fakMP5LVMyzCaj1pjvejYYAIDgDGzECjEAk1Jl3559HIon8hzlfPVTCvGJZzfcKSxXfyMWM88//9VKSxXdtnb9vomOuuRyiWVYbf+X8zp6fKGHIdycuWMMMMMMKSnp+6wsYc/9f/7z7rPPWHN556p6fP8MMMP///PPP/7+GHK9PT6p7f/unldP2np7YeHjweiYA4GLNAgAiI7u57n5//oc5/yfk6znOcPhwOBwggHCMpA4HA4KEyHOc5znO+hPIc5//+fqJh8XQPh90JU5xQinOf//87/zvP+ggAYuhCKHxdA+Hxd0EA4KKHA4ciB3kOXfXB/gmf8p/B96lAMKAgAADU+BujARHgwdisgHMfAUHAiceBg4ASBgZBiBIBH4ZaHOJsLhf8R+HYEciIgYSwj/+Bi7EqBh+AcBn5P6Bh4TuBmrAMBiZH7gaLEWgew//PkZFMlyek60MpYAShzqqZhm6gCUWeEUWAewEWYGN4X4GDEPwGE8S4GDoOIGAYKgIQOkz//gGARAUB+CwGxTwMAACAEgyAwdAlAxKhzAxXiZ///AxcEwAwJjDAziCAAwQgdAwRgdAsJQDAmAcGzYDwAhZIAKAcIQB4GT9TQMJ9/4Gi1Fv/AcAYUqKBAwGgNAwVBAAwGhwAwdBlAxFg1AwlgzAwNBuAkJQDBgEEDEqGECgChFgBgL//CIswYYH//+HKCpk4K0C9AaKKCAOBeMcR4X9C44BwABCgGAsGYCgTwHAcAwXAiAwSAQV///CJP9lwMBQAwAAAWGo5lVLCcaeneVhJAVGai3//ioaUEf//gaTAYGCj8BnEwfrIqDcsIQb/vmhU/8fAs0G8YGGwKST8Igj4GCATipksVzY8p//90FWJwh45AkX//4fCF9wMEgkL3uQc+gbGJ8t4MBAMBP/hEXf9FRuWBcAfIFjYzQdoLCBwh7IWVlxaX/w8oMCP/+EQT5poGB1Ir90DhiV6af/jFYBpT2BgoQyyt2M0ToBdEaZyzt8nTo3xdNDCTSd//o6F06CjooxRr4jVF/0bOKD6OMUNDRxiMUVFR0FFQPhGXRjDpr4MAEA4wIQUhYOIw//PkZE4nrg08sOx4ACQ0CopD2aAAlwTTBxQeMcwd8w8gZTAgABXwGABOkzpI0wAQAExnWfP4x8ZjKunWdZ1o1Qe6lFGKCNULORQCELAAPnRUf/GIx/0FHGfo3SdZ0qP2cukXKLlRtBKFgAQwCkwJgFRYBEEATmDsLSY2QiQYCEnEra+UZo6F1aKMRr6GhoaP/+j//ov+i+hjLqBgMkYoqChoo1GqCjoY06TpRqjjLOYzRRmMuizmgdKhdF8kjTAAAPMCwGswzglgwAJfzqxmioKH/////////////+ijLOAwFmN0f/////////Q///8ZjP/9DGYyFwIDBeB3MA4AlBWidKM5spykAUAQOCakK+udGqN8VDYjTRuN//0bVX//6Kio4xQFv//nf//kKOoAEIB6SY6hcxenp85///4/DEPf/84Xvl4vF4dwtxfEJgCYQHMSgKCROp4+fn////L4tQf0unz89OecLxeL0ul09y6enzp4dIhKAQFA5hKfLu1nU7/Wr71rRKJUBQSUVt////l08GUPVWgAAYAeNKsxbQNAAhQx3/oKGMM6dZ1Yy6sajMbfKio7lNcv0sScZ47lNcw32ekVFMP9fp/f+mklI6at6u/dBMQuW6y+C5BcgwAA//PkZEomGg1HYGe0riWsCqbEwiY0BJFJMxrCcxGgQ2VK8xxCF1wsABgQBBgQBAYAFCkYCgALkK4fJ00E5ctJN1Uxy5au3XoGcM4dSN0EYZwzhW0WCh1PZwzhnFFQxtnbO2d0Kt6CdXbruggkTEX5Rs4TEQUVsdVXaCcuWXIBQUGmwmgoAGcJiM4dSioHQdBnEY+hjcbov//jcb/43G43G/jCRibfxugdBnDqULO2duvQumztncbo4xGKKNxugooxGIxQULoOgFgUMTA/jbrxiMDmETL+eLhcPThzOeeOZfL5fIcBYuXD/OHv8t//LJFxjxQYHHKDGFkZMtlmKM0RgQfbVtxFIHbQBDRuzRguEI1c9XZ/rWmrZfVU3Oc/9utNNygRQkCoHlAYstNN6DJ1IINpoINppv+WG/+g2mmnUmmmpBAoJIJhnwB5xkymn606Df/t000xhU03UXC5Ol8uFyXy+Xy/l8v5w4cLk4XAvwAYUvl/QL5fTdlKZkEEEEGumaKaX00zIOemf6CH///ywI3LKpiQ2Ef4xYWMSDyI0XwzikpL0ajVHZ3j9DGIjeuXcNfjn96/S/TUz/X/vX////5K/r+v7Jn+LlAoGZOIQIwMKBQM/yppKYURmhGZkR2b//PkZE0mHg1HEG8wyiD7mlwAyDU8nJHi5htakZmZGNiRcpkJhBmUmZYQcM1VDEEBggNDV/ASEZRYKFZIhkXKLlKmk7IVTKmZNJ5KhiqVUqpX+k7+qlVKyV/ZIqZDFDWTJlFyi5TJpKhiXKBQyZzIS5JhBoayV/AQWVpGWkcYTV5O/rIVSv8/0nkr+v6/vyR/X9f1/ZN8kf5/n99/n+f5/lTKmf9/mRP8yFUqpWSv4yJUqpX9kj/MmZNJ5K/z/P9J5P/v6yFUz/CEoywgUMqZkz/FssECIsVq0aPRrRRatFFFykQZJ2Ukl///5KjmksOaA7ALROJLDnEsLbq3GTMVrwb/0dGzmM0DlfBlHQOmrdR0MYi92TX6b/cmDHLclyUVYO/4O8sFTKFTKFf8yhUyhUyhXzKlDKlCsr/+ZUoVlSsr/+ZQqZUoZUoceMfcoZUoZUoVlfOPGcmDHKcpynKg9yXJcr3JclyXJclyYO+DXJclyfgxFZVb3KcqD/g2MxzsY72L9LubPR0dCyf5K5NNd+Tv9GXRdV0XJclyfcl/n+k8kf6SX/iVynvXf///4Mg/1OkxguYzmM5jMcLHMxwwGhgNCwXMLAcwMXTLi1MD7w7tmTdBvMmE0xQFzCwHMDAd//PkZGMpigkmAGOUvCfTgcgAuC+ITswMFzC4oMLhYMByn0xvU+p9Tr0xUxf/0xv///1PBYDhgNTFTFTGTGTGTGTGTGTGTGC4GTFMDAcLAdMVMVMULAcMBpgYDhcDGFwsYXFBikLGFwMGA4MBxgcDKfTEMUoAx2BisD+mKp5Tv1OkxkxkxkxkxkxkxkxlO0xVPJi/6nv////U7CwHTFTFTGTGTGTGU+p71PpjJipihYDpipi/6n1Ov//C4GC4GTGTGTGTFU8WBSZdLhWB//yKlotyLZFiLf8tyyRUipFSKkVGRBZQMiWC3yx//wxUGKsSvDFQGHUAL3QMsHDFIYpCYy2Bmagy6BjLgy6Bh3od58DCLAizBgRb+EQiyEQiwGBFuDAi3/////20QqSBJf/qcumv/////wMZdGXAYMu1gYy6MuBEZdBgy4BjLgy6BjLgy6BjLgy6BjLQy6DBlz2cJDLgRGXAMZdGXAYMuhMZcCIy6sJGaoGMuppwNRl3WBjLgy63wiEWAwIt/+EQi0IhFsDCLAi0GBFkGBFgMJCqaqYAgAYIhBBvwf7kwcqRq7V3IctacvlN6NxqgooxRRuNRiijEZjMGRuMvz9FGozQ+1X2qe1T1S//+VhaWAtKwtML//PkZEIkbgcaAHc1xCJrjeAAtfuUAtLAWf///mWePH95ZFgsyssvM88+jzO6K+z7OM48sH+WDiweZx5YOK+zxWLGJWsa6xadAv02fTZLSpsFpvLSga0tMgWgUgWmwgUmyWl8tMgWWmLSoFJsoFlpSwsmyBg4HhEdgwHAYPBwGDgeBg8HwMHg4DB4PBgOAwcOwMHLwDn5lAwcDgiDgYDwMHA/4MB3//+EQeDAdwiDwYLPhEWQYLODBYERZBgt8Ii34GLBaB5pfAZ0FvEAQ/UfwxULlx+IXx+8f5CELH8fxKhAIDFoHBEDRc3////gwHgaRXgMHYMB2EU/gwZ/gYbw3gY3QbgYNgbgYiAbgwN3/+EQbBEG4MBuDAbAwG4RBtAwbg2////+B9B6F//////////hEdD//////////8DRahEQGCInMNg3LBElgNjDciCwG/lgNiwGxWG///mG4bf/+WA3Kw3KyIMNw2Kw3MNg3MNg3MNiIMiQ2OK0xMiA3KyJLAEZgRARmBEDEYFwBBgEgEeomowol5YAJKwCDAIAJ8sAE+gGBgCJYAQUZ/13LubMu5dyiYOAhKwEFGf9AOoz/g4CFAIol/lgAjysAgsAElYBP////lgJksBM//lgRUxF//PkZGApggsIAHt2wiNLoggAqCz8RFSwIqYioipkjEjHCCSN/+WJg5iYOYmPK5krmTmZk5mZ8sTJYmf8yJiNiIisi/ywRmRkZkZGZGRmRERkREZERGRERkREZERGxEf///hETIMEyBiZEyBiZEzwiJgDEwU4DV2gQGFOBgmP8GAiAwRAiBgIgYCIGAi/4MBHAwRgjAwRgjwMEQIgMcgY8DBGCMDBGCMDEyJn///+BiYQIBlObj4eYLIwsjAOBGFkYBoEQ84eULIw88LIA88PKHnw8sPKFkeHlANAiAaFUDAiMYPIHmDzBZBhZB///8GBOA1sH/Awni7AwnhPCITwYE/CKzBgt8Ig8GA4Ig6EQdww4Yb+F1sIgtwYCwGAsAwWAsBgLAYCwDBYCwDBaHUDF+VEDKgC2BgtBaDAWgYLQW////gYLAWQMFgLAMFoLcGAt////4RMABmBSj4NgwMOGHBsHhdcLrwbB4XXC6+F14YcLrww4Ng8MOGHBsHww4Ng8AUC4AoMQMC4fgw3wutV4QIsAgFYIBWCCVgHeVgHeWABCwAIVgH+YBwB5aRAstKgV6Ba1oMg9avtUVN/qdLWciD/cqD3Jg6DFPuTB/////lYAn//mC8C+YLwL5gvgvGC//PkZFEpLgMKYXt2xB5LogAArbdc+C8VgvlYL/mC8C+YLwL5YBfMNgNkwXixDWoHPMc4VUxVA2TBeDZPYXzXl815fK14sLxYXvK1//NeXzXl815fMtdDLSw3UsMtLCstMtLDLSwsFhWWlgt//8sFhW6f/////+WF7zXl8sL5ry+Vrxry+WF/wMLwX4MC+EQv/AwvBfAxsjYAyqpKA0lpLAwvBfBgXv///4RA2DANAYGgNgYGgNgYGwN+EQvf/BgXwYF//8DC+F4Dd6NgGBf4AgFwbB0MMF1wuuDYMBsGwusDYOBsHA2Dvg2DQuuDYOhdcMMF14YYDGYDEGwfhhgbB0MN///4RBaEQWgZByoYRWYMFvwiDsGwaF1gbB34GDwdC64XW/w1aKxDVgatir4YcLrQbBwNg7////////////+ESfgZPyff/////////4MFmBvvMAERZ8CC5adNj02E2S0qbCBSBZaYtN///oFlpU2QMXIFAYuQK9AtAo7IxLTpsIFJs/5aZRxQawaxGfIg9CsrI+PUqkWdOHp7ODsOl6cOHZ4dsulfLI9/8BAAgMBACsBLACVgBjoAWAE5OTM6WDAR0zodMdHCsJLASWAjzCQgsBJhASWAjysI8wkJMICU//PkZFkeqgcaYDd1th6DogAAqCuIxQwtDA5MdMULgyY6YqnXqdep5TpMdMVRNRL/UT9AN6jCiSjKAZRNRhAMomgFQDfCIJwYCQiCAiCMGAkGAgDBJUA3eVAMEgj////8SrDFIYqh5g8kPMHkDzB5OFkYeYPJ+HlDzB5+AYVQDibCyIPLLEZIsjIliWS0WywWyyWuWCyWJaLZbLJYDHgLAItFvy3///4eYPKBlQTh5QYEODBb//hhoNg7/C62DYPFUKwKsVgNWBq8VkVcVkVUVYrH//hGff/8Iz8GT7//////wiX4ML3gwvgwvf4ML3gZeqoHVGyBl4vgZfL2F1oNg4Lrg2DcLrg2DQw0Lrhh8Lr4YcMNBsGhdcGBcMOGGAFGAGMQsF1guuGG4XXV/4uQXOLmF1kL/8lCXkuSk4O8/FzHzuP0hIucf/CIFAMCgFPgYFAKAwCgGBQCgGHYO4GBVM4GgQI4RCMEQKQOtQjUI1CNcI14MqDKwDIAGQDzh5wsjDyw8uHmDzYeUPN+DEYRRhFEIowYgDQgIhWDApgwKAwKQYFAYFQMKkYDOxHAzsRgMKBUGBX/h5v+Hm4ecPNh5A8wBwjhZHhZAHlDzBZAFkAeQPKHnCyILIAsih5QsgDz//PkZLQgIgsMAFp1xCGLogAAA2rkB5w8oeYLIQ8kPMHkCyGHnAMKoGmAgFkUPMHmDYAtfHPHNkr/JYl8c8lCWHPHMkqSo5onMAkBDnfJX//8IgDBgBhEdAaxSQMHUGAEIgAV4risCcipBOMVRVBO////hEvAxsAwvf////8MPDDww4XWC64XXDDww4YcMPC6wYb/wYGgiGwMplMDYhTCIaCIaBga/////8GA+EQdgYPB4MB4GDwdhEH8Ig8Ig8GA6EQcEQf8DBxlBjoAweDuFwgCQXiL//iLiKCKCL4XCiKRFwEgoRT4iqpMQU1FMy4xMDCqqqqqqqr////xuDd8fv/DVwrArEIg6EQdhEHAYPBwMB+ERYBi0WAwWAZ0FoGLYOB+JfgZ0FoGdBaDBYbn+WHm9xW7/Nzzc83PLDzc8reWlA7S0oHamx//6BflpU2C0qbBaf/8yk/ysn+VkMhTKQrKVk8yE8rIVkKyYsAn/5WC+YICmCApWCGTkxtKMcVWlgFMEBTBAX////0C0C0C/////////LTFpvTZLTlpv/0Ci0xaYtOgWmymyWkAxcWmTZTYTY8tMmygUWmLTIFpsFpv9NnywLmLCx2RgWmQKTZ98UjnzZ1/++D4M6/////3//PkZOgjIg8IAFcbxCaTogAAoCuIzfJ8vfJ8Wcs5fBnD5//++L5f////////6nCKv//+o0o0FUYwqDNGCwgUMKC0VVGkVv//4Yf8LrxVCriqDVoasDVuKsNWw1bisBqwVkVkVX//gy/4YYMMGGhhgwwNg4LrhdcLrAYWGAA0zBsH4Ng3C6/////4MAoGBQIDAKBgUCAwCgYEAsDAoEAwKBAYBYMAoGBQIBgUCYGBQIBgUCwiBAYBYMAoRAgRAoGBBMBooCAYFAkIgTEUxFv/EWxFxFhFoi0LhQuFiLCL/EWqTEFNRTMuMTAwqqqq//////8TWJriV/hERAwRgYiEYGIhGBmIRgYiEYMEQMEcDMSiA1GYwMR+UDMTkA7KIgMRGIGGM0SIrRGjRlhEV4jxov8rR/5YRFhEdQSViCsR5iRJWIKxPlgQYgSViSsR5WJMQJLC78IiKERF4REeERGEREERHwMRGOBmIRhERhERAwRAYiEQGIhGBmIRAajkgG5TEEREDBHwYIwYI4REXCIV/4MCmDArgwKAYUO/gYVCkGBUGBTgYUCsGBUIhUDCoUCIVBgVBgVBgUhEKQMKhUDCoUBgVAwoFAYFcGBQDI6oA5+qQiFAMjBUGBXCyEPIFkOH//PkZPMj6gr6AFdVwifkBdwAoC2Ilh5/8PPh5Qsih5IeUA0TAZrE8PP////wMRiIIqID8piAxEIwMxCMDEQjCIj//BiIDRo8Iogii/4MKwYVhEoESkDEiIGJEgYlfBgmDBIMEAYgSBiBIREAwSERH/8I7wPfuCO4I7v/8GBvAw3Bv/////gwN34RBuBg2BuBg2BuBg2BuEQbhEG+EQbAwG4MBtwiDaDAbhEGwMBuDAbAwG4GDYG8DBsDcDBuNwDRWDcIg2AwbA34RBv///CINvCINgMRIN/////wiG4DDeG+TEFNRTMuMTAwqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq/zB4OLAP8rBxWDisHlYOKxb5WLTFgsMWiw18LDOnjPxZoxadDOgsLAtLAbKymVhsrDZWG/LAb8w2GywGysNlgNFgNGFwuBQuWnTZLToFFpisLJsFp0Cy0voFAQLFZkMCAQwIBCsC/5WBCwBCwBCwBCsCeVgUwIBCwBDEwEMCAQrAhgUClgCFgTlgCGJgJ5gQTGJwKWAKYFApgQTGBQL5kY0HMjQYnAhWJvKwIWAIVgQrApYAhWBPBgECIEBgE/8IgXhECQiBAMCkcDEwEwMCAQGAQIhoIhsGBsIhvgwN//PkZN4irgr2AAOVqiUThegAnCtMgwNwiG/8IhuDA1hENhENgYbKYHb2IBhoNhENAwN////8IhsDKZS////8IhuBhspgc0KQRDWEQ3///CNf4GAIMDAwgAwAAwhAwAwYAIgwiHBgAMAYGAAGHv///CIBqBqESDEIgRYGoGHBiDEIgGoGgMAYgxgwgxCIETwYFYMCoMCuDApBgU8DCoU//gY3G//wYFOBhUKBEKQYFfBgV8IhSEQoBkYK8IhT/wMKhXBgUCIU4RCoRCvgYVCsDCp3A1SqAYFAiFP////gY3G1TEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVgwCAwCBEC///////8IgUIgWEQIBgUCAwTQMCAQDAoEhECAYFIwGaRMBotWAasdwH/KqBmhFgaKI4GRgKVpjCBTCBPMJHNMFMIFKwnlgJ5hUxhUwGWlpAMuMuWLTFpvQKQLQKLTIFJsoFIFFZb/8woQwoT///MIF8rC+WAphAphAppgpYClgsmwBC6bHoFFpUCi0wGXFguWCwFLgUsgX6BZaVNj0Ci03//+EQKEQKEQLCIFAxMBcDAgFAwIBQYBODAKBgUCAYEAuDAKBgUCAYFAgRAngwCgwCwiBIRAmE//PkZN4iogj2AFdVxiUrhewAhOkwQIBgQ0gbOI4GRhOBiYCgYEAv////CIFAyMBQYBf///CIF8IgUDAgFBgEAwIRwMCCcDEwFAwIBf///CIEQGMDGEXCKBpCIDCEQDCAsYInDFQMMAuYSoTQMVfBlf//+DKfBgAiGDAQMIMIgCIQYADAEGAgwP////8ItgNu2/8DAAAiABgHhECBgQGEQARA8GAeEQIMABECEQIMAwYBBgCDAARA4GAAgYACDAARAwiAgwDhED4RABEBCIGBgDoH2dgZwCBgAP//BhX/+DCiTEFNRTMuMTAwqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqF1ww////////hdeGGDDBhwjeF102E2fTYAowMLjE0zTDfyyMYjEDJTwMLSwFi0/gQLoFIFpsegWmyBQsWlQL9ApNgtIWn8tMgX6BaBX+WmQL9Nn02f9NktIgUgWmwWl9Avy06bCBSbJaUtImyWkQKLTIFgYWoFFpAKFwMLzZgwAwvLTIFlpU2fQLQK///4Yf4YaF1sLrg2DAuuDYNDDQw4NgwLrhh4XXC64Ng3C60MNwbB4XWC64YcMODYPCJYDlMQBlwRLg2DOF1v/////PkZM0eKgz0AE+UuCnLFfAAk3Fsg2DAYX/ww//4YaGGDDhdbDDACFwMuXA2DADlygusDYOBsGhhv//wNU/CKQusF1oXX+F1wuuGGC6wXWDDBdbC6wXXhHv//4AHIFgC0BZAA4Ba4FkADwFsC3AtQAOcC0BYAtgAcAA9+BYAsgW4FvgWOABwC1oq+it4RT0VVG0VFGkVywo1/RWU4U59TlFb1GvRX////QK9Nj/9AtNn02fTY9NhNlAr/QK//TYTZLSJsIFemx///oF/5aZNj02U2C0yBaBfoFAS6bKBfoFVTEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVQiA8GAO/4RAf//////wYA4GAO+EQHAwB4GDoB4GA4HQRB0BhlDIBpEAcBgPDIBgPAeDAHIFAYuLTpspsJsFpi0haRApAtAstIYKClgF/ysF/ysE/ysF//LAJ6plTlYC1ZUypmqtWVK1VqyplSFgBaqqcwAAVIqcrEBAAKmMBERCQhwD5iICIQEyABMAABBVGXF4gIA4DLAAWAEQgLVSsBaq1ZUv+YKClgELAJ////5YBP//LAL5WC+Vgn+WAX/LT+gW//PkZMIhLgrsAFt1yiD7FiB6A9qwmwViybCbCbCBSBabCBSBabPpsegWmwWn9ApApApNgCGBmEwWlLSpsf////hEHgYOB3////4GDkiBxMHhEdgYOB4GDwcBg4HW23gooFYHd/+BnAz8O8Vv//FaBm4rBWh38es2zbAtD1m2PVBqBrBqBrAF7AF3/5el84MkuHTgyy+dL0dRnEajMIyOn8RsZ5HGG/kbIpHIkYUiD0y2WctlZaWSwrKx6FZbxnL54ul04eLhw9Ol84dl86cOHT3Ipb5Z8qKpVx6/j0EkLctVTEFNRTMuMTAwgWQLQAHgLf/////4Fn+EQESEcIgInCIRW9TkrCzH0YIjjWtI+YeCD0woLCBQwsKRWUaRVCBZFZRtRtFRTlTlThRtAr0C0C//02E2fTZQK/0Ck2f9TlFRTlTlFT/9TlFVFRFb/9ThThynIQbWkAREue5S11rKdlkDEVABh61ExYPU+tOD4MQgcn4iwigivxFRFoXDcRQLhoiwioXCeAkFBcNiL8RURQBIKEVEWiLBcKIqIpEXC4QReIoIpCIKAQMxFAFAsIuIr/4YYLr//ww4XWBgwC64XX/hhgw3hhwuv8MNhhguvhdcAUYgaYTAAoWDDg2DQbBg//PkZPYe6gzuADd1uDKsBhBAe89cYYdGjtDT2hDmlD2leaOv9oX0OX/0NNLplNf8ewakNWGrTfTRpphNJtfaeh7T1/tStdtTpqN532lpX/1/kmQ5eaf2hoaV9paGleJAvd0rer2pWu1ar2vk56vVrV5Hj1Nop+/lfv5Zpkem5kX/2vtata2rq783nbvtbW6VjW6a2rq13+rO6VysV58K7tbp0ru6Vrtqa1a67tr6sau1ulYrnfa2v927Vrt21NSuN5X927/D4diAQANh4gEIfxB+IQG/D/4gDw/iGIRD+HRATEFNRTMuMTAwqqqqqqqqqqqqqqqqqiIEK/ititFX///8VcVuK38VsVhXBOQTpq/qm8QCAcBmIxgdfFZCHECpxFBFAuFEUC4cLhsRTEVEUDVoDQD+KrFYirFX+KwKoVgVUVUVeKwGrhWRWBViqFYDV4rMViKyGroauBg/A0YThVgNAEVQqw1fisis4quKxxWYrAatxWIqoqg1dFYxVxWIrAavAaAArIrAqoqhWYrIqvxWMVYrARCADhcKoVYavDVgqhV4qorIrHFWKuKxFViscVUVYasAaAfFY/4YcMNhdfwuuGGC64YfDDA2DwusANygbBwAxhAECwNg8MOF1hD4//PkZOkc4g7yBjd1sDNkDhgqYwtcYoUyiQ0NCkxQ0NCQkSIbGKIyRGEo5mUaNEhGKEHMmaGhI0UokORvMmZmZSZSiMZQkRnKKZkzRoSNCQpQ0NEZoaNFJkhh3KOUJEhIcoSJCQkIzlDlEjMDMyMpQ0JCQzNCRGaNCRISEZoZihoaIwlEHKNGiMzFEjRmSIwlHMyZmcyiGyMxRyhIkIxRSZokSEYGBkiRojAPESNGjQxvKHMmMoSKTJCQkIwlFKLDYWGhoZhYWAAUGBsAwsMDYUAcK4XDPhQVwwLAH/DfgHC6TEFNRaqqBgjwYI//wYI/8GCL///+DBFhERgwRcIiPAzGYgMRKIDcskBgjAxEIwYI4GiRwNEjhFH4MRwYjA0aOEUf8Io/BiLgxGEUYRRQij/BiKDEYMR+DBGBiMRQiIgYIwiIwYIgiIgMRGIDUSjhEReEREBiIRAwR/wYI/8IiP4MEcGCIIiOERH8GCIIiMIiIIiODBHBgjBgjwiIoREcIiOEREDBFCKiAxHJcIiMGCMGCLwYI8IiPhERgwRAwRAYjEUIiIGCP/AxGIgiIsGCL//4REYMEXhERBERhExhExAzyAZiEWDBGERGeKsVsVRVFcVwTnFcVxVgnYqipFUV//PkZPkcfgzeAFaVxDgUBgAgatvIYrgnYqgnQJyK4riqK4rwTsVor4rQAjABAgnQritFSKoqQTsE5C1haxfF7F4XQtULSFqC1i4LwWkXIWkXRdF+LwqAnME4iqCdwToVuK+HAERWAkAlgJB0OB3Doc8OYc8VCsVgIB3DuHAERUAkAkHMOBwOAIisO4dwEAEAEg6AkAl4CeHA4AkKhXioBIOiv8OB3F+LgWkXxeF8XouxcF0XBeF8XBdF8XhdAdouC8AEsXYWsXOKsVBU/FaKmK/FTxXFbFUVPiv/8VfxXitVTEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVAXX/8MN/wwwYf//DDBh4Yf/+DYODDfCIFAwKBQiaANFgUDAgFAwIBcADwFuBY/At8C1wLfgAdAtwLGBawLcC3AsgWeBYwLX4XW8LrBhgiFgMlhYLrhh4XWhdb8MOGGhdcMP4YaF1gw8LrwuvhhoYYLrYXWDDBdcGwfC60MN/ww8GwZ4XXC64GZQsF1wbBnDD4XXg2DcLrBdaDYPhhgusF1uGGC64YfhhoNg0LrQwwYcMNC62F1/g2DYXW4XXC6+GH8DTIW4YeB/wR/wjwR+DP/y06bJaRAtAtAtAs//PkZNwY2grqBFW1xDhTRegABLBstL6BRaVAstN5aYtN/psIFf5aQtIgWgUWk8tImyWkQL9Avy0qbKbKbJaf02f/0CkCy05adAsC3K7+mx5aVAv0C/TZ8tOmymwB2pspsFpPLSlpy0qbCBSBXlpk2E2E2C0paUtP6Bfpslp0CkCy0ibJaZAosWQLLTJsemwgUmz6BZaYtOgUWmLTpsJsoFJslpPLT+mwmz//6Bfpsf6BSBSbCBXoFeWk9NhNhAr0Cy0/+WnTZ8tJ/oFf6bP+myWn//8tMmz/lpf//////8sPTEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVBgJwiCYMBP//gwE//////wiCAMEgkIggIqIDKpUCIIgwE8IzA4j+EYhGAZMIyDJA5gGRhGcIyDIhEQiIGAgwIRDwiAGAAwYMHBghGAZMGRCMhGYRgGRA5gIyDIwZPBk/wZIRmEY/gyOBxIMgIzCMgcTgwRBgjwYJBgngwTwYJhEQBiBGERMIrwOouAxAgGCQYIhERhEQDBGDBAREgwTCIgGCQYJgwSDBHwiI4MEAwSBiRHCIkGCYGJEQiI/Bgn4REYREeBiBIH6XhERhETwogoy04mpZibFmJuWYmx8n//PkZN4ZrgrkAFZUyDcjofgAA9qc0HafZ8HwNrjaG3xtjaDtJwTo+D55Ow7fxNRNSyLPlqJsAr/lmA/FmWYm5acTQVgTkVQTnBOQTgVxVBOBVFWK4J3FUE7gBABOwToVQTsVhUFcVRXBOhUBORWiuKkVYrCqK+KoqgnQrAnEV+K8E7FQE5xWBOQToVQTsVRW8V4qRUFYVgTsVhXioKgrCqKwqAnYqCtBOhUioK+CcisK8VRUFSKnirFQVBVFcE6FcVoJwKoqCrFXFUE7gnAqiuCcQTsE7xU8VP8VYrgnYrCuTEFNRTMuMTAwqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqn3/l/J/8vHL5f/BhwCETgGpIDOATgEIOFoDcHzgEIDgC+pG7eve1S/6tat7rXupDXSStWoIpGS4TSMQNIySMwpIyAaRkkZ8GJGdBtbwYkZ63tCKRkoIpGWDEjLCKRmEkjOvsEUjJOsIpGa6bOrgxIzsEkjIGJGYUkZVKCKRmqoDkgEjMJJGSYMSMAikZJ3qhp0dq9ru3X1I36FujPbXuxwKbr//waABfg0AC6DUDUDXBqBoAmQawJiGrhqDUGoN//PkZL8USgKcAAblrroTgfQAbhtQAag1QawaYNIAuQa12Lv9si7F2NnXa2Vsi7i/DZl2tlL9l+vL8tlUTUTUTUYQDIBlGUAyifqJKJqJ+p5Mf/U7TETEU7CxkxVO1PKdqeU8u4vq2ddnrsbM2Zs/tl9s67V2NlbIu5sgaA1Q14ExAmAaA0BrDVDXhpgTPDQGuBMA0gTKGgCYgTHDQGrDUGgNcNQaw1hoAmcNIaRIAtALVEgC1gteI8FpBaBIAtcRwkAWgFpBaILVBaIkAWgRwjhIiPEeDXwa/4NUGr+DRwaKBgX/8IhP//wiF8GBMGBP/wYEhEKEQmEQuDAoGFTAwLgwLhG/4MvCM8GSDJCNwZQOXg2DwbB8MPDDhhuGGBsHhh8Lrf4MCQYFCIUGJgiE8GBQMIFhELDDhdaGHhdYGwaGGDDhhuDYMBsHg2D8MMGHBsH8GwYDYMhEKEQgMCeDAsIhAiFBgQIhAYFhEKDAkGBMIhYGEC4GFCBELgYUKDAuEQsGBQMKEwiEBgUGBcIhQYFwYFAwgWDAoRC8GBAYE4RCgwKEQvhEJgYUIBhQkIhAiECIUGBIMC4RCgwJCIUGBAiFgaYLwiEA5cGXCNhGBGwZAw4Ng0MPBkqNqcoqorlh//PkZP8cygrkAFI0wjo7lfAAjhq4SnPor//psoFemygWmwWm/0C0Ci05adApNn/TZQLU5RU9FRFVFdTn1OFG1Gywv/UbUa9Ff0VoFgCwBbAtQLUCzAtgAchHAN8ImAbgBvwDd4RwjQDeAN8I0ImEQEQAbwRgiQjBHhHAN0A3vgG6Ab/CIhEgG5AN6EQESAbmEYI4RABvYRoRABuYRgDehEQiAiIRABvcA3QiQDfwjwDfCICPCJCJCJ4RwiYRwjBEwDehEYRgDfCNCOEQEQEcI+BagAd4FrAscCxAswLfwLAFgC1wLNVMQU1FMy4xMDBVVVVVVVVVBlBkwO3wZAZMIz//////+DJwO0IwI0DsA7cGUI34Rn4RgMuEZgywZQjAZP/hGAcmDKEYDIB28GQGQGXhGgcgRoMgRoHYBywZAZYMmBygyQYFCIUDChQYE4MCgaYJ/gwKEQsGBAiEBgXAwgQLrhdcGwcGHBsGA2Dgw4YYGwZC6/hdYMMF14YcLrYYcMPDDhdYGweF1oYb4MCwYEwYEgwJBgTCIQGBIRC/BgQDCBcIhAMKFhEKDAsGBQYFAwgTgwKDAoMCQiEgwIBhAnCIUGBQiFwiEBicIhAiFhEIgIoBooGieDPA+4GeDPCP//PkZOobXgrkAEY0jjazieQgBKKIBGgyBGhGhGgyAdoMmEaDIB2gyhGwZYMsI0GSDIEaDKB2QOWEaDKB2hGgygcoHYBygdkDtgygcngyAyhG4MgMgRgRmDJCNBkA7QjAZIRoRoMuByhGgyAyAyhGwZYHKByAcgMgRoHIEbCNCNA7fCNA7AZAO0GSEYEaDLA7cDthGgyAyAyQjAOwGUIyDKEYEaEYEYEaEbA5AZQZAZAZIMgRkGXBlBkCMgyAyQZYMngcmEb8GUGQIyDIBywZAZOEZhGBGwO2EbwjcI3hGYMqTEFNRTMuMTAwqqqqqqqqqqqqqqqqqqqqquDqz8GMDQIvBj///+DGDEGAMAifBj8IuEThFCIEQGPCLwiQYBFCKESDCETBhhGAZGEZ4MjgcSEY4GoRQigxCIEQGPCKBiBrgxwiBEwMIRAY4MQNMIkImDEGIRYMIRYRAYhFBjwYwNQYgwBgBh4GAMQiwYgxA0gahFBgBjgYBFhFhFCKEWBgEWEUGEIgMQYQMYRAMQigxhFBh4GkIgRIRYRAY4RIMQYgYhFA18IoMQYQihFhEgwhFCIDEGIRQieBiEUIoRQYhEwYDAFwGrBog0waAaQawacGsGiDQp9MZMVT3piJjJjK//PkZOYZBgjoaUJQ0DprofQgbhq0dep71PqeU69TpMdMVT6n1OlPKdJjpiKdFZkxlPKdJiJj+mKp/0xFPBoAmYaw0BpwJhDSBMQ1BqAmIEzDSGgNXDRDRhp+GsNMNAEyAmYagJkBMA1BpDXAmECZhoDXhqAmYawJjgTICZBrDSGsNAExDVDQGqGgNHDWBMIaQ0BrAmcCYgTHAmIEzDWGgNYaOBMw0BqDSBMYaA04aA1hqwJhw0ATKGqBM8NYEz4aQJkGoNMNIag1+GsCY+BMQJlDVw0cCYfDXw1ho4aQ0gTCTEFNRTMuMTAwqqoD37wjuhHf/////hGf////hHf/gycEZwRn4MnYMncIzsGTwOdOA507A507hGeEZ8GTuDJ4Rn4HPn4Mn4RnhGcDJwM3YM3gzdBm6DNwR3QjvA927Bm//Bm7Bm/Bm8GbgZvBm4GboR3gzeEd+DN8GbvBm6EZ+Bzp4Mn4HOnBGfA588IzsDnTgZPCM8Iz+EZ/CM4DnTgOdPBk8Iz4RJwGTidBi7AycTgiT4RJ8GE6ESdhEngZPJwGTieDCcESfwYToMJ3AycTwiTsDJ5OhEnwibwYb+ETdwibwibvCJuwYb/4RN/Btf/jYGyNv8bHLUtSzE0/LQBW//PkZPQcHgjQAFKVrjfDmgAge1eMLITcshNy1LX8si0LUTQtCyLQTUteWQmvLItSz4mgmnTJops0umEymDSPsnR9HwfB8k7Pvn0fAvwtcLXF/F+L4vQtQWuFoC0BaReF7AeBfwtcXBdF4XheFwXAtAui9C1haRdi6LwvirgnUE6FcVBVisKgrCvioK+KoqiqAhh3AQFXhwVYrFYCWHMOioVAI4dFWHPDgCAdDgdDmHcBIBEO+HRWAiKsBMO4dFQCAdATw6HBWHBWAgHBUHMOf/gICsOCoV4qw7+KxWHA6KhVTEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVC11Fz1+NP+/Xkqpr9OfuhQ6lZNWf+XsZc/si+KNoOj/Ntai9j7IISgZAklTqQhjToNTeQoreRdaSDODWvYAVFaWWkQmVuZPP8bRUzq896vc+XS6mm5gnnW7JLnV4z9N5XnW4k86x+uyHyxnWkmdborzraff2SvOtvljOtjd/5XRW8MivOsry/K3gUzrFPS1eFTOttTsul1+lLGdbV3c53h51mdbK7xPOr1SpnWlM63kedZnV4XPK86onnW7myrnWTmRTOqascoabXd/L+yuzS95dv1efjhl92ntYyTuIb6wuWRZFmWRa//PkZOcb/gqMACDf1LTLofggA9qc/gKRZiblqWYm3E24moSQnR9BJj6JwfBaFqWZZFoWvLUshNPxNuWn6bTZHgozRTSaNAY5oGjFQVgTkE6FUVorioK8VBXBO8VRUioAhxVioK4qAnIrYrCqKsVRVBORXBORVFcVcVwTgVBUBORUisK4rCoKwrxXFYE4FYVATjFcVhX4rcVBVFQV4rxVFSKwqCpFcV+K8VhXFYV4qivFUE4FaCdivBORUBOfBOgToE6FUVYqisKgqCtxWFbFQVxXisKgqRU4qioKuKwqCvxVBkH/4Mgf4Rg4RgfgxZBizhFYEVuDFvhFbBi3CKyEVsGLf/4MgAxaDFoGsWYMWgaxYBrFngxaDFnCKwIrAYswNas+DFgRWYGtWYMWBFZgxbgaxbgxZCKwGLIRWgxZCK2EVmDFmDFgMW4GtW8IrAYtBi3Bi0GLAYsCK0IrQit4MWeEVgRW+DFsI9cDW9AYtgxZhFaBrVgMWgxaDFvCKwIrIGtWhFaBrFuEVvBi3hFZA1i0IrYMWYMWAxaEVkGLAisCKzBizCK0DWrIRg+EYIRgQjABkHwODABkDhGDwODBgyAEYIRoRvww4YcKILYKILfhRg5htjY4OQbQ2hNAH8su//PkZP8cRgjWAFAUYjwDofQAi9pcJry1E1LUsuWvE2E0LQTcB+E2LMTctQFEEcWnFYVwTkAI4rRWFYE6BOhWwToBBAQgnIrisETANwIiAb/COEQEbgG+EcA38A3wjhGCMEeEaEeKoJwK4J2KoARBVBOIJ1FWKoJ0CcCuCdRWFcV4rioK4rAnYJ2K0V8VYr8E4xWFUVBVFQE6FXBORWFSKorxVFcVwToE5wTvBOoqwToVIJziuKwJxAQgnUVBUgnAqQCaCdxWirFaKwrRWFcE7FfFeK4rgnIrAnYrCoKoqgnAqCrip4qgnCr/////8Iu8BjvP4Ry5/8Izggc4Zw4RnD/hGcP8GThBk4eDJwgycIMnDwjOD4MnBA3eu9hF3kGO8gx3vBjvAN3jvMGO8CLvAY72DHeAbvXe+DHehF3kGO8gbvHeBF3oG713gG713vgx3oMd5BjvAi7wGO9A3eu8BjvMDd470Iu9wN3jvQY7wGO9wN3jvIRd4Bu9d6Bu9d6DHeAbvHewi7yEXecGO9BjvQN3rvIG7zykGO9A3eO9CLvYMd6EXegx3gG7x3kDd470Dd472DHewY7yDHeAKomwmhZFkAof8tCy4m5aCbcTUswFYsy0/LQTX9NBqBgJgYaY//PkZPcaTdCuAFgWlkI0DfQAe9r8Jxz6CShr8++JsArgjxNBNC0LQsyyLMsgRxallyyLIsi0LUTUtS0AVwFPia8si0LMTXiacsxNizLMTYTcsi1LMsy0E2LUsuJqJvxNyy/E1LMteWgm4mpZlkJuWRZFmWnLQTfgncVATkVcVQTsVIqQToVxWFeK4rwTmATCsCcAnQJyKoriqKwrxWiqKoqwToVRWBORWBOBVFSKgq4qAnAqAnArRVgnIJ3FYVQAgCriqK4qYqivBOsVoJwKgJ3FYVBWFfBOIqCoCcivipxUFSCd4qRV4rCvFaCc/BOhW8VxWFUVhX4r1eX/+DHgaYMOEX/CJgwwYQY8GODH/wYf4RAiQiAxCKBj4GAMYRQiwYcGEGMIoMfhECIBgEXAwCJhEA1BjhF+BhCIDEGARYGoRAYgxCLwYeESEUGMDEIoGAMAiAwBiBrCJ8GMGGEWEUGIRYMAYAxCKBiBgBqDDA1wiBEBiDAIsGIMANANMIoMAYhEBh4GMGMGARfAxBj8GAMANYRAYBFAwCIBhgYhEwNQMYGIRAMAiwNYRQi4RQYwYgahEBiBqBgESEUIqa6Z6bTBpGkmUwmkwaZpJlNCkifmkaaaBymgm0wmRPxSDTTa//PkZOYYTgboZUAQIkCEFgAAe898YTZpJtMJhNilphNdMJhMGgmemDSNJNmkaX6aNJN9MJhMc0E2mzSNJMpg0umU2mDS5oJlNJtNJnpg0DSTBoml0wmzTE8TRpGgm00mkzzRNNMJg0jQTSb/NFNdNGl02KTzRTfTPTSbTKZNLphNJpNmgmUymkwaaaTKZ/TSYTBoJr/ifptMpr9MJlNptMpg0kymumEwmf+mEwmOmkymTT5ppv80eaHTCYTHTSaNBNJtN80/0yaBoJo0k2aPTPNA0OaSaTJoplMJv9MgBAB4MBUGwbwVwUBkFAYDAU4NBoMBqhAELk//h/y1/vz6/6zMnEglwqnIsVSHBzAMMIqXiLgxS8ucoLCKl7VwipeupwYpeAxS8Z22hSl4U+qE1L2goKUva1GUJqXqwkpeAxS81IQpS8smBqXql6E1LyEVLxdEJqXoSUvAmpeJhFS8rpH3CKl6EVLxPWFKXgDUvVL1LCal4kuDFL2EVLxFlPClLyEVL2FKXhdwYpeswGpeqXkGKXqLAxS8wYpeqWk8KUvATUvQYpehJS9Bil6yoUpegipeZT2x1vN5NR2X3KrRuXjFmaS0AUCyE1BHflmWZacTYsvy05ZgAIs+WQCqJoWZ//PkZOwZ+gaYBgbm6EQMAfQAe9r8ZCbiaFkWv5ZibialoArlryyDtCSnyfR8hrE7PpNmiaIxDTGKafTJalqJoJoWQm4mnLUtSzE1LUsxNyy4mpZlqJpy14mgD8WpagKvLIEcJuWnLItBNhNhNxNgH8tSyLMTflmA/iaCaFqJqWf4mxZFmCdCvFWKwqivgnQJzFfwToAIwqgnOKgqCoK4J2KwqisKwJwKwqCqKgJwK4rYqirgnIJ2K8VhVwTmKorYrioK4JwKwrRWFQE5FUE6BOwTgV4qYqiuCdAnYqAnMVBVFUVATkVBVirioKoqCviuK8VwTjxWFfivFUVxXBOcVuKvFaKoqiuCdeEW3Bjb/gxvhFvCLYGN8GNwi3CLf8DbtuEW//CM7+DG+EW+DG8DbNgi3CLfA2zYItwY3gbZuDG8GT/Bk+DJ+DJ/hGdCM+EZwMnQZP8GT8GToRngc6dBk4IzwZOBk8GTvhGdBk+DG8Dbt8DbtgY2CLbBjYGNwi2A2zYDbN4RbhFtA2zcDbNwNu3CLaEW4RbeEW2DG8DbNwY3BjYDbNgY3A2zYItwi3CLbgbdvA27YGN/CLaDG4RbgxsDG4Rb8ItsI7oR3cGb00m0wmTT6aTZoJgUpMc0kwaa//PkZNcYqeLUAAAUHjw8DgQAe88cYTXTXNBpXiQdfLVpLJoaCSJs0TQNAbRpJpDWleaGlDmlfQ9fQ7tBZoa0IeOw3Ccq44TcHgrufDVzQNA0Om0wmDTNE0EymkymumU2mU0aSY6ZE8NFMmiaaYNDptMGn+aJpdNptNps0OmU0J50wNtMGmmEz/zQTJpppNplMJlM9M8FAYCoKAAApgqDIMACwAQbBsAMGQAQaCkGg0GgyCkGeCgNABwAAUgrwA+CoAQNAeHCAOAeAyIQHBwgDg4QYDAGgMEEOEAeA0Q4DxAHh0PEMGYMBsFQYDPgoDMGQV4NBqryAZfhGfwjMDl4Rv/wjcGT/4R/CPBH//hH4H3gzv4M7A+/gzoR4GdBnAf+DPBnwj4M7BnwivA1QIpCKcIpgaqDFgaqBogMWBqgGqgxQinA0XBi8IqEUCKBFcD7/CPwj0I+DPA/8GfBnQZ8I+EeCPwZ4R6EfhGcI0GSDIDJCNCMBkgdsIwGQDkBk4HIEZgy4RoHZwZQjcDtCMgcoRgRoRgHKDJBl4RgHIDKDIEYEaEZgyYMoRsIwGQGQIwGUGUI3A5AZMI2EaDLBlwjQZQjAjAZQoqIqqN+pypx/qNIqoqqNKNKcqchwGrCAPqN//PkZOsaCgjiBUZRnkhEBfAgw9tw+iu1RUqp2qNUNalOVG1GkV/U58sLU48sLLCiwFU4hAYA+qQQgasVhVIYQNWKwmAKpvasNoHIDm4OQHIDlGwNgbH4D+JuWQm4mhZFoJsWgm4I4TcbA2xtA5ODnGyNoHJ+NgHKDl/LMBXLUtS04mvLQteAqCaCbFoWQmha8bQ2Acg2ht8bI2RsjaGyNrjYGwNvjbByja/GxFYE4ipBOIrwTiK4J2KuK8BAK4rAnAJwKkE4FYE7FUVgEIqgIRVBO4qRXFQVBVBOMVwCcE5itBOoqAnQqCpBOIrxVBOoJ0KwJxgnQrCuKsVBXioCcCqCdAnYJyKsVYqipBOgjwiAj/8InhH4RMIkIioAC4RiJCMRP////CNk//Blkv4M6F8I9C/8GWT/8I2SwjZOEbJQZZIGWShGyYMsnwj0P///CPQ/hGyQRsn8I2SCNkgOyVkoMsnhGyQMslCNkoMsl4MsnhGyXCNk4MsmEbJ/wZZP8I2SA7JWTgyyQMiKDIieEYiAcRIihGIkIxFwZEUIxEA4iREgcRYihGIoHESIkDiJEUIxFwjEQIxEwZEWDIihGIoRiKEYiHcYJRIr9kzDmO5cIaApXE/NA0TTTKZGym/0//PkZMQWjdCsAwAWCsQ8Cfwoe89cyaXTKZACgKxsmybQFQCyPQPUbRtA9x6QAoegHKmRtClpk0RSkwaCYE/GyaaZ6ZFJTaaTCbTabTBomgmk2mxS0x0waSaTI2gcgOcT0T5MiedM9M9NCfCeGgNhMJlNJoT1MdNJhMjaNPptNGkmhtpnmgmv+KQmRSE0aaY6ZTKZTRoJg0U0aBopg0emU1/xPzTTaa5o80DRTZoJs0TRNEUhMikJs0P0x0wmkwmUymkwaKb5pppNJg0OaInhpGgmeaSbTBp9N/ptNJo0DTNAbOCkGAwGABgA4NgwAAAMAPBgAQKAyDAZgoCkFINAABWAEDAUBTAABgMBTgqACDcGAAQYDAUqLUlVt///+Bj4MIRcGH/hE//hEwY/wi8GPBj8GH8IgRcIv4RYMQi+DAIsIsIn8GHCLCJCJ/BhBgBpgwwifhFhEhF/gwCIDAGIMIMAMQYBFhFBgDH4GAMQiQNAYAwwYBECIAMIAMAAiHBgAMIAiAIg4MABgABhADAhEAGAARCBhCEQhEIMABh6EQAwIMABgCEQwMAQYD4MDCIcGACIQiHwYGDAhEOEQ4MAENB8hJD7PsnXJ0fZ98+D6LItQR3PgnZHmgPYYRopk0xj//PkZMkU/gLsKkITrkL0CfzIetvgJtNdNGkmkwCPLITf8s+WpaFmApAjgFUsyz49jSTCaGMmkyaRZlkJsWpaiagKBZlmWomh8c+CdcnROj4Pk+Akh9E6DsJ0fJ8c+OTs+idk4J2Tg+j7Pr8nB9nwfAa59k7598+D74Sk+eEkFQdDmKsOhwBEVYCeAiAmAiHcBEV4CYCIWmCHF6FqC0haRcF4XYvxfF2LwvC+FqC0xcC0C/FwLSFoF0XBfi7C0xei8LwvcLQLoWoXheC0BaIWoXBdiNiMY6cZ8RkZxnjqOojAz4zx0HUdBGozjoMw6iMjpxdi+L4uC5F/xd/haIui/bZ/+DGBp4RYMQYgw8Ivgw/CJ4RfBh/hECKBgEUGODH8InhFwY4GoMcIgRIMcInCIBrBhCIESESBp+DEGARfhEA1gxBjBj4RAYQYgxgYwiYRcIgMAiAwBjCJgYAawYYRQMQMAYhFgagYhEA0AxA1A1CJBhBgEXwMQNAihFhFAxwYAwgaYMPCJA1hEBiBrwYQiwYwiBFwYhFgxgawihFCLA0gYAwCKESBoBpCJ4MIRcGOEVB9ALR8BJj7/PonB9HyEoJwfZ9E4Dt5OeTnlqJtxNAH7nwfHPonJ8E759E6PoJI//PkZOAWKgjoZUAQYka0Ffgge9VcfZOCdE7J0To+Cc8JWTo+D7J0EqPoO0+T5PsJSfJ9n0TkJUfJOeTknPPo+z6J2Ts+ycE759/k7Pg+z5J2TonBOeTkJUTg+T5DtJ0Tvk5Pjk5DXPg+idH2HYfR9E6PgnR9k5JyfZ9cnR9cNYJSfROidE6Ps+Cdn0fB8E6DtPs+CdHwfH5OAlR8nyfB98+SdE4J0ff5OidH2TknfPkJMfYSg+Cd8+D6PsnP/JwTk++fYSQO0+D5J0fZ8HwTjn2AWDgdAL8ApALQCkO8GACwMgyAVBiAVhzwY4BeDIMgFMOh0ArDgc4BeAV4cgFsOhz/wP225HGw7wCsO4M8AqHP/+DEGQ7/8OgzgFoM4Bf4MhwO4BT4cDmHcGfDnw78GcVhUFTFXFeKoqYJ3isKuKkVgTsVoqCsKwrCoKsVQTqKorYWoXhfxeF/xdi58XOL4WmLnFYVQToE4FXiuK0VwTnFQVRVFcVhU4JzFfDgdhzBnDuHfALgyDAMQZBgGQCgBUAoDAMh3BkAsDMGcGAYgxBn+HAC0ArwCwMAS5adAry0yBSBfoFBVaKqKwVUVrRXUa9FUOEqVUjV3ySPLkvkzlNlNn02E2S06pvVKWACECpW//PkZN8VDgj+zymq5krb1ewAxJvkqe1XysDVFSqlVIo2o0ispyFVoqKNIrKcqNKcKcFhSjaKiKyKhYWiv6nCnCKijajSnPorKNKcKcqcIrqNoqKNqcKNoqKNKNqNKcIqKN//orf6BSbKbKBSBXlp0CvQKTYTZ/0C/LTxFxFoigioigCLC4cRYRWItEXC4cRcI0IoFwwXCiKwjQXC8LhRFcRcRQRSFwoikRcLhxFRFguHiKRF8E7FYVhVBOgCYVRUACKKgqgE0E6ACJFYVoJ0CcgnQrAnMVwTgE6ioKoRwDdhHhG4RARgDcCMEaEYIiAbwRIRuESEcIwRwiYRwjQjBEgWoFuBa4FngWkIQQigwhEwigx/CL/4RfCKEQGP/wYiEUwYHwYGDAwYAGA4RCEQBEGB0rCNcGU4RrBlcIsGGDCESBiEWDCBhCIEWBiDAGHCKEUGEGEGMIgMAiwigxCIDADQGIRfCIEQDXgYYRfBgBoESEXwY/CJCKEQIsGIRODEGIRQYgw8IgRYMMGAGGDEDGDAIoMcDQIkGMGGBrgwCKDAIsIkDUDWDGDDA1hE4RQMQYgwBhhFBhBh4MAMYRMIoRAiAxBjhF4mhZFpxNBNy0LQTUTXhKglR9FqWpaAP5Zi//PkZNYXkgTkGEJwlkXcDfQAe9r8bCa/k5JwTriacsizBHE6CSH1w7efHLPgjuJqWnLITYTX8tRNAH/ialqJuJuWZaFkCO4m5aE7J2TonB8E559nyEnJyfB8FmJrxNxNhNS1E3LT8sxNC0E0LTlqJuWQm3E15ZiaFqWRZiaCagP5aCblkKoqCsCdioKoqwToE6FcVwAhRWBOIrCpFbgnEVRVFUVIJ2K4rAnIqioKgrcV4riuK0E4FcVQTsVxUBOoJ3FUE7itFYE7FYVRXFUVwTkVoqAnYJxBO4rReF4XYWsLWLoui4LwuC9AdsLQL0LQA9C8Lgvi6LguBahfFwXAtIvQQ4vCqKwrxX/8E5xXit4JwKzBnwj+DF+F1guvBiQYkDRIR8I/gz8Gd4R/wYsGLBi8I8DPBneDPBnwZ/8I8DP8I8B/0Gd+DPA+8I/+Ef+DOgzvwj4R/hHoM+Ee4M4D74M+Ef/A/7gfcDPBn4R7BnfCPgz4M7Bngf8DPwjwR6B/3CPwj0Gfgz+EfBnhH8GfwioRQGIBqgRUDVAYoGiBFcIoBqoMWEUA1UDRYRUIqEUA1WBooGiAaqBogRUGJwigM8I/gz+DOCPwj/Bnwj3BnBHgZ4M4I+DOhHhNCz5aiaCb//PkZM0YugrgAAASBEUEBfQAe9rclmWompZ8shNyzLMsy1LMBUAfhNxNicBJCcE5J0JpyyLMTcBVLXloCO5aCagKACiAqflqWYmvE3E3BHlmArFnxNRNC1LUBS5aFoWhZFmWR8k5CTnxz4PonZ8E5Pg+j4LUshNQH4TYtSzLMshNBNhNSyAViyE0FQE4FYVoJ0CcRUgnYqisKoJxFWAhiuKgrCtFUE7FQE5iqKgqRVFWKorwTsVxVFYE7FTFTBOxVgnAqRXBOPisK4BOK0VoriuK0E5BOxUivFcE5FYE5FQVBXFQE6FQVBVFYVxWioCcCoLovi4LoWiL0LSL+LouBaIvBahdi6LoWgEMFoC1hacXxdC14uipiqKn8VPFbiqKsVb//Bm/+EXgx4RdhFb///hE1/8GLP//CKwIrQjoGa/wPewZqEdYM1wZqDNAetgzWEd8GagzYM2DN4M1wjoI7Bm+DNhHcD3rA9awZuEdwZvCOwjsGaA964M1A978I7hHQR0DNge98I6hHYR1wZrBmwPWgPWwZsI7wjqB63COvgzQR3BmwZsGaget8D3rCOgZoGa4R1gzYM0EdwZsI6CO+DN4R1wZsD3oGaBmwjqEdAzXCO/4R0DNAzYR0WgI8TQT//PkZL4XFgTcAE6Tekf8EfQAe9t8YTXiagKwCsJuWXLMTUtC1LMTT8TYTUJP+fAdomomoD8JuWoCgWoI7iaFkJqJqfZ8hrk4CSE5Pk+Cdk5PgnBOT7JwTg+uWZaiaCbcsgFAsy0LPlmJsWYmom5ZFmWpaibiaAjy15ZFkWQm5Zll+JsJqJvy1E0CThJz65OQlJ8H2TknZOT759k6J3z4PgTQTcsy0LITflmWfLUsiyLITYTcTT8syzLQsi0LMEcWZa8tSyE1LTibibFqWYI/8shNCyE1LQsy1LUTUBTLLloWXLUsiyLUtBN+JqJvy1E05Z8TcsxNeWn/LTnxydHwfZ9n1ydk4PsnHJyfR9E4JwTgnB8k4588nB9cnR8c+D7PjxV4q4r8VsVgTgE4/itFShX/xV4rirxVFWKoq4qfxX8E6xVxUBOYrCtwTvwTj/xV8E5/irBOsVuKwqRXFTFQVorCrFf/xWioKwrCtioCcxUFUVAToE4FUVxWFQVhUBO4rgnUVIJyKkVgToE4ipFUVoq4qisKsE64qCoCdxUFUVxXFUE78VxWFQV4qiuKgqiuK4CCK2KoqirBOATsE4FWK4qxVFUVhVFYE4gnIriuCcQTiK4rCoCdCtirBORUiqK2//PkZLAWsgzuADQNNkZj/fjIe9r8K/FXFUVwSE5aiaAKgmhaFpxNQkp8n3z5PsJOGsffLQsyyE1LItS1E1Af+CPLMTUTfibFmJoWQmxanzydHwTsnASknBaCalqWom5ZcsxNyc8nASsnHDt59BrhJgkpOicBKg7D7JyfZ8E5Pk+Cd8EeWXE0LQsiyLQtSyLIsi0LUTYsy0E3LITQshNiyLUsiy4I78shNy0LMVcE6ipgnArRVgncE4wTsVIrCqCcCtBOYrwTgE7iuKwqCoKwrcE7BOQTqKgqiqFpBEC1gPYD3F0X4WoLTF0Xxei6FqC0BaAtQWgXxdwtWFqC0haBW/FQVRUBOBUFcE6ioCcAnEVRX4rYrxWFUVBViuLsXgtAuC6FoBDBaYvi7F8XxexdF8XhcFyI//gx//CJBhgxhEwY/+EXwYgx8ImDH8GPhE8GPhEwigwhFwNQNQNfhE+DGDAGHCIBrhEhEBh4RQMAY/gYBFBhhEAxCIDCEUDEDHBiEUIoRQY+DCBp4McGEIgGgMcGIGMGIGIGgRQYgxCKBiBjBgDGESDADWBgEUDEIuDADWBpBiEUDEDDgYgwBgDEDQDUGIGgRAYQYAxCKBhCKEThEBgBiEUGIRQMQiQiwNAY//PkZKwWNgjoZUAQREakFfQAe9t8YMfwi4GBaFoJsWpaFlyyBHctCzE25ZiaFoAocsy1E24Sk+idk6LMTYTYTQsyyLUtCyLQTUswFYJMGuTg+CdHwErPotAFAsuJtxNCz4m4mvBHiafloWZZ8YppBqzTNI0jTNBMpg0jTTZZFqWfE3Af/xNf+WomhZ8suWZZCb8tC1LQtS14m/LQtBNhNOfR8hKCc8nHJ2EqCUE5CSHxydk6Pg+D7Ps+Sdk6LUTQTcTcTctSz4mwmha8TXlmJvyy5ZiaFl+WpZFoWZa8TQshNCz4mhalmJsWfLQsuWRactBNRNC1E1LLlly0E2PgJWfR8h2H2fJOSdnx+fXPs+T5PgNbnzz6Po+T7JwTk+z4PonROglJ84qCvxXxUirFX4q4qeCd1f8InCI/4R/CP//4RMI/CMEYI4RAR8I8IjCJ4REI/gG8ESEfCOERCP4R/+EaERCPCNCNCI4BvQjYRMHINobIOfjZBzDZG3xtja42gcw2PwcvG2Nng5+NgbY2wcn42htDYGwNrjYGwNsbX42vxscbI2gc3Bzg5Qcg2uNvjaGwNgbI2+Dl4OUbI2Qc42RsjbBy//g5eAboR4RwjQDeCP+EcI8IwBuwjwjBEBEA//PkZKoZMgrqADXt9EV8FfQAe9scG+EThEBGCMESESEbhEQjwjwjwjBHCIwiQDcCNCI/hHE0/LQBSLITf/lqWoSvk7LT8BWLMtRNAk58E7Po+CyLQTcsyyLUsi0Afi1LUTUTUtBNS05aFkWpZHzwkoSgnB9HwfQSsTYsizLQtRNRNgFf/8NcJWEmAWCdn0To+T4CUHyGtxNiyLT/lmJqJqJqWgmpZFqJsWgmwmwmwmv5ZiaibiactBNv/yyLTiaFkWZagKgCmJqWQmgmom3E0LTibAKfE0LLiuKgrYqipBO4JzFUE5BORXBOhVioKwqRWBOYJyKkE6BOIrYqCqCcxVxVFUV8E4FSCdcVgAgCuKkLTBDC6CHi+LwuRfha4uC+FpF8XQtAWgB2wtAWoXBchaBfwtQWnFSCdxVxXFUVhUFYV4rgnOK3wTiKir//1eDCsf//7/+wOYd///ei/f6lmiqGXpyOlCSRm+yP3UtDhFIzqBhwCFHAGFHABYGcAnAAMOAAYcAAw4BYKOAIROANgicAQYcABE4AAzgE4B+1wmcAwYcAK+sDOADgFLCJwAlWBnABwABnABwDWraETgEDOADgDBiRkgoJpGaP+64RSMoRSMGBiRkkwMSMlq7AxIy4//PkZJUYEgieBFw3fkL8FgAied8AROAAicA8DOATgAGHAAROAFwM4BOAGBhwADDgAGHANQUcAgicAYROAfgw4AIkbPNNNpo0TSNBNJgUs0l7lomEwmRSOaRp80zQFK4nvTInnTEGgwGwAwYmOmkyaInppJpMJg0k1+KUaaaTCb42U0mBsGiaSaNNNpo0OaKZNE0jTTSY5oGmaZpJrphM9MGkmDQTCZBzpg0OKR+mBtdMJobfTSbTfG2KSaBpGgaZomiaBomn02mOmkwmjSNPpjmmaKZNA0zSNFNCeClJkT40DTTCY5oJk0kyaJpdMppMdMdN80TR6ZTXTfNJNmimumem+aKZTKaTaaTRops0kwaZppk0+aSa6ZTKYTfTfTPTHTKa5omgmEyaJo9NmkaRp/pnmmaf6aNNMplMplMdMpv8TxMJlNplNJn9MdM1OM6hWa/EjiRiRiO/xIcSP+GgCZQ1+GvDRDV//4aP///4I8EwTwTBLwS/4AHgnBHBHgiwR4JgAgS4IwAYPA8ADg+AAAAFwfB4L4WADC2AGAGFwsFwuD2FgAcAPC+F/B8L4X/B7B4L4WwuD+AGDwXACAAC2D2AEFgfCwAAAAXB8LhcLeDwXCwPYPA+ACACD+AH4XwA//PkZJMVIgb2ejVLxkBMEgTIe898vwv4X/ACB/C4WwtgBg/gA4PA9gBeFhLRTXTRomkmP0yJ6J+mPy0NA0OvibNDQmfzTNNMjaFKNATxMiemiaRoGkmE2mkwmTRTHNBMGiaCZTSZTZomh0PaV9fX2hoX14kzSmOaCY5pJr80U0aPTfNFMps0jSNJMmiaSaTKb6aTKYNLphMpg0eaJoGkJ8aBoppMppMJg00xzTTabNFNJpNJpNpnpg0DT6bTRoppoJMWrSh/690NQ5paV5D2le/LND2hoQxDF/rzR1/r/aWhfaWleQxfXmheaevNDS0tLQ0ryGoch/X15D+vLy+0oc0ry/19DUOXuvIc0oav9oXkNX1/r3X18QCGHQGQHB8PiAPAaHgPDsBgcH+Hqv//+DJ0GNuEW4MbQY2wZO4Rn///4R34R3Qjv//8Gbv//8GbgZuwZvCO4GbsD37+Ed3A586DJ4Rn8DnToRn8GTsGT8GToRn4HOngyf4HOnBGeDJ0IzgjOCM8DnTwjOgydwOdP4RnYMncDbtwY2A27cItgNs3CLeDGwMbwY3CLaEWwRbQi3A27eDG0GN4G2bBFvA2zcItgY3A27YGN4G2bAxtA58/gc6eDJwHOnAyfCM7A588//PkZLMYngTSAFAUhEYcDfggw9V8DnzwOdOBk8IzuEZ4MnAyfgychsy7F3oE13Nl9sy7myrsL6qqOU5anCqkHqNDI3KVUTEU8GNU7U6gxyHKchyVV/Xa2Zspfds3rubK2VsjZmztk9djZi/TZV2NnXYu0vsm0yaBoDZNEbA2k0aKZNjmwbH/Nvm2bRtGyBVArD1G0bQPUevg9h6/+PRzZNkCrzZNgesegesermyPQPXzbHpNvm0PQPUPSbJs82TaHqAtj082h6DaNn8eo2DbHrHpNjj1m1zYHqNo2jYNnmwbPNgek2ja5sj0D1m0bJs80xtJlMdMCeps0U0meaaa6b5p/mmmU3zRTfNFN/pgHPzQNPmkaaaTXTKbTRoJk0zS6b/NBNdNJlNGj02aH6Z4IgAEEYJ4I+CMEQJcEUEXBOpHDX/w1Q0Q1Q1cNWGvw0Q0/hp/AmIaA0cCYhow0w1/w0hoDRhpw1w1BrgTLDXw1BrhrDQGgNQEzAmOGuGiGgNfgTHDQGoCZw1w0BrDWBMIEzDVDTDUGgNfAFwGjBrg1A1YNcGsAXAaQaABcBqg1g1QagaINYaoEyw1Bow1YaA1hoDSBMw1gTICYQJkGrDQGoNGGmGsNcNQawJkGuBM4aIa//PkZKAYngTsATQNQkKMFgDifh7AYagJkGsCZ+GkNWGiGsNAaYaOGgCYgTENQaw0Yaw1Q1Q0/hqw1BkAfmkaCb6ZTCY6bNBNLwmqGA5fzSTCb/lgwXOmMmMmTQ5pmkmUwmkymfzRTaZFJ6aNM002NrppMpg0kwKQmE2m0yKUaKaE95o9MJj9N9NJk0UymOaSaE/TabNNNmmmk2mU2aHTKZTCaNDjaTJoGimkyNvpk0TRTHTf6bTRopg001+mU0aXNBMjYNA0k2mjR5pfps0U100mUz0ym02mDRTHTaaTRpplMc0kz0ySA000mDSTKbNI002mzQTHTaYTf//NA0zQTBodNJk0UyKSaaZNJMprmkaP5oGmKUmEymUyaCbTHNBMJlN80DRNBMmkm/00mUwaZpprplNpvmiaJo80+aSa//TaY/6ZaH//wY4ReEX4RIMfwiAxBh/BhCJ/+ESDDwYcIn+EQIoMAiQYAw8IsGIGGETBj4MP4MQiAxhEAwAxAxBjBiEUIgMIMcDCDAGIMcGMDEGGBgEUDTwNfAx4MAiwiQYgwBgEUIgMIRAiQYhFwYAwBhA1CKDEIoGoGgMQi4MeDADGEQIkIgGMDCDAIoMAiQiAYwYYMQNQigwCJBiDEGAR//PkZJwV+gzoZUAQRkZsCfQAe9tcQYgx8GEGIMQiwMAY4MANAMcIoMSyE1E1/LMTUtCz5OD5PkJJz5Pk+uA/FqWRaib8TUTUTQtC05alqWYm4momgI8TYsxNCzE3LUtQFEsgFM+z6J0fB8nz+TsNcnB9BK+Tk+g7CdhJT6LTiaFqWYI7lqWoCvy05ZlnxNy0/LUshNBNvwR4moCj+Wv5ZlkJqWpalkCOE1E1LQsiyLITUBULLlqWZZlmJsWhZcsi1/4CiWpalkJqJuWvLXlkJsJpyy4mwm5ZlmJuWXLUTQteWgmgmwCsJoWfE3LMsiyLP8TTlkJuAolqJryzE1LTibCbloJuWhZFoWYmxZ8TcXIvxeC0i+FrF0B2i4L8LSL4uBaIDyCHF8XRfF6FqgiwtIuYWoXIrYqAncVvxVip8VIqCvUxPqtFbFf/4q/8V/8V/iririr8VATjxXipxWgnYq+KwqfFTFQVoq+KsVBW8VhUisKuK0VRXivFYE4FTBOhVFcVRV4riuCdCuCdCtwTiCcCqCcgnYqxWFcVIJwKsVxWisK8VxVgnArYqRWFeK0V4qgnQrgnYrfBOoqAnAq4J0CciqKviuK3BOoJyKsE4FUVoqisK2KwrAnArgBAFSK4//PkZJ0VagjyJgGtbkOkFfwAbh4AqRViuK4J0KgrCrFUVxVjOEeDQgjlcT4T40xtjaNI0uaI2QciGNJIl4kJZtCGIaSckIm6GL3TQ2zRTY2UwmjSNE0fxS02aY2BsJs0/+mzSE8TQpHTKZTaaG0aQpApZp/mmm+aHTfTCZGwNgUk0E0aRp/ilJlNJgT5MmkJ9zQNA0TQTH6ZG2mk0mk0mk3zQTKY/G3zQTHTabTBpGgmU0aKaTJpJpNJtMphNjYTfNE0emkymE2mEyaCbNBNfmkaJpdNJnpg0k0mDTTCbTaZTBp80k2aCbTfNE00yaCb6bTZoplMmmaKZTKaTJpmiaHTXNJNdMDbTab6Y6Z6ZNNMJhMJhN9NJrmim02m0ymU300aPTSa6Y//5pJpNdN/9NppxWxU4Jx/xWxU/xU4qcV/ipiuKwqxWFbioK/8VIqivFTwTkVgTkVor4JxFXxX8VcVxUFQE4FaCcwTvBOIqwToVuKwqCtFUVYqxXxWFeK0E7FQVYrwTnwTsV4rCqCcfFaCcRVFQE5gnAr4JyK4r4Jz4J0Koqip4rCvisKoJ3xWBOME7iuKgriuKgrAnAqgnArxXiqCcgnYJzFQVQTmAEIVYrwToAIYrwTiKkVQTkE5//PkZK4XGgzsADQNAkIcCfwAe898FXFTFYVxXxUFQVkzzS5oilg5TTTY2kyaJpJtNGgJ8aKaTQ2k2aCaNI0eaSYNJM/tKHEiQxfJG09MmgaSYTRppg0TRNNMJlMptMCfppNprmiaA2jQNLmmaRppk0jRFLNP9NJhMGmaQpZpJhNCkGkmE0J+mxSk0m0x02mjRTKbTCZ42EwaQpJpptNprmgaRpmim00J4mzSNM0kymkyKWmE0muaKYGwNjmgafTPNE0U0mUwJ7+mem01+J8mhSk2mjRTSb6aTCbNBM9MpjpjppMpk0E2mE2mE2mjSTfTaZ6YNFMcT1MpnprptNmim0wmE100aSaTRo9MGjzTTHTaaTfTSaNLpoFMAAFYNBQGwAvwAYM4NgqqkjaAN3d3d3etERNK5CZ/3Ctf8AaiAc/u7vaIn/+8XPv//+GDKv8W1/+AC8K4Yw+94xe940jgceOBwOcRaxGwH4EuNIjBfxazB8HhkwVjIAII/vDkjG/wAcJPeF5e98hNgSYLWMFvhZ73pX06aoehpmh01hMJtM9K3TKY9E10x+memvlIpZM9MUTCbShbUrtM+n5ophNZuXK+0wmemumMCemkmvc0Uwm/lJJlLe5oJZI0SyW6R0mE//PkZLcXlgrzECFvrEmMEfgAfh7k2aORpbTYUo9SBNsekGYNVKgqg6hrUXK4Q42DOwPWbILEzBSumEgkWJPU8S72G5wUuGDwguEKa1Q0UykS3Bn7FJNgg5qoEgpsGabBtGabZt7NrKCNgLYes1idmabBtm2bZsBRm0PQbF9mwPSPUBbMwzB6QojYM42ePTzbMwzR6TaNs1eBVNv81yDUJ2QYhJtD0m0PVe+x6T2M8HsbFwsh6B6D7HpugT2uZhO6HqZhmE7ISZhhkFNcghtmybBnnsD1Ho49ZtG2bZs5PVBc2EEepCaEHNjj1EJHq5smabZtmwbNEEPR+bJse9z2PY2T1SN0rdNJq6yXI06COvNJj9MGmkkGmUj7pI0TToNGlEsNEnCW10ymi5FtNFKpvps0kz0uW3aaTF00mTSzdNJu0KFYz1KjaGAhyoQCFGM//zFARgIUZ/oZpdCttay6M5drW1joSj6Ekk1b7J7R7aoQNlPrWBKLTTzOsGT5yYxLrtHTy2iUxEkES0HIBVD066VjGj31odPkkSjJtbWs6OIjKmlXHR9K5MTv5pdZolE10kk2lly74TFx5KTXeTGLrS6ExiucvJSSDUdXZm3NLn1rtYBCUEoRj77WOhCPc+g5//PkZJ8XPgzoAABMTrz8BfQyY9ONGxJULutaa1TE5LHxWEo+XecmMa3p0kqBCR6qOFaFUGpNJIIjqVi0ylBqJIkiSJIkmJj605EkSRJPetWAyVacmodBUVhGfrAZPF0AUrgRLNiqDU4VHo8h6HRObJJNdqlRBMFT0qdNE0WZhmay2tCuOY6oieWieianCnVSbzjd7SGhrLi6eUSeLcZTWqdML5PM50uOGJms9VqtYU6hrgrm4V0frUzTvYsiegR2FOkqUxSKdVCpmRCSkIAUZ4VPhqEKgFDU2VhUiIhUaIg0RAkTBYEjYBUbBMuCJgLAkCUBUinABgBE3/9EQqFRM0s0qzktk9ZFqqFC6RULE11JVDkd8Y1vVFOx9oUIpZ8Vnq5klkxBTUUzLjEwMKqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq"))},[l]);let h=(0,c.useCallback)(e=>async a=>{if(null!==t.current&&t.current.readyState>1){let{lastScan:c,contentBefore:g,lastScanHadContent:f}=e;if(a-c<i)u.current=window.requestAnimationFrame(h(e));else{let i=await d.current.detect(t.current),c=i.some(e=>!g.includes(e.rawValue)),m=i.length>0,C=e.lastOnScan;(c||s&&m&&a-C>=o)&&(l&&p.current&&p.current.paused&&p.current.play().catch(e=>console.error("Error playing the sound",e)),C=a,r(i)),m&&n(i),!m&&f&&n(i);let w={lastScan:a,lastOnScan:C,lastScanHadContent:m,contentBefore:c?i.map(e=>e.rawValue):g};u.current=window.requestAnimationFrame(h(w))}}},[t.current,r,n,i]);return{startScanning:(0,c.useCallback)(()=>{let e=performance.now();u.current=window.requestAnimationFrame(h({lastScan:e,lastOnScan:e,contentBefore:[],lastScanHadContent:!1}))},[h]),stopScanning:(0,c.useCallback)(()=>{null!==u.current&&(window.cancelAnimationFrame(u.current),u.current=null)},[])}}({videoElementRef:f,onScan:r,onFound:e=>(function(e,t,r,n){if(null==r)throw Error("onFound handler should only be called when component is mounted. Thus tracking canvas is always defined.");if(null==t)throw Error("onFound handler should only be called when component is mounted. Thus video element is always defined.");if(0===e.length||void 0===n)tl(r);else{let i=t.offsetWidth,o=t.offsetHeight,a=t.videoWidth,s=t.videoHeight,c=Math.max(i/a,o/s),l=a*c,d=s*c,p=l/a,u=d/s,h=(i-l)/2,g=(o-d)/2,f=({x:e,y:t})=>({x:Math.floor(e*p),y:Math.floor(t*u)}),m=({x:e,y:t})=>({x:Math.floor(e+h),y:Math.floor(t+g)}),C=e.map(e=>{let{boundingBox:t,cornerPoints:r}=e,{x:n,y:i}=m(f({x:t.x,y:t.y})),{x:o,y:a}=f({x:t.width,y:t.height});return{...e,cornerPoints:r.map(e=>m(f(e))),boundingBox:DOMRectReadOnly.fromRect({x:n,y:i,width:o,height:a})}});r.width=t.offsetWidth,r.height=t.offsetHeight;let w=r.getContext("2d");if(null===w)throw Error("onFound handler should only be called when component is mounted. Thus tracking canvas 2D context is always defined.");n(C,w)}})(e,f.current,C.current,A.tracker),formats:i,retryDelay:void 0===A.tracker?500:10,scanDelay:u,allowMultiple:p,sound:g});(0,c.useEffect)(()=>(E(!0),()=>{E(!1)}),[]),(0,c.useEffect)(()=>{y&&(D(),I())},[null==a?void 0:a.tracker]),(0,c.useEffect)(()=>{!function e(t,r){if(ti(t)||ti(r))return t===r;if(te(t)&&te(r))return t.getTime()===r.getTime();let n=Object.keys(t),i=Object.keys(r);if(n.length!==i.length)return!1;for(let o of n){let n=t[o];if(!i.includes(o))return!1;if("ref"!==o){let t=r[o];if(te(n)&&te(t)||tn(n)&&tn(t)||Array.isArray(n)&&Array.isArray(t)?!e(n,t):n!==t)return!1}}return!0}(w,B)&&((null==n?void 0:n.deviceId)&&delete w.facingMode,R(w))},[n]);let b=(0,c.useMemo)(()=>({constraints:B,shouldStream:y&&!o}),[B,y,o]),F=async()=>{let e=f.current;if(null==e)throw Error("Video should be defined when component is mounted.");let t=m.current;if(null==t)throw Error("Canvas should be defined when component is mounted.");let r=t.getContext("2d");if(null==r)throw Error("Canvas should be defined when component is mounted.");if(b.shouldStream){await M.stopCamera(),T(!1);try{await M.startCamera(e,b),e?T(!0):await M.stopCamera()}catch(e){null==h||h(e),console.error("error",e)}}else t.width=e.videoWidth,t.height=e.videoHeight,r.drawImage(e,0,0,e.videoWidth,e.videoHeight),await M.stopCamera(),T(!1)};(0,c.useEffect)(()=>{(async()=>{await F()})()},[b]);let q=(0,c.useMemo)(()=>b.shouldStream&&v,[b.shouldStream,v]);return(0,c.useEffect)(()=>{if(q){if(void 0===m.current)throw Error("shouldScan effect should only be triggered when component is mounted. Thus pause frame canvas is defined");if(tl(m.current),void 0===C.current)throw Error("shouldScan effect should only be triggered when component is mounted. Thus tracking canvas is defined");if(tl(C.current),null==f.current)throw Error("shouldScan effect should only be triggered when component is mounted. Thus video element is defined");I()}},[q]),c.createElement("div",{style:{...ts,...null==l?void 0:l.container},className:null==d?void 0:d.container},c.createElement("video",{ref:f,style:{...tc,...null==l?void 0:l.video,visibility:o?"hidden":"visible"},className:null==d?void 0:d.video,autoPlay:!0,muted:!0,playsInline:!0}),c.createElement("canvas",{ref:m,style:{display:o?"block":"none",position:"absolute",width:"100%",height:"100%"}}),c.createElement("canvas",{ref:C,style:{position:"absolute",width:"100%",height:"100%"}}),c.createElement("div",{style:{position:"absolute",width:"100%",height:"100%"}},A.finder&&c.createElement(e7,{scanning:v,capabilities:M.capabilities,onOff:A.onOff,zoom:A.zoom&&M.settings.zoom?{value:M.settings.zoom,onChange:async e=>{let t={...B,advanced:[{zoom:e}]};await M.updateConstraints(t)}}:void 0,torch:A.torch?{status:null!==(t=M.settings.torch)&&void 0!==t&&t,toggle:async e=>{let t={...B,advanced:[{torch:e}]};await M.updateConstraints(t)}}:void 0,startScanning:async()=>await F(),stopScanning:async()=>{await M.stopCamera(),tl(C.current),T(!1)}}),s))}}}]);