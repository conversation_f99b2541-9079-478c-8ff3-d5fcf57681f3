!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ca3219b1-75c0-4c26-9c01-4eb95f767b28",e._sentryDebugIdIdentifier="sentry-dbid-ca3219b1-75c0-4c26-9c01-4eb95f767b28")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8782],{70905:function(e,t,n){Promise.resolve().then(n.bind(n,9175)),Promise.resolve().then(n.bind(n,95458))},99376:function(e,t,n){"use strict";var a=n(35475);n.o(a,"redirect")&&n.d(t,{redirect:function(){return a.redirect}}),n.o(a,"useParams")&&n.d(t,{useParams:function(){return a.useParams}}),n.o(a,"usePathname")&&n.d(t,{usePathname:function(){return a.usePathname}}),n.o(a,"useRouter")&&n.d(t,{useRouter:function(){return a.useRouter}}),n.o(a,"useSearchParams")&&n.d(t,{useSearchParams:function(){return a.useSearchParams}})},84440:function(e,t,n){"use strict";n.d(t,{PageLoader:function(){return d},a:function(){return c}});var a=n(57437),r=n(2265),s=n(45402),i=n(27648),o=n(33145),l=n(93448);let c=e=>{let{theme:t="light",className:n,...r}=e;return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("svg",{"aria-hidden":"true",role:"status",className:(0,l.cn)("inline-block align-middle w-4 h-4 ".concat("light"===t?"text-white":"text-black"," animate-spin mb-1"),n),viewBox:"0 0 100 101",fill:"none",xmlns:"http://www.w3.org/2000/svg",...r,children:[(0,a.jsx)("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"".concat("light"===t?"#E5E7EB":"#b2b4b8")}),(0,a.jsx)("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentColor"})]})})},d=r.memo(e=>{let{size:t="screen",showLogo:n=!1,error:r,cta:l,icon:d}=e;return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"".concat("full"===t?"h-full w-full":"h-screen w-screen"," relative bg-white"),children:[n&&(0,a.jsx)("div",{className:"flex bg-white items-center h-[72px] px-10 py-5",children:(0,a.jsx)("div",{className:"w-[20%] ",children:(0,a.jsx)(i.default,{href:"/home",children:(0,a.jsx)(o.default,{src:"/assets/opendashboard-black.svg",height:32,width:34,className:"w-auto h-[32px]",alt:"logo"})})})}),(0,a.jsxs)("div",{className:"absolute -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2 p-4 text-center",children:[r?(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{children:"string"==typeof r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"my-4 flex justify-center",children:d||(0,a.jsx)(s.Z,{width:48,height:48,className:"inline"})}),(0,a.jsx)("span",{className:"text-sm",children:r})]}):(0,a.jsx)(a.Fragment,{children:r})})}):(0,a.jsx)(c,{className:"inline-block align-middle w-6 h-6 text-brand-blue animate-spin mb-1"}),l&&(0,a.jsx)("div",{className:"my-4",children:l.href?(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(i.default,{href:l.href,target:l.target,className:"text-white bg-black text-xs rounded-md px-4 h-[36px] text-center inline-flex items-center font-semibold shadow-lg",children:l.label})}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("button",{className:"text-white bg-black text-xs rounded-md px-4 h-[36px] text-center inline-flex items-center font-semibold shadow-lg",onClick:()=>{var e;return null===(e=l.onClick)||void 0===e?void 0:e.call(l)},children:l.label})})})]})]})})});d.displayName="PageLoader"},12381:function(e,t,n){"use strict";n.d(t,{d:function(){return l},z:function(){return c}});var a=n(57437),r=n(2265),s=n(37053),i=n(90535),o=n(93448);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-neutral-300 border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-8 px-4 py-2",sm:"h-7 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-8 w-8"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:n,variant:r,size:i,asChild:c=!1,...d}=e,u=c?s.Slot:"button";return(0,a.jsx)(u,{className:(0,o.cn)(l({variant:r,size:i,className:n})),ref:t,...d})});c.displayName="Button"},68245:function(e,t,n){"use strict";n.d(t,{X:function(){return l}});var a=n(57437),r=n(2265),s=n(9270),i=n(20653),o=n(93448);let l=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(s.fC,{ref:t,className:(0,o.cn)("peer h-4 w-4 shrink-0 rounded-none border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",n),...r,children:(0,a.jsx)(s.z$,{className:(0,o.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(i.nQG,{className:"h-4 w-4"})})})});l.displayName=s.fC.displayName},74291:function(e,t,n){"use strict";n.d(t,{$N:function(){return h},Vq:function(){return l},cN:function(){return x},cZ:function(){return f},fK:function(){return m},hg:function(){return c}});var a=n(57437),r=n(2265),s=n(49027),i=n(20653),o=n(93448);let l=s.fC,c=s.xz,d=s.h_;s.x8;let u=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(s.aV,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",n),...r})});u.displayName=s.aV.displayName;let f=r.forwardRef((e,t)=>{let{className:n,children:r,hideCloseBtn:l,...c}=e;return(0,a.jsxs)(d,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(s.VY,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n),...c,children:[r,!l&&(0,a.jsxs)(s.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.Pxu,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=s.VY.displayName;let m=e=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...n})};m.displayName="DialogHeader";let x=e=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...n})};x.displayName="DialogFooter";let h=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(s.Dx,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",n),...r})});h.displayName=s.Dx.displayName,r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(s.dk,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",n),...r})}).displayName=s.dk.displayName},32060:function(e,t,n){"use strict";n.d(t,{$F:function(){return c},AW:function(){return h},Ju:function(){return g},KM:function(){return b},Ph:function(){return f},Qk:function(){return d},TG:function(){return x},VD:function(){return v},Xi:function(){return p},cq:function(){return u},h_:function(){return l},kt:function(){return m}});var a=n(57437),r=n(2265),s=n(70085),i=n(20653),o=n(93448);let l=s.fC,c=s.xz,d=s.ZA,u=s.Uv,f=s.Tr;s.Ee;let m=r.forwardRef((e,t)=>{let{className:n,inset:r,children:l,...c}=e;return(0,a.jsxs)(s.fF,{ref:t,className:(0,o.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",n),...c,children:[l,(0,a.jsx)(i.XCv,{className:"ml-auto h-4 w-4"})]})});m.displayName=s.fF.displayName;let x=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(s.tu,{ref:t,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...r})});x.displayName=s.tu.displayName;let h=r.forwardRef((e,t)=>{let{className:n,sideOffset:r=4,...i}=e;return(0,a.jsx)(s.Uv,{children:(0,a.jsx)(s.VY,{ref:t,sideOffset:r,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...i})})});h.displayName=s.VY.displayName;let p=r.forwardRef((e,t)=>{let{className:n,inset:r,...i}=e;return(0,a.jsx)(s.ck,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",n),...i})});p.displayName=s.ck.displayName,r.forwardRef((e,t)=>{let{className:n,children:r,checked:l,...c}=e;return(0,a.jsxs)(s.oC,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),checked:l,...c,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(s.wU,{children:(0,a.jsx)(i.nQG,{className:"h-4 w-4"})})}),r]})}).displayName=s.oC.displayName,r.forwardRef((e,t)=>{let{className:n,children:r,...l}=e;return(0,a.jsxs)(s.Rk,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(s.wU,{children:(0,a.jsx)(i.jXb,{className:"h-4 w-4 fill-current"})})}),r]})}).displayName=s.Rk.displayName;let g=r.forwardRef((e,t)=>{let{className:n,inset:r,...i}=e;return(0,a.jsx)(s.__,{ref:t,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",n),...i})});g.displayName=s.__.displayName;let v=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(s.Z0,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",n),...r})});v.displayName=s.Z0.displayName;let b=e=>{let{className:t,...n}=e;return(0,a.jsx)("span",{className:(0,o.cn)("ml-auto text-xs tracking-widest opacity-60",t),...n})};b.displayName="DropdownMenuShortcut"},40279:function(e,t,n){"use strict";n.d(t,{I:function(){return i}});var a=n(57437),r=n(2265),s=n(93448);let i=r.forwardRef((e,t)=>{let{className:n,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,s.cn)("flex h-8 w-full rounded-md border border-input border-neutral-300  bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",n),ref:t,...i})});i.displayName="Input"},90641:function(e,t,n){"use strict";n.d(t,{B:function(){return l},ScrollArea:function(){return o}});var a=n(57437),r=n(2265),s=n(43643),i=n(93448);let o=r.forwardRef((e,t)=>{let{className:n,children:r,...o}=e;return(0,a.jsxs)(s.fC,{ref:t,className:(0,i.cn)("relative overflow-hidden",n),...o,children:[(0,a.jsx)(s.l_,{className:"h-full w-full rounded-[inherit]",children:r}),(0,a.jsx)(l,{}),(0,a.jsx)(s.Ns,{})]})});o.displayName=s.fC.displayName;let l=r.forwardRef((e,t)=>{let{className:n,orientation:r="vertical",...o}=e;return(0,a.jsx)(s.gb,{ref:t,orientation:r,className:(0,i.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",n),...o,children:(0,a.jsx)(s.q4,{className:"relative flex-1 rounded-full bg-border"})})});l.displayName=s.gb.displayName},94589:function(e,t,n){"use strict";n.d(t,{r:function(){return o}});var a=n(57437),r=n(2265),s=n(50721),i=n(93448);let o=r.forwardRef((e,t)=>{let{className:n,thumbClassName:r,...o}=e;return(0,a.jsx)(s.fC,{className:(0,i.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",n),...o,ref:t,children:(0,a.jsx)(s.bU,{className:(0,i.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0",r)})})});o.displayName=s.fC.displayName},9175:function(e,t,n){"use strict";n.d(t,{J:function(){return r},MainContentLayout:function(){return u}});var a,r,s=n(57437);n(2265);var i=n(12381),o=n(40178),l=n(32060),c=n(40279),d=n(99376);(a=r||(r={})).Import="import",a.Export="export",a.ActivateMessaging="enableMessaging",a.ManageAccess="manageAccess",a.CopyLink="copyLink",a.ConfigureTitle="configureTitle";let u=e=>{let t=(0,d.useRouter)();return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"w-full h-full overflow-hidden flex flex-col",children:[(0,s.jsx)("div",{className:"title w-full border-b border-neutral-300 h-12 flex items-center",children:(0,s.jsxs)("div",{className:"overflow-hidden w-full flex items-center p-2 gap-2 justify-start",children:[e.onBack&&(0,s.jsx)(i.z,{className:"text-xs font-semibold h-auto p-1.5 items-center hover:bg-transparent",onClick:()=>{var n;"string"==typeof e.onBack?t.push(e.onBack):null===(n=e.onBack)||void 0===n||n.call(e)},variant:"ghost",children:"←"}),(e.icon||e.emoji)&&(0,s.jsx)(i.z,{variant:"ghost",className:"text-xl hover:bg-neutral-300 p-1 size-6 rounded-full items-center justify-center",children:(0,s.jsx)("span",{className:"relative",children:e.icon?e.icon:e.emoji})}),"string"==typeof e.title?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,s.jsx)(c.I,{className:"overflow-hidden truncate text-left font-semibold text-sm !border-0 pl-2 !shadow-none !outline-none !ring-0 text-black",readOnly:!e.editable,value:e.title||"Untitled"})})}):(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:e.title}),(0,s.jsx)("div",{children:e.titleRightContent&&(0,s.jsx)(s.Fragment,{children:e.titleRightContent})}),e.moreActions&&e.moreActions.onAction&&e.moreActions.actions.length>0&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(l.h_,{children:[(0,s.jsx)(l.$F,{asChild:!0,children:(0,s.jsx)(i.z,{variant:"ghost",className:"mr-2 hover:bg-neutral-300 p-1 size-6 rounded-full",children:(0,s.jsx)(o.Z,{className:"size-4"})})}),(0,s.jsx)(l.AW,{className:"w-56 rounded-none p-1.5",align:"end",children:(0,s.jsx)(l.Qk,{children:e.moreActions.actions.map((t,n)=>(0,s.jsxs)(l.Xi,{className:"rounded-none text-xs font-semibold cursor-pointer truncate",onClick:n=>{var a;return null===(a=e.moreActions)||void 0===a?void 0:a.onAction(t.key)},children:[t.label,t.shortcut&&(0,s.jsx)(l.KM,{children:t.shortcut})]},n))})})]})})]})}),(0,s.jsx)("div",{className:"body flex-1 overflow-hidden",children:(0,s.jsx)("div",{className:"w-full h-full",children:e.children})})]})})}},95458:function(e,t,n){"use strict";n.d(t,{s:function(){return N},NotificationsSettings:function(){return j}});var a,r=n(57437),s=n(90641),i=n(2265),o=n(23121),l=n(89399),c=n(94589),d=n(68245),u=n(39255),f=n(17807);(a||(a={})).Notification="notification";var m=n(3163),x=n(84440),h=n(14438),p=n(74291),g=n(12381),v=n(55344),b=n(84977);let j=()=>{let{token:e}=(0,u.a)(),[t,n]=(0,i.useState)(!0),[p,g]=(0,i.useState)(""),[v,b]=(0,i.useState)(null),j=(0,i.useCallback)(async()=>{if(!e)return;g(""),n(!0);let t=await (0,f.Gw)(e.token);if(n(!1),t.error){g(t.error||m.E);return}b(t.data.data.settings.notification),console.log("Settings:",t.data.data.settings.notification)},[e]),y=(e,t)=>{let n={...v};n["".concat(e)]=t,w(n).then()},w=async t=>{if(!e||!v)return;let n={...v,...t},r=await (0,f.VP)(e.token,a.Notification,n);if(r.error){h.Am.error(r.error);return}b(n),h.Am.success("Notification settings updated")};return(0,i.useEffect)(()=>{j().then()},[j]),(0,r.jsxs)(r.Fragment,{children:[(t||p)&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(x.PageLoader,{error:p,size:"full",cta:p?{label:"Reload",onClick:j}:null})}),v&&(0,r.jsx)(s.ScrollArea,{className:"size-full",children:(0,r.jsx)("div",{className:"p-4 pb-20",children:(0,r.jsx)("div",{className:"max-w-[650px]",children:(0,r.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,r.jsxs)("div",{className:"flex flex-col flex-1 gap-4",children:[(0,r.jsx)("h4",{className:"block text-sm font-semibold leading-6 text-gray-900",children:"Notify me on"}),(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsx)(o.Z,{className:"size-4"}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col gap-1",children:[(0,r.jsx)("h5",{className:"font-semibold text-sm",children:"Inbox"}),(0,r.jsx)("div",{className:"text-xs",children:"You’ll always receive your notification in your Opendashboard inbox"})]}),(0,r.jsx)(c.r,{checked:!0,disabled:!0,"aria-readonly":!0})]}),(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsx)(l.Z,{className:"size-4"}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col gap-1",children:[(0,r.jsx)("h5",{className:"font-semibold text-sm",children:"Email"}),(0,r.jsx)("div",{className:"text-xs",children:"Receive your notifications by email"})]}),(0,r.jsx)(c.r,{checked:v.email.isActive,onCheckedChange:e=>{y("email",{...v.email,isActive:e})},"aria-readonly":!0})]}),(0,r.jsx)(N,{showToggle:!0})]}),v.email.isActive&&(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex flex-col flex-1 gap-4 mt-8 font-medium",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("h4",{className:"block text-sm font-semibold leading-6 text-gray-900",children:"Notify me when"}),(0,r.jsx)("div",{className:"flex-1"}),(0,r.jsx)("h4",{className:"block text-sm font-semibold leading-6 text-gray-900",children:"Mail"})]}),(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsx)("div",{className:"flex-1 flex flex-col gap-1",children:(0,r.jsx)("div",{className:"text-xs",children:"I’m mentioned in a comment or document"})}),(0,r.jsx)(d.X,{checked:v.email.notifyOn.comment,onCheckedChange:e=>{y("email",{...v.email,notifyOn:{...v.email.notifyOn,comment:!!e}})},"aria-readonly":!0})]}),(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsx)("div",{className:"flex-1 flex flex-col gap-1",children:(0,r.jsx)("div",{className:"text-xs",children:"Someone replies to my comment"})}),(0,r.jsx)(d.X,{checked:v.email.notifyOn.replies,onCheckedChange:e=>{y("email",{...v.email,notifyOn:{...v.email.notifyOn,replies:!!e}})},"aria-readonly":!0})]}),(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsx)("div",{className:"flex-1 flex flex-col gap-1",children:(0,r.jsx)("div",{className:"text-xs",children:"I’m assigned in a record"})}),(0,r.jsx)(d.X,{checked:v.email.notifyOn.assignedRecord,onCheckedChange:e=>{y("email",{...v.email,notifyOn:{...v.email.notifyOn,assignedRecord:!!e}})},"aria-readonly":!0})]}),(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsx)("div",{className:"flex-1 flex flex-col gap-1",children:(0,r.jsx)("div",{className:"text-xs",children:"A reminder is due"})}),(0,r.jsx)(d.X,{checked:v.email.notifyOn.dueTask,onCheckedChange:e=>{y("email",{...v.email,notifyOn:{...v.email.notifyOn,dueTask:!!e}})},"aria-readonly":!0})]})]})})]})})})})]})},N=e=>{let[t,n]=(0,i.useState)(!1),{permission:a,requestPermission:s,fcmToken:l,setTokenRegistered:d,tokenRegistered:m,disableNotification:x,notificationDisabled:h}=(0,v.kc)(),{token:j,user:N}=(0,u.a)(),{toast:y}=(0,b.V)(),[w,k]=(0,i.useState)(!1),[C,R]=(0,i.useState)(!1),E=async()=>{k(!0);let e=await s();k(!1),await P(e||"")},P=(0,i.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(C||!j||!e)return;R(!0);let a=await (0,f.kP)(j.token,{token:e});if(a.error){R(!1),t||y.error("Failed to register push token: "+a.error);return}R(!1),d(!0),t||y.success("Push notification enabled"),n(!1)},[C,e.src,d,y,j]),z=async()=>{x(!0);let e=await (0,f.nT)((null==j?void 0:j.token)||"");if(e.error){y.error("Error de-registering token: "+e.error);return}d(!1),y.error("Push notification disabled")},S=t=>{n(t),t||"layout"!==e.src||x(!0)},I=(0,i.useRef)(!1),_="undefined"!=typeof Notification&&!!Notification;return((0,i.useEffect)(()=>{if("layout"!==e.src||!N||I.current||!_||"undefined"==typeof Notification||!Notification)return;if(console.log({fcmToken:l,permission:a,tokenRegistered:m,notificationDisabled:h}),I.current=!0,"granted"===a&&l&&!m&&!h){P(l,!0).then();return}let t=new Date(N.createdAt||"0"),r=new Date(new Date().getTime()-36e5),s="denied"===Notification.permission||h;t<r&&!s&&n(!0)},[l,N]),console.log({permission:a}),_)?(0,r.jsxs)(r.Fragment,{children:[e.showToggle&&(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,r.jsx)(o.Z,{className:"size-4"}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col gap-1",children:[(0,r.jsx)("h5",{className:"font-semibold text-sm",children:"Push Notification"}),(0,r.jsx)("div",{className:"text-xs",children:"Receive push notifications on this device"})]}),(0,r.jsx)(c.r,{checked:"granted"===a&&!h&&m,onCheckedChange:e=>{e?(x(!1),"granted"===a&&l?P(l).then():n(!0)):z().then()},"aria-readonly":!0})]})}),(0,r.jsx)(p.Vq,{open:t,onOpenChange:S,children:(0,r.jsxs)(p.cZ,{className:"w-96 max-w-full !rounded-none p-4",children:[(0,r.jsx)(p.fK,{children:(0,r.jsx)(p.$N,{className:"font-bold",children:"Enable push notification"})}),(0,r.jsx)("div",{className:"flex flex-col gap-2 overflow-hidden",children:(0,r.jsx)("div",{className:"text-xs font-medium",children:"denied"===a?"You have denied the notification permission. Please reset the permission on your device and try again.":"default"===a?"Enable to receive push notification on this device as things happen in your workspace":"Notification permission granted successfully. You will now receive updates."})}),(0,r.jsxs)("div",{className:"flex flex-row-reverse",children:[(0,r.jsx)(g.z,{disabled:w||C||"denied"===a,onClick:E,className:"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1",children:"Enable Push Notification"}),(0,r.jsx)("div",{className:"flex-1"}),(0,r.jsx)(g.z,{onClick:()=>S(!1),variant:"ghost",className:"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1",children:"Cancel"})]})]})})]}):null}},84977:function(e,t,n){"use strict";n.d(t,{AlertProvider:function(){return u},V:function(){return f}});var a=n(57437),r=n(2265),s=n(92367),i=n(14438),o=n(74291),l=n(12381),c=n(99376);let d=r.createContext(null),u=e=>{let t=(0,c.useRouter)(),n=(e,t,n,r)=>{(0,s._1)({title:e,message:"",buttons:n,overlayClassName:"",customUI:e=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(o.Vq,{open:!0,onOpenChange:()=>e.onClose(),children:(0,a.jsxs)(o.cZ,{className:"max-w-[600px] !rounded-none p-4",children:[(0,a.jsx)(o.fK,{children:(0,a.jsx)(o.$N,{className:"font-bold",children:e.title})}),(0,a.jsx)("div",{children:(0,a.jsx)("div",{className:"flex gap-2 py-2 text-xs font-medium",children:t})}),(0,a.jsx)(o.cN,{children:(0,a.jsx)("div",{className:"flex flex-row-reverse gap-1",children:n.map((t,n)=>(0,a.jsx)(l.z,{className:"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 ".concat("danger"===t.variant?"bg-red-600":""),variant:"ghost"===t.variant?"ghost":void 0,onClick:n=>{t.onClick&&!1===t.onClick()||e.onClose()},children:t.label},n))})})]})})}),...r||{}})},r={alert:(e,t,a,r)=>{n(e,t,[{label:"Ok",onClick:()=>a?a():void 0}],r)},choice:(e,t,a,r)=>{n(e,t,a,r)},promptUpgrade:(e,a)=>{n("Upgrade Needed!",e,[{label:"Upgrade",onClick:()=>{t.push("/".concat(a,"/settings/plans"))}},{label:"Cancel",variant:"ghost",onClick:()=>void 0}])},confirm:(e,t,a,r,s,i,o,l)=>{let c={label:o||"Confirm",onClick:a};i&&(c.variant="danger"),n(e,t,[c,{label:l||"Cancel",variant:"ghost",onClick:()=>r?r():void 0}],s)},toast:i.Am};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(d.Provider,{value:r,children:e.children})})};function f(){return(0,r.useContext)(d)}},55344:function(e,t,n){"use strict";n.d(t,{Vr:function(){return d},kc:function(){return u}});var a=n(57437),r=n(2265),s=n(738),i=n(4818),o=n(40257);let l={getToken:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(0===s.C6().length)throw"Firebase not Initialized";let t=localStorage.getItem("fcm_token");if(t)return t;let n=Notification.permission;if("default"===n){if(e)n=await Notification.requestPermission();else throw"Notification not requested"}if("granted"!==n)throw"Notification not granted";try{let e=(0,i.KL)(),t=await (0,i.LP)(e,{vapidKey:o.env.NEXT_PUBLIC_VAPID_KEY});return localStorage.setItem("fcm_token",t),t}catch(e){console.log(e)}return null},init:async function(e){s.ZF({apiKey:"AIzaSyB5Yw8TjfhcTTYsTBM-mGvry7UKEmVRc4o",authDomain:"opendashboard-5ce77.firebaseapp.com",projectId:"opendashboard-5ce77",storageBucket:"opendashboard-5ce77.firebasestorage.app",messagingSenderId:"984921906680",appId:"1:984921906680:web:b9248b1d0ecbfb2c29ca0d",measurementId:"G-V99893EVB0"});let t=(0,i.KL)();(0,i.ps)(t,e.onMessage)}},c=r.createContext(null),d=e=>{let[t,n]=(0,r.useState)(localStorage.getItem("fcm_token")||void 0),[s,i]=(0,r.useState)(!1),[o,d]=(0,r.useState)("true"===localStorage.getItem("notificationDisabled")),u=(0,r.useCallback)(async()=>{let e=null;try{e=await l.getToken()}catch(e){console.error("Error getting fcm token",e)}return e?(n(e),e):null},[]),f=(0,r.useCallback)(async()=>(await Notification.requestPermission(),await u()),[u]),m=e=>{console.log("On message:",e)};(0,r.useEffect)(()=>{let e=async()=>{await l.init({onMessage:m}),await u()};"serviceWorker"in navigator&&navigator.serviceWorker.addEventListener("message",e=>{console.log("event for the service worker",e)}),e().then()},[]),(0,r.useEffect)(()=>{"serviceWorker"in navigator&&navigator.serviceWorker.addEventListener("message",e=>{console.log("event for the service worker",e)}),"permissions"in navigator&&navigator.permissions.query({name:"notifications"}).then(e=>{e.onchange=async function(){await u()}})},[u]);let x="undefined"==typeof Notification?"denied":Notification.permission;return(0,a.jsx)(c.Provider,{value:{permission:x,fcmToken:t,requestPermission:f,notificationDisabled:o,disableNotification:e=>{d(e),localStorage.setItem("notificationDisabled",String(e))},tokenRegistered:s,setTokenRegistered:i},children:e.children})};function u(){return(0,r.useContext)(c)}},89399:function(e,t,n){"use strict";var a=n(2265);let r=a.forwardRef(function(e,t){let{title:n,titleId:r,...s}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))});t.Z=r},9270:function(e,t,n){"use strict";n.d(t,{fC:function(){return N},z$:function(){return w}});var a=n(2265),r=n(98575),s=n(73966),i=n(6741),o=n(80886),l=n(6718),c=n(90420),d=n(71599),u=n(66840),f=n(57437),m="Checkbox",[x,h]=(0,s.b)(m),[p,g]=x(m);function v(e){let{__scopeCheckbox:t,checked:n,children:r,defaultChecked:s,disabled:i,form:l,name:c,onCheckedChange:d,required:u,value:x="on",internal_do_not_use_render:h}=e,[g,v]=(0,o.T)({prop:n,defaultProp:null!=s&&s,onChange:d,caller:m}),[b,j]=a.useState(null),[N,y]=a.useState(null),w=a.useRef(!1),k=!b||!!l||!!b.closest("form"),C={checked:g,disabled:i,setChecked:v,control:b,setControl:j,name:c,form:l,value:x,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!R(s)&&s,isFormControl:k,bubbleInput:N,setBubbleInput:y};return(0,f.jsx)(p,{scope:t,...C,children:"function"==typeof h?h(C):r})}var b="CheckboxTrigger",j=a.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:s,onClick:o,...l}=e,{control:c,value:d,disabled:m,checked:x,required:h,setControl:p,setChecked:v,hasConsumerStoppedPropagationRef:j,isFormControl:N,bubbleInput:y}=g(b,n),w=(0,r.e)(t,p),k=a.useRef(x);return a.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>v(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,v]),(0,f.jsx)(u.WV.button,{type:"button",role:"checkbox","aria-checked":R(x)?"mixed":x,"aria-required":h,"data-state":E(x),"data-disabled":m?"":void 0,disabled:m,value:d,...l,ref:w,onKeyDown:(0,i.M)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(o,e=>{v(e=>!!R(e)||!e),y&&N&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})})});j.displayName=b;var N=a.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:a,checked:r,defaultChecked:s,required:i,disabled:o,value:l,onCheckedChange:c,form:d,...u}=e;return(0,f.jsx)(v,{__scopeCheckbox:n,checked:r,defaultChecked:s,disabled:o,required:i,onCheckedChange:c,name:a,form:d,value:l,internal_do_not_use_render:e=>{let{isFormControl:a}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(j,{...u,ref:t,__scopeCheckbox:n}),a&&(0,f.jsx)(C,{__scopeCheckbox:n})]})}})});N.displayName=m;var y="CheckboxIndicator",w=a.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:a,...r}=e,s=g(y,n);return(0,f.jsx)(d.z,{present:a||R(s.checked)||!0===s.checked,children:(0,f.jsx)(u.WV.span,{"data-state":E(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=y;var k="CheckboxBubbleInput",C=a.forwardRef((e,t)=>{let{__scopeCheckbox:n,...s}=e,{control:i,hasConsumerStoppedPropagationRef:o,checked:d,defaultChecked:m,required:x,disabled:h,name:p,value:v,form:b,bubbleInput:j,setBubbleInput:N}=g(k,n),y=(0,r.e)(t,N),w=(0,l.D)(d),C=(0,c.t)(i);a.useEffect(()=>{if(!j)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!o.current;if(w!==d&&e){let n=new Event("click",{bubbles:t});j.indeterminate=R(d),e.call(j,!R(d)&&d),j.dispatchEvent(n)}},[j,w,d,o]);let E=a.useRef(!R(d)&&d);return(0,f.jsx)(u.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=m?m:E.current,required:x,disabled:h,name:p,value:v,form:b,...s,tabIndex:-1,ref:y,style:{...s.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function E(e){return R(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=k},50721:function(e,t,n){"use strict";n.d(t,{bU:function(){return w},fC:function(){return y}});var a=n(2265),r=n(6741),s=n(98575),i=n(73966),o=n(80886),l=n(6718),c=n(90420),d=n(66840),u=n(57437),f="Switch",[m,x]=(0,i.b)(f),[h,p]=m(f),g=a.forwardRef((e,t)=>{let{__scopeSwitch:n,name:i,checked:l,defaultChecked:c,required:m,disabled:x,value:p="on",onCheckedChange:g,form:v,...b}=e,[y,w]=a.useState(null),k=(0,s.e)(t,e=>w(e)),C=a.useRef(!1),R=!y||v||!!y.closest("form"),[E,P]=(0,o.T)({prop:l,defaultProp:null!=c&&c,onChange:g,caller:f});return(0,u.jsxs)(h,{scope:n,checked:E,disabled:x,children:[(0,u.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":E,"aria-required":m,"data-state":N(E),"data-disabled":x?"":void 0,disabled:x,value:p,...b,ref:k,onClick:(0,r.M)(e.onClick,e=>{P(e=>!e),R&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),R&&(0,u.jsx)(j,{control:y,bubbles:!C.current,name:i,value:p,checked:E,required:m,disabled:x,form:v,style:{transform:"translateX(-100%)"}})]})});g.displayName=f;var v="SwitchThumb",b=a.forwardRef((e,t)=>{let{__scopeSwitch:n,...a}=e,r=p(v,n);return(0,u.jsx)(d.WV.span,{"data-state":N(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:t})});b.displayName=v;var j=a.forwardRef((e,t)=>{let{__scopeSwitch:n,control:r,checked:i,bubbles:o=!0,...d}=e,f=a.useRef(null),m=(0,s.e)(f,t),x=(0,l.D)(i),h=(0,c.t)(r);return a.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==i&&t){let n=new Event("click",{bubbles:o});t.call(e,i),e.dispatchEvent(n)}},[x,i,o]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...d,tabIndex:-1,ref:m,style:{...d.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var y=g,w=b},6718:function(e,t,n){"use strict";n.d(t,{D:function(){return r}});var a=n(2265);function r(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}},function(e){e.O(0,[8310,6137,7648,311,2534,4451,1107,85,3493,3139,8107,4693,7900,991,2971,6577,1744],function(){return e(e.s=70905)}),_N_E=e.O()}]);