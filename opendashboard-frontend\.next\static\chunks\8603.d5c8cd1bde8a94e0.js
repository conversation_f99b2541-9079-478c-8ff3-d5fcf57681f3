!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="6ecab41e-dad7-4daf-9226-8994101366cf",e._sentryDebugIdIdentifier="sentry-dbid-6ecab41e-dad7-4daf-9226-8994101366cf")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8603],{53731:function(e,a){var t=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;a.G=function(e){if(!e||e.length>254||!t.test(e))return!1;var a=e.split("@");return!(a[0].length>64||a[1].split(".").some(function(e){return e.length>63}))}},89034:function(e,a,t){t.r(a),t.d(a,{BoardView:function(){return J}});var r=t(57437),d=t(32060),l=t(12381),s=t(40178),i=t(2265),o=t(68245),n=t(93662),c=t(59892),u=t(68738),m=t(36109),p=t(24681),f=t(82375),x=t(32659),h=t(24754),b=t(42212),v=t(56227),g=t(54207),y=t(95473),j=t(87957),I=t(32469);let w=e=>{let{selectedIds:a,setSelectedIds:t}=(0,y.Bf)(),[u,p]=(0,i.useState)(!1),{deleteRecords:f}=(0,y.Bf)(),{url:x}=(0,b.cF)(),{openRecord:h}=(0,I.x)(),{item:v,databaseDefinition:g,dataColumnsOrder:w,dataColumnPropsMap:C,titleColId:N}=e,{setNodeRef:O,attributes:P,listeners:F,transform:k,transition:M,isDragging:A}=(0,n.nB)({id:"".concat(v.id,"|").concat(e.columnId),data:{type:"Item",item:v,columnId:e.columnId}}),T={transition:M,transform:c.ux.Transform.toString(k)},S=(0,m.recordValueToText)(v.processedRecord.processedRecordValues[N])||"Untitled";return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"w-full border bg-white overflow-hidden ".concat(A?"opacity-0 invisible":""),ref:O,style:T,onClick:()=>h(e.item.id,e.databaseId),...P,...F,children:[(0,r.jsxs)("div",{className:"p-2.5 text-sm font-semibold flex gap-2 items-center",children:[!e.disabled&&(0,r.jsx)(o.X,{onClick:e=>e.stopPropagation(),checked:a.includes(e.item.id),onCheckedChange:r=>{r?t([...a,e.item.id]):t([...(0,j.removeAllArrayItem)(a,e.item.id)])}}),(0,r.jsx)("div",{className:"flex-1 text-xs truncate",children:S}),!e.disabled&&e.databaseId&&(0,r.jsxs)(d.h_,{children:[(0,r.jsx)(d.$F,{asChild:!0,children:(0,r.jsx)(l.z,{variant:"ghost",className:"rounded-full h-auto p-1",children:(0,r.jsx)(s.Z,{className:"size-4"})})}),(0,r.jsx)(d.AW,{className:"w-56  rounded-none text-neutral-800 font-semibold",align:"end",children:(0,r.jsx)(d.Xi,{className:"text-xs rounded-none p-2",onClick:()=>{f(e.databaseId,[e.item.id]).then()},children:"Delete"})})]})]}),(0,r.jsx)("div",{className:"flex flex-col gap-3 px-2.5  pb-4",children:w.map(a=>C[a].isHidden?null:(0,r.jsx)(i.Fragment,{children:(0,r.jsx)(D,{definition:e.databaseDefinition,columnId:a,item:e.item})},a))})]})})},D=e=>{let a;let{databaseStore:t,databaseErrorStore:d,members:l}=(0,b.cF)(),s=e.definition.columnsMap[e.columnId];if(!s)return null;let i=e.item.processedRecord.processedRecordValues[e.columnId]||"";if(!i||"string"==typeof i&&!i.trim()||Array.isArray(i)&&0===i.length)return null;switch(s.type){case u.DatabaseFieldDataType.UUID:case u.DatabaseFieldDataType.Text:case u.DatabaseFieldDataType.Number:case u.DatabaseFieldDataType.AI:case u.DatabaseFieldDataType.Summarize:case u.DatabaseFieldDataType.Derived:let n=String(i);(s.type===u.DatabaseFieldDataType.Text||s.type===u.DatabaseFieldDataType.Number)&&(n=(0,f.Xt)(s,n).displayValue),a=(0,r.jsx)("div",{className:"font-medium truncate text-xs",children:n});break;case u.DatabaseFieldDataType.Select:a=(0,r.jsx)(x.LH,{column:s,row:e.item});break;case u.DatabaseFieldDataType.Checkbox:a=(0,r.jsx)(o.X,{checked:!!i});break;case u.DatabaseFieldDataType.Linked:a=(0,r.jsx)(h.t_,{refDatabaseId:e.item.record.databaseId,column:s,row:e.item,databaseErrorStore:d,databaseStore:t});break;case u.DatabaseFieldDataType.Date:case u.DatabaseFieldDataType.CreatedAt:case u.DatabaseFieldDataType.UpdatedAt:a=(0,r.jsx)(v.d$,{column:s,className:"text-xs font-medium",row:e.item});break;case u.DatabaseFieldDataType.CreatedBy:case u.DatabaseFieldDataType.UpdatedBy:case u.DatabaseFieldDataType.Person:a=(0,r.jsx)(g.VW,{column:s,row:e.item,members:l});break;case u.DatabaseFieldDataType.Files:let c=i&&Array.isArray(i)?i:[];a=(0,r.jsxs)("div",{className:"font-medium truncate text-xs",children:[c.length," file(s)"]})}return a?(0,r.jsxs)("div",{className:"flex gap-2 items-center w-full overflow-hidden",children:[(0,r.jsx)(p.e,{type:s.type,className:"col-3"}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden",children:a})]}):null};var C=t(90641),N=t(5867),O=t(20029);let P=e=>{let{items:a,updateColumn:t,column:d}=e,{setNodeRef:l,attributes:s,listeners:i,transform:o,transition:u,isDragging:m}=(0,n.nB)({id:d.id,data:{type:"Column",column:d},disabled:!!e.dndDisabled}),p={transition:u,transform:c.ux.Transform.toString(o)};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex-none flex flex-col gap-2 h-full overflow-hidden w-72 ".concat(m?"opacity-0 invisible":""),ref:l,style:p,...s,...i,children:[(0,r.jsx)(F,{column:d,items:a,addRecord:e.addRecord,columnProps:e.columnProps,disabled:e.dndDisabled||!e.canEditStructure,updateColumn:e.updateColumn}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,r.jsx)(C.ScrollArea,{className:"size-full scrollBlockChild","aria-orientation":"vertical",children:(0,r.jsx)("div",{className:"w-full h-full overflow-y-auto overflow-x-hidden flex flex-col gap-4 pb-4",children:(0,r.jsx)(n.Fo,{items:a,disabled:e.dndDisabled,children:a.map((a,t)=>(0,r.jsx)(w,{dataColumnPropsMap:e.dataColumnPropsMap,dataColumnsOrder:e.dataColumnsOrder,databaseDefinition:e.databaseDefinition,disabled:e.dndDisabled||!e.canEditData,titleColId:e.titleColId,columnId:e.column.id,databaseId:e.databaseId,item:a},a.id||t))})})})})]})})},F=e=>{let{column:a,items:t,columnProps:i,updateColumn:o,addRecord:n,disabled:c,...u}=e,m=null;a.color&&(m=(0,N.S8)(a.color));let p={};return m&&(p.backgroundColor="".concat(m.bg)),(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("header",{...u,className:"py-2 flex gap-2 pr-0.5 group",children:[(0,r.jsxs)("div",{className:"flex-1 overflow-hidden flex gap-1 items-center justify-start",children:[(0,r.jsx)("div",{className:"text-xs bg-neutral-200 rounded-full py-0.5 px-2 font-semibold w-min max-w-full truncate",style:p,children:a.title}),(0,r.jsx)(l.z,{variant:"ghost",className:"h-auto w-auto py-0.5 px-2 rounded-full items-center text-xs gap-1 mr-0.5",children:t.length})]}),!c&&(0,r.jsxs)(r.Fragment,{children:[n&&(0,r.jsx)(l.z,{variant:"ghost",onClick:()=>null==n?void 0:n(a.id),className:"rounded-full size-6 items-center p-1.5 -mr-1 hidden group-hover:block",children:(0,r.jsx)(O.oFk,{className:"size-3"})}),(0,r.jsxs)(d.h_,{children:[(0,r.jsx)(d.$F,{asChild:!0,children:(0,r.jsx)(l.z,{variant:"ghost",className:"rounded-full h-auto p-1",children:(0,r.jsx)(s.Z,{className:"size-4"})})}),(0,r.jsx)(d.AW,{className:"w-56  rounded-none text-neutral-800 font-semibold",align:"end",children:(0,r.jsx)(d.Xi,{className:"text-xs rounded-none p-2 py-1.5",onClick:()=>null==o?void 0:o(a.id,{isHidden:!(null==i?void 0:i.isHidden)}),children:(null==i?void 0:i.isHidden)?"Show":"Hide"})})]})]})]})})},k=e=>{let{boardItems:a,rowIdsMap:t,columns:d,groupItemsProps:l,updateColumn:s,dataColumnsOrder:i,dataColumnPropsMap:o,databaseDefinition:n,titleColId:c}=e;return(console.log({boardItems:a,columns:d,groupItemsProps:l}),d)?(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex-none flex flex-col gap-2 h-full w-64",children:[(0,r.jsx)("header",{className:"py-2 flex gap-2",children:(0,r.jsx)("div",{className:"text-xs font-semibold",children:"Hidden column"})}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,r.jsx)(C.ScrollArea,{className:"w-full h-full",children:(0,r.jsx)("div",{className:"h-full w-full overflow-y-auto flex flex-col gap-1 pb-4",children:d.map(d=>(0,r.jsx)(F,{column:d,updateColumn:s,disabled:e.disabled,items:a[d.id].map(e=>t[e]),columnProps:l[d.id]},d.id))})})})]})}):null};var M=t(10795),A=t(26652),T=t(98116);let S=e=>{let[a,t]=(0,i.useState)(!1),d=(0,i.useRef)(null),s=N.Pp[Math.floor(Math.random()*N.Pp.length)];return(0,i.useEffect)(()=>{let e=d.current;if(!e)return;e.focus();let a=()=>{t(!1)};return e.addEventListener("blur",a),()=>{e.removeEventListener("blur",a)}},[a]),(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex-none flex flex-col gap-2 h-full w-64",children:[!a&&(0,r.jsx)("header",{className:"py-2 flex gap-2",children:(0,r.jsxs)(l.z,{variant:"outline",onClick:e=>{t(!0)},className:"h-auto w-full p-2 py-1 rounded-full items-center text-xs gap-1 mr-0.5",children:[(0,r.jsx)(A.Z,{className:"h-3 w-3"}),"New Column"]})}),a&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)(T.h,{shortEnter:!0,ref:d,onChange:a=>{(a=a.trim())&&(e.createColumn(a,s.color),t(!1))},wrapperClassname:"h-8 pr-1"})})})]})})};var E=t(54887),z=t(78068),R=t(18626),Z=t(88119),B=t(31096),H=t(66312),_=t(51810),L=t(14803),V=t(52292),U=t(92326),X=t(16720);let $="ungrouped",W=e=>{let{databaseStore:a}=(0,b.cF)(),{definition:t}=e;return a[t.databaseId].database.definition.columnsMap[t.groupByIds[0]]?(0,r.jsx)(G,{...e}):null},G=e=>{let a;let{databaseStore:t,databaseErrorStore:d,members:l,workspace:s}=(0,b.cF)(),{definition:o}=e,{updateViewDefinition:c,updateRecordValues:m,updateDatabaseColumn:p,createRecords:f,cache:x}=(0,y.Bf)(),{filter:h,search:v}=(0,y.Jy)(),{selectedIds:g,setSelectedIds:D}=(0,y.eX)(),{openRecord:C}=(0,I.x)(),{context:N}=(0,X.sV)(),[O,F]=(0,i.useState)(!1),[A,T]=(0,i.useState)("");console.log("Newly created on render:",{recordIds:x.getCache(_.ac.NewlyCreatedColumnKey,[])});let W=t[o.databaseId],G=W.database.definition.columnsMap[o.groupByIds[0]],q=(0,L.cL)(),J=!o.lockContent&&!q,K=(0,V.nM)(),Q=!K&&!q&&!o.lockContent&&J,Y=!K&&!q&&!o.lockContent&&J;o.filter=o.filter||{conditions:[],match:u.Match.All},o.sorts=o.sorts||[],o.groupOrder=o.groupOrder||[],o.groupItemsProps=o.groupItemsProps||{},o.columnsOrder=o.columnsOrder||[],o.columnPropsMap=o.columnPropsMap||{};let ee=W.database.definition.titleColumnId||"";(()=>{if(!ee){for(let e of Object.values(W.database.definition.columnsMap))if(e.type===u.DatabaseFieldDataType.Text){ee=e.id;break}}ee||(ee=W.database.definition.columnIds[0]);let{columnsOrder:e,columnPropsMap:a}=o,t=Object.values(a).filter(e=>!e.isHidden).length,r=t;for(let d of Object.values(W.database.definition.columnsMap)){let l=d.id;e.includes(l)||e.push(l),a[l]||(a[l]={isHidden:!0}),l!==ee&&0===r&&t<5&&(a[l]={isHidden:!1},t++)}})();let ea=async a=>{await c(e.view.id,e.view.pageId,a)},et=(()=>{let e=[];e.push({columnId:u.MagicColumn.CreatedAt,order:u.Sort.Desc});let{rows:a}=(0,R.filterAndSortRecords)(W,l,t,o.filter,h,e,s.workspaceMember.userId);return a})(),{columns:er,hiddenColumns:ed}=(()=>{let e=[],a=[],t=Array.isArray(o.groupOrder)?o.groupOrder:[],r=o.groupItemsProps?o.groupItemsProps:{};for(let e of G.optionIds)t.includes(e)||t.push(e),r[e]||(r[e]={isHidden:!1,itemsOrder:[]});let d=!1;for(let l of t){let t;let s=G.optionsMap[l];if(l===$)t={id:$,title:"Ungrouped",value:$},r[$]=r[l]||{isHidden:!1,itemsOrder:[]},d=!0;else{if(!s)continue;t={id:l,title:s.title,color:s.color,value:l}}r[l].isHidden?a.push(t):e.push(t)}return d||(e.unshift({id:$,title:"Ungrouped",value:""}),r[$]={isHidden:!1,itemsOrder:[]}),o.groupOrder=[...e.map(e=>e.id),...a.map(e=>e.id)],{columns:e,hiddenColumns:a}})(),el=(a=et,v&&v.trim()&&(a=et.filter(e=>e.processedRecord.valuesText.toLowerCase().includes(v.trim().toLowerCase()))),((e,a)=>{let t={};for(let e of(o.groupItemsProps||(o.groupItemsProps={}),a))t[e]=[];let r=G.id,d=[],l={};for(let s of e){let e=s.record.recordValues[r];Array.isArray(e)||(e=[]);let i=!1;if(e&&e.length>0)for(let r of a)e.includes(r)&&(t[r].push(s.id),i=!0);i||d.push(s.id),l[s.id]={id:s.id,record:s.record,processedRecord:s.processedRecord,updatedAt:s.updatedAt}}let s={};for(let e of a){s[e]=[];let a=o.groupItemsProps[e]||{itemsOrder:[]};a.itemsOrder||(a.itemsOrder=[]);let r=new Set(t[e]);for(let t of a.itemsOrder)r.has(t)&&s[e].push(t);for(let r of t[e])a.itemsOrder.includes(r)||s[e].push(r);a.itemsOrder=s[e]}return s[$]=d,o.groupItemsProps[$]||(o.groupItemsProps[$]={isHidden:!1,itemsOrder:[]}),o.groupItemsProps[$].itemsOrder=d,{boardItems:s,rowIdsMap:l}})(a,o.groupOrder)),es=er.map(e=>e.id),[ei,eo]=(0,i.useState)(null),[en,ec]=(0,i.useState)(null),eu=(0,M.Dy)((0,M.VT)(M.we,{activationConstraint:{distance:10}}));function em(e,a){var t;if(!o.groupItemsProps)return;let r=null===(t=o.groupItemsProps)||void 0===t?void 0:t[e];r&&(r={...r,...a},o.groupItemsProps[e]=r,ea({groupItemsProps:o.groupItemsProps}).then())}let ep=e=>{"record_tab"===N?(T(e),F(!0)):ef(e)},ef=async e=>{let a={};e!==$&&(a[G.id]=[e]),await f(W.database.id,[a])};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"size-full flex flex-col overflow-hidden",children:[o.lockContent&&!q&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(Z.mb,{})}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden select-none",children:(0,r.jsxs)(M.LB,{autoScroll:{acceleration:.5,threshold:{x:25,y:10}},sensors:eu,onDragStart:function(e){var a,t;if((null===(a=e.active.data.current)||void 0===a?void 0:a.type)==="Column"){eo(e.active.data.current.column);return}if((null===(t=e.active.data.current)||void 0===t?void 0:t.type)==="Item"){ec(e.active.data.current.item);return}},onDragEnd:function(e){var a,t,r,d,l,s,i,c,u,p,f,x;eo(null),ec(null);let{active:h,over:b}=e;if(!b)return;let v=h.id,g=b.id;if(v!==g){if((null===(a=h.data.current)||void 0===a?void 0:a.type)==="Column"){console.log("DRAG END for a column");let e=er.findIndex(e=>e.id===v),a=er.findIndex(e=>e.id===g);ea({groupOrder:(0,n.Rp)(o.groupOrder,e,a)}).then()}else if((null===(t=h.data.current)||void 0===t?void 0:t.type)==="Item"){let e=(null===(r=h.data.current)||void 0===r?void 0:r.columnId)||"",a=(null===(d=b.data.current)||void 0===d?void 0:d.columnId)||(null===(s=b.data.current)||void 0===s?void 0:null===(l=s.column)||void 0===l?void 0:l.id)||"",t=(null===(c=h.data.current)||void 0===c?void 0:null===(i=c.item)||void 0===i?void 0:i.id)||"",n=(null===(p=b.data.current)||void 0===p?void 0:null===(u=p.item)||void 0===u?void 0:u.id)||"",v=null===(f=h.data.current)||void 0===f?void 0:f.item;if(!v||!a)return;if(e!==a){let t=v.record.recordValues[G.id],r=[...Array.isArray(t)?t:[]];r=(0,j.removeAllArrayItem)(r,e),a&&a!==$&&(r.push(a),r=(0,j.arrayDeDuplicate)(r));let d={};d[G.id]=r,m(W.database.id,[v.id],d)}let y=null===(x=o.groupItemsProps)||void 0===x?void 0:x[a];y&&(y.itemsOrder=y.itemsOrder||[],g?y.itemsOrder=(0,B.arrayAddElementAdjacent)(y.itemsOrder,n,t,"before"):y.itemsOrder.push(t),y.itemsOrder=(0,j.arrayDeDuplicate)(y.itemsOrder),ea({groupItemsProps:o.groupItemsProps}).then()),console.log({active:h,over:b,activeColumnId:e,overColumnId:a,activeItemId:t,overItemId:n})}}},onDragOver:function(e){},children:[(0,r.jsxs)("div",{className:"w-full h-full overflow-x-auto overflow-y-hidden gap-4 p-4 pt-2 pb-0 flex",children:[(0,r.jsx)(n.Fo,{items:es,children:er.map(e=>(0,r.jsx)(P,{column:e,columnProps:o.groupItemsProps[e.id],items:el.boardItems[e.id].map(e=>el.rowIdsMap[e]),updateColumn:em,canEditStructure:Q,canEditData:Y,databaseDefinition:W.database.definition,dataColumnPropsMap:o.columnPropsMap,dataColumnsOrder:o.columnsOrder,titleColId:ee,dndDisabled:!J,addRecord:ep,databaseId:W.database.id},e.id))}),Q&&(0,r.jsx)(S,{createColumn:function(e,a){let t={id:(0,H.generateUUID)(),title:e,color:a};G.optionIds.push(t.id),G.optionsMap[t.id]=t,p(W.database.id,G.id,G.type,{optionsMap:G.optionsMap,optionIds:G.optionIds})}}),Q&&(0,r.jsx)(k,{boardItems:el.boardItems,columns:ed,groupItemsProps:o.groupItemsProps,rowIdsMap:el.rowIdsMap,updateColumn:em,databaseDefinition:W.database.definition,dataColumnPropsMap:o.columnPropsMap,dataColumnsOrder:o.columnsOrder,disabled:!J,titleColId:ee})]}),(0,E.createPortal)((0,r.jsxs)(M.y9,{children:[ei&&(0,r.jsx)(P,{column:ei,columnProps:o.groupItemsProps[ei.id],items:el.boardItems[ei.id].map(e=>el.rowIdsMap[e]),updateColumn:em,databaseDefinition:W.database.definition,dataColumnPropsMap:o.columnPropsMap,dataColumnsOrder:o.columnsOrder,titleColId:ee,dndDisabled:!J,databaseId:W.database.id},ei.id),en&&(0,r.jsx)(w,{dataColumnPropsMap:o.columnPropsMap,dataColumnsOrder:o.columnsOrder,databaseDefinition:W.database.definition,titleColId:ee,columnId:"",databaseId:W.database.id,item:en},en.id)]}),document.body)]})}),O&&(0,r.jsx)(U.S,{open:O,onClose:()=>F(!1),databaseId:W.database.id,viewFilter:(()=>{if(A&&A!==$)return{conditions:[{columnId:G.id,op:z.CompareOperator.Equals,value:[A]}],match:u.Match.All}})(),contextualFilter:h,onRecordCreated:e=>{C(e,W.database.id),F(!1)}})]})})};var q=t(84440);let J=e=>{let{databaseStore:a,databaseErrorStore:t,members:d,workspace:l}=(0,b.cF)(),{definition:s}=e,i=a[s.databaseId],o=i.database.definition.columnsMap[s.groupByIds[0]];return(0,r.jsxs)(r.Fragment,{children:[(!i||!o||o.type!==u.DatabaseFieldDataType.Select)&&(0,r.jsx)(q.PageLoader,{size:"full",error:"Error loading board"}),i&&o&&(0,r.jsx)(W,{database:i,...e})]})}},74610:function(e,a,t){var r=t(2265);let d=r.forwardRef(function(e,a){let{title:t,titleId:d,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":d},l),t?r.createElement("title",{id:d},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"}))});a.Z=d},21726:function(e,a,t){var r=t(2265);let d=r.forwardRef(function(e,a){let{title:t,titleId:d,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":d},l),t?r.createElement("title",{id:d},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});a.Z=d}}]);