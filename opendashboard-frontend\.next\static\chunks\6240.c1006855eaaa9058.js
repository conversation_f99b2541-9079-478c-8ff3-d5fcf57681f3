!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="bdd5ec7c-c2b8-4851-91db-7f9ba87efe2b",e._sentryDebugIdIdentifier="sentry-dbid-bdd5ec7c-c2b8-4851-91db-7f9ba87efe2b")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6240],{17297:function(e,t,a){"use strict";a.d(t,{k:function(){return n}});var s=a(57437);a(2265);var l=a(23500),r=a(93448);let n=e=>{let{title:t,icon:a,value:n,error:i,iconPosition:d=l.Ly.Right,className:o}=e,c=(0,s.jsx)("div",{className:"p-2 size-8 flex items-center bg-neutral-100 rounded-full justify-center",children:a});return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:(0,r.cn)("rounded-none cursor-pointer p-4 bg-white flex-1 h-auto",o),children:(0,s.jsxs)("div",{className:"flex justify-between",children:[d===l.Ly.Left&&(0,s.jsx)(s.Fragment,{children:c}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-xs text-gray-light font-medium truncate",children:t}),(0,s.jsx)("h1",{className:"font-bold text-base",children:n}),i&&(0,s.jsx)("div",{className:"text-xs !text-[10px] font-medium text-red-500 truncate mt-1",children:i})]}),d!==l.Ly.Left&&(0,s.jsx)(s.Fragment,{children:c})]})})})}},65969:function(e,t,a){"use strict";a.d(t,{l:function(){return o}});var s=a(57437),l=a(75060),r=a(77165),n=a(12381),i=a(20029),d=a(2265);let o=e=>{let t=(0,d.useRef)(null),a=e.uploads||[],o=(0,d.useRef)(e.images);o.current=e.images;let c=t=>{var a;let s=o.current.filter((e,a)=>a!==t);null===(a=e.onChange)||void 0===a||a.call(e,s)};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:[!e.hideTitle&&(0,s.jsx)(l._,{className:"text-xs text-neutral-500",children:e.title||"Images"}),(a.length>0||o.current.length>0)&&(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4",children:[a.map((e,t)=>(0,s.jsx)("div",{className:"relative bg-neutral-100 group min-h-[75px]",children:(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-xs h-0.5 absolute bottom-0 left-0",children:(0,s.jsx)("div",{className:"bg-blue-600 h-0.5 rounded-xs dark:bg-blue-500",style:{width:"".concat(e.progress,"%")}})})},e.id)),o.current.map((t,a)=>(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("div",{className:"bg-neutral-100 p-[1px]",children:(0,s.jsx)("img",{className:"h-auto max-w-full rounded-xs",src:t,alt:""})}),(0,s.jsx)("button",{disabled:e.disabled,className:"absolute -top-2 -right-2 w-4 h-4 p-0.5 rounded-full text-brand-red bg-neutral-100 shadow-lg invisible group-hover:visible",onClick:()=>c(a),children:(0,s.jsx)(r.Z,{width:12,height:12})})]},"img-".concat(a)))]}),(0,s.jsx)("div",{className:"hidden",children:(0,s.jsx)("input",{type:"file",ref:t,disabled:e.disabled,multiple:!0,accept:"image/*",onChange:t=>{if(!e.uploadImage)return;let a=t.target.files;if(a&&0!==a.length)for(let t of a)e.uploadImage(t,t=>{var a;let s=t.data.data.upload,l=[...o.current,s.finalUrl];null===(a=e.onChange)||void 0===a||a.call(e,l)})}})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n.z,{variant:"outline",disabled:e.disabled,onClick:()=>{t&&t.current&&t.current.click()},className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,s.jsx)(i.oFk,{className:"size-3"}),"Add Images"]})})]})})}},15119:function(e,t,a){"use strict";a.d(t,{u:function(){return d}});var s=a(57437),l=a(42212),r=a(2265),n=a(41426),i=a(36675);let d=e=>{let{databasePageStore:t,databasePagesId:a}=(0,l.cF)(),{selectedId:d}=e,o=(0,r.useMemo)(()=>{let e=[],s=[...a];for(let a of(s.includes(d)||s.push(d),s)){let s=t[a];if(!s)continue;let{page:l}=s,r=l.icon&&l.icon.type===n.ObjectType.Emoji?l.icon.emoji:"\uD83D\uDCD5",i={color:void 0,data:void 0,id:a,title:"".concat(r," ").concat(s.page.name),value:a};e.push(i)}return e},[t,a,d]);return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(i.A,{onChange:t=>{e.onChange(t[0])},selectedIds:d?[d]:[],placeholder:"Choose a database",options:o,disabled:e.disabled,className:e.className})})}},26240:function(e,t,a){"use strict";a.r(t),a.d(t,{DashboardView:function(){return th}});var s,l,r=a(57437),n=a(75060),i=a(40279),d=a(2265),o=a(90641),c=a(20029),u=a(23500),x=a(78931),h=a(30916),m=a(4355),f=a(41602);(s=l||(l={})).Left="left",s.Right="right",s.Center="center";let p=e=>(0,r.jsx)("div",{className:"flex",children:(0,r.jsxs)("div",{className:"inline-flex rounded-none shadow-sm size-8",role:"group",children:[(0,r.jsx)("button",{type:"button",className:"p-2 text-sm font-medium text-gray-900  ".concat("left"===e.value?"bg-gray-100":"bg-white"," border border-gray-200 border-r-0 rounded-none hover:bg-gray-100"),onClick:()=>{var t;null===(t=e.onChange)||void 0===t||t.call(e,"left")},children:(0,r.jsx)(h.Z,{className:"size-4"})}),e.enableCenter&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("button",{type:"button",className:"p-2 text-sm font-medium text-gray-900  ".concat("center"===e.value?"bg-gray-100":"bg-white"," border border-gray-200 border-r-0 hover:bg-gray-100"),onClick:()=>{var t;null===(t=e.onChange)||void 0===t||t.call(e,"center")},children:(0,r.jsx)(m.Z,{className:"size-4"})})}),(0,r.jsx)("button",{type:"button",className:"p-2 text-sm font-medium text-gray-900 ".concat("right"===e.value?"bg-gray-100":"bg-white"," border border-gray-200 rounded-none hover:bg-gray-100"),onClick:()=>{var t;null===(t=e.onChange)||void 0===t||t.call(e,"right")},children:(0,r.jsx)(f.Z,{className:"size-4"})})]})});var g=a(41556),b=a(99472),j=a(30166);let v=e=>{let t=(0,d.useRef)(null),a=(0,d.useRef)(!1);return(0,d.useEffect)(()=>{let s=t.current;if(!s||a.current)return;let{top:l,left:r,bottom:n,right:i,height:d}=s.getBoundingClientRect(),o=window.innerHeight,c=window.innerWidth;console.log("Adjust:",{ele:s.getBoundingClientRect(),viewportHeight:o,viewportWidth:c,offset:e.offset});let u="number"==typeof e.offset?{top:e.offset,left:e.offset,right:e.offset,bottom:e.offset}:"object"==typeof e.offset?e.offset:{top:0,left:0,right:0,bottom:0};u.top=u.top||0,u.bottom=u.bottom||0,u.right=u.right||0,u.bottom=u.bottom||0;let x=(u.top||0)-Math.min(l,u.top||l-n+(u.bottom||0)),h=(u.left||0)-Math.min(r,u.left||r-i+(u.right||0));n>o-u.bottom&&(x=-(n-(o-u.bottom))),i>c-u.right&&(h=-(i-(c-u.right))),s.style.transform="translate(".concat(h,"px, ").concat(x,"px)"),a.current=!0},[e.offset]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{ref:t,className:"your-absolutely-positioned-element",children:e.children})})};var N=a(41426);a(21976);let y=Object.keys(b),I=(0,j.default)(()=>Promise.all([a.e(4560),a.e(7256)]).then(a.bind(a,41799)),{loadableGenerated:{webpack:()=>[41799]},ssr:!1}),w=e=>{let{close:t,onPick:a,requestRemove:s,xAlign:l,yAlign:n,enableRemove:o,enableEmojis:c}=e,[u,x]=(0,d.useState)(c?"emoji":"icon"),h=(0,d.useRef)(null),[m,f]=(0,d.useState)("");return(0,d.useEffect)(()=>{let e=e=>{h.current&&!h.current.contains(e.target)&&t()};return document.addEventListener("click",e),()=>{document.removeEventListener("click",e)}}),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"relative ",children:(0,r.jsx)("div",{className:"fixed z-20",children:(0,r.jsx)("div",{className:"w-[420px] absolute p-1 ".concat("b"===n?"bottom-0":"top-0","\n            ").concat("r"===l?"right-0":"left-0"),ref:h,children:(0,r.jsx)(v,{offset:20,children:(0,r.jsxs)("div",{className:"relative bg-white overflow-hidden rounded-none shadow-lg ring-1 ring-black ring-opacity-5 pt-1 z-20",children:[(0,r.jsxs)("div",{className:"text-neutral-800 text-xs font-semibold px-1 border-b flex",children:[c&&(0,r.jsx)("button",{className:"mr-2 p-2 border-b-2 ".concat("emoji"===u?"border-brand-blue":"text-gray-500  border-transparent"),onClick:()=>x("emoji"),children:"Emoji"}),(0,r.jsx)("button",{className:"mr-2 p-2 border-b-2  ".concat("icon"===u?"border-brand-blue":"text-gray-500  border-transparent"),onClick:()=>x("icon"),children:"Icon"}),(0,r.jsx)("span",{className:"inline-block grow"}),o&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("button",{className:"mb-2 p-2 border-transparent  text-xs hover:bg-[#ddd] rounded",onClick:()=>{null==s||s(),t()},children:"Remove"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"".concat(c&&"emoji"===u?"":"hidden"," h-[302px]"),children:(0,r.jsx)("div",{className:"emoji-picker",children:(0,r.jsx)(I,{onEmojiClick:e=>{a({type:N.ObjectType.Emoji,emoji:e.emoji}),t()},width:"100%",height:302,previewConfig:{showPreview:!1},searchDisabled:!0})})})}),"icon"===u&&(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"".concat("icon"===u?"":"hidden"),children:[(0,r.jsxs)("div",{className:"relative p-2 border-b",children:[(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-full pointer-events-none z-5 p-4",children:(0,r.jsx)(g.Z,{className:"w-4 h-4"})}),(0,r.jsx)(i.I,{type:"search",id:"sidebar-search",onChange:e=>{f(e.target.value)},value:m,className:"text-xs rounded-none p-3 pl-8 h-8",placeholder:"Search",required:!0})]}),(0,r.jsx)("div",{className:"h-[250px] overflow-y-auto icon-picker",children:(0,r.jsx)("div",{className:"icons",children:y.filter(e=>e.toLowerCase().includes(m.toLowerCase())).map((e,s)=>(0,r.jsx)("button",{className:"icon hover:bg-[#ddd] rounded",onClick:()=>{a({type:N.ObjectType.Icon,icon:e}),t()},children:(0,r.jsx)(C,{icon:e})},e))})})]})})]})]})})})})})})},C=e=>{let{icon:t,...a}=e,s=b[t];return s&&(0,r.jsx)(s,{width:24,height:24,...a})},k=e=>{let{icon:t,...a}=e;return t.type===N.ObjectType.Icon?(0,r.jsx)(C,{icon:t.icon,...a}):t.type===N.ObjectType.Emoji?(0,r.jsx)("div",{...a,children:t.emoji}):null};var R=a(12381),D=a(15119),M=a(87957),E=a(26644),A=a(68738),z=a(36675),F=a(73299);let T=e=>{let t=[...F.bc,...F._k];e.fieldType&&(e.fieldType===A.DatabaseFieldDataType.Checkbox?t=[...F.xB,...F.fj]:e.fieldType===A.DatabaseFieldDataType.Number&&t.unshift(...F._f));let a=t.map(e=>({data:void 0,value:e.value,id:e.value,title:e.label}));return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(z.A,{onChange:t=>e.onChange(t[0]),selectedIds:e.selectedId?[e.selectedId]:[],placeholder:e.placeholder||"Summarize by",options:a})})};var B=a(42212),L=a(18626),_=a(36109),S=a(13465),P=a(95473),O=a(84440),U=a(17939);a(17297);var q=a(29119);let V=e=>{let{databaseStore:t,members:a,workspace:s,databaseErrorStore:l}=(0,B.cF)(),{refreshDatabase:n}=(0,P.Bf)(),i=e.dashboardDefinition.elementMap[e.id];i.valueResolve=i.valueResolve||{databaseId:"",columnId:"",aggregateBy:M.CountAggregateFunction.CountAll,filter:{match:A.Match.All,conditions:[]}},i.valueResolve.filter=i.valueResolve.filter||{match:A.Match.All,conditions:[]};let{filter:o,aggregateBy:h,columnId:m}=i.valueResolve,f=t[i.valueResolve.databaseId],p=l[i.valueResolve.databaseId],g=!1,b=!1,j="";i.valueResolve.databaseId?f||p?!f&&p?(g=!!p.loading,j=p.error||""):f&&!f.recordsLoaded&&(b=!0,g=!0):(b=!0,g=!0):j="Database not defined";let v=(()=>{if(!f)return"-";let{rows:e}=(0,L.filterAndSortRecords)(f,a,t,o,{match:A.Match.All,conditions:[]},[],s.workspaceMember.userId),l=e.map(e=>e.record.recordValues[m]),r=parseInt(String((0,_.resolveColumnValuesAggregation)(l,h)));return isNaN(r)?"-":S.Mi.includes(h)?"".concat(r,"%"):r})(),N=(0,r.jsx)("div",{className:"p-2 size-8 flex items-center bg-neutral-100 rounded-full",children:i.icon?(0,r.jsx)(k,{icon:i.icon,className:"size-full"}):(0,r.jsx)(x.Z,{className:"size-full"})});return(0,d.useEffect)(()=>{b&&n(i.valueResolve.databaseId).then()},[i.valueResolve.databaseId]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"rounded-none cursor-pointer p-4 bg-white flex-1 h-auto",children:(0,r.jsxs)("div",{className:"flex justify-between",children:[i.iconPosition===u.Ly.Left&&(0,r.jsx)(r.Fragment,{children:N}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xs text-gray-light font-medium truncate",children:i.title||"Infobox"}),(0,r.jsx)("h1",{className:"font-bold text-base",children:g?(0,r.jsx)(O.a,{className:"size-4 mt-2"}):j?(0,r.jsx)(c.P7M,{className:"size-3 mt-2",title:j}):v}),j&&(0,r.jsx)("div",{className:"text-xs !text-[10px] font-medium text-red-500 truncate mt-1",children:j})]}),i.iconPosition!==u.Ly.Left&&(0,r.jsx)(r.Fragment,{children:N})]})})})},Z=e=>{let t,a;let{databaseStore:s}=(0,B.cF)(),{updateElement:i}=e,o=e.element,h=o.valueResolve=o.valueResolve||{databaseId:"",columnId:"",aggregateBy:M.CountAggregateFunction.CountAll,filter:{match:A.Match.All,conditions:[]}},[m,f]=(0,d.useState)(!1);h.databaseId&&(s[h.databaseId]&&(t=s[h.databaseId].database),s[h.databaseId]&&s[h.databaseId].database.definition.columnsMap[h.columnId]&&(a=s[h.databaseId].database.definition.columnsMap[h.columnId].type));let{page:g}=(0,q.qt)();return(0,d.useEffect)(()=>{if(!h.databaseId&&g.databaseId){let{databaseId:e}=g;i({valueResolve:{...h,databaseId:e}})}},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Box Icon"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"h-8 mr-2",children:(0,r.jsx)(R.z,{variant:"outline",className:"rounded-none p-2 size-8 item-center",onClick:()=>f(!m),children:o.icon?(0,r.jsx)(k,{icon:o.icon,className:"size-full"}):(0,r.jsx)(x.Z,{className:"size-full"})})}),(0,r.jsx)("div",{className:"relative top-10",children:m&&(0,r.jsx)(w,{onPick:function(e){i({icon:e})},requestRemove:function(){i({icon:void 0})},close:function(){f(!1)},xAlign:"r",yAlign:"t"})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Icon Align"}),(0,r.jsx)(p,{value:o.iconPosition===u.Ly.Left?l.Left:l.Right,onChange:e=>{i({iconPosition:e===l.Left?u.Ly.Left:u.Ly.Right})}})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Choose database"}),(0,r.jsx)(D.u,{disabled:!!g.databaseId,onChange:e=>i({valueResolve:{...h,databaseId:e,columnId:"",aggregateBy:M.CountAggregateFunction.CountAll,filter:{match:A.Match.All,conditions:[]}}}),selectedId:h.databaseId})]}),h.databaseId&&t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Filter"}),(0,r.jsx)(U.o,{database:t,trigger:(0,r.jsxs)(R.z,{variant:"outline",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,r.jsx)(c.hQu,{className:"size-3"}),h.filter.conditions.length>0?"".concat(h.filter.conditions.length," filters"):"Filter records"]}),filter:h.filter,onChange:e=>{i({valueResolve:{...h,filter:e}})}})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Choose column"}),(0,r.jsx)(E.Y,{onChange:e=>i({valueResolve:{...h,columnId:e[0]}}),selected:h.columnId?[h.columnId]:[],databaseId:h.databaseId})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Summarize by"}),(0,r.jsx)(T,{selectedId:h.aggregateBy,fieldType:a,onChange:e=>i({valueResolve:{...h,aggregateBy:e}})})]})]})]})};var K=a(14084),W=a(76705);let H=e=>(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"rounded-none cursor-pointer bg-white flex-1",children:[(0,r.jsx)("div",{className:"p-4 py-2.5 border-b text-xs font-bold",children:e.title||"Untitled"}),(0,r.jsx)("div",{className:"p-4",children:e.children})]})});var Q=a(99972),J=a(24681),$=a(94589),G=a(5867),X=a(69700),Y=a(63127),ee=a(47625),et=a(8147),ea=a(22190),es=a(93448);let el={light:"",dark:".dark"},er=d.createContext(null);function en(){let e=d.useContext(er);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let ei=d.forwardRef((e,t)=>{let{id:a,className:s,children:l,config:n,...i}=e,o=d.useId(),c="chart-".concat(a||o.replace(/:/g,""));return(0,r.jsx)(er.Provider,{value:{config:n},children:(0,r.jsxs)("div",{"data-chart":c,ref:t,className:(0,es.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",s),...i,children:[(0,r.jsx)(ed,{id:c,config:n}),(0,r.jsx)(ee.h,{children:l})]})})});ei.displayName="Chart";let ed=e=>{let{id:t,config:a}=e,s=Object.entries(a).filter(e=>{let[t,a]=e;return a.theme||a.color});return s.length?(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(el).map(e=>{let[a,l]=e;return"\n".concat(l," [data-chart=").concat(t,"] {\n").concat(s.map(e=>{var t;let[s,l]=e,r=(null===(t=l.theme)||void 0===t?void 0:t[a])||l.color;return r?"  --color-".concat(s,": ").concat(r,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},eo=et.u,ec=d.forwardRef((e,t)=>{let{active:a,payload:s,className:l,indicator:n="dot",hideLabel:i=!1,hideIndicator:o=!1,label:c,labelFormatter:u,labelClassName:x,formatter:h,color:m,nameKey:f,labelKey:p}=e,{config:g}=en(),b=d.useMemo(()=>{var e;if(i||!(null==s?void 0:s.length))return null;let[t]=s,a="".concat(p||t.dataKey||t.name||"value"),l=eh(g,t,a),n=p||"string"!=typeof c?null==l?void 0:l.label:(null===(e=g[c])||void 0===e?void 0:e.label)||c;return u?(0,r.jsx)("div",{className:(0,es.cn)("font-medium",x),children:u(n,s)}):n?(0,r.jsx)("div",{className:(0,es.cn)("font-medium",x),children:n}):null},[c,u,s,i,x,g,p]);if(!a||!(null==s?void 0:s.length))return null;let j=1===s.length&&"dot"!==n;return(0,r.jsxs)("div",{ref:t,className:(0,es.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",l),children:[j?null:b,(0,r.jsx)("div",{className:"grid gap-1.5",children:s.map((e,t)=>{let a="".concat(f||e.name||e.dataKey||"value"),s=eh(g,e,a),l=m||e.payload.fill||e.color;return(0,r.jsx)("div",{className:(0,es.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===n&&"items-center"),children:h&&(null==e?void 0:e.value)!==void 0&&e.name?h(e.value,e.name,e,t,e.payload):(0,r.jsxs)(r.Fragment,{children:[(null==s?void 0:s.icon)?(0,r.jsx)(s.icon,{}):!o&&(0,r.jsx)("div",{className:(0,es.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":j&&"dashed"===n}),style:{"--color-bg":l,"--color-border":l}}),(0,r.jsxs)("div",{className:(0,es.cn)("flex flex-1 justify-between leading-none",j?"items-end":"items-center"),children:[(0,r.jsxs)("div",{className:"grid gap-1.5",children:[j?b:null,(0,r.jsx)("span",{className:"text-muted-foreground",children:(null==s?void 0:s.label)||e.name})]}),e.value&&(0,r.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});ec.displayName="ChartTooltip";let eu=ea.D,ex=d.forwardRef((e,t)=>{let{className:a,hideIcon:s=!1,payload:l,verticalAlign:n="bottom",nameKey:i}=e,{config:d}=en();return(null==l?void 0:l.length)?(0,r.jsx)("div",{ref:t,className:(0,es.cn)("flex items-center justify-center gap-4","top"===n?"pb-3":"pt-3",a),children:l.map(e=>{let t="".concat(i||e.dataKey||"value"),a=eh(d,e,t);return(0,r.jsxs)("div",{className:(0,es.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[(null==a?void 0:a.icon)&&!s?(0,r.jsx)(a.icon,{}):(0,r.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),null==a?void 0:a.label]},e.value)})}):null});function eh(e,t,a){if("object"!=typeof t||null===t)return;let s="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,l=a;return a in t&&"string"==typeof t[a]?l=t[a]:s&&a in s&&"string"==typeof s[a]&&(l=s[a]),l in e?e[l]:e[a]}ex.displayName="ChartLegend";var em=a(45683),ef=a(56940),ep=a(97059),eg=a(54061);K.kL.register(K.uw,K.f$,K.od,K.jn,K.Dx,K.u,K.De);let eb=e=>{let{databaseStore:t,members:a,workspace:s,databaseErrorStore:l}=(0,B.cF)(),{refreshDatabase:n}=(0,P.Bf)(),[i,o]=(0,d.useState)(1),[u,x]=(0,d.useState)(20),h=e.dashboardDefinition.elementMap[e.id],m={databaseId:"",filter:{match:A.Match.All,conditions:[]},sorts:[]},f=h.recordsResolve=h.recordsResolve||m,p=h.columnPropsMap||{};f.sorts=f.sorts||[];let g=t[h.recordsResolve.databaseId],b=l[h.recordsResolve.databaseId],j=!1,v=!1,N="";h.recordsResolve.databaseId?g||b?!g&&b?(j=!!b.loading,N=b.error||""):g&&!g.recordsLoaded&&(v=!0,j=!0):(v=!0,j=!0):N="Database not defined";let y=0,I=()=>{if(!g)return[];let e=[...f.sorts];0===e.length&&e.push({columnId:A.MagicColumn.CreatedAt,order:A.Sort.Asc});let{rows:l}=(0,L.filterAndSortRecords)(g,a,t,f.filter,{match:A.Match.All,conditions:[]},e,s.workspaceMember.userId);return y=Math.ceil(l.length/u),l};(0,d.useEffect)(()=>{v&&n(h.recordsResolve.databaseId).then()},[h.recordsResolve.databaseId]);let w=(()=>{let e={datasets:[],labels:[]};if(g){let a=(0,Y.$P)(g.database).titleColId,s=g.database.definition,l=s.columnIds.filter(e=>!!p[e]&&!!s.columnsMap[e]&&!p[e].isHidden);for(let a=0;a<l.length;a++){var t;let r=l[a],n=G.Pp[a%G.Pp.length],i=n.info.fg,d=n.info.bg;e.datasets.push({backgroundColor:d,borderColor:i,data:[],label:(null===(t=s.columnsMap[r])||void 0===t?void 0:t.title)||"Unknown column"})}let r=I(),n=(i-1)*u;for(let t of r.slice(n,n+u)){let{record:s,processedRecord:r}=t,n=(0,_.recordValueToText)(r.processedRecordValues[a]);e.labels.push(n&&String(n).trim()?String(n).trim():"Untitled");for(let t=0;t<l.length;t++){let a=l[t],s=Number((0,_.recordValueToText)(r.processedRecordValues[a]));isNaN(s)&&(s=0),e.datasets[t].data.push(s)}}}return{options:{responsive:!0,plugins:{legend:{position:"top"}}},data:e}})(),C=(()=>{let e=[],t={};if(g){let s=(0,Y.$P)(g.database).titleColId,l=g.database.definition,r=l.columnIds.filter(e=>!!p[e]&&!!l.columnsMap[e]&&!p[e].isHidden),n=(0,G.UR)(r.length);for(let e=0;e<r.length;e++){var a;let s=r[e];t[s]={label:(null===(a=l.columnsMap[s])||void 0===a?void 0:a.title)||"Unknown column",color:n[e]}}let d=I(),o=(i-1)*u;for(let t of d.slice(o,o+u)){let{record:a,processedRecord:l}=t,n=(0,_.recordValueToText)(l.processedRecordValues[s]),i={label:n&&String(n).trim()?String(n).trim():"Untitled"};for(let e=0;e<r.length;e++){let t=r[e],a=Number((0,_.recordValueToText)(l.processedRecordValues[t]));isNaN(a)&&(a=0),i[t]=a}e.push(i)}}return{config:t,data:e}})();return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(H,{title:h.title||"Line Chart",children:j||N?(0,r.jsx)("div",{className:"h-36",children:(0,r.jsx)(O.PageLoader,{size:"full",error:N})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"hidden",children:(0,r.jsx)(W.x1,{options:w.options,data:w.data,className:"w-full"})}),(0,r.jsx)(ei,{config:C.config,children:(0,r.jsxs)(em.w,{accessibilityLayer:!0,data:C.data,margin:{left:12,right:12},children:[(0,r.jsx)(ef.q,{vertical:!1}),(0,r.jsx)(ep.K,{dataKey:"label",tickLine:!1,axisLine:!1,tickMargin:8}),(0,r.jsx)(eo,{cursor:!1,content:(0,r.jsx)(ec,{indicator:"line"})}),(0,r.jsx)(eu,{content:(0,r.jsx)(ex,{})}),Object.keys(C.config).map(e=>(0,r.jsx)(eg.x,{dataKey:e,type:"linear",stroke:C.config[e].color,strokeWidth:2,dot:{fill:C.config[e].color},activeDot:{r:6}},e))]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-4","aria-label":"Table navigation",children:[(0,r.jsxs)("div",{className:"text-xs font-medium text-muted-foreground flex gap-1 items-center",children:[(0,r.jsx)("div",{className:"whitespace-nowrap",children:"Records per page"}),(0,r.jsxs)(X.Ph,{onValueChange:e=>{let t=parseInt(e);t!==u&&(x(t),o(1))},children:[(0,r.jsx)(X.i4,{className:"h-7 rounded-none text-xs",children:(0,r.jsx)(X.ki,{className:"!text-xs",placeholder:u})}),(0,r.jsxs)(X.Bw,{className:"rounded-none text-xs",children:[(0,r.jsx)(X.Ql,{value:"10",className:"rounded-none text-xs",children:"10"}),(0,r.jsx)(X.Ql,{value:"20",className:"rounded-none text-xs",children:"20"}),(0,r.jsx)(X.Ql,{value:"50",className:"rounded-none text-xs",children:"50"}),(0,r.jsx)(X.Ql,{value:"100",className:"rounded-none text-xs",children:"100"})]})]})]}),(0,r.jsxs)("div",{className:"inline-flex -space-x-px gap-2 text-sm h-8 items-center",children:[(0,r.jsxs)("div",{className:"text-xs font-medium text-muted-foreground",children:["Page ",i," of ",y]}),(0,r.jsxs)("ul",{className:"inline-flex",children:[(0,r.jsx)("li",{children:(0,r.jsx)(R.z,{variant:"outline",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",disabled:i<=1,onClick:()=>{i>1&&o(i-1)},children:(0,r.jsx)(c.wyc,{className:"size-3"})})}),(0,r.jsx)("li",{children:(0,r.jsx)(R.z,{variant:"outline",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",disabled:i>=y,onClick:()=>{i<y&&o(i+1)},children:(0,r.jsx)(c.XCv,{className:"size-3"})})})]})]})]})]})})})},ej=(e,t,a)=>{if(t=t||[],a=a||{},e){for(let s of e.definition.columnIds)t.includes(s)||t.push(s),a[s]||(a[s]={});t=(0,M.arrayDeDuplicate)(t)}return{columnsOrder:t,columnPropsMap:a}},ev=e=>{let t;let{databaseStore:a}=(0,B.cF)(),{updateElement:s}=e,l=e.element,i={databaseId:"",filter:{match:A.Match.All,conditions:[]},sorts:[]},o=l.recordsResolve=l.recordsResolve||i;if(o.sorts=o.sorts||[],o.databaseId&&a[o.databaseId]&&(t=a[o.databaseId].database),t){let{columnsOrder:e,columnPropsMap:a}=ej(t,l.columnsOrder,l.columnPropsMap);l.columnsOrder=e,l.columnPropsMap=a}let{page:u}=(0,q.qt)();return(0,d.useEffect)(()=>{if(!o.databaseId&&u.databaseId){let{databaseId:e}=u;s({recordsResolve:{...o,databaseId:e}})}},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Choose database"}),(0,r.jsx)(D.u,{disabled:!!u.databaseId,onChange:e=>s({recordsResolve:{...o,databaseId:e}}),selectedId:o.databaseId})]}),o.databaseId&&t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Choose Fields"}),(0,r.jsx)("div",{children:Object.values(t.definition.columnsMap).map((e,t)=>{let a=l.columnPropsMap[e.id].isHidden;return(0,r.jsxs)("div",{role:"button",className:"flex select-none font-medium cursor-pointer text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden relative items-center hover:bg-transparent",children:[(0,r.jsx)(J.e,{type:e.type,className:"size-3"}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden truncate text-left",children:e.title}),(0,r.jsx)($.r,{className:"h-4 w-8",thumbClassName:"!size-3",checked:!a,onCheckedChange:t=>{let a={...l.columnPropsMap};a[e.id].isHidden=!t,s({columnPropsMap:a})}})]},e.id)})})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Filter"}),(0,r.jsx)(U.o,{database:t,trigger:(0,r.jsxs)(R.z,{variant:"outline",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,r.jsx)(c.hQu,{className:"size-3"}),o.filter.conditions.length>0?"".concat(o.filter.conditions.length," filters"):"Filter records"]}),filter:o.filter,onChange:e=>{s({recordsResolve:{...o,filter:e}})}})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Sort"}),(0,r.jsx)(Q.H,{database:t,sorts:o.sorts,onChange:e=>s({recordsResolve:{...o,sorts:e}}),trigger:(0,r.jsxs)(R.z,{variant:"outline",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,r.jsx)(c.E92,{className:"size-3"}),o.sorts.length>0?"".concat(o.sorts.length," sorts"):"Sort"]})})]})]})]})};var eN=a(77715),ey=a(68989),eI=a.n(ey),ew=a(75169),eC=a(3574),ek=a(58772),eR=a(26680);K.kL.register(K.qi,K.u,K.De);let eD=e=>{let{databaseStore:t,members:a,workspace:s,databaseErrorStore:l}=(0,B.cF)(),{refreshDatabase:n}=(0,P.Bf)(),i=e.dashboardDefinition.elementMap[e.id],o=i.recordsResolve=i.recordsResolve||{databaseId:"",filter:{match:A.Match.All,conditions:[]},groupByIds:[],titleColId:""};o.filter=o.filter||{match:A.Match.All,conditions:[]},o.groupByIds=o.groupByIds||[];let c=t[i.recordsResolve.databaseId],u=l[i.recordsResolve.databaseId],x=!1,h=!1,m="";i.recordsResolve.databaseId?i.recordsResolve.groupByIds&&Array.isArray(i.recordsResolve.groupByIds)&&!(i.recordsResolve.groupByIds.length<1)?c||u?!c&&u?(x=!!u.loading,m=u.error||""):c&&!c.recordsLoaded&&(h=!0,x=!0):(h=!0,x=!0):m="Group columns not configured":m="Database not defined";let f=()=>{if(!c||!o.groupByIds)return{rowMap:{},groupRowMap:{}};let e=[];e.push({columnId:A.MagicColumn.CreatedAt,order:A.Sort.Asc});let{rows:l}=(0,L.filterAndSortRecords)(c,a,t,o.filter,{match:A.Match.All,conditions:[]},e,s.workspaceMember.userId),r={},n={},i=o.groupByIds;for(let e of l){let t={},a={},s=[];for(let l of i){var d;t[l]="string"==typeof e.record.recordValues[l]?e.record.recordValues[l].trim():e.record.recordValues[l],a[l]="string"==typeof e.processedRecord.processedRecordValues[l]?e.processedRecord.processedRecordValues[l].trim():e.processedRecord.processedRecordValues[l];let r=(null===(d=c.database.definition.columnsMap[l])||void 0===d?void 0:d.title)||"Unknown column",n=(0,_.recordValueToText)(e.processedRecord.processedRecordValues[l]);s.push("".concat(r,": ").concat(n))}let l=JSON.stringify(t),o=eI().createHash("sha1").update(l).digest("hex");if(r[o]){let t=Math.max(new Date(r[o].updatedAt).getTime(),new Date(e.updatedAt).getTime());r[o].updatedAt=new Date(t).toISOString(),r[o].recordIds.push(e.id)}else r[o]={recordIds:[e.id],updatedAt:e.updatedAt,id:o,rowMap:{},valuesText:s.join(",")};r[o].rowMap[e.id]=e,n[e.id]=e}return{groupRowMap:r,rowMap:n}},p=(()=>{let e=f().groupRowMap,t={labels:[],datasets:[{label:"# of Records",data:[],backgroundColor:[],borderColor:[],borderWidth:1}]};for(let l=0;l<Object.values(e).length;l++){var a,s;let r=Object.values(e)[l];t.labels.push(r.valuesText||""),t.datasets[0].data.push(Object.keys(r.rowMap).length);let n=G.Pp[l%G.Pp.length],i=n.info.fg,d=n.info.bg;null===(a=t.datasets[0].backgroundColor)||void 0===a||a.push(d),null===(s=t.datasets[0].borderColor)||void 0===s||s.push(i)}return t})(),g=(()=>{let e=f().groupRowMap,t=[],a={records:{label:"Records"}},s=Object.values(e).length,l=(0,G.UR)(s,80,35),r=0;for(let n=0;n<s;n++){let s=Object.values(e)[n],i=s.valuesText||"";a[i]={label:i,color:l[n]};let d={label:i,count:Object.keys(s.rowMap).length,fill:l[n]};t.push(d),r+=Object.keys(s.rowMap).length}return{config:a,data:t,totalCount:r}})();return(0,d.useEffect)(()=>{h&&n(i.recordsResolve.databaseId).then()},[i.recordsResolve.databaseId]),console.log({pieData:g}),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(H,{title:i.title||"Pie Chart",children:x||m?(0,r.jsx)("div",{className:"h-36",children:(0,r.jsx)(O.PageLoader,{size:"full",error:m})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"hidden",children:(0,r.jsx)(W.by,{data:p,plugins:[eN.Z],className:"w-full"})}),(0,r.jsx)(ei,{config:g.config,className:"mx-auto aspect-square max-h-[300px]",children:(0,r.jsxs)(ew.u,{children:[(0,r.jsx)(eo,{cursor:!1,content:(0,r.jsx)(ec,{hideLabel:!0})}),(0,r.jsx)(eu,{content:(0,r.jsx)(ex,{})}),(0,r.jsxs)(eC.b,{data:g.data,dataKey:"count",nameKey:"label",innerRadius:60,strokeWidth:5,children:[(0,r.jsx)(ek.e,{dataKey:"label",className:"fill-background",stroke:"none",fontSize:12,formatter:e=>{var t;return null===(t=g.config[e])||void 0===t?void 0:t.label}}),(0,r.jsx)(eR._,{content:e=>{let{viewBox:t}=e;if(t&&"cx"in t&&"cy"in t)return(0,r.jsxs)("text",{x:t.cx,y:t.cy,textAnchor:"middle",dominantBaseline:"middle",children:[(0,r.jsx)("tspan",{x:t.cx,y:t.cy,className:"fill-foreground text-3xl font-bold",children:g.totalCount.toString()}),(0,r.jsx)("tspan",{x:t.cx,y:(t.cy||0)+24,className:"fill-muted-foreground",children:"Records"})]})}})]})]})})]})})})},eM=e=>{let t;let{databaseStore:a}=(0,B.cF)(),{updateElement:s}=e,l=e.element,i=l.recordsResolve=l.recordsResolve||{databaseId:"",filter:{match:A.Match.All,conditions:[]},groupByIds:[],titleColId:""};i.filter=i.filter||{match:A.Match.All,conditions:[]},i.groupByIds=i.groupByIds||[],i.titleColId=i.titleColId||"",i.databaseId&&a[i.databaseId]&&(t=a[i.databaseId].database);let{page:o}=(0,q.qt)();return(0,d.useEffect)(()=>{if(!i.databaseId&&o.databaseId){let{databaseId:e}=o;s({recordsResolve:{...i,databaseId:e}})}},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Choose database"}),(0,r.jsx)(D.u,{disabled:!!o.databaseId,onChange:e=>s({recordsResolve:{...i,databaseId:e}}),selectedId:i.databaseId})]}),i.databaseId&&t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Filter"}),(0,r.jsx)(U.o,{database:t,trigger:(0,r.jsxs)(R.z,{variant:"outline",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",children:[(0,r.jsx)(c.hQu,{className:"size-3"}),i.filter.conditions.length>0?"".concat(i.filter.conditions.length," filters"):"Filter records"]}),filter:i.filter,onChange:e=>{s({recordsResolve:{...i,filter:e}})}})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{className:"text-xs text-neutral-500",children:"Group by columns"}),(0,r.jsx)(E.Y,{onChange:e=>s({recordsResolve:{...i,groupByIds:e}}),selected:i.groupByIds,databaseId:i.databaseId,isMultiple:!0})]})]})]})};var eE=a(65969);let eA=e=>{let t=e.dashboardDefinition.elementMap[e.id],[a,s]=(0,d.useState)(0),l=t.images||[],n=t.autoRotate,i=(0,d.useCallback)(()=>{l.length<1||s((a+1)%l.length)},[s,a,l]),o=l[a];return(0,d.useEffect)(()=>{if(!n)return;let e=setInterval(i,5e3);return()=>{clearInterval(e)}},[n,i]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(H,{title:t.title,children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-full h-full relative",children:(0,r.jsx)("div",{className:"w-full h-full bg-center bg-cover bg-norepeat min-h-[200px]",style:{backgroundImage:"url(".concat(o,")")},children:(0,r.jsx)("img",{src:o,className:"w-full block",alt:""})})}),l&&l.length>0&&(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,r.jsx)("div",{className:"flex-1"}),(0,r.jsxs)("div",{className:"inline-flex text-sm h-8 gap-2 items-center text-black",children:[(0,r.jsxs)("div",{className:"text-xs font-medium",children:["Image ",a+1," of ",l.length]}),(0,r.jsxs)("ul",{className:"inline-flex",children:[(0,r.jsx)("li",{children:(0,r.jsx)(R.z,{variant:"outline",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",onClick:()=>{s(a-1<0?l.length-1:a-1)},children:(0,r.jsx)(c.wyc,{className:"size-3"})})}),(0,r.jsx)("li",{children:(0,r.jsx)(R.z,{variant:"outline",className:"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium",onClick:i,children:(0,r.jsx)(c.XCv,{className:"size-3"})})})]})]})]})]}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"h-28",children:(0,r.jsx)(O.PageLoader,{error:"No images to show"})})})})})},ez=e=>{let{uploadWorkspaceFile:t,uploadQueue:a}=(0,P.Bf)(),{updateElement:s}=e,l=e.element;l.images=l.images||[];let i=a.dashboard&&a.dashboard[l.id]&&a.dashboard[l.id].panel?Object.values(a.dashboard[l.id].panel):[];(0,d.useRef)(null);let o=(0,d.useRef)(l.images);return o.current=l.images,(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eE.l,{images:o.current,onChange:e=>s({images:e}),allowRemovingImages:!0,uploadImage:(e,a)=>{t("dashboard",l.id,"panel",e,a)},uploads:i}),(0,r.jsx)("div",{className:"flex flex-col gap-2",children:(0,r.jsxs)(n._,{className:"flex select-none font-medium text-xs h-auto gap-2 w-full",children:[(0,r.jsx)("div",{className:"flex-1 overflow-hidden truncate text-left",children:"Auto rotate"}),(0,r.jsx)($.r,{className:"h-4 w-8",thumbClassName:"!size-3",checked:l.autoRotate,onCheckedChange:e=>{s({autoRotate:e})}})]})})]})};var eF=a(23675),eT=a(65337);let eB=e=>{let t=e.dashboardDefinition.elementMap[e.id];return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(H,{title:t.title,children:(0,r.jsxs)("div",{className:"text-xs font-medium",children:[" ",t.content]})})})},eL=e=>{let{updateElement:t}=e,a=(0,eT.Z)(),s=e.element;return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{htmlFor:"title",className:"text-xs text-neutral-500",children:"Content"}),(0,r.jsx)(eF.g,{id:"title",className:"rounded-none text-xs",ref:a,defaultValue:s.content||"",rows:5,onBlur:e=>t({content:e.target.value.trim()||s.content||""})})]})})};var e_=a(18848);let eS={ALLOWED_TAGS:["p","iframe","div"],ALLOWED_ATTR:["src","width","height","title","frameBorder","allow","allowFullScreen"]},eP=e=>{let t=e.dashboardDefinition.elementMap[e.id],a=t.embedCode;return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(H,{title:t.title,children:(0,r.jsx)(eO,{embedCode:a})})})},eO=d.memo(e=>{let{embedCode:t}=e,a=(0,d.useRef)(null),s=e_.Z.sanitize(t,eS);return(0,d.useEffect)(()=>{if(a.current){let e=a.current.attachShadow({mode:"open"}),t=document.createElement("div");t.innerHTML=s,t.className="h-min-[350px] rounded-lg overflow-hidden",e.appendChild(t)}},[s]),(0,r.jsx)("div",{ref:a})});eO.displayName="EmbedViewer";let eU=e=>{let{updateElement:t}=e,a=(0,eT.Z)(),s=e.element;return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{htmlFor:"title",className:"text-xs text-neutral-500",children:"Content"}),(0,r.jsx)(eF.g,{id:"title",className:"rounded-none text-xs",ref:a,defaultValue:s.embedCode||"",rows:5,onBlur:e=>{let a=e_.Z.sanitize(e.target.value.trim(),eS);s.embedCode!==a&&t({embedCode:a})}})]})})},eq={Infobox:{name:"Infobox",icon:(0,r.jsx)(c.qd9,{className:"size-3.5"}),type:u.Bq.Infobox,panel:e=>(0,r.jsx)(Z,{...e})},LineChart:{name:"Line Chart",icon:(0,r.jsx)(c.pyZ,{className:"size-3.5"}),type:u.Bq.LineChart,panel:e=>(0,r.jsx)(ev,{...e})},PieChart:{name:"Pie Chart",icon:(0,r.jsx)(c.Npz,{className:"size-3.5"}),type:u.Bq.PieChart,panel:e=>(0,r.jsx)(eM,{...e})},Image:{name:"Images",icon:(0,r.jsx)(c.Ka2,{className:"size-3.5"}),type:u.Bq.Image,panel:e=>(0,r.jsx)(ez,{...e})},Text:{name:"Text",icon:(0,r.jsx)(c.os0,{className:"size-3.5"}),type:u.Bq.Text,panel:e=>(0,r.jsx)(eL,{...e})},Embed:{name:"Embed",icon:(0,r.jsx)(c.dNJ,{className:"size-3.5"}),type:u.Bq.Embed,panel:e=>(0,r.jsx)(eU,{...e})}};var eV=a(84977);let eZ=e=>{let t;let{confirm:a}=(0,eV.V)();if(!e.isEditing)return null;let s=e.dashboardDefinition.elementMap[e.activeElementId],l=t=>{let a={...s,...t};e.pushTransactions([{action:"updateElement",element:a}])};switch(null==s?void 0:s.type){case u.Bq.Embed:t=eq.Embed;break;case u.Bq.Infobox:t=eq.Infobox;break;case u.Bq.LineChart:t=eq.LineChart;break;case u.Bq.PieChart:t=eq.PieChart;break;case u.Bq.Image:t=eq.Image;break;case u.Bq.Text:t=eq.Text}let d=(0,r.jsx)(r.Fragment,{}),x=(0,r.jsxs)(r.Fragment,{children:[" ",null==t?void 0:t.name]});return t&&(d=t.panel({element:s,updateElement:l})),(0,r.jsx)("div",{className:"w-80 min-w-80 border-l",children:e.activeElementId&&s&&t?(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex flex-col h-full w-full",children:[(0,r.jsx)("header",{className:"flex gap-2 border-b",children:(0,r.jsx)("div",{className:"font-semibold p-3 flex gap-2 items-center text-xs",children:x})}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,r.jsx)(o.ScrollArea,{className:"w-full h-full",children:(0,r.jsxs)("div",{className:"h-full w-full overflow-y-auto flex flex-col gap-3 p-3 pb-12",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(n._,{htmlFor:"title",className:"text-xs text-neutral-500",children:"Title"}),(0,r.jsx)(i.I,{id:"title",className:"rounded-none text-xs",defaultValue:s.title||"",onBlur:e=>l({title:e.target.value.trim()||s.title})})]}),d,(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(R.z,{onClick:()=>{a("Delete element?","Are you sure you want to delete this element?",()=>{let t=[],a=null,l="";for(let[t,r]of Object.entries(e.dashboardDefinition.rowsMap))if(r.children&&r.children.includes(s.id)){a=r,l=t;break}if(!a){console.error("Could not find parent row for element:",s.id);return}t.push({action:"deleteElement",element:s,parentId:l}),0===(0,M.removeAllArrayItem)([...a.children],s.id).length&&t.push({action:"deleteRow",row:a}),e.pushTransactions(t),e.setActiveElementId("")})},variant:"outline",className:"text-xs rounded-none p-1.5 !px-3 h-auto w-auto gap-2 justify-start font-medium",children:[(0,r.jsx)(c.XHJ,{className:"size-3"}),"Delete"]})})]})})})]},e.activeElementId)}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"size-full relative",children:(0,r.jsx)("div",{className:"absolute -translate-y-1/2 top-1/2 w-full p-6 text-center",children:(0,r.jsxs)("div",{className:"my-2 flex flex-col gap-4 items-center justify-center",children:[(0,r.jsx)("div",{className:"",children:(0,r.jsx)(c.dP0,{className:"size-8"})}),(0,r.jsx)("span",{className:"text-xs font-medium",children:"Add an element or click an existing element to customize"})]})})})})})};var eK=a(10795);let eW="dashboardElement";var eH=a(91080),eQ=a(66312);let eJ=()=>{let e=(0,d.useRef)((0,eQ.generateUUID)());return(0,d.useEffect)(()=>{let t=document.getElementById("fc-".concat(e.current));if(!t)return;let a=t.getContext("2d");if(a)try{new eH.GV(a,{data:{labels:["Step 1","Step 2","Step 3"],datasets:[{data:[1,.75,.5],shrinkAnchor:"top"}]},plugins:[eN.Z]})}catch(e){}}),(0,r.jsx)(H,{title:"Funnel Chart",children:(0,r.jsx)("canvas",{id:"fc-".concat(e.current),className:"w-full"})})};var e$=a(50014);K.kL.register(K.uw,K.f$,K.ZL,K.Dx,K.u,K.De);let eG={plugins:{title:{display:!0,text:"Chart.js Bar Chart - Stacked"}},responsive:!0,scales:{x:{stacked:!0},y:{stacked:!0}}},eX=["January","February","March","April","May","June","July"],eY={labels:eX,datasets:[{label:"Dataset 1",data:eX.map(()=>e$.We.datatype.number({min:0,max:1e3})),backgroundColor:"rgb(255, 99, 132)"},{label:"Dataset 2",data:eX.map(()=>e$.We.datatype.number({min:0,max:1e3})),backgroundColor:"rgb(75, 192, 192)"},{label:"Dataset 3",data:eX.map(()=>e$.We.datatype.number({min:0,max:1e3})),backgroundColor:"rgb(53, 162, 235)"}]},e0=()=>(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(H,{title:"Bar Chart",children:(0,r.jsx)(W.$Q,{data:eY,options:eG,className:"w-full",plugins:[eN.Z]})})});var e1=a(59892),e2=a(89506),e5=a(61601),e3=a(26652),e4=a(32060);let e6=e=>(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(e9,{trigger:(0,r.jsxs)(R.z,{className:"gap-2 absolute bottom-4 left-4 w-auto z-20 rounded-none shadow-2xl",children:[(0,r.jsx)(e3.Z,{className:"size-4"}),"Add Element"]}),align:"start",pushTransactions:e.pushTransactions,setActiveElementId:e.setActiveElementId})}),e9=e=>{let t=(t,a)=>{let s=[],l={title:a,id:(0,eQ.generateUUID)(),type:t};if(e.refElementId){let t=e.refCreateDirection||"r",a=e.refParentId||"",r=e.refElementId;if("t"===t||"b"===t){let e={id:(0,eQ.generateUUID)(),children:[]},r={action:"addRow",row:e};"t"===t?r.beforeId=a:r.afterId=a,s.push(r),s.push({action:"addElement",element:l,parentId:e.id})}else{let e={action:"addElement",element:l,parentId:a};"l"===t?e.beforeId=r:e.afterId=r,s.push(e)}}else{let e={id:(0,eQ.generateUUID)(),children:[]};l.parentId=e.id,s.push({action:"addRow",row:e}),s.push({action:"addElement",element:l,parentId:e.id})}e.pushTransactions(s),setTimeout(()=>e.setActiveElementId(l.id),200)};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(e4.h_,{children:[(0,r.jsx)(e4.$F,{asChild:!0,children:e.trigger}),(0,r.jsx)(e4.AW,{className:"w-56  rounded-none text-neutral-800 font-semibold",align:e.align,children:(0,r.jsx)(e4.Qk,{className:"p-0.5 flex flex-col gap-0.5",children:Object.keys(eq).map(e=>{let a=eq[e];return(0,r.jsx)(e4.Xi,{className:"p-0",children:(0,r.jsxs)(R.z,{variant:"ghost",onClick:e=>{t(a.type,a.name)},className:"h-auto p-2 gap-2 text-xs rounded-none w-full justify-start border-none",children:[a.icon," ",a.name]})},e)})})})]})})};var e7=a(46795);let e8=e=>e.dashboardDefinition.elementMap[e.id]?(0,r.jsx)(te,{...e}):null,te=e=>{let t=e.dashboardDefinition.elementMap[e.id],a={type:eW,subType:null==t?void 0:t.type,id:e.id,parentId:e.parentId},{attributes:s,listeners:l,setNodeRef:n,transform:i,isDragging:d}=(0,eK.O1)({id:e.id,disabled:!1,data:a}),{setNodeRef:o}=(0,eK.Zj)({id:e.id,disabled:!1,data:{accepts:[eW],...a}}),c={transform:e1.ux.Translate.toString(i)},x=e.activeElementId===t.id;return(0,r.jsxs)("div",{className:"flex-1 flex relative ".concat(e.isDragOverlay&&"opacity-60 shadow-2xl"," \n        ").concat(d&&"invisible opacity-0","\n         border hover:border-black group ").concat(x&&"border-black"," dashER"),"data-ele-id":e.id,ref:e=>{n(e),o(e)},style:c,onClick:a=>{a.stopPropagation(),a.preventDefault(),x||e.setActiveElementId(t.id)},children:[t.type===u.Bq.Infobox?(0,r.jsx)(V,{...e}):t.type===u.Bq.FunnelChart?(0,r.jsx)(eJ,{}):t.type===u.Bq.LineChart?(0,r.jsx)(eb,{...e}):t.type===u.Bq.BarChart?(0,r.jsx)(e0,{}):t.type===u.Bq.PieChart?(0,r.jsx)(eD,{...e}):t.type===u.Bq.Text?(0,r.jsx)(eB,{...e}):t.type===u.Bq.Image?(0,r.jsx)(eA,{...e}):t.type===u.Bq.Embed?(0,r.jsx)(eP,{...e}):null,x&&(0,r.jsx)(tt,{...e,listeners:l,attributes:s})]})},tt=e=>{let{confirm:t}=(0,eV.V)(),a=()=>{if(!({...e.dashboardDefinition}).rowsMap[e.parentId]){console.log("Row not found");return}let t={...e.dashboardDefinition.elementMap[e.id],id:(0,eQ.generateUUID)()},a=[];a.push({action:"addElement",element:t,parentId:e.parentId,afterId:e.id}),e.pushTransactions(a),e.setActiveElementId(t.id)},s=()=>{let a=e.dashboardDefinition.elementMap[e.id];t("Delete element?","Are you sure you want to delete this element?",()=>{let t=[];t.push({action:"deleteElement",element:a,parentId:e.parentId}),e.pushTransactions(t),0===(0,M.removeAllArrayItem)(e.dashboardDefinition.rowsMap[e.parentId].children,e.id).length&&t.push({action:"deleteRow",row:e.dashboardDefinition.rowsMap[e.parentId]}),e.pushTransactions(t),e.setActiveElementId("")})};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"pge-e-controls absolute left-[-45px] w-[40px] z-20 top-[50%] -translate-y-1/2",children:(0,r.jsxs)("div",{className:"inline-flex rounded-md shadow-xl flex-col text-center",role:"group",children:[(0,r.jsx)(R.z,{variant:"ghost",className:"px-1.5 py-3 h-auto text-sm bg-white border rounded-none hover:bg-gray-100",...e.listeners,...e.attributes,children:(0,r.jsx)(e7.V,{className:"mx-auto size-3.5"})}),(0,r.jsx)(R.z,{variant:"ghost",className:"px-1.5 py-3 h-auto text-sm bg-white border border-t-0 rounded-none hover:bg-gray-100",onClick:e=>{e.stopPropagation(),e.preventDefault(),a()},children:(0,r.jsx)(e2.Z,{className:"mx-auto size-3.5"})}),(0,r.jsx)(R.z,{variant:"ghost",type:"button",className:"px-1.5 py-3 h-auto text-sm bg-white border border-t-0 rounded-none hover:bg-gray-100",onClick:e=>{e.preventDefault(),e.stopPropagation(),s()},children:(0,r.jsx)(e5.Z,{className:"mx-auto size-3.5"})})]})}),(0,r.jsx)("div",{className:"absolute top-[-11px] left-0 right-0 h-[20px] text-center z-20 pg-knob-wrap",children:(0,r.jsx)(e9,{trigger:(0,r.jsx)(R.z,{variant:"ghost",className:"size-[21px] p-0 text-center rounded-full bg-neutral-100 shadow-md",children:(0,r.jsx)(e3.Z,{width:21,height:21})}),align:"center",pushTransactions:e.pushTransactions,refElementId:e.id,refParentId:e.parentId,setActiveElementId:e.setActiveElementId,refCreateDirection:"t"})}),(0,r.jsx)("div",{className:"absolute bottom-[-11px] left-0 right-0 h-[20px] text-center z-20 pg-knob-wrap pt-[0px]",children:(0,r.jsx)(e9,{trigger:(0,r.jsx)(R.z,{variant:"ghost",className:"size-[21px] p-0 text-center rounded-full bg-neutral-100 shadow-md",children:(0,r.jsx)(e3.Z,{width:21,height:21})}),align:"center",pushTransactions:e.pushTransactions,refElementId:e.id,refParentId:e.parentId,setActiveElementId:e.setActiveElementId,refCreateDirection:"b"})}),(0,r.jsx)("div",{className:"absolute left-[-11px] w-[70px] text-left z-20 pg-knob-wrap top-[50%] -translate-y-1/2  h-[21px]",children:(0,r.jsx)(e9,{trigger:(0,r.jsx)(R.z,{variant:"ghost",className:"size-[21px] p-0 text-center rounded-full bg-neutral-100 shadow-md",children:(0,r.jsx)(e3.Z,{width:21,height:21})}),align:"start",pushTransactions:e.pushTransactions,refElementId:e.id,refParentId:e.parentId,setActiveElementId:e.setActiveElementId,refCreateDirection:"l"})}),(0,r.jsx)("div",{className:"absolute right-[-11px] w-[70px] text-right z-20 pg-knob-wrap top-[50%] -translate-y-1/2  h-[21px]",children:(0,r.jsx)(e9,{trigger:(0,r.jsx)(R.z,{variant:"ghost",className:"size-[21px] p-0 text-center rounded-full bg-neutral-100 shadow-md",children:(0,r.jsx)(e3.Z,{width:21,height:21})}),align:"end",pushTransactions:e.pushTransactions,refElementId:e.id,refParentId:e.parentId,setActiveElementId:e.setActiveElementId,refCreateDirection:"r"})})]})};var ta=a(54887),ts=a(31096);let tl=e=>{let{dashboardDefinition:t}=e,a=t.children.length>0;return(0,r.jsx)("div",{className:"w-full h-full max-w-full overflow-hidden relative dashCW",onClick:t=>e.setActiveElementId(""),children:a?(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(tr,{...e,children:[(0,r.jsx)(o.ScrollArea,{className:"w-full h-full scrollBlockChild overflow-x-hidden",children:(0,r.jsx)("div",{className:"".concat(e.isEditing&&"pl-10"),children:(0,r.jsx)("div",{className:(0,es.cn)("p-4 flex flex-col gap-4 w-full max-w-full",e.isEditing?"pt-8 pb-24":""),children:e.dashboardDefinition.children.map(t=>{let a=e.dashboardDefinition.rowsMap[t];if(!a||0===a.children.length)return null;let s="".concat(t,":").concat(a.updatedTs);return(0,r.jsx)("div",{className:"flex gap-4",children:a.children.map(t=>(0,d.createElement)(e8,{...e,parentId:a.id,id:t,key:t}))},s)})})})}),e.isEditing&&(0,r.jsx)(e6,{...e}),(0,ta.createPortal)((0,r.jsx)(eK.y9,{children:e.dragElementId&&(0,r.jsx)(e8,{...e,id:e.dragElementId,isDragOverlay:!0,parentId:""})}),document.body)]})}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(O.PageLoader,{size:"full",error:(0,r.jsxs)("div",{className:"my-2 flex flex-col gap-4 items-center justify-center",children:[(0,r.jsx)("div",{className:"",children:(0,r.jsx)(c.Npz,{className:"size-8"})}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Add an element to get started"}),e.isEditing&&(0,r.jsx)(e9,{align:"center",pushTransactions:e.pushTransactions,setActiveElementId:e.setActiveElementId,trigger:(0,r.jsxs)(R.z,{variant:"outline",className:"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 font-semibold",children:[(0,r.jsx)(c.oFk,{className:"size-3"}),"Add Element"]})})]})})})})},tr=e=>{let t=(0,d.useRef)(null);(0,d.useRef)(null);let a=(t,a,s)=>{let l=e.dashboardDefinition.elementMap[t.id],r=e.dashboardDefinition.rowsMap[t.parentId],n=e.dashboardDefinition.elementMap[a.id],i=e.dashboardDefinition.rowsMap[a.parentId];if(!l||!r||!n||!i)return;let d=[];if(r.children=(0,ts.removeArrayItem)(r.children,l.id),r.updatedTs=new Date().getTime(),d.push({action:"updateRow",row:r}),"t"===s||"b"===s){let e={id:(0,eQ.generateUUID)(),children:[l.id]};d.push({action:"addRow",row:e,beforeId:"t"===s?i.id:"",afterId:"b"===s?i.id:""})}else i.children=(0,ts.arrayAddElementAdjacent)(i.children,n.id,l.id,"l"===s?"before":"after"),i.updatedTs=new Date().getTime(),d.push({action:"updateRow",row:i});0===r.children.length&&d.push({action:"deleteRow",row:r}),e.pushTransactions(d),e.setActiveElementId(t.id)},s=()=>{let e=t.current;e&&e.element.classList.remove("dragover","l","r","t","b"),t.current=null;let a=document.querySelector(".dashER[data-ele-id].dragover");a&&a.classList.remove("dragover","l","r","t","b")},l=(e,t,a)=>{let s=null==t?void 0:t.rect;if(!s)return"";let{left:l,top:r,right:n,bottom:i}=e.rect.current.translated,d=Math.abs(l-s.left),o=Math.abs(l-s.right),c=Math.abs(r-s.top);return(console.log("Distance: ",{leftDistance:d,topDistance:c,rightDistance:o}),d<10)?"l":o<10?"r":c<10?"t":""},n=(0,eK.Dy)((0,eK.VT)(eK.we,{activationConstraint:{distance:10}}));return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(eK.LB,{onDragEnd:function(r){var n,i,d,o;if(e.dragElementId&&e.setDragElementId(""),!t.current)return;let{active:c,over:u,delta:x}=r;if(!(u&&(null===(o=u.data)||void 0===o?void 0:null===(d=o.current)||void 0===d?void 0:d.accepts.includes(null===(i=c.data)||void 0===i?void 0:null===(n=i.current)||void 0===n?void 0:n.type)))||u.id===c.id)return;let h=u.data.current,m=c.data.current,f=l(c,u,x),{id:p,parentId:g}=u.data.current;a({id:m.id,parentId:m.parentId||""},{id:h.id,parentId:h.parentId||""},f),console.log("DragEnd:",r),s()},onDragMove:e=>{var a;let{active:s,over:r,delta:n}=e;if(!r){console.log("No over");return}if(r.id===s.id){console.log("Over over active");return}if(t.current)t.current.id!==r.id&&(t.current.element.classList.remove("l","r","t","b","dragover"),t.current=null);else{let e=document.querySelector(".dashER[data-ele-id='".concat(r.id,"']"));e&&(t.current={element:e,id:r.id})}t.current||console.log("dragOverRef.current is null");let i=null===(a=t.current)||void 0===a?void 0:a.element;if(!i){console.log("Drag Over Ele is null or undefined");return}let d=l(s,r,n);i.classList.remove("l","r","t","b","dragover"),d&&i.classList.add(d,"dragover"),console.log("Over",r)},onDragOver:e=>{},onDragStart:t=>{console.log("DragStart:",t),t.active.id&&e.setDragElementId(t.active.id.toString())},onDragCancel:e=>{s()},sensors:n,children:e.children})})},tn={elementMap:{},rowsMap:{},children:[]},ti={id:(0,eQ.generateUUID)(),children:[]};for(let e=0;e<3;e++){let e={iconPosition:u.Ly.Left,valueResolve:{databaseId:"",filter:{conditions:[],match:A.Match.All},aggregateBy:M.CountAggregateFunction.CountAll,columnId:""},title:"Infobox",id:(0,eQ.generateUUID)(),type:u.Bq.Infobox};tn.elementMap[e.id]=e,ti.children.push(e.id)}let td={id:(0,eQ.generateUUID)(),children:[]};for(let e=0;e<3;e++){let e={title:"PieChart",id:(0,eQ.generateUUID)(),type:u.Bq.PieChart,recordsResolve:{databaseId:"",filter:{conditions:[],match:A.Match.All},groupByIds:[],titleColId:""}};tn.elementMap[e.id]=e,td.children.push(e.id)}let to={id:(0,eQ.generateUUID)(),children:[]};for(let e=0;e<2;e++){let e={columnPropsMap:{},columnsOrder:[],title:"Infobox",id:(0,eQ.generateUUID)(),type:u.Bq.LineChart,recordsResolve:{databaseId:"",filter:{conditions:[],match:A.Match.All},sorts:[]}};tn.elementMap[e.id]=e,to.children.push(e.id)}let tc={id:(0,eQ.generateUUID)(),children:[]};for(let e=0;e<2;e++){let e={title:"Infobox",id:(0,eQ.generateUUID)(),type:u.Bq.Text,content:""};tn.elementMap[e.id]=e,tc.children.push(e.id)}[ti,td,tc,to].forEach(e=>{tn.rowsMap[e.id]=e,tn.children.push(e.id)});let tu=Object.freeze(tn);a(91695);var tx=a(38218);let th=e=>{let[t,a]=(0,d.useState)(""),[s,l]=(0,d.useState)(""),{updateViewDefinition:n,cache:i,pushDashboardTransactions:o}=(0,P.Bf)(),c=!!i.getCache(tx.w_,!1),{definition:u}=e,[x,h]=(0,d.useState)(u.definition);u.definition=u.definition||{},u.definition.children=u.definition.children||[],u.definition.rowsMap=u.definition.rowsMap||{},u.definition.elementMap=u.definition.elementMap||{};let[m,f]=(0,d.useState)((()=>{let e=localStorage.getItem("dfDashboard");if(e)try{return JSON.parse(e)}catch(e){}return null})()||tu);c||(s="");let p=async t=>{0!==t.length&&await o(e.view.id,e.view.pageId,t)},g={dashboardDefinition:u.definition,updateData:e=>{},dragElementId:t,setDragElementId:a,activeElementId:s,setActiveElementId:l,isEditing:c,pushTransactions:p};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"w-full h-full flex",children:[(0,r.jsx)(tl,{...g},c?1:0),(0,r.jsx)(eZ,{...g})]})})}},21976:function(){},91695:function(){}}]);