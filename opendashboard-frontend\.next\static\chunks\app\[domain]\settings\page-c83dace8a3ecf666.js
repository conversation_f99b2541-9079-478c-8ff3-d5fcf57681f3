!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="10cda4ca-c3e6-4d54-89ae-be686bb49c05",e._sentryDebugIdIdentifier="sentry-dbid-10cda4ca-c3e6-4d54-89ae-be686bb49c05")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2725],{87036:function(e,n,r){Promise.resolve().then(r.bind(r,97865))},99376:function(e,n,r){"use strict";var t=r(35475);r.o(t,"redirect")&&r.d(n,{redirect:function(){return t.redirect}}),r.o(t,"useParams")&&r.d(n,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(n,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(n,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(n,{useSearchParams:function(){return t.useSearchParams}})},97865:function(e,n,r){"use strict";r.r(n);var t=r(99376),u=r(42212);n.default=()=>{let{url:e}=(0,u.cF)();(0,t.redirect)(e("/settings/account"))}}},function(e){e.O(0,[6018,8310,6137,7648,311,2534,4451,1107,85,3493,3139,8107,5737,7900,2211,2212,991,2971,6577,1744],function(){return e(e.s=87036)}),_N_E=e.O()}]);