!function(){try{var t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="68f35cf9-6b11-4208-9a34-5c56bea2e88a",t._sentryDebugIdIdentifier="sentry-dbid-68f35cf9-6b11-4208-9a34-5c56bea2e88a")}catch(t){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1107],{2859:function(t,e,n){n.d(e,{x7:function(){return Z},Me:function(){return _},oo:function(){return U},RR:function(){return Q},Cp:function(){return q},Qo:function(){return $},dr:function(){return G},cv:function(){return I},uY:function(){return J},dp:function(){return X}});let r=["top","right","bottom","left"],i=Math.min,o=Math.max,l=Math.round,f=Math.floor,u=t=>({x:t,y:t}),a={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function s(t,e){return"function"==typeof t?t(e):t}function d(t){return t.split("-")[0]}function p(t){return t.split("-")[1]}function h(t){return"x"===t?"y":"x"}function g(t){return"y"===t?"height":"width"}function m(t){return["top","bottom"].includes(d(t))?"y":"x"}function y(t){return t.replace(/start|end/g,t=>c[t])}function w(t){return t.replace(/left|right|bottom|top/g,t=>a[t])}function x(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function v(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function b(t,e,n){let r,{reference:i,floating:o}=t,l=m(e),f=h(m(e)),u=g(f),a=d(e),c="y"===l,s=i.x+i.width/2-o.width/2,y=i.y+i.height/2-o.height/2,w=i[u]/2-o[u]/2;switch(a){case"top":r={x:s,y:i.y-o.height};break;case"bottom":r={x:s,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:y};break;case"left":r={x:i.x-o.width,y:y};break;default:r={x:i.x,y:i.y}}switch(p(e)){case"start":r[f]-=w*(n&&c?-1:1);break;case"end":r[f]+=w*(n&&c?-1:1)}return r}let R=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,f=o.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(e)),a=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:c,y:s}=b(a,r,u),d=r,p={},h=0;for(let n=0;n<f.length;n++){let{name:o,fn:g}=f[n],{x:m,y:y,data:w,reset:x}=await g({x:c,y:s,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:a,platform:l,elements:{reference:t,floating:e}});c=null!=m?m:c,s=null!=y?y:s,p={...p,[o]:{...p[o],...w}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(a=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):x.rects),{x:c,y:s}=b(a,d,u)),n=-1)}return{x:c,y:s,placement:d,strategy:i,middlewareData:p}};async function A(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:f,strategy:u}=t,{boundary:a="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=s(e,t),g=x(h),m=f[p?"floating"===d?"reference":"floating":d],y=v(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(m)))||n?m:m.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(f.floating)),boundary:a,rootBoundary:c,strategy:u})),w="floating"===d?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(f.floating)),R=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},A=v(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:w,offsetParent:b,strategy:u}):w);return{top:(y.top-A.top+g.top)/R.y,bottom:(A.bottom-y.bottom+g.bottom)/R.y,left:(y.left-A.left+g.left)/R.x,right:(A.right-y.right+g.right)/R.x}}function L(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function k(t){return r.some(e=>t[e]>=0)}function C(t){let e=i(...t.map(t=>t.left)),n=i(...t.map(t=>t.top));return{x:e,y:n,width:o(...t.map(t=>t.right))-e,height:o(...t.map(t=>t.bottom))-n}}async function P(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=d(n),f=p(n),u="y"===m(n),a=["left","top"].includes(l)?-1:1,c=o&&u?-1:1,h=s(e,t),{mainAxis:g,crossAxis:y,alignmentAxis:w}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return f&&"number"==typeof w&&(y="end"===f?-1*w:w),u?{x:y*c,y:g*a}:{x:g*a,y:y*c}}var O=n(94046);function T(t){let e=(0,O.Dx)(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=(0,O.Re)(t),o=i?t.offsetWidth:n,f=i?t.offsetHeight:r,u=l(n)!==o||l(r)!==f;return u&&(n=o,r=f),{width:n,height:r,$:u}}function E(t){return(0,O.kK)(t)?t:t.contextElement}function D(t){let e=E(t);if(!(0,O.Re)(e))return u(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=T(e),f=(o?l(n.width):n.width)/r,a=(o?l(n.height):n.height)/i;return f&&Number.isFinite(f)||(f=1),a&&Number.isFinite(a)||(a=1),{x:f,y:a}}let S=u(0);function F(t){let e=(0,O.Jj)(t);return(0,O.Pf)()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:S}function j(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=E(t),f=u(1);e&&(r?(0,O.kK)(r)&&(f=D(r)):f=D(t));let a=(void 0===(i=n)&&(i=!1),r&&(!i||r===(0,O.Jj)(l))&&i)?F(l):u(0),c=(o.left+a.x)/f.x,s=(o.top+a.y)/f.y,d=o.width/f.x,p=o.height/f.y;if(l){let t=(0,O.Jj)(l),e=r&&(0,O.kK)(r)?(0,O.Jj)(r):r,n=t,i=(0,O.wK)(n);for(;i&&r&&e!==n;){let t=D(i),e=i.getBoundingClientRect(),r=(0,O.Dx)(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,s*=t.y,d*=t.x,p*=t.y,c+=o,s+=l,n=(0,O.Jj)(i),i=(0,O.wK)(n)}}return v({width:d,height:p,x:c,y:s})}function H(t,e){let n=(0,O.Lw)(t).scrollLeft;return e?e.left+n:j((0,O.tF)(t)).left+n}function W(t,e,n){void 0===n&&(n=!1);let r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:H(t,r)),y:r.top+e.scrollTop}}function K(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=(0,O.Jj)(t),r=(0,O.tF)(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,f=0,u=0;if(i){o=i.width,l=i.height;let t=(0,O.Pf)();(!t||t&&"fixed"===e)&&(f=i.offsetLeft,u=i.offsetTop)}return{width:o,height:l,x:f,y:u}}(t,n);else if("document"===e)r=function(t){let e=(0,O.tF)(t),n=(0,O.Lw)(t),r=t.ownerDocument.body,i=o(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),l=o(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),f=-n.scrollLeft+H(t),u=-n.scrollTop;return"rtl"===(0,O.Dx)(r).direction&&(f+=o(e.clientWidth,r.clientWidth)-i),{width:i,height:l,x:f,y:u}}((0,O.tF)(t));else if((0,O.kK)(e))r=function(t,e){let n=j(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=(0,O.Re)(t)?D(t):u(1),l=t.clientWidth*o.x;return{width:l,height:t.clientHeight*o.y,x:i*o.x,y:r*o.y}}(e,n);else{let n=F(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return v(r)}function M(t){return"static"===(0,O.Dx)(t).position}function V(t,e){if(!(0,O.Re)(t)||"fixed"===(0,O.Dx)(t).position)return null;if(e)return e(t);let n=t.offsetParent;return(0,O.tF)(t)===n&&(n=n.ownerDocument.body),n}function z(t,e){let n=(0,O.Jj)(t);if((0,O.tR)(t))return n;if(!(0,O.Re)(t)){let e=(0,O.Ow)(t);for(;e&&!(0,O.Py)(e);){if((0,O.kK)(e)&&!M(e))return e;e=(0,O.Ow)(e)}return n}let r=V(t,e);for(;r&&(0,O.Ze)(r)&&M(r);)r=V(r,e);return r&&(0,O.Py)(r)&&M(r)&&!(0,O.hT)(r)?n:r||(0,O.gQ)(t)||n}let B=async function(t){let e=this.getOffsetParent||z,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=(0,O.Re)(e),i=(0,O.tF)(e),o="fixed"===n,l=j(t,!0,o,e),f={scrollLeft:0,scrollTop:0},a=u(0);if(r||!r&&!o){if(("body"!==(0,O.wk)(e)||(0,O.ao)(i))&&(f=(0,O.Lw)(e)),r){let t=j(e,!0,o,e);a.x=t.x+e.clientLeft,a.y=t.y+e.clientTop}else i&&(a.x=H(i))}o&&!r&&i&&(a.x=H(i));let c=!i||r||o?u(0):W(i,f);return{x:l.left+f.scrollLeft-a.x-c.x,y:l.top+f.scrollTop-a.y-c.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},N={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=(0,O.tF)(r),f=!!e&&(0,O.tR)(e.floating);if(r===l||f&&o)return n;let a={scrollLeft:0,scrollTop:0},c=u(1),s=u(0),d=(0,O.Re)(r);if((d||!d&&!o)&&(("body"!==(0,O.wk)(r)||(0,O.ao)(l))&&(a=(0,O.Lw)(r)),(0,O.Re)(r))){let t=j(r);c=D(r),s.x=t.x+r.clientLeft,s.y=t.y+r.clientTop}let p=!l||d||o?u(0):W(l,a,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+s.x+p.x,y:n.y*c.y-a.scrollTop*c.y+s.y+p.y}},getDocumentElement:O.tF,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:l}=t,f=[..."clippingAncestors"===n?(0,O.tR)(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=(0,O.Kx)(t,[],!1).filter(t=>(0,O.kK)(t)&&"body"!==(0,O.wk)(t)),i=null,o="fixed"===(0,O.Dx)(t).position,l=o?(0,O.Ow)(t):t;for(;(0,O.kK)(l)&&!(0,O.Py)(l);){let e=(0,O.Dx)(l),n=(0,O.hT)(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&["absolute","fixed"].includes(i.position)||(0,O.ao)(l)&&!n&&function t(e,n){let r=(0,O.Ow)(e);return!(r===n||!(0,O.kK)(r)||(0,O.Py)(r))&&("fixed"===(0,O.Dx)(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=(0,O.Ow)(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],u=f[0],a=f.reduce((t,n)=>{let r=K(e,n,l);return t.top=o(r.top,t.top),t.right=i(r.right,t.right),t.bottom=i(r.bottom,t.bottom),t.left=o(r.left,t.left),t},K(e,u,l));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:z,getElementRects:B,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=T(t);return{width:e,height:n}},getScale:D,isElement:O.kK,isRTL:function(t){return"rtl"===(0,O.Dx)(t).direction}};function Y(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function _(t,e,n,r){let l;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:a=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=E(t),h=u||a?[...p?(0,O.Kx)(p):[],...(0,O.Kx)(e)]:[];h.forEach(t=>{u&&t.addEventListener("scroll",n,{passive:!0}),a&&t.addEventListener("resize",n)});let g=p&&s?function(t,e){let n,r=null,l=(0,O.tF)(t);function u(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function a(c,s){void 0===c&&(c=!1),void 0===s&&(s=1),u();let d=t.getBoundingClientRect(),{left:p,top:h,width:g,height:m}=d;if(c||e(),!g||!m)return;let y=f(h),w=f(l.clientWidth-(p+g)),x={rootMargin:-y+"px "+-w+"px "+-f(l.clientHeight-(h+m))+"px "+-f(p)+"px",threshold:o(0,i(1,s))||1},v=!0;function b(e){let r=e[0].intersectionRatio;if(r!==s){if(!v)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||Y(d,t.getBoundingClientRect())||a(),v=!1}try{r=new IntersectionObserver(b,{...x,root:l.ownerDocument})}catch(t){r=new IntersectionObserver(b,x)}r.observe(t)}(!0),u}(p,n):null,m=-1,y=null;c&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===p&&y&&(y.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),p&&!d&&y.observe(p),y.observe(e));let w=d?j(t):null;return d&&function e(){let r=j(t);w&&!Y(w,r)&&n(),w=r,l=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{u&&t.removeEventListener("scroll",n),a&&t.removeEventListener("resize",n)}),null==g||g(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(l)}}let I=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:i,y:o,placement:l,middlewareData:f}=e,u=await P(e,t);return l===(null==(n=f.offset)?void 0:n.placement)&&null!=(r=f.arrow)&&r.alignmentOffset?{}:{x:i+u.x,y:o+u.y,data:{...u,placement:l}}}}},J=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:l}=e,{mainAxis:f=!0,crossAxis:u=!1,limiter:a={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=s(t,e),p={x:n,y:r},g=await A(e,c),y=m(d(l)),w=h(y),x=p[w],v=p[y];if(f){let t="y"===w?"top":"left",e="y"===w?"bottom":"right",n=x+g[t],r=x-g[e];x=o(n,i(x,r))}if(u){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=v+g[t],r=v-g[e];v=o(n,i(v,r))}let b=a.fn({...e,[w]:x,[y]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:f,[y]:u}}}}}},Q=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r,i,o,l,f;let{placement:u,middlewareData:a,rects:c,initialPlacement:x,platform:v,elements:b}=e,{mainAxis:R=!0,crossAxis:L=!0,fallbackPlacements:k,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:O=!0,...T}=s(t,e);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};let E=d(u),D=m(x),S=d(x)===x,F=await (null==v.isRTL?void 0:v.isRTL(b.floating)),j=k||(S||!O?[w(x)]:function(t){let e=w(t);return[y(t),e,y(e)]}(x)),H="none"!==P;!k&&H&&j.push(...function(t,e,n,r){let i=p(t),o=function(t,e,n){let r=["left","right"],i=["right","left"];switch(t){case"top":case"bottom":if(n)return e?i:r;return e?r:i;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(d(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(y)))),o}(x,O,P,F));let W=[x,...j],K=await A(e,T),M=[],V=(null==(r=a.flip)?void 0:r.overflows)||[];if(R&&M.push(K[E]),L){let t=function(t,e,n){void 0===n&&(n=!1);let r=p(t),i=h(m(t)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=w(l)),[l,w(l)]}(u,c,F);M.push(K[t[0]],K[t[1]])}if(V=[...V,{placement:u,overflows:M}],!M.every(t=>t<=0)){let t=((null==(i=a.flip)?void 0:i.index)||0)+1,e=W[t];if(e){let n="alignment"===L&&D!==m(e),r=(null==(l=V[0])?void 0:l.overflows[0])>0;if(!n||r)return{data:{index:t,overflows:V},reset:{placement:e}}}let n=null==(o=V.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let t=null==(f=V.filter(t=>{if(H){let e=m(t.placement);return e===D||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:f[0];t&&(n=t);break}case"initialPlacement":n=x}if(u!==n)return{reset:{placement:n}}}return{}}}},X=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;let l,f;let{placement:u,rects:a,platform:c,elements:h}=e,{apply:g=()=>{},...y}=s(t,e),w=await A(e,y),x=d(u),v=p(u),b="y"===m(u),{width:R,height:L}=a.floating;"top"===x||"bottom"===x?(l=x,f=v===(await (null==c.isRTL?void 0:c.isRTL(h.floating))?"start":"end")?"left":"right"):(f=x,l="end"===v?"top":"bottom");let k=L-w.top-w.bottom,C=R-w.left-w.right,P=i(L-w[l],k),O=i(R-w[f],C),T=!e.middlewareData.shift,E=P,D=O;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(D=C),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(E=k),T&&!v){let t=o(w.left,0),e=o(w.right,0),n=o(w.top,0),r=o(w.bottom,0);b?D=R-2*(0!==t||0!==e?t+e:o(w.left,w.right)):E=L-2*(0!==n||0!==r?n+r:o(w.top,w.bottom))}await g({...e,availableWidth:D,availableHeight:E});let S=await c.getDimensions(h.floating);return R!==S.width||L!==S.height?{reset:{rects:!0}}:{}}}},q=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...i}=s(t,e);switch(r){case"referenceHidden":{let t=L(await A(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:k(t)}}}case"escaped":{let t=L(await A(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:k(t)}}}default:return{}}}}},Z=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:l,rects:f,platform:u,elements:a,middlewareData:c}=e,{element:d,padding:y=0}=s(t,e)||{};if(null==d)return{};let w=x(y),v={x:n,y:r},b=h(m(l)),R=g(b),A=await u.getDimensions(d),L="y"===b,k=L?"clientHeight":"clientWidth",C=f.reference[R]+f.reference[b]-v[b]-f.floating[R],P=v[b]-f.reference[b],O=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),T=O?O[k]:0;T&&await (null==u.isElement?void 0:u.isElement(O))||(T=a.floating[k]||f.floating[R]);let E=T/2-A[R]/2-1,D=i(w[L?"top":"left"],E),S=i(w[L?"bottom":"right"],E),F=T-A[R]-S,j=T/2-A[R]/2+(C/2-P/2),H=o(D,i(j,F)),W=!c.arrow&&null!=p(l)&&j!==H&&f.reference[R]/2-(j<D?D:S)-A[R]/2<0,K=W?j<D?j-D:j-F:0;return{[b]:v[b]+K,data:{[b]:H,centerOffset:j-H-K,...W&&{alignmentOffset:K}},reset:W}}}),$=function(t){return void 0===t&&(t={}),{name:"inline",options:t,async fn(e){let{placement:n,elements:r,rects:l,platform:f,strategy:u}=e,{padding:a=2,x:c,y:p}=s(t,e),h=Array.from(await (null==f.getClientRects?void 0:f.getClientRects(r.reference))||[]),g=function(t){let e=t.slice().sort((t,e)=>t.y-e.y),n=[],r=null;for(let t=0;t<e.length;t++){let i=e[t];!r||i.y-r.y>r.height/2?n.push([i]):n[n.length-1].push(i),r=i}return n.map(t=>v(C(t)))}(h),y=v(C(h)),w=x(a),b=await f.getElementRects({reference:{getBoundingClientRect:function(){if(2===g.length&&g[0].left>g[1].right&&null!=c&&null!=p)return g.find(t=>c>t.left-w.left&&c<t.right+w.right&&p>t.top-w.top&&p<t.bottom+w.bottom)||y;if(g.length>=2){if("y"===m(n)){let t=g[0],e=g[g.length-1],r="top"===d(n),i=t.top,o=e.bottom,l=r?t.left:e.left,f=r?t.right:e.right;return{top:i,bottom:o,left:l,right:f,width:f-l,height:o-i,x:l,y:i}}let t="left"===d(n),e=o(...g.map(t=>t.right)),r=i(...g.map(t=>t.left)),l=g.filter(n=>t?n.left===r:n.right===e),f=l[0].top,u=l[l.length-1].bottom;return{top:f,bottom:u,left:r,right:e,width:e-r,height:u-f,x:r,y:f}}return y}},floating:r.floating,strategy:u});return l.reference.x!==b.reference.x||l.reference.y!==b.reference.y||l.reference.width!==b.reference.width||l.reference.height!==b.reference.height?{reset:{rects:b}}:{}}}},G=function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:f=0,mainAxis:u=!0,crossAxis:a=!0}=s(t,e),c={x:n,y:r},p=m(i),g=h(p),y=c[g],w=c[p],x=s(f,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(u){let t="y"===g?"height":"width",e=o.reference[g]-o.floating[t]+v.mainAxis,n=o.reference[g]+o.reference[t]-v.mainAxis;y<e?y=e:y>n&&(y=n)}if(a){var b,R;let t="y"===g?"width":"height",e=["top","left"].includes(d(i)),n=o.reference[p]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[p])||0)+(e?0:v.crossAxis),r=o.reference[p]+o.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[p])||0)-(e?v.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[g]:y,[p]:w}}}},U=(t,e,n)=>{let r=new Map,i={platform:N,...n},o={...i.platform,_c:r};return R(t,e,{...i,platform:o})}},97859:function(t,e,n){n.d(e,{Cp:function(){return w},Qo:function(){return x},RR:function(){return m},YF:function(){return s},cv:function(){return p},dp:function(){return y},dr:function(){return g},uY:function(){return h},x7:function(){return v}});var r=n(2859),i=n(2265),o=n(54887),l="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;function f(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!f(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!f(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function u(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function a(t,e){let n=u(t);return Math.round(e*n)/n}function c(t){let e=i.useRef(t);return l(()=>{e.current=t}),e}function s(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:s=[],platform:d,elements:{reference:p,floating:h}={},transform:g=!0,whileElementsMounted:m,open:y}=t,[w,x]=i.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[v,b]=i.useState(s);f(v,s)||b(s);let[R,A]=i.useState(null),[L,k]=i.useState(null),C=i.useCallback(t=>{t!==E.current&&(E.current=t,A(t))},[]),P=i.useCallback(t=>{t!==D.current&&(D.current=t,k(t))},[]),O=p||R,T=h||L,E=i.useRef(null),D=i.useRef(null),S=i.useRef(w),F=null!=m,j=c(m),H=c(d),W=c(y),K=i.useCallback(()=>{if(!E.current||!D.current)return;let t={placement:e,strategy:n,middleware:v};H.current&&(t.platform=H.current),(0,r.oo)(E.current,D.current,t).then(t=>{let e={...t,isPositioned:!1!==W.current};M.current&&!f(S.current,e)&&(S.current=e,o.flushSync(()=>{x(e)}))})},[v,e,n,H,W]);l(()=>{!1===y&&S.current.isPositioned&&(S.current.isPositioned=!1,x(t=>({...t,isPositioned:!1})))},[y]);let M=i.useRef(!1);l(()=>(M.current=!0,()=>{M.current=!1}),[]),l(()=>{if(O&&(E.current=O),T&&(D.current=T),O&&T){if(j.current)return j.current(O,T,K);K()}},[O,T,K,j,F]);let V=i.useMemo(()=>({reference:E,floating:D,setReference:C,setFloating:P}),[C,P]),z=i.useMemo(()=>({reference:O,floating:T}),[O,T]),B=i.useMemo(()=>{let t={position:n,left:0,top:0};if(!z.floating)return t;let e=a(z.floating,w.x),r=a(z.floating,w.y);return g?{...t,transform:"translate("+e+"px, "+r+"px)",...u(z.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,g,z.floating,w.x,w.y]);return i.useMemo(()=>({...w,update:K,refs:V,elements:z,floatingStyles:B}),[w,K,V,z,B])}let d=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:i}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.x7)({element:n.current,padding:i}).fn(e):{}:n?(0,r.x7)({element:n,padding:i}).fn(e):{}}}),p=(t,e)=>({...(0,r.cv)(t),options:[t,e]}),h=(t,e)=>({...(0,r.uY)(t),options:[t,e]}),g=(t,e)=>({...(0,r.dr)(t),options:[t,e]}),m=(t,e)=>({...(0,r.RR)(t),options:[t,e]}),y=(t,e)=>({...(0,r.dp)(t),options:[t,e]}),w=(t,e)=>({...(0,r.Cp)(t),options:[t,e]}),x=(t,e)=>({...(0,r.Qo)(t),options:[t,e]}),v=(t,e)=>({...d(t),options:[t,e]})},94046:function(t,e,n){function r(){return"undefined"!=typeof window}function i(t){return f(t)?(t.nodeName||"").toLowerCase():"#document"}function o(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function l(t){var e;return null==(e=(f(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function f(t){return!!r()&&(t instanceof Node||t instanceof o(t).Node)}function u(t){return!!r()&&(t instanceof Element||t instanceof o(t).Element)}function a(t){return!!r()&&(t instanceof HTMLElement||t instanceof o(t).HTMLElement)}function c(t){return!!r()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof o(t).ShadowRoot)}function s(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=w(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(i)}function d(t){return["table","td","th"].includes(i(t))}function p(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function h(t){let e=m(),n=u(t)?w(t):t;return["transform","translate","scale","rotate","perspective"].some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function g(t){let e=v(t);for(;a(e)&&!y(e);){if(h(e))return e;if(p(e))break;e=v(e)}return null}function m(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function y(t){return["html","body","#document"].includes(i(t))}function w(t){return o(t).getComputedStyle(t)}function x(t){return u(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function v(t){if("html"===i(t))return t;let e=t.assignedSlot||t.parentNode||c(t)&&t.host||l(t);return c(e)?e.host:e}function b(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}n.d(e,{Dx:function(){return w},Jj:function(){return o},Kx:function(){return function t(e,n,r){var i;void 0===n&&(n=[]),void 0===r&&(r=!0);let l=function t(e){let n=v(e);return y(n)?e.ownerDocument?e.ownerDocument.body:e.body:a(n)&&s(n)?n:t(n)}(e),f=l===(null==(i=e.ownerDocument)?void 0:i.body),u=o(l);if(f){let e=b(u);return n.concat(u,u.visualViewport||[],s(l)?l:[],e&&r?t(e):[])}return n.concat(l,t(l,[],r))}},Lw:function(){return x},Ow:function(){return v},Pf:function(){return m},Py:function(){return y},Re:function(){return a},Ze:function(){return d},Zq:function(){return c},ao:function(){return s},gQ:function(){return g},hT:function(){return h},kK:function(){return u},tF:function(){return l},tR:function(){return p},wK:function(){return b},wk:function(){return i}})},21107:function(t,e,n){n.d(e,{ee:function(){return F},Eh:function(){return H},VY:function(){return j},fC:function(){return S},D7:function(){return m}});var r=n(2265),i=n(97859),o=n(2859),l=n(66840),f=n(57437),u=r.forwardRef((t,e)=>{let{children:n,width:r=10,height:i=5,...o}=t;return(0,f.jsx)(l.WV.svg,{...o,ref:e,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:(0,f.jsx)("polygon",{points:"0,0 30,0 15,10"})})});u.displayName="Arrow";var a=n(98575),c=n(73966),s=n(26606),d=n(61188),p=n(90420),h="Popper",[g,m]=(0,c.b)(h),[y,w]=g(h),x=t=>{let{__scopePopper:e,children:n}=t,[i,o]=r.useState(null);return(0,f.jsx)(y,{scope:e,anchor:i,onAnchorChange:o,children:n})};x.displayName=h;var v="PopperAnchor",b=r.forwardRef((t,e)=>{let{__scopePopper:n,virtualRef:i,...o}=t,u=w(v,n),c=r.useRef(null),s=(0,a.e)(e,c);return r.useEffect(()=>{u.onAnchorChange((null==i?void 0:i.current)||c.current)}),i?null:(0,f.jsx)(l.WV.div,{...o,ref:s})});b.displayName=v;var R="PopperContent",[A,L]=g(R),k=r.forwardRef((t,e)=>{var n,u,c,h,g,m,y,x;let{__scopePopper:v,side:b="bottom",sideOffset:L=0,align:k="center",alignOffset:C=0,arrowPadding:P=0,avoidCollisions:O=!0,collisionBoundary:S=[],collisionPadding:F=0,sticky:j="partial",hideWhenDetached:H=!1,updatePositionStrategy:W="optimized",onPlaced:K,...M}=t,V=w(R,v),[z,B]=r.useState(null),N=(0,a.e)(e,t=>B(t)),[Y,_]=r.useState(null),I=(0,p.t)(Y),J=null!==(y=null==I?void 0:I.width)&&void 0!==y?y:0,Q=null!==(x=null==I?void 0:I.height)&&void 0!==x?x:0,X="number"==typeof F?F:{top:0,right:0,bottom:0,left:0,...F},q=Array.isArray(S)?S:[S],Z=q.length>0,$={padding:X,boundary:q.filter(T),altBoundary:Z},{refs:G,floatingStyles:U,placement:tt,isPositioned:te,middlewareData:tn}=(0,i.YF)({strategy:"fixed",placement:b+("center"!==k?"-"+k:""),whileElementsMounted:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return(0,o.Me)(...e,{animationFrame:"always"===W})},elements:{reference:V.anchor},middleware:[(0,i.cv)({mainAxis:L+Q,alignmentAxis:C}),O&&(0,i.uY)({mainAxis:!0,crossAxis:!1,limiter:"partial"===j?(0,i.dr)():void 0,...$}),O&&(0,i.RR)({...$}),(0,i.dp)({...$,apply:t=>{let{elements:e,rects:n,availableWidth:r,availableHeight:i}=t,{width:o,height:l}=n.reference,f=e.floating.style;f.setProperty("--radix-popper-available-width","".concat(r,"px")),f.setProperty("--radix-popper-available-height","".concat(i,"px")),f.setProperty("--radix-popper-anchor-width","".concat(o,"px")),f.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),Y&&(0,i.x7)({element:Y,padding:P}),E({arrowWidth:J,arrowHeight:Q}),H&&(0,i.Cp)({strategy:"referenceHidden",...$})]}),[tr,ti]=D(tt),to=(0,s.W)(K);(0,d.b)(()=>{te&&(null==to||to())},[te,to]);let tl=null===(n=tn.arrow)||void 0===n?void 0:n.x,tf=null===(u=tn.arrow)||void 0===u?void 0:u.y,tu=(null===(c=tn.arrow)||void 0===c?void 0:c.centerOffset)!==0,[ta,tc]=r.useState();return(0,d.b)(()=>{z&&tc(window.getComputedStyle(z).zIndex)},[z]),(0,f.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:te?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ta,"--radix-popper-transform-origin":[null===(h=tn.transformOrigin)||void 0===h?void 0:h.x,null===(g=tn.transformOrigin)||void 0===g?void 0:g.y].join(" "),...(null===(m=tn.hide)||void 0===m?void 0:m.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:(0,f.jsx)(A,{scope:v,placedSide:tr,onArrowChange:_,arrowX:tl,arrowY:tf,shouldHideArrow:tu,children:(0,f.jsx)(l.WV.div,{"data-side":tr,"data-align":ti,...M,ref:N,style:{...M.style,animation:te?void 0:"none"}})})})});k.displayName=R;var C="PopperArrow",P={top:"bottom",right:"left",bottom:"top",left:"right"},O=r.forwardRef(function(t,e){let{__scopePopper:n,...r}=t,i=L(C,n),o=P[i.placedSide];return(0,f.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,f.jsx)(u,{...r,ref:e,style:{...r.style,display:"block"}})})});function T(t){return null!==t}O.displayName=C;var E=t=>({name:"transformOrigin",options:t,fn(e){var n,r,i,o,l;let{placement:f,rects:u,middlewareData:a}=e,c=(null===(n=a.arrow)||void 0===n?void 0:n.centerOffset)!==0,s=c?0:t.arrowWidth,d=c?0:t.arrowHeight,[p,h]=D(f),g={start:"0%",center:"50%",end:"100%"}[h],m=(null!==(o=null===(r=a.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+s/2,y=(null!==(l=null===(i=a.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,w="",x="";return"bottom"===p?(w=c?g:"".concat(m,"px"),x="".concat(-d,"px")):"top"===p?(w=c?g:"".concat(m,"px"),x="".concat(u.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),x=c?g:"".concat(y,"px")):"left"===p&&(w="".concat(u.floating.width+d,"px"),x=c?g:"".concat(y,"px")),{data:{x:w,y:x}}}});function D(t){let[e,n="center"]=t.split("-");return[e,n]}var S=x,F=b,j=k,H=O},90420:function(t,e,n){n.d(e,{t:function(){return o}});var r=n(2265),i=n(61188);function o(t){let[e,n]=r.useState(void 0);return(0,i.b)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}}}]);