!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="412c6ed0-0d66-442d-8a47-606ec671d85c",e._sentryDebugIdIdentifier="sentry-dbid-412c6ed0-0d66-442d-8a47-606ec671d85c")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[183],{809:function(e,t,r){Promise.resolve().then(r.bind(r,23325)),Promise.resolve().then(r.bind(r,9175))},53731:function(e,t){"use strict";var r=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;t.G=function(e){if(!e||e.length>254||!r.test(e))return!1;var t=e.split("@");return!(t[0].length>64||t[1].split(".").some(function(e){return e.length>63}))}},99376:function(e,t,r){"use strict";var a=r(35475);r.o(a,"redirect")&&r.d(t,{redirect:function(){return a.redirect}}),r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},23500:function(e,t){"use strict";var r,a,n;t.Bq=t.Ly=t.bW=void 0,(r=t.bW||(t.bW={})).Table="table",r.Board="board",r.Form="form",r.Document="document",r.Dashboard="dashboard",r.SummaryTable="summary-table",r.ListView="list-view",r.Calendar="calendar",(a=t.Ly||(t.Ly={})).Left="left",a.Right="right",(n=t.Bq||(t.Bq={})).Infobox="infobox",n.LineChart="lineChart",n.BarChart="barChart",n.PieChart="pieChart",n.FunnelChart="funnelChart",n.Embed="embed",n.Image="image",n.Text="text"},24369:function(e,t,r){"use strict";var a=r(2265),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},s=a.useState,l=a.useEffect,o=a.useLayoutEffect,i=a.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!n(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),a=s({inst:{value:r,getSnapshot:t}}),n=a[0].inst,d=a[1];return o(function(){n.value=r,n.getSnapshot=t,c(n)&&d({inst:n})},[e,r,t]),l(function(){return c(n)&&d({inst:n}),e(function(){c(n)&&d({inst:n})})},[e]),i(r),r};t.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:d},82558:function(e,t,r){"use strict";e.exports=r(24369)},23325:function(e,t,r){"use strict";r.d(t,{AddPurchaseModal:function(){return w},CreatorTemplatePurchases:function(){return j}});var a=r(57437),n=r(2265),s=r(90641),l=r(39255),o=r(55043),i=r(68898),c=r(99376),d=r(84440),u=r(21966),f=r(12381),h=r(27648),m=r(74291),x=r(75060),p=r(40279),v=r(94589),g=r(84977),b=r(12250);let j=()=>{var e,t,r;let{token:m}=(0,l.a)(),x=(0,c.useSearchParams)(),{currentCreator:p,creatorUrl:v,currentTemplate:g}=(0,o.KC)(),b=null==p?void 0:p.creator,j=Number(x.get("page"));(Number.isNaN(j)||j<1)&&(j=1);let[w,N]=(0,n.useState)({}),y=async()=>{if(w.isLoading||!m)return;N({isLoading:!0,error:void 0});let e=await (0,i.p4)(m.token,b.id,{perPage:50,page:j,templateId:null==g?void 0:g.template.id});if(e.error){N({isLoading:!1,error:e.error});return}N({isLoading:!1,data:{purchases:e.data.data.purchases}})};return(0,n.useEffect)(()=>{y().then()},[j]),(0,a.jsx)(a.Fragment,{children:w.isLoading||w.error||!w.data?(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"h-56",children:(0,a.jsx)(d.PageLoader,{cta:w.error?{label:"Reload",onClick:()=>y().then()}:null,error:w.error,size:"full"})})}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(s.ScrollArea,{className:"scrollBlockChild size-full",children:(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[w.data.purchases.length>0&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"text-sm",children:(0,a.jsx)(u.$,{backUrl:"/creators/".concat(b.domain,"/templates/").concat(null==g?void 0:null===(e=g.template)||void 0===e?void 0:e.id,"/purchases?page=").concat(j),creator:b,purchases:w.data.purchases})})}),0===w.data.purchases.length&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"h-24",children:(0,a.jsx)(d.PageLoader,{error:"It's empty here",size:"full"})})}),(0,a.jsxs)("div",{className:"flex gap-4 p-4 pt-0 -mt-4",children:[j>1&&(0,a.jsx)(f.z,{asChild:!0,variant:"link",className:"text-xs !p-1 !px-0 h-8 mt-1 w-auto rounded-full font-semibold gap-1",children:(0,a.jsx)(h.default,{href:"/creators/".concat(b.domain,"/templates/").concat(null==g?void 0:null===(t=g.template)||void 0===t?void 0:t.id,"/purchases?page=").concat(j-1),children:"← Previous page "})}),50===w.data.purchases.length&&(0,a.jsx)(f.z,{asChild:!0,variant:"link",className:"text-xs !p-1 !px-0 h-8 mt-1 w-auto rounded-full font-semibold gap-1",children:(0,a.jsx)(h.default,{href:"/creators/".concat(b.domain,"/templates/").concat(null==g?void 0:null===(r=g.template)||void 0===r?void 0:r.id,"/purchases?page=").concat(j+1),children:"Next page → "})})]})]})})})})},w=()=>{let[e,t]=(0,n.useState)(""),[r,d]=(0,n.useState)(""),[u,h]=(0,n.useState)(!0),[j,w]=(0,n.useState)(!1),{token:N}=(0,l.a)(),{currentCreator:y,currentTemplate:C}=(0,o.KC)(),{toast:k}=(0,g.V)(),S=(0,c.useRouter)(),P=!!r.trim()&&!!e.trim()&&(0,b.o)(r.trim()),E=async()=>{var t;if(!N||!y||!C)return;w(!0);let a=await (0,i.Z)(N.token,y.creator.id,{name:e,email:r,sendNotification:u,templateId:C.template.id});if(w(!1),a.error){k.error(a.error);return}let n=a.data.data.purchase,s="/creators/".concat(y.creator.domain,"/templates/").concat(null==C?void 0:null===(t=C.template)||void 0===t?void 0:t.id,"/purchases"),l="/creators/".concat(y.creator.domain,"/purchases/").concat(n.purchase.id,"?back=").concat(s);S.push(l)};return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(m.Vq,{children:[(0,a.jsx)(m.hg,{asChild:!0,children:(0,a.jsx)(f.z,{className:"text-xs rounded-full font-semibold",children:"Add Offline Purchase"})}),(0,a.jsx)(m.cZ,{className:"max-w-[600px] h-3/4 max-h-72 !rounded-none p-0",children:(0,a.jsxs)("div",{className:"size-full flex flex-col overflow-hidden",children:[(0,a.jsxs)(m.fK,{className:"p-4",children:[(0,a.jsx)(m.$N,{className:"font-bold !px-2",children:"Add Offline Purchase"}),(0,a.jsx)("div",{className:"text-xs text[9px] px-2",children:"Record an offline purchase and send a purchase code to the buyer"})]}),(0,a.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden",children:(0,a.jsx)(s.ScrollArea,{className:"scrollBlockChild",children:(0,a.jsxs)("div",{className:"p-4 flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-2",children:[(0,a.jsx)(x._,{className:"block text-xs font-medium leading-6 text-gray-900  lg:w-20",htmlFor:"name",children:"Buyer Name"}),(0,a.jsx)(p.I,{id:"name",type:"text",autoCapitalize:"none",autoCorrect:"off",disabled:j,value:e,onChange:e=>t(e.target.value),className:"text-xs rounded-none lg:flex-1"})]}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-2",children:[(0,a.jsx)(x._,{className:"block text-xs font-medium leading-6 text-gray-900  lg:w-20",htmlFor:"email",children:"Buyer Email"}),(0,a.jsx)(p.I,{id:"email",type:"text",autoCapitalize:"none",autoCorrect:"off",disabled:j,value:r,onChange:e=>d(e.target.value),className:"text-xs rounded-none lg:flex-1"})]}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-2",children:[(0,a.jsx)(x._,{className:"block text-xs font-medium leading-6 text-gray-900  lg:w-28",htmlFor:"name",children:"Send notification"}),(0,a.jsx)(v.r,{checked:u,disabled:j,onCheckedChange:h,"aria-readonly":!0})]}),(0,a.jsxs)("div",{className:"flex gap-4 mt-2",children:[(0,a.jsx)("div",{className:"flex-1"}),(0,a.jsx)(f.z,{className:"text-xs rounded-full font-semibold",disabled:j||!P,onClick:E,children:"Add purchase"})]})]})})})]})})]})})}},75060:function(e,t,r){"use strict";r.d(t,{_:function(){return c}});var a=r(57437),n=r(2265),s=r(6394),l=r(90535),o=r(93448);let i=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.f,{ref:t,className:(0,o.cn)(i(),r),...n})});c.displayName=s.f.displayName},94589:function(e,t,r){"use strict";r.d(t,{r:function(){return o}});var a=r(57437),n=r(2265),s=r(50721),l=r(93448);let o=n.forwardRef((e,t)=>{let{className:r,thumbClassName:n,...o}=e;return(0,a.jsx)(s.fC,{className:(0,l.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...o,ref:t,children:(0,a.jsx)(s.bU,{className:(0,l.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0",n)})})});o.displayName=s.fC.displayName},84977:function(e,t,r){"use strict";r.d(t,{AlertProvider:function(){return u},V:function(){return f}});var a=r(57437),n=r(2265),s=r(92367),l=r(14438),o=r(74291),i=r(12381),c=r(99376);let d=n.createContext(null),u=e=>{let t=(0,c.useRouter)(),r=(e,t,r,n)=>{(0,s._1)({title:e,message:"",buttons:r,overlayClassName:"",customUI:e=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(o.Vq,{open:!0,onOpenChange:()=>e.onClose(),children:(0,a.jsxs)(o.cZ,{className:"max-w-[600px] !rounded-none p-4",children:[(0,a.jsx)(o.fK,{children:(0,a.jsx)(o.$N,{className:"font-bold",children:e.title})}),(0,a.jsx)("div",{children:(0,a.jsx)("div",{className:"flex gap-2 py-2 text-xs font-medium",children:t})}),(0,a.jsx)(o.cN,{children:(0,a.jsx)("div",{className:"flex flex-row-reverse gap-1",children:r.map((t,r)=>(0,a.jsx)(i.z,{className:"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 ".concat("danger"===t.variant?"bg-red-600":""),variant:"ghost"===t.variant?"ghost":void 0,onClick:r=>{t.onClick&&!1===t.onClick()||e.onClose()},children:t.label},r))})})]})})}),...n||{}})},n={alert:(e,t,a,n)=>{r(e,t,[{label:"Ok",onClick:()=>a?a():void 0}],n)},choice:(e,t,a,n)=>{r(e,t,a,n)},promptUpgrade:(e,a)=>{r("Upgrade Needed!",e,[{label:"Upgrade",onClick:()=>{t.push("/".concat(a,"/settings/plans"))}},{label:"Cancel",variant:"ghost",onClick:()=>void 0}])},confirm:(e,t,a,n,s,l,o,i)=>{let c={label:o||"Confirm",onClick:a};l&&(c.variant="danger"),r(e,t,[c,{label:i||"Cancel",variant:"ghost",onClick:()=>n?n():void 0}],s)},toast:l.Am};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(d.Provider,{value:n,children:e.children})})};function f(){return(0,n.useContext)(d)}},12250:function(e,t,r){"use strict";r.d(t,{o:function(){return n}});var a=r(53731);let n=e=>a.G(e)},61146:function(e,t,r){"use strict";r.d(t,{NY:function(){return C},Ee:function(){return y},fC:function(){return N}});var a=r(2265),n=r(73966),s=r(26606),l=r(61188),o=r(66840),i=r(82558);function c(){return()=>{}}var d=r(57437),u="Avatar",[f,h]=(0,n.b)(u),[m,x]=f(u),p=a.forwardRef((e,t)=>{let{__scopeAvatar:r,...n}=e,[s,l]=a.useState("idle");return(0,d.jsx)(m,{scope:r,imageLoadingStatus:s,onImageLoadingStatusChange:l,children:(0,d.jsx)(o.WV.span,{...n,ref:t})})});p.displayName=u;var v="AvatarImage",g=a.forwardRef((e,t)=>{let{__scopeAvatar:r,src:n,onLoadingStatusChange:u=()=>{},...f}=e,h=x(v,r),m=function(e,t){let{referrerPolicy:r,crossOrigin:n}=t,s=(0,i.useSyncExternalStore)(c,()=>!0,()=>!1),o=a.useRef(null),d=s?(o.current||(o.current=new window.Image),o.current):null,[u,f]=a.useState(()=>w(d,e));return(0,l.b)(()=>{f(w(d,e))},[d,e]),(0,l.b)(()=>{let e=e=>()=>{f(e)};if(!d)return;let t=e("loaded"),a=e("error");return d.addEventListener("load",t),d.addEventListener("error",a),r&&(d.referrerPolicy=r),"string"==typeof n&&(d.crossOrigin=n),()=>{d.removeEventListener("load",t),d.removeEventListener("error",a)}},[d,n,r]),u}(n,f),p=(0,s.W)(e=>{u(e),h.onImageLoadingStatusChange(e)});return(0,l.b)(()=>{"idle"!==m&&p(m)},[m,p]),"loaded"===m?(0,d.jsx)(o.WV.img,{...f,ref:t,src:n}):null});g.displayName=v;var b="AvatarFallback",j=a.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:n,...s}=e,l=x(b,r),[i,c]=a.useState(void 0===n);return a.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>c(!0),n);return()=>window.clearTimeout(e)}},[n]),i&&"loaded"!==l.imageLoadingStatus?(0,d.jsx)(o.WV.span,{...s,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}j.displayName=b;var N=p,y=g,C=j},6394:function(e,t,r){"use strict";r.d(t,{f:function(){return o}});var a=r(2265),n=r(66840),s=r(57437),l=a.forwardRef((e,t)=>(0,s.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var o=l},50721:function(e,t,r){"use strict";r.d(t,{bU:function(){return y},fC:function(){return N}});var a=r(2265),n=r(6741),s=r(98575),l=r(73966),o=r(80886),i=r(6718),c=r(90420),d=r(66840),u=r(57437),f="Switch",[h,m]=(0,l.b)(f),[x,p]=h(f),v=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:l,checked:i,defaultChecked:c,required:h,disabled:m,value:p="on",onCheckedChange:v,form:g,...b}=e,[N,y]=a.useState(null),C=(0,s.e)(t,e=>y(e)),k=a.useRef(!1),S=!N||g||!!N.closest("form"),[P,E]=(0,o.T)({prop:i,defaultProp:null!=c&&c,onChange:v,caller:f});return(0,u.jsxs)(x,{scope:r,checked:P,disabled:m,children:[(0,u.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":P,"aria-required":h,"data-state":w(P),"data-disabled":m?"":void 0,disabled:m,value:p,...b,ref:C,onClick:(0,n.M)(e.onClick,e=>{E(e=>!e),S&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),S&&(0,u.jsx)(j,{control:N,bubbles:!k.current,name:l,value:p,checked:P,required:h,disabled:m,form:g,style:{transform:"translateX(-100%)"}})]})});v.displayName=f;var g="SwitchThumb",b=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,n=p(g,r);return(0,u.jsx)(d.WV.span,{"data-state":w(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:t})});b.displayName=g;var j=a.forwardRef((e,t)=>{let{__scopeSwitch:r,control:n,checked:l,bubbles:o=!0,...d}=e,f=a.useRef(null),h=(0,s.e)(f,t),m=(0,i.D)(l),x=(0,c.t)(n);return a.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==l&&t){let r=new Event("click",{bubbles:o});t.call(e,l),e.dispatchEvent(r)}},[m,l,o]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...d,tabIndex:-1,ref:h,style:{...d.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var N=v,y=b},6718:function(e,t,r){"use strict";r.d(t,{D:function(){return n}});var a=r(2265);function n(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}},function(e){e.O(0,[8310,6137,7648,311,2534,4451,1107,85,3493,3139,7515,8107,7900,2211,3561,7878,991,2971,6577,1744],function(){return e(e.s=809)}),_N_E=e.O()}]);